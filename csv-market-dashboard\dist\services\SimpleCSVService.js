"use strict";
// Simple JavaScript CSV Service that works
const fs = require('fs');
const csv = require('csv-parser');
class SimpleCSVService {
    constructor(csvFilePath = './instruments.csv') {
        this.csvFilePath = csvFilePath;
        this.instruments = [];
        this.cache = new Map();
    }
    async loadInstruments() {
        console.log(`📊 Loading instruments from CSV: ${this.csvFilePath}`);
        const startTime = Date.now();
        return new Promise((resolve, reject) => {
            const results = [];
            fs.createReadStream(this.csvFilePath)
                .pipe(csv())
                .on('data', (data) => {
                // Process each row
                const instrument = this.normalizeInstrument(data);
                if (instrument) {
                    results.push(instrument);
                }
            })
                .on('end', () => {
                this.instruments = results;
                const loadTime = Date.now() - startTime;
                console.log(`✅ Loaded ${this.instruments.length} instruments in ${loadTime}ms`);
                this.logInstrumentStats();
                resolve(this.instruments);
            })
                .on('error', (error) => {
                console.error('❌ Error loading CSV:', error);
                reject(error);
            });
        });
    }
    normalizeInstrument(row) {
        // Skip invalid rows
        if (!row.SECURITY_ID || !row.SYMBOL_NAME) {
            return null;
        }
        // Determine exchange segment
        const exchange = this.getExchangeSegment(row.EXCH_ID, row.SEGMENT);
        const exchangeCode = this.getExchangeCode(exchange);
        // Parse numeric values
        const lotSize = this.parseNumber(row.LOT_SIZE, 1);
        const tickSize = this.parseNumber(row.TICK_SIZE, 0.01);
        const strikePrice = this.parseNumber(row.STRIKE_PRICE);
        // Parse expiry date
        let expiryDate;
        if (row.SM_EXPIRY_DATE && row.SM_EXPIRY_DATE !== '-0.01000') {
            expiryDate = this.parseDate(row.SM_EXPIRY_DATE);
        }
        // Determine option type
        let optionType;
        if (row.OPTION_TYPE && ['CE', 'PE', 'XX'].includes(row.OPTION_TYPE)) {
            optionType = row.OPTION_TYPE;
        }
        return {
            securityId: row.SECURITY_ID,
            symbol: row.SYMBOL_NAME,
            displayName: row.DISPLAY_NAME || row.SYMBOL_NAME,
            exchange,
            segment: row.SEGMENT || 'C',
            instrumentType: row.INSTRUMENT || row.INSTRUMENT_TYPE || 'EQUITY',
            isin: row.ISIN !== 'NA' ? row.ISIN : undefined,
            lotSize,
            tickSize,
            underlyingSymbol: row.UNDERLYING_SYMBOL !== 'NA' ? row.UNDERLYING_SYMBOL : undefined,
            expiryDate,
            strikePrice,
            optionType,
            isActive: true,
            exchangeCode,
        };
    }
    getExchangeSegment(exchId, segment) {
        const exchange = (exchId || 'NSE').toUpperCase();
        const seg = (segment || 'C').toUpperCase();
        if (exchange === 'NSE') {
            if (seg === 'D')
                return 'NSE_FNO'; // Derivatives segment
            return seg === 'F' || seg === 'O' ? 'NSE_FNO' : 'NSE_EQ';
        }
        else if (exchange === 'BSE') {
            return seg === 'F' || seg === 'O' ? 'BSE_FNO' : 'BSE_EQ';
        }
        else if (exchange === 'MCX') {
            return 'MCX_COMM';
        }
        return 'NSE_EQ'; // Default
    }
    getExchangeCode(exchange) {
        const EXCHANGE_SEGMENTS = {
            'NSE_EQ': 1,
            'NSE_FNO': 2,
            'BSE_EQ': 3,
            'BSE_FNO': 4,
            'MCX_COMM': 5,
            'IDX_I': 6,
        };
        return EXCHANGE_SEGMENTS[exchange] || 1;
    }
    parseNumber(value, defaultValue = 0) {
        if (!value || value === 'NA' || value === '-0.01000') {
            return defaultValue;
        }
        const parsed = parseFloat(value);
        return isNaN(parsed) ? defaultValue : parsed;
    }
    parseDate(dateStr) {
        try {
            const date = new Date(dateStr);
            return isNaN(date.getTime()) ? undefined : date;
        }
        catch {
            return undefined;
        }
    }
    async getNSEDerivativesAndIndex() {
        if (this.instruments.length === 0) {
            await this.loadInstruments();
        }
        const targetInstruments = this.instruments.filter(inst => {
            // Check for NSE exchange and segment D
            const isNSE = inst.exchange.includes('NSE') || inst.exchange === 'NSE_FNO';
            const isSegmentD = inst.segment === 'D';
            const isTargetInstrument = ['FUTIDX', 'OPTIDX', 'INDEX'].includes(inst.instrumentType);
            return isNSE && isSegmentD && isTargetInstrument;
        });
        console.log(`🎯 Found ${targetInstruments.length} NSE derivatives and index instruments for subscription`);
        // Log breakdown by instrument type
        const breakdown = {};
        targetInstruments.forEach(inst => {
            breakdown[inst.instrumentType] = (breakdown[inst.instrumentType] || 0) + 1;
        });
        console.log('📊 Breakdown by instrument type:');
        Object.entries(breakdown).forEach(([type, count]) => {
            console.log(`   ${type}: ${count}`);
        });
        return targetInstruments;
    }
    async getNSEDerivativesSecurityIds() {
        const instruments = await this.getNSEDerivativesAndIndex();
        return instruments.map(inst => inst.securityId);
    }
    async getInstruments(filter = {}) {
        if (this.instruments.length === 0) {
            await this.loadInstruments();
        }
        let filtered = [...this.instruments];
        if (filter) {
            filtered = this.applyFilter(filtered, filter);
        }
        return {
            instruments: filtered,
            total: filtered.length,
            page: 1,
            pageSize: filtered.length,
            hasMore: false,
        };
    }
    applyFilter(instruments, filter) {
        let filtered = instruments;
        // Filter by exchange
        if (filter.exchange && filter.exchange.length > 0) {
            filtered = filtered.filter(inst => filter.exchange.includes(inst.exchange));
        }
        // Filter by instrument type
        if (filter.instrumentType && filter.instrumentType.length > 0) {
            filtered = filtered.filter(inst => filter.instrumentType.includes(inst.instrumentType));
        }
        // Filter by segment
        if (filter.segment && filter.segment.length > 0) {
            filtered = filtered.filter(inst => filter.segment.includes(inst.segment));
        }
        // Search filter
        if (filter.search) {
            const searchTerm = filter.search.toLowerCase();
            filtered = filtered.filter(inst => inst.symbol.toLowerCase().includes(searchTerm) ||
                inst.displayName.toLowerCase().includes(searchTerm) ||
                (inst.isin && inst.isin.toLowerCase().includes(searchTerm)) ||
                (inst.underlyingSymbol && inst.underlyingSymbol.toLowerCase().includes(searchTerm)));
        }
        return filtered;
    }
    async getExchanges() {
        if (this.instruments.length === 0) {
            await this.loadInstruments();
        }
        const exchanges = [...new Set(this.instruments.map(inst => inst.exchange))];
        return exchanges.sort();
    }
    async getInstrumentTypes() {
        if (this.instruments.length === 0) {
            await this.loadInstruments();
        }
        const types = [...new Set(this.instruments.map(inst => inst.instrumentType))];
        return types.sort();
    }
    async searchInstruments(query, limit = 50) {
        if (this.instruments.length === 0) {
            await this.loadInstruments();
        }
        const searchTerm = query.toLowerCase();
        const results = this.instruments
            .filter(inst => inst.symbol.toLowerCase().includes(searchTerm) ||
            inst.displayName.toLowerCase().includes(searchTerm) ||
            (inst.isin && inst.isin.toLowerCase().includes(searchTerm)))
            .slice(0, limit);
        return results;
    }
    async getInstrumentById(securityId) {
        if (this.instruments.length === 0) {
            await this.loadInstruments();
        }
        return this.instruments.find(inst => inst.securityId === securityId) || null;
    }
    logInstrumentStats() {
        const stats = {
            total: this.instruments.length,
            byExchange: {},
            byInstrumentType: {},
        };
        this.instruments.forEach(inst => {
            stats.byExchange[inst.exchange] = (stats.byExchange[inst.exchange] || 0) + 1;
            stats.byInstrumentType[inst.instrumentType] = (stats.byInstrumentType[inst.instrumentType] || 0) + 1;
        });
        console.log('📊 Instrument Statistics:');
        console.log(`   Total: ${stats.total}`);
        console.log('   By Exchange:', stats.byExchange);
        console.log('   By Type:', stats.byInstrumentType);
    }
}
module.exports = { SimpleCSVService };
//# sourceMappingURL=SimpleCSVService.js.map