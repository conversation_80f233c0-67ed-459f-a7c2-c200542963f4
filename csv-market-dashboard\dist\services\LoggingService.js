"use strict";
/**
 * ✅ ENHANCED LOGGING SERVICE
 * Provides structured logging for market data debugging
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.loggingService = exports.LogCategory = exports.LogLevel = void 0;
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["DEBUG"] = 0] = "DEBUG";
    LogLevel[LogLevel["INFO"] = 1] = "INFO";
    LogLevel[LogLevel["WARN"] = 2] = "WARN";
    LogLevel[LogLevel["ERROR"] = 3] = "ERROR";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
var LogCategory;
(function (LogCategory) {
    LogCategory["SPOT"] = "SPOT";
    LogCategory["DEPTH"] = "DEPTH";
    LogCategory["ASK"] = "ASK";
    LogCategory["BID"] = "BID";
    LogCategory["STRIKE"] = "STRIKE";
    LogCategory["EXPIRY"] = "EXPIRY";
    LogCategory["SUBSCRIPTION"] = "SUBSCRIPTION";
    LogCategory["CONNECTION"] = "CONNECTION";
    LogCategory["PARSING"] = "PARSING";
})(LogCategory || (exports.LogCategory = LogCategory = {}));
class LoggingService {
    logs = [];
    maxLogs = 1000;
    currentLevel = LogLevel.INFO;
    enabledCategories = new Set(Object.values(LogCategory));
    /**
     * Set minimum log level
     */
    setLevel(level) {
        this.currentLevel = level;
    }
    /**
     * Enable/disable specific log categories
     */
    setCategories(categories) {
        this.enabledCategories = new Set(categories);
    }
    /**
     * Log debug message
     */
    debug(category, message, data) {
        this.log(LogLevel.DEBUG, category, message, data);
    }
    /**
     * Log info message
     */
    info(category, message, data) {
        this.log(LogLevel.INFO, category, message, data);
    }
    /**
     * Log warning message
     */
    warn(category, message, data) {
        this.log(LogLevel.WARN, category, message, data);
    }
    /**
     * Log error message
     */
    error(category, message, data) {
        this.log(LogLevel.ERROR, category, message, data);
    }
    /**
     * Internal logging method
     */
    log(level, category, message, data) {
        if (level < this.currentLevel || !this.enabledCategories.has(category)) {
            return;
        }
        const entry = {
            timestamp: Date.now(),
            level,
            category,
            message,
            data
        };
        this.logs.push(entry);
        // Trim logs if exceeding max
        if (this.logs.length > this.maxLogs) {
            this.logs = this.logs.slice(-this.maxLogs);
        }
        // Console output with structured format
        const levelName = LogLevel[level];
        const prefix = `[${category}][${levelName}]`;
        const timestamp = new Date(entry.timestamp).toLocaleTimeString();
        switch (level) {
            case LogLevel.DEBUG:
                console.debug(`${timestamp} ${prefix} ${message}`, data || '');
                break;
            case LogLevel.INFO:
                console.info(`${timestamp} ${prefix} ${message}`, data || '');
                break;
            case LogLevel.WARN:
                console.warn(`${timestamp} ${prefix} ${message}`, data || '');
                break;
            case LogLevel.ERROR:
                console.error(`${timestamp} ${prefix} ${message}`, data || '');
                break;
        }
    }
    /**
     * Get recent logs
     */
    getRecentLogs(count = 100) {
        return this.logs.slice(-count);
    }
    /**
     * Get logs by category
     */
    getLogsByCategory(category, count = 50) {
        return this.logs
            .filter(log => log.category === category)
            .slice(-count);
    }
    /**
     * Clear all logs
     */
    clear() {
        this.logs = [];
    }
    /**
     * Export logs as JSON
     */
    exportLogs() {
        return JSON.stringify(this.logs, null, 2);
    }
    /**
     * Log market data packet details
     */
    logMarketDataPacket(symbol, responseCode, bufferLength, parsedData) {
        const packetTypes = {
            2: 'TICKER',
            4: 'QUOTE',
            5: 'OI',
            6: 'CLOSE',
            8: 'FULL'
        };
        const packetType = packetTypes[responseCode] || `UNKNOWN_${responseCode}`;
        this.debug(LogCategory.PARSING, `${symbol} ${packetType} packet`, {
            responseCode,
            bufferLength,
            ltp: parsedData.ltp,
            volume: parsedData.volume,
            openInterest: parsedData.openInterest,
            bid: parsedData.bid,
            ask: parsedData.ask
        });
    }
    /**
     * Log market depth details
     */
    logMarketDepth(symbol, marketDepth) {
        if (!marketDepth || marketDepth.length === 0) {
            this.warn(LogCategory.DEPTH, `${symbol} No market depth data`);
            return;
        }
        this.debug(LogCategory.DEPTH, `${symbol} Market depth (${marketDepth.length} levels)`, {
            bestBid: marketDepth[0]?.bidPrice,
            bestAsk: marketDepth[0]?.askPrice,
            bidQty: marketDepth[0]?.bidQty,
            askQty: marketDepth[0]?.askQty,
            allLevels: marketDepth
        });
    }
    /**
     * Log NIFTY spot price updates
     */
    logNiftySpotUpdate(price, source, securityId) {
        this.info(LogCategory.SPOT, `NIFTY Spot: ₹${price} from ${source}`, {
            price,
            source,
            securityId,
            timestamp: Date.now()
        });
    }
    /**
     * Log subscription details
     */
    logSubscription(instruments, batchSize) {
        const breakdown = instruments.reduce((acc, inst) => {
            acc[inst.instrumentType] = (acc[inst.instrumentType] || 0) + 1;
            return acc;
        }, {});
        this.info(LogCategory.SUBSCRIPTION, `Subscribing to ${instruments.length} instruments`, {
            total: instruments.length,
            batchSize,
            breakdown,
            sample: instruments.slice(0, 3).map(i => ({
                symbol: i.symbol,
                exchange: i.exchange,
                securityId: i.securityId
            }))
        });
    }
    /**
     * Log connection events
     */
    logConnection(event, details) {
        this.info(LogCategory.CONNECTION, event, details);
    }
    /**
     * Log option chain building
     */
    logOptionChainBuild(expiry, spotPrice, strikeCount) {
        this.info(LogCategory.STRIKE, `Building option chain for ${expiry}`, {
            expiry,
            spotPrice,
            strikeCount,
            timestamp: Date.now()
        });
    }
}
// Export singleton instance
exports.loggingService = new LoggingService();
// Set default configuration for development
if (process.env.NODE_ENV === 'development') {
    exports.loggingService.setLevel(LogLevel.DEBUG);
}
else {
    exports.loggingService.setLevel(LogLevel.INFO);
}
//# sourceMappingURL=LoggingService.js.map