'use client';

import OptionChain from '../../components/OptionChain';
import { useMarketData } from '../../hooks/useMarketData';

export default function OptionChainPage() {
  const { marketData } = useMarketData();

  // Convert array to Map for OptionChain component
  const marketDataMap = new Map();
  marketData.forEach(item => {
    if (item.securityId) {
      marketDataMap.set(item.securityId, item);
    }
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Bar */}
      <div className="bg-white border-b border-gray-200 px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <a
              href="/"
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors"
            >
              ← Main Dashboard
            </a>
            <a
              href="/subscribed"
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors"
            >
              📊 Subscribed Data
            </a>
          </div>
          {/* Status indicators can be re-added if you reimplement connection state in the store */}
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600">
              {marketData.length} instruments
            </span>
            <span className="text-sm text-gray-600">
              {marketData.filter(d => (d.ltp ?? 0) > 0).length} active
            </span>
          </div>
        </div>
      </div>
      {/* Option Chain Component */}
      <OptionChain marketData={marketDataMap} />
    </div>
  );
}
