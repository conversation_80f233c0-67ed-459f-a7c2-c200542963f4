{"version": 3, "file": "path-cache.js", "sourceRoot": "", "sources": ["../../src/utils/path-cache.ts"], "names": [], "mappings": ";;;AAQA,2BAAgC;AAChC,+BAA4B;AAE5B,MAAa,SAAS;IAMpB,YAAY,QAAiB,EAAE,cAAyB;QACtD,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI;YACtC,IAAI;YACJ,MAAM;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,MAAM;YACN,OAAO;YACP,OAAO;YACP,OAAO;SACR,CAAC;QACF,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;YAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;SAChC;IACH,CAAC;IAOM,mBAAmB,CAAC,IAAY;QACrC,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC9B,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SACnC;aAAM;YACL,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACjC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YACnC,OAAO,MAAM,CAAC;SACf;IACH,CAAC;IAQM,oBAAoB,CAAC,QAAgB,EAAE,SAAiB;QAC7D,MAAM,OAAO,GAAG,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;QACxC,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAChD,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE;YACrD,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;SAC1D;aAAM;YACL,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACpC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,CAAC;YAC1D,OAAO,MAAM,CAAC;SACf;IACH,CAAC;IAEO,WAAW,CAAC,EAClB,QAAQ,EACR,SAAS,EAIV;QACC,OAAO,GAAG,QAAQ,MAAM,SAAS,EAAE,CAAC;IACtC,CAAC;IAQO,MAAM,CAAC,EACb,QAAQ,EACR,SAAS,EAIV;QACC,MAAM,cAAc,GAAG,SAAS;aAC7B,KAAK,CAAC,GAAG,CAAC;aACV,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;QAE/C,IAAI,aAAa,GAAG,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;QAEjD,IAAI,UAAU,GAAG,KAAK,CAAC;QAEvB,OACE,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAA,WAAI,EAAC,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC;YAC1D,cAAc,CAAC,MAAM,EACrB;YACA,aAAa,GAAG,cAAc,CAAC,KAAK,EAAE,CAAC;SACxC;QAED,IAAI,UAAU,EAAE;YACd,OAAO,IAAA,WAAI,EAAC,QAAQ,EAAE,aAAa,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;SAChE;QAED,OAAO,KAAK,GAAG,IAAA,WAAI,EAAC,QAAQ,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1D,CAAC;IAOO,MAAM,CAAC,IAAY;QACzB,OAAO,CACL,IAAA,eAAU,EAAC,IAAI,CAAC;YAChB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CACrC,IAAA,eAAU,EAAC,GAAG,IAAI,IAAI,SAAS,EAAE,CAAC,CACnC,CACF,CAAC;IACJ,CAAC;CACF;AArHD,8BAqHC", "sourcesContent": ["/**\n * @file\n *\n * The PathCache class is meant to cache path lookups like\n * exists and getAbsoluteAliasPath.\n */\n\n/** */\nimport { existsSync } from 'fs';\nimport { join } from 'path';\n\nexport class PathCache {\n  useCache: boolean;\n  existsCache: Map<string, boolean>;\n  absoluteCache: Map<string, string>;\n  fileExtensions: string[];\n\n  constructor(useCache: boolean, fileExtensions?: string[]) {\n    this.fileExtensions = fileExtensions || [\n      'js',\n      'json',\n      'jsx',\n      'cjs',\n      'mjs',\n      'd.ts',\n      'd.tsx',\n      'd.cts',\n      'd.mts'\n    ];\n    this.useCache = useCache;\n    if (useCache) {\n      this.existsCache = new Map();\n      this.absoluteCache = new Map();\n    }\n  }\n\n  /**\n   * existsResolvedAlias checks if file exists, uses cache when possible.\n   * @param {string} path the filepath to check.\n   * @returns {boolean} result of check.\n   */\n  public existsResolvedAlias(path: string): boolean {\n    if (!this.useCache) return this.exists(path);\n    if (this.existsCache.has(path)) {\n      return this.existsCache.get(path);\n    } else {\n      const result = this.exists(path);\n      this.existsCache.set(path, result);\n      return result;\n    }\n  }\n\n  /**\n   * getAbsoluteAliasPath finds the absolute alias path, uses cache when possible.\n   * @param {string} basePath the basepath of the alias.\n   * @param {string} aliasPath the aliaspath of the alias.\n   * @returns {string} the absolute alias path.\n   */\n  public getAbsoluteAliasPath(basePath: string, aliasPath: string): string {\n    const request = { basePath, aliasPath };\n    if (!this.useCache) return this.getAAP(request);\n    if (this.absoluteCache.has(this.getCacheKey(request))) {\n      return this.absoluteCache.get(this.getCacheKey(request));\n    } else {\n      const result = this.getAAP(request);\n      this.absoluteCache.set(this.getCacheKey(request), result);\n      return result;\n    }\n  }\n\n  private getCacheKey({\n    basePath,\n    aliasPath\n  }: {\n    basePath: string;\n    aliasPath: string;\n  }): string {\n    return `${basePath}___${aliasPath}`;\n  }\n\n  /**\n   * getAAP finds the absolute alias path.\n   * @param {string} basePath the basepath of the alias.\n   * @param {string} aliasPath the aliaspath of the alias.\n   * @returns {string} the absolute alias path.\n   */\n  private getAAP({\n    basePath,\n    aliasPath\n  }: {\n    basePath: string;\n    aliasPath: string;\n  }): string {\n    const aliasPathParts = aliasPath\n      .split('/')\n      .filter((part) => !part.match(/^\\.$|^\\s*$/));\n\n    let aliasPathPart = aliasPathParts.shift() || '';\n\n    let pathExists = false;\n\n    while (\n      !(pathExists = this.exists(join(basePath, aliasPathPart))) &&\n      aliasPathParts.length\n    ) {\n      aliasPathPart = aliasPathParts.shift();\n    }\n\n    if (pathExists) {\n      return join(basePath, aliasPathPart, aliasPathParts.join('/'));\n    }\n\n    return '---' + join(basePath, aliasPathParts.join('/'));\n  }\n\n  /**\n   * exists checks if file exists.\n   * @param path the filepath to check.\n   * @returns {boolean} result of check.\n   */\n  private exists(path: string): boolean {\n    return (\n      existsSync(path) ||\n      this.fileExtensions.some((extension) =>\n        existsSync(`${path}.${extension}`)\n      )\n    );\n  }\n}\n"]}