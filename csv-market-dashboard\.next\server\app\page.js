(()=>{var e={};e.id=974,e.ids=[974],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1204:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\page.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5093:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>o});var a=s(5239),r=s(8088),n=s(8170),l=s.n(n),i=s(893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let o=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1204)),"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}],d=["D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},5141:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(687),r=s(3210);let n=({instruments:e,marketData:t,onInstrumentSelect:s,loading:n=!1})=>{let[l,i]=(0,r.useState)("symbol"),[c,o]=(0,r.useState)("asc"),d=(0,r.useMemo)(()=>[...e].sort((e,t)=>{let s=e[l],a=t[l];if(void 0===s&&void 0===a)return 0;if(void 0===s)return 1;if(void 0===a)return -1;if(s===a)return 0;let r=s<a?-1:1;return"asc"===c?r:-r}),[e,l,c]),m=e=>{l===e?o("asc"===c?"desc":"asc"):(i(e),o("asc"))},x=e=>void 0===e||0===e?"-":`₹${e.toFixed(2)}`,h=(e,t)=>{if(void 0===e||void 0===t)return"-";let s=e>=0?"+":"";return`${s}${e.toFixed(2)} (${s}${t.toFixed(2)}%)`},u=e=>void 0===e||0===e?"text-gray-600":e>0?"text-green-600":"text-red-600",g=e=>void 0===e||0===e?"-":e>=1e7?`${(e/1e7).toFixed(1)}Cr`:e>=1e5?`${(e/1e5).toFixed(1)}L`:e>=1e3?`${(e/1e3).toFixed(1)}K`:e.toString(),p=({field:e})=>l!==e?(0,a.jsx)("span",{className:"text-gray-400",children:"↕"}):"asc"===c?(0,a.jsx)("span",{className:"text-blue-600",children:"↑"}):(0,a.jsx)("span",{className:"text-blue-600",children:"↓"});return n?(0,a.jsx)("div",{className:"glass rounded-2xl shadow-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)("div",{className:"spinner w-8 h-8 mr-3"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Loading instruments..."})]})}):0===e.length?(0,a.jsx)("div",{className:"glass rounded-2xl shadow-lg p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"No instruments found"}),(0,a.jsx)("p",{className:"text-gray-500 text-sm mt-2",children:"Try adjusting your filters"})]})}):(0,a.jsxs)("div",{className:"glass rounded-2xl shadow-lg overflow-hidden",children:[(0,a.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Market Instruments"}),(0,a.jsxs)("p",{className:"text-gray-600 text-sm mt-1",children:["Showing ",d.length," instruments"]})]}),(0,a.jsx)("div",{className:"overflow-x-auto custom-scrollbar",style:{maxHeight:"600px"},children:(0,a.jsxs)("table",{className:"market-table",children:[(0,a.jsx)("thead",{className:"sticky top-0 bg-gray-50 z-10",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>m("securityId"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Security ID",(0,a.jsx)(p,{field:"securityId"})]})}),(0,a.jsx)("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>m("symbol"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Symbol",(0,a.jsx)(p,{field:"symbol"})]})}),(0,a.jsx)("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>m("displayName"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Name",(0,a.jsx)(p,{field:"displayName"})]})}),(0,a.jsx)("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>m("exchange"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Exchange",(0,a.jsx)(p,{field:"exchange"})]})}),(0,a.jsx)("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>m("instrumentType"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Type",(0,a.jsx)(p,{field:"instrumentType"})]})}),(0,a.jsx)("th",{className:"text-right",children:"LTP"}),(0,a.jsx)("th",{className:"text-right",children:"Change"}),(0,a.jsx)("th",{className:"text-right",children:"Volume"}),(0,a.jsx)("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>m("lotSize"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Lot Size",(0,a.jsx)(p,{field:"lotSize"})]})})]})}),(0,a.jsx)("tbody",{children:d.map(e=>{let r=t.get(e.securityId);return(0,a.jsxs)("tr",{className:"hover:bg-gray-50 cursor-pointer transition-colors",onClick:()=>s?.(e),children:[(0,a.jsx)("td",{className:"font-mono text-sm text-gray-700",children:e.securityId}),(0,a.jsx)("td",{className:"font-medium text-blue-600",children:e.symbol}),(0,a.jsx)("td",{className:"max-w-xs truncate",title:e.displayName,children:e.displayName}),(0,a.jsx)("td",{children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e.exchange})}),(0,a.jsx)("td",{children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:e.instrumentType})}),(0,a.jsx)("td",{className:"text-right font-medium",children:x(r?.ltp)}),(0,a.jsx)("td",{className:`text-right font-medium ${u(r?.change)}`,children:h(r?.change,r?.changePercent)}),(0,a.jsx)("td",{className:"text-right",children:g(r?.volume)}),(0,a.jsx)("td",{className:"text-right",children:e.lotSize.toLocaleString()})]},e.securityId)})})]})}),d.length>100&&(0,a.jsx)("div",{className:"p-4 bg-gray-50 border-t border-gray-200",children:(0,a.jsx)("p",{className:"text-sm text-gray-600 text-center",children:"Showing first 100 instruments. Use filters to narrow down results."})})]})},l=({filter:e,onFilterChange:t,exchanges:s,instrumentTypes:n,segments:l})=>{let[i,c]=(0,r.useState)(e.search||""),o=(s,a)=>{let r=e.exchange||[],n=a?[...r,s]:r.filter(e=>e!==s);t({...e,exchange:n.length>0?n:void 0})},d=(s,a)=>{let r=e.instrumentType||[],n=a?[...r,s]:r.filter(e=>e!==s);t({...e,instrumentType:n.length>0?n:void 0})},m=(s,a)=>{let r=e.segment||[],n=a?[...r,s]:r.filter(e=>e!==s);t({...e,segment:n.length>0?n:void 0})},x=s=>{c(s),t({...e,search:s||void 0})},h=()=>!!(e.exchange?.length||e.instrumentType?.length||e.segment?.length||e.search||void 0!==e.isActive||void 0!==e.hasExpiry);return(0,a.jsxs)("div",{className:"filter-panel",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Filters"}),h()&&(0,a.jsx)("button",{onClick:()=>{c(""),t({})},className:"text-sm text-blue-600 hover:text-blue-800 font-medium",children:"Clear All"})]}),(0,a.jsxs)("div",{className:"filter-group",children:[(0,a.jsx)("label",{className:"filter-label",children:"Search"}),(0,a.jsx)("input",{type:"text",placeholder:"Search by symbol, name, or ISIN...",value:i,onChange:e=>x(e.target.value),className:"filter-input"})]}),(0,a.jsxs)("div",{className:"filter-group",children:[(0,a.jsx)("label",{className:"filter-label",children:"Exchanges"}),(0,a.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto custom-scrollbar",children:s.map(t=>(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:e.exchange?.includes(t)||!1,onChange:e=>o(t,e.target.checked),className:"filter-checkbox"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:t})]},t))})]}),(0,a.jsxs)("div",{className:"filter-group",children:[(0,a.jsx)("label",{className:"filter-label",children:"Instrument Types"}),(0,a.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto custom-scrollbar",children:n.map(t=>(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:e.instrumentType?.includes(t)||!1,onChange:e=>d(t,e.target.checked),className:"filter-checkbox"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:t})]},t))})]}),(0,a.jsxs)("div",{className:"filter-group",children:[(0,a.jsx)("label",{className:"filter-label",children:"Segments"}),(0,a.jsx)("div",{className:"space-y-2",children:l.map(t=>(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:e.segment?.includes(t)||!1,onChange:e=>m(t,e.target.checked),className:"filter-checkbox"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:"C"===t?"Cash (C)":"F"===t?"Futures (F)":"O"===t?"Options (O)":t})]},t))})]}),(0,a.jsxs)("div",{className:"filter-group",children:[(0,a.jsx)("label",{className:"filter-label",children:"Additional Filters"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:!0===e.isActive,onChange:s=>t({...e,isActive:!!s.target.checked||void 0}),className:"filter-checkbox"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:"Active Only"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:!0===e.hasExpiry,onChange:s=>t({...e,hasExpiry:!!s.target.checked||void 0}),className:"filter-checkbox"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:"Has Expiry"})]})]})]}),(0,a.jsxs)("div",{className:"filter-group",children:[(0,a.jsx)("label",{className:"filter-label",children:"Lot Size Range"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsx)("input",{type:"number",placeholder:"Min",value:e.minLotSize||"",onChange:s=>t({...e,minLotSize:s.target.value?parseInt(s.target.value):void 0}),className:"filter-input text-sm"}),(0,a.jsx)("input",{type:"number",placeholder:"Max",value:e.maxLotSize||"",onChange:s=>t({...e,maxLotSize:s.target.value?parseInt(s.target.value):void 0}),className:"filter-input text-sm"})]})]}),h()&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"Active Filters:"}),(0,a.jsxs)("div",{className:"space-y-1 text-xs text-blue-700",children:[e.exchange?.length&&(0,a.jsxs)("div",{children:["Exchanges: ",e.exchange.join(", ")]}),e.instrumentType?.length&&(0,a.jsxs)("div",{children:["Types: ",e.instrumentType.join(", ")]}),e.segment?.length&&(0,a.jsxs)("div",{children:["Segments: ",e.segment.join(", ")]}),e.search&&(0,a.jsxs)("div",{children:["Search: “",e.search,"”"]}),e.isActive&&(0,a.jsx)("div",{children:"Active instruments only"}),e.hasExpiry&&(0,a.jsx)("div",{children:"With expiry date"}),(e.minLotSize||e.maxLotSize)&&(0,a.jsxs)("div",{children:["Lot size: ",e.minLotSize||0," - ",e.maxLotSize||"∞"]})]})]})]})},i=({connected:e})=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:`w-3 h-3 rounded-full ${e?"bg-green-500 animate-pulse":"bg-red-500"}`}),(0,a.jsx)("span",{className:`text-sm font-medium ${e?"text-green-700":"text-red-700"}`,children:e?"Connected":"Disconnected"})]}),(0,a.jsx)("div",{className:`px-3 py-1 rounded-full text-xs font-semibold ${e?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e?"LIVE":"OFFLINE"})]}),c=({totalInstruments:e,filteredInstruments:t,marketDataCount:s,connected:r,connectionStats:n})=>{let l=[{label:"Total Instruments",value:e.toLocaleString(),icon:"\uD83D\uDCCA",color:"bg-blue-500",description:"Available instruments"},{label:"Filtered Results",value:t.toLocaleString(),icon:"\uD83D\uDD0D",color:"bg-purple-500",description:"Matching filters"},{label:"Live Data",value:s.toLocaleString(),icon:"\uD83D\uDCC8",color:r?"bg-green-500":"bg-gray-500",description:n?`${n.connectedInstruments} active`:"Market data points"},{label:"Connection",value:r?"Active":"Inactive",icon:r?"\uD83D\uDFE2":"\uD83D\uDD34",color:r?"bg-green-500":"bg-red-500",description:n?`${n.messagesReceived} messages`:"WebSocket status"}],i=n?[{label:"Cache Size",value:n.cacheSize.toLocaleString(),icon:"\uD83D\uDCBE",color:"bg-indigo-500",description:n.isAutoSaving?"Auto-saving":"Manual save"},{label:"Last Update",value:(e=>{if(!e)return"Never";let t=Math.floor((new Date().getTime()-e.getTime())/1e3);return t<60?`${t}s ago`:t<3600?`${Math.floor(t/60)}m ago`:e.toLocaleTimeString()})(n.lastUpdate),icon:"\uD83D\uDD52",color:"bg-orange-500",description:"Data freshness"},{label:"Uptime",value:(e=>{if(e<60)return`${e}s`;if(e<3600)return`${Math.floor(e/60)}m ${e%60}s`;let t=Math.floor(e/3600),s=Math.floor(e%3600/60);return`${t}h ${s}m`})(n.connectionUptime),icon:"⏱️",color:"bg-teal-500",description:"Connection stability"},{label:"Reconnects",value:n.reconnectAttempts.toString(),icon:n.reconnectAttempts>0?"\uD83D\uDD04":"✅",color:n.reconnectAttempts>0?"bg-yellow-500":"bg-green-500",description:"Connection reliability"}]:[];return(0,a.jsxs)("div",{className:"space-y-6 mb-6",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:l.map((e,t)=>(0,a.jsxs)("div",{className:"glass rounded-xl shadow-lg p-4 card-hover",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.label}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:e.value}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.description})]}),(0,a.jsx)("div",{className:"text-2xl ml-3",children:e.icon})]}),(0,a.jsx)("div",{className:`mt-3 h-1 rounded-full ${e.color}`})]},t))}),i.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 mb-3 flex items-center",children:[(0,a.jsx)("span",{className:"text-xl mr-2",children:"⚡"}),"Real-time Connection Stats"]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:i.map((e,t)=>(0,a.jsxs)("div",{className:"glass rounded-xl shadow-lg p-4 card-hover border-l-4 border-l-blue-400",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.label}),(0,a.jsx)("p",{className:"text-xl font-bold text-gray-900 mt-1",children:e.value}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.description})]}),(0,a.jsx)("div",{className:"text-xl ml-3",children:e.icon})]}),(0,a.jsx)("div",{className:`mt-3 h-1 rounded-full ${e.color}`})]},t))})]})]})};var o=s(9255),d=s(2457);function m(){let{marketData:e,marketDataMap:t,isConnected:s,connectionError:m,connectionStatus:x,isLoading:h,cacheLoaded:u,refresh:g,stats:p,updateFilters:b,filters:f,getFilteredData:v,getSortedData:j}=(0,o.Q)(),[y,N]=(0,r.useState)([]),[k,C]=(0,r.useState)(new Map),[S,w]=(0,r.useState)({}),[L,I]=(0,r.useState)(!0),[D,E]=(0,r.useState)(null),[T,A]=(0,r.useState)([]),[M,$]=(0,r.useState)([]),z=(0,r.useCallback)(async()=>{try{console.log("\uD83C\uDF10 Dashboard: Loading fresh instruments from API...");let e=await fetch("http://localhost:8080]/api/instruments");if(e.ok){let t=await e.json();console.log("✅ Dashboard: Loaded",t.data.instruments.length,"instruments from API"),N(t.data.instruments),await d.ko.cacheStaticData(d.aq.INSTRUMENTS,t.data.instruments),console.log("\uD83D\uDCBE Dashboard: Cached instruments data"),I(!1)}else console.error("❌ Dashboard: Failed to load instruments:",e.statusText),E("Failed to load instruments"),I(!1)}catch(e){console.error("❌ Dashboard: Error fetching fresh instruments:",e),E("Error fetching instruments"),I(!1)}},[]);(0,r.useCallback)(async()=>{try{I(!0);let e=await d.ko.getCachedStaticData(d.aq.INSTRUMENTS);if(e&&Array.isArray(e)){console.log("✅ Dashboard: Loaded instruments from cache"),N(e),I(!1),z();return}await z()}catch(e){console.error("❌ Dashboard: Error loading instruments:",e),E("Error loading instruments"),I(!1)}},[z]);let F=y.filter(e=>{if(S.exchange&&S.exchange.length>0&&!S.exchange.includes(e.exchange)||S.instrumentType&&S.instrumentType.length>0&&!S.instrumentType.includes(e.instrumentType))return!1;if(S.search){let t=S.search.toLowerCase();return e.symbol.toLowerCase().includes(t)||e.displayName.toLowerCase().includes(t)||e.isin&&e.isin.toLowerCase().includes(t)}return!0});return L?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading market data..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen p-6",children:[(0,a.jsx)("div",{className:"glass rounded-2xl shadow-lg p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold gradient-text",children:"CSV Market Dashboard"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Real-time market data from CSV instruments"}),(0,a.jsxs)("div",{className:"mt-3 space-x-3",children:[(0,a.jsx)("a",{href:"/subscribed",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors",children:"\uD83D\uDCCA View Subscribed Data Dashboard"}),(0,a.jsx)("a",{href:"/option-chain",className:"inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors",children:"\uD83D\uDD17 NIFTY Option Chain"})]})]}),(0,a.jsx)(i,{connected:s})]})}),(D||m)&&(0,a.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6",children:[(0,a.jsx)("strong",{children:"Error:"})," ",D||m]}),(0,a.jsx)(c,{totalInstruments:y.length,filteredInstruments:F.length,marketDataCount:p.totalInstruments,connected:s,connectionStats:p}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsx)(l,{filter:S,onFilterChange:e=>{w(e)},exchanges:T,instrumentTypes:M,segments:["C","F","O"]})}),(0,a.jsx)("div",{className:"lg:col-span-3",children:(0,a.jsx)(n,{instruments:F.slice(0,100),marketData:k,onInstrumentSelect:e=>{console.log("Selected instrument:",e)},loading:L})})]}),(0,a.jsxs)("div",{className:"mt-8 text-center text-sm text-gray-500",children:[(0,a.jsxs)("p",{children:["CSV Market Dashboard - Real-time data from ",y.length," instruments"]}),(0,a.jsxs)("p",{children:["Last updated: ",new Date().toLocaleTimeString()]})]})]})}},5844:(e,t,s)=>{Promise.resolve().then(s.bind(s,1204))},8580:(e,t,s)=>{Promise.resolve().then(s.bind(s,5141))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9255:(e,t,s)=>{"use strict";s.d(t,{Q:()=>r});var a=s(3210);let r=(e={})=>{let{autoConnect:t=!0,autoLoadCache:s=!0,autoSaveInterval:r=3e4,reconnectOnError:n=!0,maxReconnectAttempts:l=5}=e,i={status:"disconnected",isConnected:!1,error:null,connectionStats:{totalMessages:0,connectionUptime:0}},c=new Map,o={isLoaded:!1,totalCacheSize:0,pendingUpdates:0,lastCacheUpdate:null},d=(0,a.useMemo)(()=>({filters:{},sortConfig:{field:"symbol",direction:"asc"},selectedInstruments:new Set,viewMode:"table",autoRefresh:!0}),[]),m=(0,a.useCallback)(async()=>{},[]),x=(0,a.useCallback)(async()=>{},[]),h=(0,a.useCallback)(()=>{},[]),u=(0,a.useCallback)(async()=>{},[]),g=(0,a.useCallback)(async()=>{},[]),p=(0,a.useCallback)(async e=>{},[]),b=(0,a.useCallback)(async()=>{},[]),f=(0,a.useCallback)(e=>{},[]),v=(0,a.useCallback)((e,t)=>{},[]),j=(0,a.useCallback)(()=>[],[]),y=(0,a.useCallback)(()=>[],[]),N=(0,a.useCallback)(e=>void 0,[]),k=(0,a.useCallback)(e=>void 0,[]),C=(0,a.useCallback)(e=>{},[]),S=(0,a.useCallback)(e=>{},[]),w=(0,a.useCallback)(()=>{},[]),L=(0,a.useRef)(null),I=(0,a.useRef)(0),D=(0,a.useRef)(!1),E=Array.from(c.values());(0,a.useEffect)(()=>((async()=>{if(!D.current){D.current=!0,console.log("\uD83D\uDE80 Enhanced Hook: Initializing...");try{s&&!o.isLoaded&&await g(),t&&"disconnected"===i.status&&await m()}catch(e){console.error("❌ Enhanced Hook: Initialization failed:",e)}}})(),()=>{L.current&&clearInterval(L.current)}),[t,s,o.isLoaded,i.status,m,g]),(0,a.useEffect)(()=>{if(r>0&&E.length>0)return L.current&&clearInterval(L.current),L.current=setInterval(()=>{p()},r),()=>{L.current&&clearInterval(L.current)}},[r,E.length,p]),(0,a.useEffect)(()=>{if(n&&"error"===i.status&&I.current<l){let e=Math.min(1e3*Math.pow(2,I.current),3e4);console.log(`🔄 Enhanced Hook: Auto-reconnecting in ${e}ms (attempt ${I.current+1}/${l})`);let t=setTimeout(async()=>{try{I.current++,await u()}catch(e){console.error("❌ Enhanced Hook: Auto-reconnect failed:",e)}},e);return()=>clearTimeout(t)}},[i.status,u,n,l]),(0,a.useEffect)(()=>{"connected"===i.status&&(I.current=0)},[i.status]);let T=(0,a.useCallback)(()=>j(),[j]),A=(0,a.useCallback)(()=>y(),[y]),M=(0,a.useCallback)(e=>N(e),[N]),$=(0,a.useCallback)(e=>k(e),[k]),z=(0,a.useCallback)(e=>{f(e)},[f,d]),F=(0,a.useCallback)((e,t)=>{v(e,t)},[v]),P=(0,a.useCallback)(e=>{C(e)},[C]),R=(0,a.useCallback)(e=>{S(e)},[S]),U=(0,a.useCallback)(async()=>{try{I.current=0,await u()}catch(e){throw console.error("❌ Enhanced Hook: Manual reconnect failed:",e),e}},[u]),_=(0,a.useCallback)(async()=>{try{await g()}catch(e){throw console.error("❌ Enhanced Hook: Force refresh failed:",e),e}},[g]),q=(0,a.useCallback)(async()=>{try{await p(!0)}catch(e){throw console.error("❌ Enhanced Hook: Force save failed:",e),e}},[p]),H={totalInstruments:E.length,connectedInstruments:E.filter(e=>e.ltp&&e.ltp>0).length,lastUpdate:null,cacheSize:o.totalCacheSize,connectionUptime:i.connectionStats.connectionUptime,messagesReceived:i.connectionStats.totalMessages,reconnectAttempts:I.current,isAutoSaving:null!==L.current};return{marketData:E,marketDataMap:c,filteredData:T(),sortedData:A(),isConnected:i.isConnected,connectionStatus:i.status,connectionError:i.error,connectionStats:i.connectionStats,cacheLoaded:o.isLoaded,cacheUpdating:o.pendingUpdates>0,lastCacheUpdate:o.lastCacheUpdate,filters:d.filters,sortConfig:d.sortConfig,selectedInstruments:d.selectedInstruments,viewMode:d.viewMode,autoRefresh:d.autoRefresh,isLoading:!1,isInitializing:!D.current,getDataBySecurityId:M,getDataBySymbol:$,getFilteredData:T,getSortedData:A,updateFilters:z,updateSort:F,subscribe:P,unsubscribe:R,connect:x,disconnect:h,reconnect:U,refresh:_,save:q,clearCache:b,reset:w,stats:H,_store:{setFilters:f,setSortConfig:v,subscribeToInstrument:C,unsubscribeFromInstrument:S}}}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,151,590],()=>s(5093));module.exports=a})();