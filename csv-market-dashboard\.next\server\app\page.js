/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPUQlM0ElNUNsb3ZlJTVDZGFzaGJvYXJkJTVDY3N2LW1hcmtldC1kYXNoYm9hcmQlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUQlM0ElNUNsb3ZlJTVDZGFzaGJvYXJkJTVDY3N2LW1hcmtldC1kYXNoYm9hcmQmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHNCQUFzQixvSkFBb0c7QUFDMUgsc0JBQXNCLDBOQUFnRjtBQUN0RyxzQkFBc0IsME5BQWdGO0FBQ3RHLHNCQUFzQixnT0FBbUY7QUFDekcsb0JBQW9CLGdKQUFrRztBQUdwSDtBQUdBO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFHckI7QUFDRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFHRTtBQUNGO0FBQ08sd0JBQXdCLHVHQUFrQjtBQUNqRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBtb2R1bGUwID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxsb3ZlXFxcXGRhc2hib2FyZFxcXFxjc3YtbWFya2V0LWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIik7XG5jb25zdCBtb2R1bGUxID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpO1xuY29uc3QgbW9kdWxlMiA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2ZvcmJpZGRlbi1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTMgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy91bmF1dGhvcml6ZWQtZXJyb3JcIik7XG5jb25zdCBwYWdlNCA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcbG92ZVxcXFxkYXNoYm9hcmRcXFxcY3N2LW1hcmtldC1kYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc3NyJ1xufTtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogW3BhZ2U0LCBcIkQ6XFxcXGxvdmVcXFxcZGFzaGJvYXJkXFxcXGNzdi1tYXJrZXQtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIl0sXG4gICAgICAgICAgXG4gICAgICAgIH1dXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbbW9kdWxlMCwgXCJEOlxcXFxsb3ZlXFxcXGRhc2hib2FyZFxcXFxjc3YtbWFya2V0LWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIl0sXG4nbm90LWZvdW5kJzogW21vZHVsZTEsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbidmb3JiaWRkZW4nOiBbbW9kdWxlMiwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZm9yYmlkZGVuLWVycm9yXCJdLFxuJ3VuYXV0aG9yaXplZCc6IFttb2R1bGUzLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy91bmF1dGhvcml6ZWQtZXJyb3JcIl0sXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiRDpcXFxcbG92ZVxcXFxkYXNoYm9hcmRcXFxcY3N2LW1hcmtldC1kYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiXTtcbmV4cG9ydCB7IHRyZWUsIHBhZ2VzIH07XG5leHBvcnQgeyBkZWZhdWx0IGFzIEdsb2JhbEVycm9yIH0gZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuZXhwb3J0ICogZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogJycsXG4gICAgICAgIGZpbGVuYW1lOiAnJyxcbiAgICAgICAgYXBwUGF0aHM6IFtdXG4gICAgfSxcbiAgICB1c2VybGFuZDoge1xuICAgICAgICBsb2FkZXJUcmVlOiB0cmVlXG4gICAgfVxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1wYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNsb3ZlJTVDJTVDZGFzaGJvYXJkJTVDJTVDY3N2LW1hcmtldC1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBb0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGxvdmVcXFxcZGFzaGJvYXJkXFxcXGNzdi1tYXJrZXQtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNsb3ZlJTVDJTVDZGFzaGJvYXJkJTVDJTVDY3N2LW1hcmtldC1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQWtHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxsb3ZlXFxcXGRhc2hib2FyZFxcXFxjc3YtbWFya2V0LWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNsb3ZlJTVDJTVDZGFzaGJvYXJkJTVDJTVDY3N2LW1hcmtldC1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBb0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGxvdmVcXFxcZGFzaGJvYXJkXFxcXGNzdi1tYXJrZXQtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNsb3ZlJTVDJTVDZGFzaGJvYXJkJTVDJTVDY3N2LW1hcmtldC1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQWtHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxsb3ZlXFxcXGRhc2hib2FyZFxcXFxjc3YtbWFya2V0LWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"146531be5b05\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcbG92ZVxcZGFzaGJvYXJkXFxjc3YtbWFya2V0LWRhc2hib2FyZFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMTQ2NTMxYmU1YjA1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var react_error_boundary__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-error-boundary */ \"(ssr)/./node_modules/react-error-boundary/dist/react-error-boundary.development.esm.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Error fallback component\nfunction ErrorFallback({ error, resetErrorBoundary }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg border border-red-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-600 text-lg\",\n                                children: \"⚠\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Application Error\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-4\",\n                    children: \"An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"cursor-pointer text-sm text-gray-500 hover:text-gray-700\",\n                            children: \"Technical Details\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto max-h-32\",\n                            children: error.message\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: resetErrorBoundary,\n                            className: \"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\",\n                            children: \"Try Again\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors\",\n                            children: \"Refresh Page\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"CSV Market Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Real-time market data dashboard with enhanced WebSocket management and data caching\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_error_boundary__WEBPACK_IMPORTED_MODULE_4__.ErrorBoundary, {\n                    FallbackComponent: ErrorFallback,\n                    onError: (error, errorInfo)=>{\n                        console.error('Application Error:', error, errorInfo);\n                    // Here you could send the error to an error reporting service\n                    },\n                    onReset: ()=>{\n                        // Clear any state that might be causing the error\n                        localStorage.removeItem('enhanced-market-data');\n                        localStorage.removeItem('enhanced-market-timestamp');\n                        sessionStorage.clear();\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20\",\n                        children: [\n                            children,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                                position: \"top-right\",\n                                toastOptions: {\n                                    duration: 4000,\n                                    style: {\n                                        background: '#363636',\n                                        color: '#fff'\n                                    },\n                                    success: {\n                                        style: {\n                                            background: '#10B981'\n                                        }\n                                    },\n                                    error: {\n                                        style: {\n                                            background: '#EF4444'\n                                        }\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_InstrumentTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/InstrumentTable */ \"(ssr)/./src/components/InstrumentTable.tsx\");\n/* harmony import */ var _components_FilterPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/FilterPanel */ \"(ssr)/./src/components/FilterPanel.tsx\");\n/* harmony import */ var _components_ConnectionStatus__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ConnectionStatus */ \"(ssr)/./src/components/ConnectionStatus.tsx\");\n/* harmony import */ var _components_Stats__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Stats */ \"(ssr)/./src/components/Stats.tsx\");\n/* harmony import */ var _hooks_useEnhancedMarketData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useEnhancedMarketData */ \"(ssr)/./src/hooks/useEnhancedMarketData.ts\");\n/* harmony import */ var _lib_data_cache__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/data-cache */ \"(ssr)/./src/lib/data-cache.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Dashboard() {\n    const { marketData: marketDataArray, marketDataMap, isConnected, connectionError, connectionStatus, isLoading: wsLoading, cacheLoaded, refresh: refreshFromCache, stats, updateFilters, filters, getFilteredData, getSortedData } = (0,_hooks_useEnhancedMarketData__WEBPACK_IMPORTED_MODULE_6__.useEnhancedMarketData)();\n    const [instruments, setInstruments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [exchanges, setExchanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [instrumentTypes, setInstrumentTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Convert market data array to Map for compatibility\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            const dataMap = new Map();\n            marketDataArray.forEach({\n                \"Dashboard.useEffect\": (item)=>{\n                    if (item.securityId) {\n                        dataMap.set(item.securityId, item);\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            setMarketData(dataMap);\n        }\n    }[\"Dashboard.useEffect\"], [\n        marketDataArray\n    ]);\n    const fetchFreshInstruments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Dashboard.useCallback[fetchFreshInstruments]\": async ()=>{\n            try {\n                const serverUrl = \"http://localhost:8081\" || 0;\n                console.log('🌐 Dashboard: Loading fresh instruments from API...');\n                const response = await fetch(`${serverUrl}/api/instruments`);\n                if (response.ok) {\n                    const data = await response.json();\n                    console.log('✅ Dashboard: Loaded', data.data.instruments.length, 'instruments from API');\n                    setInstruments(data.data.instruments);\n                    // Cache the instruments\n                    await _lib_data_cache__WEBPACK_IMPORTED_MODULE_7__.cacheHelpers.cacheStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_7__.MARKET_DATA_CACHE_KEYS.INSTRUMENTS, data.data.instruments);\n                    console.log('💾 Dashboard: Cached instruments data');\n                    setLoading(false);\n                } else {\n                    console.error('❌ Dashboard: Failed to load instruments:', response.statusText);\n                    setError('Failed to load instruments');\n                    setLoading(false);\n                }\n            } catch (error) {\n                console.error('❌ Dashboard: Error fetching fresh instruments:', error);\n                setError('Error fetching instruments');\n                setLoading(false);\n            }\n        }\n    }[\"Dashboard.useCallback[fetchFreshInstruments]\"], []);\n    // Load all instruments from API with caching\n    const loadAllInstruments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Dashboard.useCallback[loadAllInstruments]\": async ()=>{\n            try {\n                setLoading(true);\n                // Check cache first\n                const cachedInstruments = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_7__.cacheHelpers.getCachedStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_7__.MARKET_DATA_CACHE_KEYS.INSTRUMENTS);\n                if (cachedInstruments && Array.isArray(cachedInstruments)) {\n                    console.log('✅ Dashboard: Loaded instruments from cache');\n                    setInstruments(cachedInstruments);\n                    setLoading(false);\n                    // Still fetch fresh data in background\n                    fetchFreshInstruments();\n                    return;\n                }\n                // Fetch fresh data\n                await fetchFreshInstruments();\n            } catch (error) {\n                console.error('❌ Dashboard: Error loading instruments:', error);\n                setError('Error loading instruments');\n                setLoading(false);\n            }\n        }\n    }[\"Dashboard.useCallback[loadAllInstruments]\"], [\n        fetchFreshInstruments\n    ]);\n    // Load all instruments on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            loadAllInstruments();\n        }\n    }[\"Dashboard.useEffect\"], [\n        loadAllInstruments\n    ]);\n    // Load additional data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            const loadMetadata = {\n                \"Dashboard.useEffect.loadMetadata\": async ()=>{\n                    try {\n                        const serverUrl = \"http://localhost:8081\" || 0;\n                        // Load exchanges\n                        const exchangesResponse = await fetch(`${serverUrl}/api/exchanges`);\n                        if (exchangesResponse.ok) {\n                            const exchangesData = await exchangesResponse.json();\n                            setExchanges(exchangesData.data);\n                        }\n                        // Load instrument types\n                        const typesResponse = await fetch(`${serverUrl}/api/instrument-types`);\n                        if (typesResponse.ok) {\n                            const typesData = await typesResponse.json();\n                            setInstrumentTypes(typesData.data);\n                        }\n                    } catch (error) {\n                        console.error('❌ Error loading metadata:', error);\n                    }\n                }\n            }[\"Dashboard.useEffect.loadMetadata\"];\n            loadMetadata();\n        }\n    }[\"Dashboard.useEffect\"], []);\n    // Filter instruments based on current filter\n    const filteredInstruments = instruments.filter((instrument)=>{\n        if (filter.exchange && filter.exchange.length > 0 && !filter.exchange.includes(instrument.exchange)) {\n            return false;\n        }\n        if (filter.instrumentType && filter.instrumentType.length > 0 && !filter.instrumentType.includes(instrument.instrumentType)) {\n            return false;\n        }\n        if (filter.search) {\n            const searchTerm = filter.search.toLowerCase();\n            return instrument.symbol.toLowerCase().includes(searchTerm) || instrument.displayName.toLowerCase().includes(searchTerm) || instrument.isin && instrument.isin.toLowerCase().includes(searchTerm);\n        }\n        return true;\n    });\n    // Note: Subscriptions are now handled server-side automatically\n    const handleFilterChange = (newFilter)=>{\n        setFilter(newFilter);\n    };\n    const handleInstrumentSelect = (instrument)=>{\n        console.log('Selected instrument:', instrument);\n    // You can add more functionality here, like showing detailed view\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner w-12 h-12 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading market data...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 169,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass rounded-2xl shadow-lg p-6 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold gradient-text\",\n                                    children: \"CSV Market Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-1\",\n                                    children: \"Real-time market data from CSV instruments\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3 space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/subscribed\",\n                                            className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors\",\n                                            children: \"\\uD83D\\uDCCA View Subscribed Data Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/option-chain\",\n                                            className: \"inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors\",\n                                            children: \"\\uD83D\\uDD17 NIFTY Option Chain\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConnectionStatus__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            connected: isConnected\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            (error || connectionError) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Error:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this),\n                    \" \",\n                    error || connectionError\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Stats__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                totalInstruments: instruments.length,\n                filteredInstruments: filteredInstruments.length,\n                marketDataCount: stats.totalInstruments,\n                connected: isConnected,\n                connectionStats: stats\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FilterPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            filter: filter,\n                            onFilterChange: handleFilterChange,\n                            exchanges: exchanges,\n                            instrumentTypes: instrumentTypes,\n                            segments: [\n                                'C',\n                                'F',\n                                'O'\n                            ]\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InstrumentTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            instruments: filteredInstruments.slice(0, 100),\n                            marketData: marketData,\n                            onInstrumentSelect: handleInstrumentSelect,\n                            loading: loading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 text-center text-sm text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"CSV Market Dashboard - Real-time data from \",\n                            instruments.length,\n                            \" instruments\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Last updated: \",\n                            new Date().toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ConnectionStatus.tsx":
/*!*********************************************!*\
  !*** ./src/components/ConnectionStatus.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst ConnectionStatus = ({ connected })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `w-3 h-3 rounded-full ${connected ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-medium ${connected ? 'text-green-700' : 'text-red-700'}`,\n                        children: connected ? 'Connected' : 'Disconnected'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `px-3 py-1 rounded-full text-xs font-semibold ${connected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                children: connected ? 'LIVE' : 'OFFLINE'\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ConnectionStatus);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ConnectionStatus.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FilterPanel.tsx":
/*!****************************************!*\
  !*** ./src/components/FilterPanel.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst FilterPanel = ({ filter, onFilterChange, exchanges, instrumentTypes, segments })=>{\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(filter.search || '');\n    const handleExchangeChange = (exchange, checked)=>{\n        const currentExchanges = filter.exchange || [];\n        const newExchanges = checked ? [\n            ...currentExchanges,\n            exchange\n        ] : currentExchanges.filter((e)=>e !== exchange);\n        onFilterChange({\n            ...filter,\n            exchange: newExchanges.length > 0 ? newExchanges : undefined\n        });\n    };\n    const handleInstrumentTypeChange = (type, checked)=>{\n        const currentTypes = filter.instrumentType || [];\n        const newTypes = checked ? [\n            ...currentTypes,\n            type\n        ] : currentTypes.filter((t)=>t !== type);\n        onFilterChange({\n            ...filter,\n            instrumentType: newTypes.length > 0 ? newTypes : undefined\n        });\n    };\n    const handleSegmentChange = (segment, checked)=>{\n        const currentSegments = filter.segment || [];\n        const newSegments = checked ? [\n            ...currentSegments,\n            segment\n        ] : currentSegments.filter((s)=>s !== segment);\n        onFilterChange({\n            ...filter,\n            segment: newSegments.length > 0 ? newSegments : undefined\n        });\n    };\n    const handleSearchChange = (value)=>{\n        setSearchTerm(value);\n        onFilterChange({\n            ...filter,\n            search: value || undefined\n        });\n    };\n    const handleClearFilters = ()=>{\n        setSearchTerm('');\n        onFilterChange({});\n    };\n    const isFilterActive = ()=>{\n        return !!(filter.exchange?.length || filter.instrumentType?.length || filter.segment?.length || filter.search || filter.isActive !== undefined || filter.hasExpiry !== undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"filter-panel\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Filters\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    isFilterActive() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleClearFilters,\n                        className: \"text-sm text-blue-600 hover:text-blue-800 font-medium\",\n                        children: \"Clear All\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Search\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        placeholder: \"Search by symbol, name, or ISIN...\",\n                        value: searchTerm,\n                        onChange: (e)=>handleSearchChange(e.target.value),\n                        className: \"filter-input\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Exchanges\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 max-h-32 overflow-y-auto custom-scrollbar\",\n                        children: exchanges.map((exchange)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filter.exchange?.includes(exchange) || false,\n                                        onChange: (e)=>handleExchangeChange(exchange, e.target.checked),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: exchange\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, exchange, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Instrument Types\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 max-h-32 overflow-y-auto custom-scrollbar\",\n                        children: instrumentTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filter.instrumentType?.includes(type) || false,\n                                        onChange: (e)=>handleInstrumentTypeChange(type, e.target.checked),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: type\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, type, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Segments\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: segments.map((segment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filter.segment?.includes(segment) || false,\n                                        onChange: (e)=>handleSegmentChange(segment, e.target.checked),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: segment === 'C' ? 'Cash (C)' : segment === 'F' ? 'Futures (F)' : segment === 'O' ? 'Options (O)' : segment\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, segment, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Additional Filters\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filter.isActive === true,\n                                        onChange: (e)=>onFilterChange({\n                                                ...filter,\n                                                isActive: e.target.checked ? true : undefined\n                                            }),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: \"Active Only\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filter.hasExpiry === true,\n                                        onChange: (e)=>onFilterChange({\n                                                ...filter,\n                                                hasExpiry: e.target.checked ? true : undefined\n                                            }),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: \"Has Expiry\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Lot Size Range\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"number\",\n                                placeholder: \"Min\",\n                                value: filter.minLotSize || '',\n                                onChange: (e)=>onFilterChange({\n                                        ...filter,\n                                        minLotSize: e.target.value ? parseInt(e.target.value) : undefined\n                                    }),\n                                className: \"filter-input text-sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"number\",\n                                placeholder: \"Max\",\n                                value: filter.maxLotSize || '',\n                                onChange: (e)=>onFilterChange({\n                                        ...filter,\n                                        maxLotSize: e.target.value ? parseInt(e.target.value) : undefined\n                                    }),\n                                className: \"filter-input text-sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, undefined),\n            isFilterActive() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-blue-900 mb-2\",\n                        children: \"Active Filters:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1 text-xs text-blue-700\",\n                        children: [\n                            filter.exchange?.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Exchanges: \",\n                                    filter.exchange.join(', ')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.instrumentType?.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Types: \",\n                                    filter.instrumentType.join(', ')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.segment?.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Segments: \",\n                                    filter.segment.join(', ')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.search && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Search: “\",\n                                    filter.search,\n                                    \"”\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Active instruments only\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.hasExpiry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"With expiry date\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, undefined),\n                            (filter.minLotSize || filter.maxLotSize) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Lot size: \",\n                                    filter.minLotSize || 0,\n                                    \" - \",\n                                    filter.maxLotSize || '∞'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 220,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FilterPanel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9GaWx0ZXJQYW5lbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRXdDO0FBR3hDLE1BQU1FLGNBQTBDLENBQUMsRUFDL0NDLE1BQU0sRUFDTkMsY0FBYyxFQUNkQyxTQUFTLEVBQ1RDLGVBQWUsRUFDZkMsUUFBUSxFQUNUO0lBQ0MsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUdSLCtDQUFRQSxDQUFDRSxPQUFPTyxNQUFNLElBQUk7SUFFOUQsTUFBTUMsdUJBQXVCLENBQUNDLFVBQWtCQztRQUM5QyxNQUFNQyxtQkFBbUJYLE9BQU9TLFFBQVEsSUFBSSxFQUFFO1FBQzlDLE1BQU1HLGVBQWVGLFVBQ2pCO2VBQUlDO1lBQWtCRjtTQUFTLEdBQy9CRSxpQkFBaUJYLE1BQU0sQ0FBQ2EsQ0FBQUEsSUFBS0EsTUFBTUo7UUFFdkNSLGVBQWU7WUFDYixHQUFHRCxNQUFNO1lBQ1RTLFVBQVVHLGFBQWFFLE1BQU0sR0FBRyxJQUFJRixlQUFlRztRQUNyRDtJQUNGO0lBRUEsTUFBTUMsNkJBQTZCLENBQUNDLE1BQWNQO1FBQ2hELE1BQU1RLGVBQWVsQixPQUFPbUIsY0FBYyxJQUFJLEVBQUU7UUFDaEQsTUFBTUMsV0FBV1YsVUFDYjtlQUFJUTtZQUFjRDtTQUFLLEdBQ3ZCQyxhQUFhbEIsTUFBTSxDQUFDcUIsQ0FBQUEsSUFBS0EsTUFBTUo7UUFFbkNoQixlQUFlO1lBQ2IsR0FBR0QsTUFBTTtZQUNUbUIsZ0JBQWdCQyxTQUFTTixNQUFNLEdBQUcsSUFBSU0sV0FBV0w7UUFDbkQ7SUFDRjtJQUVBLE1BQU1PLHNCQUFzQixDQUFDQyxTQUFpQmI7UUFDNUMsTUFBTWMsa0JBQWtCeEIsT0FBT3VCLE9BQU8sSUFBSSxFQUFFO1FBQzVDLE1BQU1FLGNBQWNmLFVBQ2hCO2VBQUljO1lBQWlCRDtTQUFRLEdBQzdCQyxnQkFBZ0J4QixNQUFNLENBQUMwQixDQUFBQSxJQUFLQSxNQUFNSDtRQUV0Q3RCLGVBQWU7WUFDYixHQUFHRCxNQUFNO1lBQ1R1QixTQUFTRSxZQUFZWCxNQUFNLEdBQUcsSUFBSVcsY0FBY1Y7UUFDbEQ7SUFDRjtJQUVBLE1BQU1ZLHFCQUFxQixDQUFDQztRQUMxQnRCLGNBQWNzQjtRQUNkM0IsZUFBZTtZQUNiLEdBQUdELE1BQU07WUFDVE8sUUFBUXFCLFNBQVNiO1FBQ25CO0lBQ0Y7SUFFQSxNQUFNYyxxQkFBcUI7UUFDekJ2QixjQUFjO1FBQ2RMLGVBQWUsQ0FBQztJQUNsQjtJQUVBLE1BQU02QixpQkFBaUI7UUFDckIsT0FBTyxDQUFDLENBQ045QixDQUFBQSxPQUFPUyxRQUFRLEVBQUVLLFVBQ2pCZCxPQUFPbUIsY0FBYyxFQUFFTCxVQUN2QmQsT0FBT3VCLE9BQU8sRUFBRVQsVUFDaEJkLE9BQU9PLE1BQU0sSUFDYlAsT0FBTytCLFFBQVEsS0FBS2hCLGFBQ3BCZixPQUFPZ0MsU0FBUyxLQUFLakIsU0FBUTtJQUVqQztJQUVBLHFCQUNFLDhEQUFDa0I7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUdELFdBQVU7a0NBQXNDOzs7Ozs7b0JBQ25ESixrQ0FDQyw4REFBQ007d0JBQ0NDLFNBQVNSO3dCQUNUSyxXQUFVO2tDQUNYOzs7Ozs7Ozs7Ozs7MEJBT0wsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0k7d0JBQU1KLFdBQVU7a0NBQWU7Ozs7OztrQ0FDaEMsOERBQUNLO3dCQUNDdEIsTUFBSzt3QkFDTHVCLGFBQVk7d0JBQ1paLE9BQU92Qjt3QkFDUG9DLFVBQVUsQ0FBQzVCLElBQU1jLG1CQUFtQmQsRUFBRTZCLE1BQU0sQ0FBQ2QsS0FBSzt3QkFDbERNLFdBQVU7Ozs7Ozs7Ozs7OzswQkFLZCw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDSTt3QkFBTUosV0FBVTtrQ0FBZTs7Ozs7O2tDQUNoQyw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ1poQyxVQUFVeUMsR0FBRyxDQUFDLENBQUNsQyx5QkFDZCw4REFBQzZCO2dDQUFxQkosV0FBVTs7a0RBQzlCLDhEQUFDSzt3Q0FDQ3RCLE1BQUs7d0NBQ0xQLFNBQVNWLE9BQU9TLFFBQVEsRUFBRW1DLFNBQVNuQyxhQUFhO3dDQUNoRGdDLFVBQVUsQ0FBQzVCLElBQU1MLHFCQUFxQkMsVUFBVUksRUFBRTZCLE1BQU0sQ0FBQ2hDLE9BQU87d0NBQ2hFd0IsV0FBVTs7Ozs7O2tEQUVaLDhEQUFDVzt3Q0FBS1gsV0FBVTtrREFBeUJ6Qjs7Ozs7OzsrQkFQL0JBOzs7Ozs7Ozs7Ozs7Ozs7OzBCQWNsQiw4REFBQ3dCO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0k7d0JBQU1KLFdBQVU7a0NBQWU7Ozs7OztrQ0FDaEMsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNaL0IsZ0JBQWdCd0MsR0FBRyxDQUFDLENBQUMxQixxQkFDcEIsOERBQUNxQjtnQ0FBaUJKLFdBQVU7O2tEQUMxQiw4REFBQ0s7d0NBQ0N0QixNQUFLO3dDQUNMUCxTQUFTVixPQUFPbUIsY0FBYyxFQUFFeUIsU0FBUzNCLFNBQVM7d0NBQ2xEd0IsVUFBVSxDQUFDNUIsSUFBTUcsMkJBQTJCQyxNQUFNSixFQUFFNkIsTUFBTSxDQUFDaEMsT0FBTzt3Q0FDbEV3QixXQUFVOzs7Ozs7a0RBRVosOERBQUNXO3dDQUFLWCxXQUFVO2tEQUF5QmpCOzs7Ozs7OytCQVAvQkE7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBY2xCLDhEQUFDZ0I7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDSTt3QkFBTUosV0FBVTtrQ0FBZTs7Ozs7O2tDQUNoQyw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ1o5QixTQUFTdUMsR0FBRyxDQUFDLENBQUNwQix3QkFDYiw4REFBQ2U7Z0NBQW9CSixXQUFVOztrREFDN0IsOERBQUNLO3dDQUNDdEIsTUFBSzt3Q0FDTFAsU0FBU1YsT0FBT3VCLE9BQU8sRUFBRXFCLFNBQVNyQixZQUFZO3dDQUM5Q2tCLFVBQVUsQ0FBQzVCLElBQU1TLG9CQUFvQkMsU0FBU1YsRUFBRTZCLE1BQU0sQ0FBQ2hDLE9BQU87d0NBQzlEd0IsV0FBVTs7Ozs7O2tEQUVaLDhEQUFDVzt3Q0FBS1gsV0FBVTtrREFDYlgsWUFBWSxNQUFNLGFBQ2xCQSxZQUFZLE1BQU0sZ0JBQ2xCQSxZQUFZLE1BQU0sZ0JBQWdCQTs7Ozs7OzsrQkFWM0JBOzs7Ozs7Ozs7Ozs7Ozs7OzBCQWtCbEIsOERBQUNVO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0k7d0JBQU1KLFdBQVU7a0NBQWU7Ozs7OztrQ0FDaEMsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0k7Z0NBQU1KLFdBQVU7O2tEQUNmLDhEQUFDSzt3Q0FDQ3RCLE1BQUs7d0NBQ0xQLFNBQVNWLE9BQU8rQixRQUFRLEtBQUs7d0NBQzdCVSxVQUFVLENBQUM1QixJQUFNWixlQUFlO2dEQUM5QixHQUFHRCxNQUFNO2dEQUNUK0IsVUFBVWxCLEVBQUU2QixNQUFNLENBQUNoQyxPQUFPLEdBQUcsT0FBT0s7NENBQ3RDO3dDQUNBbUIsV0FBVTs7Ozs7O2tEQUVaLDhEQUFDVzt3Q0FBS1gsV0FBVTtrREFBd0I7Ozs7Ozs7Ozs7OzswQ0FHMUMsOERBQUNJO2dDQUFNSixXQUFVOztrREFDZiw4REFBQ0s7d0NBQ0N0QixNQUFLO3dDQUNMUCxTQUFTVixPQUFPZ0MsU0FBUyxLQUFLO3dDQUM5QlMsVUFBVSxDQUFDNUIsSUFBTVosZUFBZTtnREFDOUIsR0FBR0QsTUFBTTtnREFDVGdDLFdBQVduQixFQUFFNkIsTUFBTSxDQUFDaEMsT0FBTyxHQUFHLE9BQU9LOzRDQUN2Qzt3Q0FDQW1CLFdBQVU7Ozs7OztrREFFWiw4REFBQ1c7d0NBQUtYLFdBQVU7a0RBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTTlDLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNJO3dCQUFNSixXQUFVO2tDQUFlOzs7Ozs7a0NBQ2hDLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNLO2dDQUNDdEIsTUFBSztnQ0FDTHVCLGFBQVk7Z0NBQ1paLE9BQU81QixPQUFPOEMsVUFBVSxJQUFJO2dDQUM1QkwsVUFBVSxDQUFDNUIsSUFBTVosZUFBZTt3Q0FDOUIsR0FBR0QsTUFBTTt3Q0FDVDhDLFlBQVlqQyxFQUFFNkIsTUFBTSxDQUFDZCxLQUFLLEdBQUdtQixTQUFTbEMsRUFBRTZCLE1BQU0sQ0FBQ2QsS0FBSyxJQUFJYjtvQ0FDMUQ7Z0NBQ0FtQixXQUFVOzs7Ozs7MENBRVosOERBQUNLO2dDQUNDdEIsTUFBSztnQ0FDTHVCLGFBQVk7Z0NBQ1paLE9BQU81QixPQUFPZ0QsVUFBVSxJQUFJO2dDQUM1QlAsVUFBVSxDQUFDNUIsSUFBTVosZUFBZTt3Q0FDOUIsR0FBR0QsTUFBTTt3Q0FDVGdELFlBQVluQyxFQUFFNkIsTUFBTSxDQUFDZCxLQUFLLEdBQUdtQixTQUFTbEMsRUFBRTZCLE1BQU0sQ0FBQ2QsS0FBSyxJQUFJYjtvQ0FDMUQ7Z0NBQ0FtQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFNZkosa0NBQ0MsOERBQUNHO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ2U7d0JBQUdmLFdBQVU7a0NBQXlDOzs7Ozs7a0NBQ3ZELDhEQUFDRDt3QkFBSUMsV0FBVTs7NEJBQ1psQyxPQUFPUyxRQUFRLEVBQUVLLHdCQUNoQiw4REFBQ21COztvQ0FBSTtvQ0FBWWpDLE9BQU9TLFFBQVEsQ0FBQ3lDLElBQUksQ0FBQzs7Ozs7Ozs0QkFFdkNsRCxPQUFPbUIsY0FBYyxFQUFFTCx3QkFDdEIsOERBQUNtQjs7b0NBQUk7b0NBQVFqQyxPQUFPbUIsY0FBYyxDQUFDK0IsSUFBSSxDQUFDOzs7Ozs7OzRCQUV6Q2xELE9BQU91QixPQUFPLEVBQUVULHdCQUNmLDhEQUFDbUI7O29DQUFJO29DQUFXakMsT0FBT3VCLE9BQU8sQ0FBQzJCLElBQUksQ0FBQzs7Ozs7Ozs0QkFFckNsRCxPQUFPTyxNQUFNLGtCQUNaLDhEQUFDMEI7O29DQUFJO29DQUFnQmpDLE9BQU9PLE1BQU07b0NBQUM7Ozs7Ozs7NEJBRXBDUCxPQUFPK0IsUUFBUSxrQkFDZCw4REFBQ0U7MENBQUk7Ozs7Ozs0QkFFTmpDLE9BQU9nQyxTQUFTLGtCQUNmLDhEQUFDQzswQ0FBSTs7Ozs7OzRCQUVMakMsQ0FBQUEsT0FBTzhDLFVBQVUsSUFBSTlDLE9BQU9nRCxVQUFVLG1CQUN0Qyw4REFBQ2Y7O29DQUFJO29DQUNRakMsT0FBTzhDLFVBQVUsSUFBSTtvQ0FBRTtvQ0FBSTlDLE9BQU9nRCxVQUFVLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRM0U7QUFFQSxpRUFBZWpELFdBQVdBLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxsb3ZlXFxkYXNoYm9hcmRcXGNzdi1tYXJrZXQtZGFzaGJvYXJkXFxzcmNcXGNvbXBvbmVudHNcXEZpbHRlclBhbmVsLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgRmlsdGVyUGFuZWxQcm9wcywgSW5zdHJ1bWVudEZpbHRlciB9IGZyb20gJ0AvdHlwZXMnO1xyXG5cclxuY29uc3QgRmlsdGVyUGFuZWw6IFJlYWN0LkZDPEZpbHRlclBhbmVsUHJvcHM+ID0gKHtcclxuICBmaWx0ZXIsXHJcbiAgb25GaWx0ZXJDaGFuZ2UsXHJcbiAgZXhjaGFuZ2VzLFxyXG4gIGluc3RydW1lbnRUeXBlcyxcclxuICBzZWdtZW50cyxcclxufSkgPT4ge1xyXG4gIGNvbnN0IFtzZWFyY2hUZXJtLCBzZXRTZWFyY2hUZXJtXSA9IHVzZVN0YXRlKGZpbHRlci5zZWFyY2ggfHwgJycpO1xyXG5cclxuICBjb25zdCBoYW5kbGVFeGNoYW5nZUNoYW5nZSA9IChleGNoYW5nZTogc3RyaW5nLCBjaGVja2VkOiBib29sZWFuKSA9PiB7XHJcbiAgICBjb25zdCBjdXJyZW50RXhjaGFuZ2VzID0gZmlsdGVyLmV4Y2hhbmdlIHx8IFtdO1xyXG4gICAgY29uc3QgbmV3RXhjaGFuZ2VzID0gY2hlY2tlZFxyXG4gICAgICA/IFsuLi5jdXJyZW50RXhjaGFuZ2VzLCBleGNoYW5nZV1cclxuICAgICAgOiBjdXJyZW50RXhjaGFuZ2VzLmZpbHRlcihlID0+IGUgIT09IGV4Y2hhbmdlKTtcclxuICAgIFxyXG4gICAgb25GaWx0ZXJDaGFuZ2Uoe1xyXG4gICAgICAuLi5maWx0ZXIsXHJcbiAgICAgIGV4Y2hhbmdlOiBuZXdFeGNoYW5nZXMubGVuZ3RoID4gMCA/IG5ld0V4Y2hhbmdlcyA6IHVuZGVmaW5lZCxcclxuICAgIH0pO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUluc3RydW1lbnRUeXBlQ2hhbmdlID0gKHR5cGU6IHN0cmluZywgY2hlY2tlZDogYm9vbGVhbikgPT4ge1xyXG4gICAgY29uc3QgY3VycmVudFR5cGVzID0gZmlsdGVyLmluc3RydW1lbnRUeXBlIHx8IFtdO1xyXG4gICAgY29uc3QgbmV3VHlwZXMgPSBjaGVja2VkXHJcbiAgICAgID8gWy4uLmN1cnJlbnRUeXBlcywgdHlwZV1cclxuICAgICAgOiBjdXJyZW50VHlwZXMuZmlsdGVyKHQgPT4gdCAhPT0gdHlwZSk7XHJcbiAgICBcclxuICAgIG9uRmlsdGVyQ2hhbmdlKHtcclxuICAgICAgLi4uZmlsdGVyLFxyXG4gICAgICBpbnN0cnVtZW50VHlwZTogbmV3VHlwZXMubGVuZ3RoID4gMCA/IG5ld1R5cGVzIDogdW5kZWZpbmVkLFxyXG4gICAgfSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlU2VnbWVudENoYW5nZSA9IChzZWdtZW50OiBzdHJpbmcsIGNoZWNrZWQ6IGJvb2xlYW4pID0+IHtcclxuICAgIGNvbnN0IGN1cnJlbnRTZWdtZW50cyA9IGZpbHRlci5zZWdtZW50IHx8IFtdO1xyXG4gICAgY29uc3QgbmV3U2VnbWVudHMgPSBjaGVja2VkXHJcbiAgICAgID8gWy4uLmN1cnJlbnRTZWdtZW50cywgc2VnbWVudF1cclxuICAgICAgOiBjdXJyZW50U2VnbWVudHMuZmlsdGVyKHMgPT4gcyAhPT0gc2VnbWVudCk7XHJcbiAgICBcclxuICAgIG9uRmlsdGVyQ2hhbmdlKHtcclxuICAgICAgLi4uZmlsdGVyLFxyXG4gICAgICBzZWdtZW50OiBuZXdTZWdtZW50cy5sZW5ndGggPiAwID8gbmV3U2VnbWVudHMgOiB1bmRlZmluZWQsXHJcbiAgICB9KTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVTZWFyY2hDaGFuZ2UgPSAodmFsdWU6IHN0cmluZykgPT4ge1xyXG4gICAgc2V0U2VhcmNoVGVybSh2YWx1ZSk7XHJcbiAgICBvbkZpbHRlckNoYW5nZSh7XHJcbiAgICAgIC4uLmZpbHRlcixcclxuICAgICAgc2VhcmNoOiB2YWx1ZSB8fCB1bmRlZmluZWQsXHJcbiAgICB9KTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVDbGVhckZpbHRlcnMgPSAoKSA9PiB7XHJcbiAgICBzZXRTZWFyY2hUZXJtKCcnKTtcclxuICAgIG9uRmlsdGVyQ2hhbmdlKHt9KTtcclxuICB9O1xyXG5cclxuICBjb25zdCBpc0ZpbHRlckFjdGl2ZSA9ICgpID0+IHtcclxuICAgIHJldHVybiAhIShcclxuICAgICAgZmlsdGVyLmV4Y2hhbmdlPy5sZW5ndGggfHxcclxuICAgICAgZmlsdGVyLmluc3RydW1lbnRUeXBlPy5sZW5ndGggfHxcclxuICAgICAgZmlsdGVyLnNlZ21lbnQ/Lmxlbmd0aCB8fFxyXG4gICAgICBmaWx0ZXIuc2VhcmNoIHx8XHJcbiAgICAgIGZpbHRlci5pc0FjdGl2ZSAhPT0gdW5kZWZpbmVkIHx8XHJcbiAgICAgIGZpbHRlci5oYXNFeHBpcnkgIT09IHVuZGVmaW5lZFxyXG4gICAgKTtcclxuICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmaWx0ZXItcGFuZWxcIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxyXG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPkZpbHRlcnM8L2gzPlxyXG4gICAgICAgIHtpc0ZpbHRlckFjdGl2ZSgpICYmIChcclxuICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ2xlYXJGaWx0ZXJzfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTgwMCBmb250LW1lZGl1bVwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIENsZWFyIEFsbFxyXG4gICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgKX1cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogU2VhcmNoICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpbHRlci1ncm91cFwiPlxyXG4gICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJmaWx0ZXItbGFiZWxcIj5TZWFyY2g8L2xhYmVsPlxyXG4gICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWFyY2ggYnkgc3ltYm9sLCBuYW1lLCBvciBJU0lOLi4uXCJcclxuICAgICAgICAgIHZhbHVlPXtzZWFyY2hUZXJtfVxyXG4gICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVTZWFyY2hDaGFuZ2UoZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwiZmlsdGVyLWlucHV0XCJcclxuICAgICAgICAvPlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHsvKiBFeGNoYW5nZXMgKi99XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmlsdGVyLWdyb3VwXCI+XHJcbiAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImZpbHRlci1sYWJlbFwiPkV4Y2hhbmdlczwvbGFiZWw+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTIgbWF4LWgtMzIgb3ZlcmZsb3cteS1hdXRvIGN1c3RvbS1zY3JvbGxiYXJcIj5cclxuICAgICAgICAgIHtleGNoYW5nZXMubWFwKChleGNoYW5nZSkgPT4gKFxyXG4gICAgICAgICAgICA8bGFiZWwga2V5PXtleGNoYW5nZX0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXHJcbiAgICAgICAgICAgICAgICBjaGVja2VkPXtmaWx0ZXIuZXhjaGFuZ2U/LmluY2x1ZGVzKGV4Y2hhbmdlKSB8fCBmYWxzZX1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlRXhjaGFuZ2VDaGFuZ2UoZXhjaGFuZ2UsIGUudGFyZ2V0LmNoZWNrZWQpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmlsdGVyLWNoZWNrYm94XCJcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTcwMFwiPntleGNoYW5nZX08L3NwYW4+XHJcbiAgICAgICAgICAgIDwvbGFiZWw+XHJcbiAgICAgICAgICApKX1cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogSW5zdHJ1bWVudCBUeXBlcyAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaWx0ZXItZ3JvdXBcIj5cclxuICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiZmlsdGVyLWxhYmVsXCI+SW5zdHJ1bWVudCBUeXBlczwvbGFiZWw+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTIgbWF4LWgtMzIgb3ZlcmZsb3cteS1hdXRvIGN1c3RvbS1zY3JvbGxiYXJcIj5cclxuICAgICAgICAgIHtpbnN0cnVtZW50VHlwZXMubWFwKCh0eXBlKSA9PiAoXHJcbiAgICAgICAgICAgIDxsYWJlbCBrZXk9e3R5cGV9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxyXG4gICAgICAgICAgICAgICAgY2hlY2tlZD17ZmlsdGVyLmluc3RydW1lbnRUeXBlPy5pbmNsdWRlcyh0eXBlKSB8fCBmYWxzZX1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5zdHJ1bWVudFR5cGVDaGFuZ2UodHlwZSwgZS50YXJnZXQuY2hlY2tlZCl9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmaWx0ZXItY2hlY2tib3hcIlxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNzAwXCI+e3R5cGV9PC9zcGFuPlxyXG4gICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgKSl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIFNlZ21lbnRzICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpbHRlci1ncm91cFwiPlxyXG4gICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJmaWx0ZXItbGFiZWxcIj5TZWdtZW50czwvbGFiZWw+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgIHtzZWdtZW50cy5tYXAoKHNlZ21lbnQpID0+IChcclxuICAgICAgICAgICAgPGxhYmVsIGtleT17c2VnbWVudH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXHJcbiAgICAgICAgICAgICAgICBjaGVja2VkPXtmaWx0ZXIuc2VnbWVudD8uaW5jbHVkZXMoc2VnbWVudCkgfHwgZmFsc2V9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVNlZ21lbnRDaGFuZ2Uoc2VnbWVudCwgZS50YXJnZXQuY2hlY2tlZCl9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmaWx0ZXItY2hlY2tib3hcIlxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNzAwXCI+XHJcbiAgICAgICAgICAgICAgICB7c2VnbWVudCA9PT0gJ0MnID8gJ0Nhc2ggKEMpJyA6IFxyXG4gICAgICAgICAgICAgICAgIHNlZ21lbnQgPT09ICdGJyA/ICdGdXR1cmVzIChGKScgOiBcclxuICAgICAgICAgICAgICAgICBzZWdtZW50ID09PSAnTycgPyAnT3B0aW9ucyAoTyknIDogc2VnbWVudH1cclxuICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgIDwvbGFiZWw+XHJcbiAgICAgICAgICApKX1cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogQWRkaXRpb25hbCBGaWx0ZXJzICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpbHRlci1ncm91cFwiPlxyXG4gICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJmaWx0ZXItbGFiZWxcIj5BZGRpdGlvbmFsIEZpbHRlcnM8L2xhYmVsPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcclxuICAgICAgICAgICAgICBjaGVja2VkPXtmaWx0ZXIuaXNBY3RpdmUgPT09IHRydWV9XHJcbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBvbkZpbHRlckNoYW5nZSh7XHJcbiAgICAgICAgICAgICAgICAuLi5maWx0ZXIsXHJcbiAgICAgICAgICAgICAgICBpc0FjdGl2ZTogZS50YXJnZXQuY2hlY2tlZCA/IHRydWUgOiB1bmRlZmluZWQsXHJcbiAgICAgICAgICAgICAgfSl9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmlsdGVyLWNoZWNrYm94XCJcclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNzAwXCI+QWN0aXZlIE9ubHk8L3NwYW4+XHJcbiAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgXHJcbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcclxuICAgICAgICAgICAgICBjaGVja2VkPXtmaWx0ZXIuaGFzRXhwaXJ5ID09PSB0cnVlfVxyXG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gb25GaWx0ZXJDaGFuZ2Uoe1xyXG4gICAgICAgICAgICAgICAgLi4uZmlsdGVyLFxyXG4gICAgICAgICAgICAgICAgaGFzRXhwaXJ5OiBlLnRhcmdldC5jaGVja2VkID8gdHJ1ZSA6IHVuZGVmaW5lZCxcclxuICAgICAgICAgICAgICB9KX1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmaWx0ZXItY2hlY2tib3hcIlxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS03MDBcIj5IYXMgRXhwaXJ5PC9zcGFuPlxyXG4gICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogTG90IFNpemUgUmFuZ2UgKi99XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmlsdGVyLWdyb3VwXCI+XHJcbiAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImZpbHRlci1sYWJlbFwiPkxvdCBTaXplIFJhbmdlPC9sYWJlbD5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTJcIj5cclxuICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcclxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJNaW5cIlxyXG4gICAgICAgICAgICB2YWx1ZT17ZmlsdGVyLm1pbkxvdFNpemUgfHwgJyd9XHJcbiAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gb25GaWx0ZXJDaGFuZ2Uoe1xyXG4gICAgICAgICAgICAgIC4uLmZpbHRlcixcclxuICAgICAgICAgICAgICBtaW5Mb3RTaXplOiBlLnRhcmdldC52YWx1ZSA/IHBhcnNlSW50KGUudGFyZ2V0LnZhbHVlKSA6IHVuZGVmaW5lZCxcclxuICAgICAgICAgICAgfSl9XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZpbHRlci1pbnB1dCB0ZXh0LXNtXCJcclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXHJcbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiTWF4XCJcclxuICAgICAgICAgICAgdmFsdWU9e2ZpbHRlci5tYXhMb3RTaXplIHx8ICcnfVxyXG4gICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IG9uRmlsdGVyQ2hhbmdlKHtcclxuICAgICAgICAgICAgICAuLi5maWx0ZXIsXHJcbiAgICAgICAgICAgICAgbWF4TG90U2l6ZTogZS50YXJnZXQudmFsdWUgPyBwYXJzZUludChlLnRhcmdldC52YWx1ZSkgOiB1bmRlZmluZWQsXHJcbiAgICAgICAgICAgIH0pfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJmaWx0ZXItaW5wdXQgdGV4dC1zbVwiXHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHsvKiBGaWx0ZXIgU3VtbWFyeSAqL31cclxuICAgICAge2lzRmlsdGVyQWN0aXZlKCkgJiYgKFxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCBwLTMgYmctYmx1ZS01MCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItYmx1ZS0yMDBcIj5cclxuICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS05MDAgbWItMlwiPkFjdGl2ZSBGaWx0ZXJzOjwvaDQ+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMSB0ZXh0LXhzIHRleHQtYmx1ZS03MDBcIj5cclxuICAgICAgICAgICAge2ZpbHRlci5leGNoYW5nZT8ubGVuZ3RoICYmIChcclxuICAgICAgICAgICAgICA8ZGl2PkV4Y2hhbmdlczoge2ZpbHRlci5leGNoYW5nZS5qb2luKCcsICcpfTwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgICB7ZmlsdGVyLmluc3RydW1lbnRUeXBlPy5sZW5ndGggJiYgKFxyXG4gICAgICAgICAgICAgIDxkaXY+VHlwZXM6IHtmaWx0ZXIuaW5zdHJ1bWVudFR5cGUuam9pbignLCAnKX08L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAge2ZpbHRlci5zZWdtZW50Py5sZW5ndGggJiYgKFxyXG4gICAgICAgICAgICAgIDxkaXY+U2VnbWVudHM6IHtmaWx0ZXIuc2VnbWVudC5qb2luKCcsICcpfTwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgICB7ZmlsdGVyLnNlYXJjaCAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdj5TZWFyY2g6ICZsZHF1bzt7ZmlsdGVyLnNlYXJjaH0mcmRxdW87PC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIHtmaWx0ZXIuaXNBY3RpdmUgJiYgKFxyXG4gICAgICAgICAgICAgIDxkaXY+QWN0aXZlIGluc3RydW1lbnRzIG9ubHk8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAge2ZpbHRlci5oYXNFeHBpcnkgJiYgKFxyXG4gICAgICAgICAgICAgIDxkaXY+V2l0aCBleHBpcnkgZGF0ZTwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgICB7KGZpbHRlci5taW5Mb3RTaXplIHx8IGZpbHRlci5tYXhMb3RTaXplKSAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgIExvdCBzaXplOiB7ZmlsdGVyLm1pbkxvdFNpemUgfHwgMH0gLSB7ZmlsdGVyLm1heExvdFNpemUgfHwgJ+KInid9XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgKX1cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBGaWx0ZXJQYW5lbDtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJGaWx0ZXJQYW5lbCIsImZpbHRlciIsIm9uRmlsdGVyQ2hhbmdlIiwiZXhjaGFuZ2VzIiwiaW5zdHJ1bWVudFR5cGVzIiwic2VnbWVudHMiLCJzZWFyY2hUZXJtIiwic2V0U2VhcmNoVGVybSIsInNlYXJjaCIsImhhbmRsZUV4Y2hhbmdlQ2hhbmdlIiwiZXhjaGFuZ2UiLCJjaGVja2VkIiwiY3VycmVudEV4Y2hhbmdlcyIsIm5ld0V4Y2hhbmdlcyIsImUiLCJsZW5ndGgiLCJ1bmRlZmluZWQiLCJoYW5kbGVJbnN0cnVtZW50VHlwZUNoYW5nZSIsInR5cGUiLCJjdXJyZW50VHlwZXMiLCJpbnN0cnVtZW50VHlwZSIsIm5ld1R5cGVzIiwidCIsImhhbmRsZVNlZ21lbnRDaGFuZ2UiLCJzZWdtZW50IiwiY3VycmVudFNlZ21lbnRzIiwibmV3U2VnbWVudHMiLCJzIiwiaGFuZGxlU2VhcmNoQ2hhbmdlIiwidmFsdWUiLCJoYW5kbGVDbGVhckZpbHRlcnMiLCJpc0ZpbHRlckFjdGl2ZSIsImlzQWN0aXZlIiwiaGFzRXhwaXJ5IiwiZGl2IiwiY2xhc3NOYW1lIiwiaDMiLCJidXR0b24iLCJvbkNsaWNrIiwibGFiZWwiLCJpbnB1dCIsInBsYWNlaG9sZGVyIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJtYXAiLCJpbmNsdWRlcyIsInNwYW4iLCJtaW5Mb3RTaXplIiwicGFyc2VJbnQiLCJtYXhMb3RTaXplIiwiaDQiLCJqb2luIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FilterPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/InstrumentTable.tsx":
/*!********************************************!*\
  !*** ./src/components/InstrumentTable.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst InstrumentTable = ({ instruments, marketData, onInstrumentSelect, loading = false })=>{\n    const [sortField, setSortField] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('symbol');\n    const [sortDirection, setSortDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('asc');\n    // Sort instruments\n    const sortedInstruments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"InstrumentTable.useMemo[sortedInstruments]\": ()=>{\n            return [\n                ...instruments\n            ].sort({\n                \"InstrumentTable.useMemo[sortedInstruments]\": (a, b)=>{\n                    const aValue = a[sortField];\n                    const bValue = b[sortField];\n                    // Handle undefined values\n                    if (aValue === undefined && bValue === undefined) return 0;\n                    if (aValue === undefined) return 1;\n                    if (bValue === undefined) return -1;\n                    if (aValue === bValue) return 0;\n                    const comparison = aValue < bValue ? -1 : 1;\n                    return sortDirection === 'asc' ? comparison : -comparison;\n                }\n            }[\"InstrumentTable.useMemo[sortedInstruments]\"]);\n        }\n    }[\"InstrumentTable.useMemo[sortedInstruments]\"], [\n        instruments,\n        sortField,\n        sortDirection\n    ]);\n    const handleSort = (field)=>{\n        if (sortField === field) {\n            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n        } else {\n            setSortField(field);\n            setSortDirection('asc');\n        }\n    };\n    const formatPrice = (price)=>{\n        if (price === undefined || price === 0) return '-';\n        return `₹${price.toFixed(2)}`;\n    };\n    const formatChange = (change, changePercent)=>{\n        if (change === undefined || changePercent === undefined) return '-';\n        const sign = change >= 0 ? '+' : '';\n        return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`;\n    };\n    const getChangeColor = (change)=>{\n        if (change === undefined || change === 0) return 'text-gray-600';\n        return change > 0 ? 'text-green-600' : 'text-red-600';\n    };\n    const formatVolume = (volume)=>{\n        if (volume === undefined || volume === 0) return '-';\n        if (volume >= 10000000) return `${(volume / 10000000).toFixed(1)}Cr`;\n        if (volume >= 100000) return `${(volume / 100000).toFixed(1)}L`;\n        if (volume >= 1000) return `${(volume / 1000).toFixed(1)}K`;\n        return volume.toString();\n    };\n    const SortIcon = ({ field })=>{\n        if (sortField !== field) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-gray-400\",\n                children: \"↕\"\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 68,\n                columnNumber: 14\n            }, undefined);\n        }\n        return sortDirection === 'asc' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-blue-600\",\n            children: \"↑\"\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 70,\n            columnNumber: 38\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-blue-600\",\n            children: \"↓\"\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 70,\n            columnNumber: 81\n        }, undefined);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"glass rounded-2xl shadow-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner w-8 h-8 mr-3\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading instruments...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (instruments.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"glass rounded-2xl shadow-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-lg\",\n                        children: \"No instruments found\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 text-sm mt-2\",\n                        children: \"Try adjusting your filters\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"glass rounded-2xl shadow-lg overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900\",\n                        children: \"Market Instruments\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-sm mt-1\",\n                        children: [\n                            \"Showing \",\n                            sortedInstruments.length,\n                            \" instruments\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto custom-scrollbar\",\n                style: {\n                    maxHeight: '600px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"market-table\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"sticky top-0 bg-gray-50 z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('securityId'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Security ID\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"securityId\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('symbol'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Symbol\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"symbol\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('displayName'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Name\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"displayName\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('exchange'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Exchange\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"exchange\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('instrumentType'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Type\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"instrumentType\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right\",\n                                        children: \"LTP\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right\",\n                                        children: \"Change\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right\",\n                                        children: \"Volume\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('lotSize'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Lot Size\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"lotSize\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            children: sortedInstruments.map((instrument)=>{\n                                const data = marketData.get(instrument.securityId);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-gray-50 cursor-pointer transition-colors\",\n                                    onClick: ()=>onInstrumentSelect?.(instrument),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"font-mono text-sm text-gray-700\",\n                                            children: instrument.securityId\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"font-medium text-blue-600\",\n                                            children: instrument.symbol\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"max-w-xs truncate\",\n                                            title: instrument.displayName,\n                                            children: instrument.displayName\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                children: instrument.exchange\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                children: instrument.instrumentType\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"text-right font-medium\",\n                                            children: formatPrice(data?.ltp)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: `text-right font-medium ${getChangeColor(data?.change)}`,\n                                            children: formatChange(data?.change, data?.changePercent)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"text-right\",\n                                            children: formatVolume(data?.volume)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"text-right\",\n                                            children: instrument.lotSize.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, instrument.securityId, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, undefined),\n            sortedInstruments.length > 100 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-gray-50 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600 text-center\",\n                    children: \"Showing first 100 instruments. Use filters to narrow down results.\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InstrumentTable);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/InstrumentTable.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Stats.tsx":
/*!**********************************!*\
  !*** ./src/components/Stats.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Stats = ({ totalInstruments, filteredInstruments, marketDataCount, connected, connectionStats })=>{\n    const formatUptime = (seconds)=>{\n        if (seconds < 60) return `${seconds}s`;\n        if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;\n        const hours = Math.floor(seconds / 3600);\n        const minutes = Math.floor(seconds % 3600 / 60);\n        return `${hours}h ${minutes}m`;\n    };\n    const formatLastUpdate = (date)=>{\n        if (!date) return 'Never';\n        const now = new Date();\n        const diffMs = now.getTime() - date.getTime();\n        const diffSeconds = Math.floor(diffMs / 1000);\n        if (diffSeconds < 60) return `${diffSeconds}s ago`;\n        if (diffSeconds < 3600) return `${Math.floor(diffSeconds / 60)}m ago`;\n        return date.toLocaleTimeString();\n    };\n    const basicStats = [\n        {\n            label: 'Total Instruments',\n            value: totalInstruments.toLocaleString(),\n            icon: '📊',\n            color: 'bg-blue-500',\n            description: 'Available instruments'\n        },\n        {\n            label: 'Filtered Results',\n            value: filteredInstruments.toLocaleString(),\n            icon: '🔍',\n            color: 'bg-purple-500',\n            description: 'Matching filters'\n        },\n        {\n            label: 'Live Data',\n            value: marketDataCount.toLocaleString(),\n            icon: '📈',\n            color: connected ? 'bg-green-500' : 'bg-gray-500',\n            description: connectionStats ? `${connectionStats.connectedInstruments} active` : 'Market data points'\n        },\n        {\n            label: 'Connection',\n            value: connected ? 'Active' : 'Inactive',\n            icon: connected ? '🟢' : '🔴',\n            color: connected ? 'bg-green-500' : 'bg-red-500',\n            description: connectionStats ? `${connectionStats.messagesReceived} messages` : 'WebSocket status'\n        }\n    ];\n    const enhancedStats = connectionStats ? [\n        {\n            label: 'Cache Size',\n            value: connectionStats.cacheSize.toLocaleString(),\n            icon: '💾',\n            color: 'bg-indigo-500',\n            description: connectionStats.isAutoSaving ? 'Auto-saving' : 'Manual save'\n        },\n        {\n            label: 'Last Update',\n            value: formatLastUpdate(connectionStats.lastUpdate),\n            icon: '🕒',\n            color: 'bg-orange-500',\n            description: 'Data freshness'\n        },\n        {\n            label: 'Uptime',\n            value: formatUptime(connectionStats.connectionUptime),\n            icon: '⏱️',\n            color: 'bg-teal-500',\n            description: 'Connection stability'\n        },\n        {\n            label: 'Reconnects',\n            value: connectionStats.reconnectAttempts.toString(),\n            icon: connectionStats.reconnectAttempts > 0 ? '🔄' : '✅',\n            color: connectionStats.reconnectAttempts > 0 ? 'bg-yellow-500' : 'bg-green-500',\n            description: 'Connection reliability'\n        }\n    ] : [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: basicStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glass rounded-xl shadow-lg p-4 card-hover\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: stat.label\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900 mt-1\",\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: stat.description\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl ml-3\",\n                                        children: stat.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `mt-3 h-1 rounded-full ${stat.color}`\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined),\n            enhancedStats.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800 mb-3 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl mr-2\",\n                                children: \"⚡\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Real-time Connection Stats\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: enhancedStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"glass rounded-xl shadow-lg p-4 card-hover border-l-4 border-l-blue-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: stat.label\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-gray-900 mt-1\",\n                                                        children: stat.value\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: stat.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl ml-3\",\n                                                children: stat.icon\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `mt-3 h-1 rounded-full ${stat.color}`\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Stats);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TdGF0cy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRTBCO0FBcUIxQixNQUFNQyxRQUE4QixDQUFDLEVBQ25DQyxnQkFBZ0IsRUFDaEJDLG1CQUFtQixFQUNuQkMsZUFBZSxFQUNmQyxTQUFTLEVBQ1RDLGVBQWUsRUFDaEI7SUFDQyxNQUFNQyxlQUFlLENBQUNDO1FBQ3BCLElBQUlBLFVBQVUsSUFBSSxPQUFPLEdBQUdBLFFBQVEsQ0FBQyxDQUFDO1FBQ3RDLElBQUlBLFVBQVUsTUFBTSxPQUFPLEdBQUdDLEtBQUtDLEtBQUssQ0FBQ0YsVUFBVSxJQUFJLEVBQUUsRUFBRUEsVUFBVSxHQUFHLENBQUMsQ0FBQztRQUMxRSxNQUFNRyxRQUFRRixLQUFLQyxLQUFLLENBQUNGLFVBQVU7UUFDbkMsTUFBTUksVUFBVUgsS0FBS0MsS0FBSyxDQUFDLFVBQVcsT0FBUTtRQUM5QyxPQUFPLEdBQUdDLE1BQU0sRUFBRSxFQUFFQyxRQUFRLENBQUMsQ0FBQztJQUNoQztJQUVBLE1BQU1DLG1CQUFtQixDQUFDQztRQUN4QixJQUFJLENBQUNBLE1BQU0sT0FBTztRQUNsQixNQUFNQyxNQUFNLElBQUlDO1FBQ2hCLE1BQU1DLFNBQVNGLElBQUlHLE9BQU8sS0FBS0osS0FBS0ksT0FBTztRQUMzQyxNQUFNQyxjQUFjVixLQUFLQyxLQUFLLENBQUNPLFNBQVM7UUFFeEMsSUFBSUUsY0FBYyxJQUFJLE9BQU8sR0FBR0EsWUFBWSxLQUFLLENBQUM7UUFDbEQsSUFBSUEsY0FBYyxNQUFNLE9BQU8sR0FBR1YsS0FBS0MsS0FBSyxDQUFDUyxjQUFjLElBQUksS0FBSyxDQUFDO1FBQ3JFLE9BQU9MLEtBQUtNLGtCQUFrQjtJQUNoQztJQUVBLE1BQU1DLGFBQWE7UUFDakI7WUFDRUMsT0FBTztZQUNQQyxPQUFPckIsaUJBQWlCc0IsY0FBYztZQUN0Q0MsTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLGFBQWE7UUFDZjtRQUNBO1lBQ0VMLE9BQU87WUFDUEMsT0FBT3BCLG9CQUFvQnFCLGNBQWM7WUFDekNDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxhQUFhO1FBQ2Y7UUFDQTtZQUNFTCxPQUFPO1lBQ1BDLE9BQU9uQixnQkFBZ0JvQixjQUFjO1lBQ3JDQyxNQUFNO1lBQ05DLE9BQU9yQixZQUFZLGlCQUFpQjtZQUNwQ3NCLGFBQWFyQixrQkFBa0IsR0FBR0EsZ0JBQWdCc0Isb0JBQW9CLENBQUMsT0FBTyxDQUFDLEdBQUc7UUFDcEY7UUFDQTtZQUNFTixPQUFPO1lBQ1BDLE9BQU9sQixZQUFZLFdBQVc7WUFDOUJvQixNQUFNcEIsWUFBWSxPQUFPO1lBQ3pCcUIsT0FBT3JCLFlBQVksaUJBQWlCO1lBQ3BDc0IsYUFBYXJCLGtCQUFrQixHQUFHQSxnQkFBZ0J1QixnQkFBZ0IsQ0FBQyxTQUFTLENBQUMsR0FBRztRQUNsRjtLQUNEO0lBRUQsTUFBTUMsZ0JBQWdCeEIsa0JBQWtCO1FBQ3RDO1lBQ0VnQixPQUFPO1lBQ1BDLE9BQU9qQixnQkFBZ0J5QixTQUFTLENBQUNQLGNBQWM7WUFDL0NDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxhQUFhckIsZ0JBQWdCMEIsWUFBWSxHQUFHLGdCQUFnQjtRQUM5RDtRQUNBO1lBQ0VWLE9BQU87WUFDUEMsT0FBT1YsaUJBQWlCUCxnQkFBZ0IyQixVQUFVO1lBQ2xEUixNQUFNO1lBQ05DLE9BQU87WUFDUEMsYUFBYTtRQUNmO1FBQ0E7WUFDRUwsT0FBTztZQUNQQyxPQUFPaEIsYUFBYUQsZ0JBQWdCNEIsZ0JBQWdCO1lBQ3BEVCxNQUFNO1lBQ05DLE9BQU87WUFDUEMsYUFBYTtRQUNmO1FBQ0E7WUFDRUwsT0FBTztZQUNQQyxPQUFPakIsZ0JBQWdCNkIsaUJBQWlCLENBQUNDLFFBQVE7WUFDakRYLE1BQU1uQixnQkFBZ0I2QixpQkFBaUIsR0FBRyxJQUFJLE9BQU87WUFDckRULE9BQU9wQixnQkFBZ0I2QixpQkFBaUIsR0FBRyxJQUFJLGtCQUFrQjtZQUNqRVIsYUFBYTtRQUNmO0tBQ0QsR0FBRyxFQUFFO0lBRU4scUJBQ0UsOERBQUNVO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDWmpCLFdBQVdrQixHQUFHLENBQUMsQ0FBQ0MsTUFBTUMsc0JBQ3JCLDhEQUFDSjt3QkFBZ0JDLFdBQVU7OzBDQUN6Qiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNJO2dEQUFFSixXQUFVOzBEQUFxQ0UsS0FBS2xCLEtBQUs7Ozs7OzswREFDNUQsOERBQUNvQjtnREFBRUosV0FBVTswREFBeUNFLEtBQUtqQixLQUFLOzs7Ozs7MERBQ2hFLDhEQUFDbUI7Z0RBQUVKLFdBQVU7MERBQThCRSxLQUFLYixXQUFXOzs7Ozs7Ozs7Ozs7a0RBRTdELDhEQUFDVTt3Q0FBSUMsV0FBVTtrREFBaUJFLEtBQUtmLElBQUk7Ozs7Ozs7Ozs7OzswQ0FFM0MsOERBQUNZO2dDQUFJQyxXQUFXLENBQUMsc0JBQXNCLEVBQUVFLEtBQUtkLEtBQUssRUFBRTs7Ozs7Ozt1QkFUN0NlOzs7Ozs7Ozs7O1lBZWJYLGNBQWNhLE1BQU0sR0FBRyxtQkFDdEIsOERBQUNOOztrQ0FDQyw4REFBQ087d0JBQUdOLFdBQVU7OzBDQUNaLDhEQUFDTztnQ0FBS1AsV0FBVTswQ0FBZTs7Ozs7OzRCQUFROzs7Ozs7O2tDQUd6Qyw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ1pSLGNBQWNTLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxzQkFDeEIsOERBQUNKO2dDQUFnQkMsV0FBVTs7a0RBQ3pCLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0k7d0RBQUVKLFdBQVU7a0VBQXFDRSxLQUFLbEIsS0FBSzs7Ozs7O2tFQUM1RCw4REFBQ29CO3dEQUFFSixXQUFVO2tFQUF3Q0UsS0FBS2pCLEtBQUs7Ozs7OztrRUFDL0QsOERBQUNtQjt3REFBRUosV0FBVTtrRUFBOEJFLEtBQUtiLFdBQVc7Ozs7Ozs7Ozs7OzswREFFN0QsOERBQUNVO2dEQUFJQyxXQUFVOzBEQUFnQkUsS0FBS2YsSUFBSTs7Ozs7Ozs7Ozs7O2tEQUUxQyw4REFBQ1k7d0NBQUlDLFdBQVcsQ0FBQyxzQkFBc0IsRUFBRUUsS0FBS2QsS0FBSyxFQUFFOzs7Ozs7OytCQVQ3Q2U7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFpQnhCO0FBRUEsaUVBQWV4QyxLQUFLQSxFQUFDIiwic291cmNlcyI6WyJEOlxcbG92ZVxcZGFzaGJvYXJkXFxjc3YtbWFya2V0LWRhc2hib2FyZFxcc3JjXFxjb21wb25lbnRzXFxTdGF0cy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5cclxuaW50ZXJmYWNlIENvbm5lY3Rpb25TdGF0cyB7XHJcbiAgdG90YWxJbnN0cnVtZW50czogbnVtYmVyO1xyXG4gIGNvbm5lY3RlZEluc3RydW1lbnRzOiBudW1iZXI7XHJcbiAgbGFzdFVwZGF0ZTogRGF0ZSB8IG51bGw7XHJcbiAgY2FjaGVTaXplOiBudW1iZXI7XHJcbiAgY29ubmVjdGlvblVwdGltZTogbnVtYmVyO1xyXG4gIG1lc3NhZ2VzUmVjZWl2ZWQ6IG51bWJlcjtcclxuICByZWNvbm5lY3RBdHRlbXB0czogbnVtYmVyO1xyXG4gIGlzQXV0b1NhdmluZzogYm9vbGVhbjtcclxufVxyXG5cclxuaW50ZXJmYWNlIFN0YXRzUHJvcHMge1xyXG4gIHRvdGFsSW5zdHJ1bWVudHM6IG51bWJlcjtcclxuICBmaWx0ZXJlZEluc3RydW1lbnRzOiBudW1iZXI7XHJcbiAgbWFya2V0RGF0YUNvdW50OiBudW1iZXI7XHJcbiAgY29ubmVjdGVkOiBib29sZWFuO1xyXG4gIGNvbm5lY3Rpb25TdGF0cz86IENvbm5lY3Rpb25TdGF0cztcclxufVxyXG5cclxuY29uc3QgU3RhdHM6IFJlYWN0LkZDPFN0YXRzUHJvcHM+ID0gKHtcclxuICB0b3RhbEluc3RydW1lbnRzLFxyXG4gIGZpbHRlcmVkSW5zdHJ1bWVudHMsXHJcbiAgbWFya2V0RGF0YUNvdW50LFxyXG4gIGNvbm5lY3RlZCxcclxuICBjb25uZWN0aW9uU3RhdHMsXHJcbn0pID0+IHtcclxuICBjb25zdCBmb3JtYXRVcHRpbWUgPSAoc2Vjb25kczogbnVtYmVyKTogc3RyaW5nID0+IHtcclxuICAgIGlmIChzZWNvbmRzIDwgNjApIHJldHVybiBgJHtzZWNvbmRzfXNgO1xyXG4gICAgaWYgKHNlY29uZHMgPCAzNjAwKSByZXR1cm4gYCR7TWF0aC5mbG9vcihzZWNvbmRzIC8gNjApfW0gJHtzZWNvbmRzICUgNjB9c2A7XHJcbiAgICBjb25zdCBob3VycyA9IE1hdGguZmxvb3Ioc2Vjb25kcyAvIDM2MDApO1xyXG4gICAgY29uc3QgbWludXRlcyA9IE1hdGguZmxvb3IoKHNlY29uZHMgJSAzNjAwKSAvIDYwKTtcclxuICAgIHJldHVybiBgJHtob3Vyc31oICR7bWludXRlc31tYDtcclxuICB9O1xyXG5cclxuICBjb25zdCBmb3JtYXRMYXN0VXBkYXRlID0gKGRhdGU6IERhdGUgfCBudWxsKTogc3RyaW5nID0+IHtcclxuICAgIGlmICghZGF0ZSkgcmV0dXJuICdOZXZlcic7XHJcbiAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xyXG4gICAgY29uc3QgZGlmZk1zID0gbm93LmdldFRpbWUoKSAtIGRhdGUuZ2V0VGltZSgpO1xyXG4gICAgY29uc3QgZGlmZlNlY29uZHMgPSBNYXRoLmZsb29yKGRpZmZNcyAvIDEwMDApO1xyXG4gICAgXHJcbiAgICBpZiAoZGlmZlNlY29uZHMgPCA2MCkgcmV0dXJuIGAke2RpZmZTZWNvbmRzfXMgYWdvYDtcclxuICAgIGlmIChkaWZmU2Vjb25kcyA8IDM2MDApIHJldHVybiBgJHtNYXRoLmZsb29yKGRpZmZTZWNvbmRzIC8gNjApfW0gYWdvYDtcclxuICAgIHJldHVybiBkYXRlLnRvTG9jYWxlVGltZVN0cmluZygpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGJhc2ljU3RhdHMgPSBbXHJcbiAgICB7XHJcbiAgICAgIGxhYmVsOiAnVG90YWwgSW5zdHJ1bWVudHMnLFxyXG4gICAgICB2YWx1ZTogdG90YWxJbnN0cnVtZW50cy50b0xvY2FsZVN0cmluZygpLFxyXG4gICAgICBpY29uOiAn8J+TiicsXHJcbiAgICAgIGNvbG9yOiAnYmctYmx1ZS01MDAnLFxyXG4gICAgICBkZXNjcmlwdGlvbjogJ0F2YWlsYWJsZSBpbnN0cnVtZW50cycsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBsYWJlbDogJ0ZpbHRlcmVkIFJlc3VsdHMnLFxyXG4gICAgICB2YWx1ZTogZmlsdGVyZWRJbnN0cnVtZW50cy50b0xvY2FsZVN0cmluZygpLFxyXG4gICAgICBpY29uOiAn8J+UjScsXHJcbiAgICAgIGNvbG9yOiAnYmctcHVycGxlLTUwMCcsXHJcbiAgICAgIGRlc2NyaXB0aW9uOiAnTWF0Y2hpbmcgZmlsdGVycycsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBsYWJlbDogJ0xpdmUgRGF0YScsXHJcbiAgICAgIHZhbHVlOiBtYXJrZXREYXRhQ291bnQudG9Mb2NhbGVTdHJpbmcoKSxcclxuICAgICAgaWNvbjogJ/Cfk4gnLFxyXG4gICAgICBjb2xvcjogY29ubmVjdGVkID8gJ2JnLWdyZWVuLTUwMCcgOiAnYmctZ3JheS01MDAnLFxyXG4gICAgICBkZXNjcmlwdGlvbjogY29ubmVjdGlvblN0YXRzID8gYCR7Y29ubmVjdGlvblN0YXRzLmNvbm5lY3RlZEluc3RydW1lbnRzfSBhY3RpdmVgIDogJ01hcmtldCBkYXRhIHBvaW50cycsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBsYWJlbDogJ0Nvbm5lY3Rpb24nLFxyXG4gICAgICB2YWx1ZTogY29ubmVjdGVkID8gJ0FjdGl2ZScgOiAnSW5hY3RpdmUnLFxyXG4gICAgICBpY29uOiBjb25uZWN0ZWQgPyAn8J+foicgOiAn8J+UtCcsXHJcbiAgICAgIGNvbG9yOiBjb25uZWN0ZWQgPyAnYmctZ3JlZW4tNTAwJyA6ICdiZy1yZWQtNTAwJyxcclxuICAgICAgZGVzY3JpcHRpb246IGNvbm5lY3Rpb25TdGF0cyA/IGAke2Nvbm5lY3Rpb25TdGF0cy5tZXNzYWdlc1JlY2VpdmVkfSBtZXNzYWdlc2AgOiAnV2ViU29ja2V0IHN0YXR1cycsXHJcbiAgICB9LFxyXG4gIF07XHJcblxyXG4gIGNvbnN0IGVuaGFuY2VkU3RhdHMgPSBjb25uZWN0aW9uU3RhdHMgPyBbXHJcbiAgICB7XHJcbiAgICAgIGxhYmVsOiAnQ2FjaGUgU2l6ZScsXHJcbiAgICAgIHZhbHVlOiBjb25uZWN0aW9uU3RhdHMuY2FjaGVTaXplLnRvTG9jYWxlU3RyaW5nKCksXHJcbiAgICAgIGljb246ICfwn5K+JyxcclxuICAgICAgY29sb3I6ICdiZy1pbmRpZ28tNTAwJyxcclxuICAgICAgZGVzY3JpcHRpb246IGNvbm5lY3Rpb25TdGF0cy5pc0F1dG9TYXZpbmcgPyAnQXV0by1zYXZpbmcnIDogJ01hbnVhbCBzYXZlJyxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGxhYmVsOiAnTGFzdCBVcGRhdGUnLFxyXG4gICAgICB2YWx1ZTogZm9ybWF0TGFzdFVwZGF0ZShjb25uZWN0aW9uU3RhdHMubGFzdFVwZGF0ZSksXHJcbiAgICAgIGljb246ICfwn5WSJyxcclxuICAgICAgY29sb3I6ICdiZy1vcmFuZ2UtNTAwJyxcclxuICAgICAgZGVzY3JpcHRpb246ICdEYXRhIGZyZXNobmVzcycsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBsYWJlbDogJ1VwdGltZScsXHJcbiAgICAgIHZhbHVlOiBmb3JtYXRVcHRpbWUoY29ubmVjdGlvblN0YXRzLmNvbm5lY3Rpb25VcHRpbWUpLFxyXG4gICAgICBpY29uOiAn4o+x77iPJyxcclxuICAgICAgY29sb3I6ICdiZy10ZWFsLTUwMCcsXHJcbiAgICAgIGRlc2NyaXB0aW9uOiAnQ29ubmVjdGlvbiBzdGFiaWxpdHknLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgbGFiZWw6ICdSZWNvbm5lY3RzJyxcclxuICAgICAgdmFsdWU6IGNvbm5lY3Rpb25TdGF0cy5yZWNvbm5lY3RBdHRlbXB0cy50b1N0cmluZygpLFxyXG4gICAgICBpY29uOiBjb25uZWN0aW9uU3RhdHMucmVjb25uZWN0QXR0ZW1wdHMgPiAwID8gJ/CflIQnIDogJ+KchScsXHJcbiAgICAgIGNvbG9yOiBjb25uZWN0aW9uU3RhdHMucmVjb25uZWN0QXR0ZW1wdHMgPiAwID8gJ2JnLXllbGxvdy01MDAnIDogJ2JnLWdyZWVuLTUwMCcsXHJcbiAgICAgIGRlc2NyaXB0aW9uOiAnQ29ubmVjdGlvbiByZWxpYWJpbGl0eScsXHJcbiAgICB9LFxyXG4gIF0gOiBbXTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02IG1iLTZcIj5cclxuICAgICAgey8qIEJhc2ljIFN0YXRzICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLTRcIj5cclxuICAgICAgICB7YmFzaWNTdGF0cy5tYXAoKHN0YXQsIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImdsYXNzIHJvdW5kZWQteGwgc2hhZG93LWxnIHAtNCBjYXJkLWhvdmVyXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMFwiPntzdGF0LmxhYmVsfTwvcD5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG10LTFcIj57c3RhdC52YWx1ZX08L3A+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMVwiPntzdGF0LmRlc2NyaXB0aW9ufTwvcD5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIG1sLTNcIj57c3RhdC5pY29ufTwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BtdC0zIGgtMSByb3VuZGVkLWZ1bGwgJHtzdGF0LmNvbG9yfWB9IC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApKX1cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogRW5oYW5jZWQgU3RhdHMgKHdoZW4gYXZhaWxhYmxlKSAqL31cclxuICAgICAge2VuaGFuY2VkU3RhdHMubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgPGRpdj5cclxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMCBtYi0zIGZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteGwgbXItMlwiPuKaoTwvc3Bhbj5cclxuICAgICAgICAgICAgUmVhbC10aW1lIENvbm5lY3Rpb24gU3RhdHNcclxuICAgICAgICAgIDwvaDM+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLTRcIj5cclxuICAgICAgICAgICAge2VuaGFuY2VkU3RhdHMubWFwKChzdGF0LCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiZ2xhc3Mgcm91bmRlZC14bCBzaGFkb3ctbGcgcC00IGNhcmQtaG92ZXIgYm9yZGVyLWwtNCBib3JkZXItbC1ibHVlLTQwMFwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDBcIj57c3RhdC5sYWJlbH08L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtdC0xXCI+e3N0YXQudmFsdWV9PC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtdC0xXCI+e3N0YXQuZGVzY3JpcHRpb259PC9wPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhsIG1sLTNcIj57c3RhdC5pY29ufTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YG10LTMgaC0xIHJvdW5kZWQtZnVsbCAke3N0YXQuY29sb3J9YH0gLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgKX1cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBTdGF0cztcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU3RhdHMiLCJ0b3RhbEluc3RydW1lbnRzIiwiZmlsdGVyZWRJbnN0cnVtZW50cyIsIm1hcmtldERhdGFDb3VudCIsImNvbm5lY3RlZCIsImNvbm5lY3Rpb25TdGF0cyIsImZvcm1hdFVwdGltZSIsInNlY29uZHMiLCJNYXRoIiwiZmxvb3IiLCJob3VycyIsIm1pbnV0ZXMiLCJmb3JtYXRMYXN0VXBkYXRlIiwiZGF0ZSIsIm5vdyIsIkRhdGUiLCJkaWZmTXMiLCJnZXRUaW1lIiwiZGlmZlNlY29uZHMiLCJ0b0xvY2FsZVRpbWVTdHJpbmciLCJiYXNpY1N0YXRzIiwibGFiZWwiLCJ2YWx1ZSIsInRvTG9jYWxlU3RyaW5nIiwiaWNvbiIsImNvbG9yIiwiZGVzY3JpcHRpb24iLCJjb25uZWN0ZWRJbnN0cnVtZW50cyIsIm1lc3NhZ2VzUmVjZWl2ZWQiLCJlbmhhbmNlZFN0YXRzIiwiY2FjaGVTaXplIiwiaXNBdXRvU2F2aW5nIiwibGFzdFVwZGF0ZSIsImNvbm5lY3Rpb25VcHRpbWUiLCJyZWNvbm5lY3RBdHRlbXB0cyIsInRvU3RyaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwibWFwIiwic3RhdCIsImluZGV4IiwicCIsImxlbmd0aCIsImgzIiwic3BhbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Stats.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useEnhancedMarketData.ts":
/*!********************************************!*\
  !*** ./src/hooks/useEnhancedMarketData.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useEnhancedMarketData: () => (/* binding */ useEnhancedMarketData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * Enhanced Market Data Hook\r\n * Provides optimized access to market data with automatic initialization\r\n * Features:\r\n * - Automatic WebSocket connection management\r\n * - Optimized re-rendering with selective subscriptions\r\n * - Auto-save functionality\r\n * - Error recovery and reconnection\r\n */ \nconst useEnhancedMarketData = (options = {})=>{\n    const { autoConnect = true, autoLoadCache = true, autoSaveInterval = 30000, reconnectOnError = true, maxReconnectAttempts = 5 } = options;\n    // Temporary fallback implementation\n    const connection = {\n        status: 'disconnected',\n        isConnected: false,\n        error: null,\n        connectionStats: {\n            totalMessages: 0,\n            connectionUptime: 0\n        }\n    };\n    const marketDataMap = new Map();\n    const cache = {\n        isLoaded: false,\n        totalCacheSize: 0,\n        pendingUpdates: 0,\n        lastCacheUpdate: null\n    };\n    const ui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useEnhancedMarketData.useMemo[ui]\": ()=>({\n                filters: {},\n                sortConfig: {\n                    field: 'symbol',\n                    direction: 'asc'\n                },\n                selectedInstruments: new Set(),\n                viewMode: 'table',\n                autoRefresh: true\n            })\n    }[\"useEnhancedMarketData.useMemo[ui]\"], []);\n    const isLoading = false;\n    // Store actions - temporary mock functions with useCallback to fix dependency warnings\n    const initializeConnection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[initializeConnection]\": async ()=>{}\n    }[\"useEnhancedMarketData.useCallback[initializeConnection]\"], []);\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[connect]\": async ()=>{}\n    }[\"useEnhancedMarketData.useCallback[connect]\"], []);\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[disconnect]\": ()=>{}\n    }[\"useEnhancedMarketData.useCallback[disconnect]\"], []);\n    const reconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[reconnect]\": async ()=>{}\n    }[\"useEnhancedMarketData.useCallback[reconnect]\"], []);\n    const loadFromCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[loadFromCache]\": async ()=>{}\n    }[\"useEnhancedMarketData.useCallback[loadFromCache]\"], []);\n    const saveToCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[saveToCache]\": async (force)=>{}\n    }[\"useEnhancedMarketData.useCallback[saveToCache]\"], []);\n    const clearCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[clearCache]\": async ()=>{}\n    }[\"useEnhancedMarketData.useCallback[clearCache]\"], []);\n    const setFilters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[setFilters]\": (filters)=>{}\n    }[\"useEnhancedMarketData.useCallback[setFilters]\"], []);\n    const setSortConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[setSortConfig]\": (field, direction)=>{}\n    }[\"useEnhancedMarketData.useCallback[setSortConfig]\"], []);\n    const getFilteredMarketData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[getFilteredMarketData]\": ()=>[]\n    }[\"useEnhancedMarketData.useCallback[getFilteredMarketData]\"], []);\n    const getSortedMarketData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[getSortedMarketData]\": ()=>[]\n    }[\"useEnhancedMarketData.useCallback[getSortedMarketData]\"], []);\n    const getMarketDataBySecurityId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[getMarketDataBySecurityId]\": (securityId)=>undefined\n    }[\"useEnhancedMarketData.useCallback[getMarketDataBySecurityId]\"], []);\n    const getMarketDataBySymbol = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[getMarketDataBySymbol]\": (symbol)=>undefined\n    }[\"useEnhancedMarketData.useCallback[getMarketDataBySymbol]\"], []);\n    const subscribeToInstrument = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[subscribeToInstrument]\": (securityId)=>{}\n    }[\"useEnhancedMarketData.useCallback[subscribeToInstrument]\"], []);\n    const unsubscribeFromInstrument = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[unsubscribeFromInstrument]\": (securityId)=>{}\n    }[\"useEnhancedMarketData.useCallback[unsubscribeFromInstrument]\"], []);\n    const reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[reset]\": ()=>{}\n    }[\"useEnhancedMarketData.useCallback[reset]\"], []);\n    // Refs for intervals and tracking\n    const autoSaveIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const initializationRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Convert Map to Array for components that expect arrays\n    const marketDataArray = Array.from(marketDataMap.values());\n    // Initialize connection and cache on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEnhancedMarketData.useEffect\": ()=>{\n            const initialize = {\n                \"useEnhancedMarketData.useEffect.initialize\": async ()=>{\n                    if (initializationRef.current) return;\n                    initializationRef.current = true;\n                    console.log('🚀 Enhanced Hook: Initializing...');\n                    try {\n                        // Load cache first for instant data display\n                        if (autoLoadCache && !cache.isLoaded) {\n                            await loadFromCache();\n                        }\n                        // Initialize WebSocket connection\n                        if (autoConnect && connection.status === 'disconnected') {\n                            await initializeConnection();\n                        }\n                    } catch (error) {\n                        console.error('❌ Enhanced Hook: Initialization failed:', error);\n                    }\n                }\n            }[\"useEnhancedMarketData.useEffect.initialize\"];\n            initialize();\n            // Cleanup on unmount\n            return ({\n                \"useEnhancedMarketData.useEffect\": ()=>{\n                    if (autoSaveIntervalRef.current) {\n                        clearInterval(autoSaveIntervalRef.current);\n                    }\n                }\n            })[\"useEnhancedMarketData.useEffect\"];\n        }\n    }[\"useEnhancedMarketData.useEffect\"], [\n        autoConnect,\n        autoLoadCache,\n        cache.isLoaded,\n        connection.status,\n        initializeConnection,\n        loadFromCache\n    ]);\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEnhancedMarketData.useEffect\": ()=>{\n            if (autoSaveInterval > 0 && marketDataArray.length > 0) {\n                if (autoSaveIntervalRef.current) {\n                    clearInterval(autoSaveIntervalRef.current);\n                }\n                autoSaveIntervalRef.current = setInterval({\n                    \"useEnhancedMarketData.useEffect\": ()=>{\n                        saveToCache();\n                    }\n                }[\"useEnhancedMarketData.useEffect\"], autoSaveInterval);\n                return ({\n                    \"useEnhancedMarketData.useEffect\": ()=>{\n                        if (autoSaveIntervalRef.current) {\n                            clearInterval(autoSaveIntervalRef.current);\n                        }\n                    }\n                })[\"useEnhancedMarketData.useEffect\"];\n            }\n        }\n    }[\"useEnhancedMarketData.useEffect\"], [\n        autoSaveInterval,\n        marketDataArray.length,\n        saveToCache\n    ]);\n    // Auto-reconnect on error\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEnhancedMarketData.useEffect\": ()=>{\n            if (reconnectOnError && connection.status === 'error' && reconnectAttemptsRef.current < maxReconnectAttempts) {\n                const retryDelay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);\n                console.log(`🔄 Enhanced Hook: Auto-reconnecting in ${retryDelay}ms (attempt ${reconnectAttemptsRef.current + 1}/${maxReconnectAttempts})`);\n                const timeoutId = setTimeout({\n                    \"useEnhancedMarketData.useEffect.timeoutId\": async ()=>{\n                        try {\n                            reconnectAttemptsRef.current++;\n                            await reconnect();\n                        } catch (error) {\n                            console.error('❌ Enhanced Hook: Auto-reconnect failed:', error);\n                        }\n                    }\n                }[\"useEnhancedMarketData.useEffect.timeoutId\"], retryDelay);\n                return ({\n                    \"useEnhancedMarketData.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"useEnhancedMarketData.useEffect\"];\n            }\n        }\n    }[\"useEnhancedMarketData.useEffect\"], [\n        connection.status,\n        reconnect,\n        reconnectOnError,\n        maxReconnectAttempts\n    ]);\n    // Reset reconnect attempts on successful connection\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEnhancedMarketData.useEffect\": ()=>{\n            if (connection.status === 'connected') {\n                reconnectAttemptsRef.current = 0;\n            }\n        }\n    }[\"useEnhancedMarketData.useEffect\"], [\n        connection.status\n    ]);\n    // Optimized data access functions\n    const getFilteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[getFilteredData]\": ()=>{\n            return getFilteredMarketData();\n        }\n    }[\"useEnhancedMarketData.useCallback[getFilteredData]\"], [\n        getFilteredMarketData\n    ]);\n    const getSortedData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[getSortedData]\": ()=>{\n            return getSortedMarketData();\n        }\n    }[\"useEnhancedMarketData.useCallback[getSortedData]\"], [\n        getSortedMarketData\n    ]);\n    const getDataBySecurityId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[getDataBySecurityId]\": (securityId)=>{\n            return getMarketDataBySecurityId(securityId);\n        }\n    }[\"useEnhancedMarketData.useCallback[getDataBySecurityId]\"], [\n        getMarketDataBySecurityId\n    ]);\n    const getDataBySymbol = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[getDataBySymbol]\": (symbol)=>{\n            return getMarketDataBySymbol(symbol);\n        }\n    }[\"useEnhancedMarketData.useCallback[getDataBySymbol]\"], [\n        getMarketDataBySymbol\n    ]);\n    // Enhanced filter functions\n    const updateFilters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[updateFilters]\": (filters)=>{\n            setFilters(filters);\n        }\n    }[\"useEnhancedMarketData.useCallback[updateFilters]\"], [\n        setFilters,\n        ui\n    ]);\n    const updateSort = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[updateSort]\": (field, direction)=>{\n            setSortConfig(field, direction);\n        }\n    }[\"useEnhancedMarketData.useCallback[updateSort]\"], [\n        setSortConfig\n    ]);\n    // Subscription management\n    const subscribe = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[subscribe]\": (securityId)=>{\n            subscribeToInstrument(securityId);\n        }\n    }[\"useEnhancedMarketData.useCallback[subscribe]\"], [\n        subscribeToInstrument\n    ]);\n    const unsubscribe = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[unsubscribe]\": (securityId)=>{\n            unsubscribeFromInstrument(securityId);\n        }\n    }[\"useEnhancedMarketData.useCallback[unsubscribe]\"], [\n        unsubscribeFromInstrument\n    ]);\n    // Connection management\n    const forceReconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[forceReconnect]\": async ()=>{\n            try {\n                reconnectAttemptsRef.current = 0; // Reset attempts for manual reconnect\n                await reconnect();\n            } catch (error) {\n                console.error('❌ Enhanced Hook: Manual reconnect failed:', error);\n                throw error;\n            }\n        }\n    }[\"useEnhancedMarketData.useCallback[forceReconnect]\"], [\n        reconnect\n    ]);\n    const forceRefresh = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[forceRefresh]\": async ()=>{\n            try {\n                await loadFromCache();\n            } catch (error) {\n                console.error('❌ Enhanced Hook: Force refresh failed:', error);\n                throw error;\n            }\n        }\n    }[\"useEnhancedMarketData.useCallback[forceRefresh]\"], [\n        loadFromCache\n    ]);\n    const forceSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[forceSave]\": async ()=>{\n            try {\n                await saveToCache(true);\n            } catch (error) {\n                console.error('❌ Enhanced Hook: Force save failed:', error);\n                throw error;\n            }\n        }\n    }[\"useEnhancedMarketData.useCallback[forceSave]\"], [\n        saveToCache\n    ]);\n    // Stats and computed values\n    const stats = {\n        totalInstruments: marketDataArray.length,\n        connectedInstruments: marketDataArray.filter((item)=>item.ltp && item.ltp > 0).length,\n        lastUpdate: null,\n        cacheSize: cache.totalCacheSize,\n        connectionUptime: connection.connectionStats.connectionUptime,\n        messagesReceived: connection.connectionStats.totalMessages,\n        reconnectAttempts: reconnectAttemptsRef.current,\n        isAutoSaving: autoSaveIntervalRef.current !== null\n    };\n    return {\n        // Data\n        marketData: marketDataArray,\n        marketDataMap,\n        filteredData: getFilteredData(),\n        sortedData: getSortedData(),\n        // Connection state\n        isConnected: connection.isConnected,\n        connectionStatus: connection.status,\n        connectionError: connection.error,\n        connectionStats: connection.connectionStats,\n        // Cache state\n        cacheLoaded: cache.isLoaded,\n        cacheUpdating: cache.pendingUpdates > 0,\n        lastCacheUpdate: cache.lastCacheUpdate,\n        // UI state\n        filters: ui.filters,\n        sortConfig: ui.sortConfig,\n        selectedInstruments: ui.selectedInstruments,\n        viewMode: ui.viewMode,\n        autoRefresh: ui.autoRefresh,\n        // Loading states\n        isLoading,\n        isInitializing: !initializationRef.current,\n        // Data access functions\n        getDataBySecurityId,\n        getDataBySymbol,\n        getFilteredData,\n        getSortedData,\n        // Actions\n        updateFilters,\n        updateSort,\n        subscribe,\n        unsubscribe,\n        // Connection management\n        connect,\n        disconnect,\n        reconnect: forceReconnect,\n        // Cache management\n        refresh: forceRefresh,\n        save: forceSave,\n        clearCache,\n        // Utility\n        reset,\n        stats,\n        // Advanced actions (expose if needed)\n        _store: {\n            setFilters,\n            setSortConfig,\n            subscribeToInstrument,\n            unsubscribeFromInstrument\n        }\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useEnhancedMarketData);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlRW5oYW5jZWRNYXJrZXREYXRhLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTs7Ozs7Ozs7Q0FRQyxHQUUrRDtBQVl6RCxNQUFNSSx3QkFBd0IsQ0FBQ0MsVUFBd0MsQ0FBQyxDQUFDO0lBQzlFLE1BQU0sRUFDSkMsY0FBYyxJQUFJLEVBQ2xCQyxnQkFBZ0IsSUFBSSxFQUNwQkMsbUJBQW1CLEtBQUssRUFDeEJDLG1CQUFtQixJQUFJLEVBQ3ZCQyx1QkFBdUIsQ0FBQyxFQUN6QixHQUFHTDtJQUVKLG9DQUFvQztJQUNwQyxNQUFNTSxhQUFhO1FBQ2pCQyxRQUFRO1FBQ1JDLGFBQWE7UUFDYkMsT0FBTztRQUNQQyxpQkFBaUI7WUFDZkMsZUFBZTtZQUNmQyxrQkFBa0I7UUFDcEI7SUFDRjtJQUNBLE1BQU1DLGdCQUFnQixJQUFJQztJQUMxQixNQUFNQyxRQUFRO1FBQ1pDLFVBQVU7UUFDVkMsZ0JBQWdCO1FBQ2hCQyxnQkFBZ0I7UUFDaEJDLGlCQUFpQjtJQUNuQjtJQUNBLE1BQU1DLEtBQUt0Qiw4Q0FBT0E7NkNBQUMsSUFBTztnQkFDeEJ1QixTQUFTLENBQUM7Z0JBQ1ZDLFlBQVk7b0JBQUVDLE9BQU87b0JBQVVDLFdBQVc7Z0JBQWE7Z0JBQ3ZEQyxxQkFBcUIsSUFBSUM7Z0JBQ3pCQyxVQUFVO2dCQUNWQyxhQUFhO1lBQ2Y7NENBQUksRUFBRTtJQUNOLE1BQU1DLFlBQVk7SUFFbEIsdUZBQXVGO0lBQ3ZGLE1BQU1DLHVCQUF1QmxDLGtEQUFXQTttRUFBQyxXQUFhO2tFQUFHLEVBQUU7SUFDM0QsTUFBTW1DLFVBQVVuQyxrREFBV0E7c0RBQUMsV0FBYTtxREFBRyxFQUFFO0lBQzlDLE1BQU1vQyxhQUFhcEMsa0RBQVdBO3lEQUFDLEtBQU87d0RBQUcsRUFBRTtJQUMzQyxNQUFNcUMsWUFBWXJDLGtEQUFXQTt3REFBQyxXQUFhO3VEQUFHLEVBQUU7SUFDaEQsTUFBTXNDLGdCQUFnQnRDLGtEQUFXQTs0REFBQyxXQUFhOzJEQUFHLEVBQUU7SUFDcEQsTUFBTXVDLGNBQWN2QyxrREFBV0E7MERBQUMsT0FBT3dDLFNBQXFCO3lEQUFHLEVBQUU7SUFDakUsTUFBTUMsYUFBYXpDLGtEQUFXQTt5REFBQyxXQUFhO3dEQUFHLEVBQUU7SUFDakQsTUFBTTBDLGFBQWExQyxrREFBV0E7eURBQUMsQ0FBQ3lCLFdBQWtCO3dEQUFHLEVBQUU7SUFDdkQsTUFBTWtCLGdCQUFnQjNDLGtEQUFXQTs0REFBQyxDQUFDMkIsT0FBZUMsYUFBZ0M7MkRBQUcsRUFBRTtJQUN2RixNQUFNZ0Isd0JBQXdCNUMsa0RBQVdBO29FQUFDLElBQU0sRUFBRTttRUFBRSxFQUFFO0lBQ3RELE1BQU02QyxzQkFBc0I3QyxrREFBV0E7a0VBQUMsSUFBTSxFQUFFO2lFQUFFLEVBQUU7SUFDcEQsTUFBTThDLDRCQUE0QjlDLGtEQUFXQTt3RUFBQyxDQUFDK0MsYUFBdUJDO3VFQUFXLEVBQUU7SUFDbkYsTUFBTUMsd0JBQXdCakQsa0RBQVdBO29FQUFDLENBQUNrRCxTQUFtQkY7bUVBQVcsRUFBRTtJQUMzRSxNQUFNRyx3QkFBd0JuRCxrREFBV0E7b0VBQUMsQ0FBQytDLGNBQXdCO21FQUFHLEVBQUU7SUFDeEUsTUFBTUssNEJBQTRCcEQsa0RBQVdBO3dFQUFDLENBQUMrQyxjQUF3Qjt1RUFBRyxFQUFFO0lBQzVFLE1BQU1NLFFBQVFyRCxrREFBV0E7b0RBQUMsS0FBTzttREFBRyxFQUFFO0lBRXRDLGtDQUFrQztJQUNsQyxNQUFNc0Qsc0JBQXNCckQsNkNBQU1BLENBQXdCO0lBQzFELE1BQU1zRCx1QkFBdUJ0RCw2Q0FBTUEsQ0FBQztJQUNwQyxNQUFNdUQsb0JBQW9CdkQsNkNBQU1BLENBQUM7SUFFakMseURBQXlEO0lBQ3pELE1BQU13RCxrQkFBa0JDLE1BQU1DLElBQUksQ0FBQzFDLGNBQWMyQyxNQUFNO0lBRXZELDJDQUEyQztJQUMzQzdELGdEQUFTQTsyQ0FBQztZQUNSLE1BQU04RDs4REFBYTtvQkFDakIsSUFBSUwsa0JBQWtCTSxPQUFPLEVBQUU7b0JBQy9CTixrQkFBa0JNLE9BQU8sR0FBRztvQkFFNUJDLFFBQVFDLEdBQUcsQ0FBQztvQkFFWixJQUFJO3dCQUNGLDRDQUE0Qzt3QkFDNUMsSUFBSTFELGlCQUFpQixDQUFDYSxNQUFNQyxRQUFRLEVBQUU7NEJBQ3BDLE1BQU1rQjt3QkFDUjt3QkFFQSxrQ0FBa0M7d0JBQ2xDLElBQUlqQyxlQUFlSyxXQUFXQyxNQUFNLEtBQUssZ0JBQWdCOzRCQUN2RCxNQUFNdUI7d0JBQ1I7b0JBQ0YsRUFBRSxPQUFPckIsT0FBTzt3QkFDZGtELFFBQVFsRCxLQUFLLENBQUMsMkNBQTJDQTtvQkFDM0Q7Z0JBQ0Y7O1lBRUFnRDtZQUVBLHFCQUFxQjtZQUNyQjttREFBTztvQkFDTCxJQUFJUCxvQkFBb0JRLE9BQU8sRUFBRTt3QkFDL0JHLGNBQWNYLG9CQUFvQlEsT0FBTztvQkFDM0M7Z0JBQ0Y7O1FBQ0Y7MENBQUc7UUFBQ3pEO1FBQWFDO1FBQWVhLE1BQU1DLFFBQVE7UUFBRVYsV0FBV0MsTUFBTTtRQUFFdUI7UUFBc0JJO0tBQWM7SUFFdkcsMEJBQTBCO0lBQzFCdkMsZ0RBQVNBOzJDQUFDO1lBQ1IsSUFBSVEsbUJBQW1CLEtBQUtrRCxnQkFBZ0JTLE1BQU0sR0FBRyxHQUFHO2dCQUN0RCxJQUFJWixvQkFBb0JRLE9BQU8sRUFBRTtvQkFDL0JHLGNBQWNYLG9CQUFvQlEsT0FBTztnQkFDM0M7Z0JBRUFSLG9CQUFvQlEsT0FBTyxHQUFHSzt1REFBWTt3QkFDeEM1QjtvQkFDRjtzREFBR2hDO2dCQUVIO3VEQUFPO3dCQUNMLElBQUkrQyxvQkFBb0JRLE9BQU8sRUFBRTs0QkFDL0JHLGNBQWNYLG9CQUFvQlEsT0FBTzt3QkFDM0M7b0JBQ0Y7O1lBQ0Y7UUFDRjswQ0FBRztRQUFDdkQ7UUFBa0JrRCxnQkFBZ0JTLE1BQU07UUFBRTNCO0tBQVk7SUFFMUQsMEJBQTBCO0lBQzFCeEMsZ0RBQVNBOzJDQUFDO1lBQ1IsSUFDRVMsb0JBQ0FFLFdBQVdDLE1BQU0sS0FBSyxXQUN0QjRDLHFCQUFxQk8sT0FBTyxHQUFHckQsc0JBQy9CO2dCQUNBLE1BQU0yRCxhQUFhQyxLQUFLQyxHQUFHLENBQUMsT0FBT0QsS0FBS0UsR0FBRyxDQUFDLEdBQUdoQixxQkFBcUJPLE9BQU8sR0FBRztnQkFFOUVDLFFBQVFDLEdBQUcsQ0FDVCxDQUFDLHVDQUF1QyxFQUFFSSxXQUFXLFlBQVksRUFBRWIscUJBQXFCTyxPQUFPLEdBQUcsRUFBRSxDQUFDLEVBQUVyRCxxQkFBcUIsQ0FBQyxDQUFDO2dCQUdoSSxNQUFNK0QsWUFBWUM7aUVBQVc7d0JBQzNCLElBQUk7NEJBQ0ZsQixxQkFBcUJPLE9BQU87NEJBQzVCLE1BQU16Qjt3QkFDUixFQUFFLE9BQU94QixPQUFPOzRCQUNka0QsUUFBUWxELEtBQUssQ0FBQywyQ0FBMkNBO3dCQUMzRDtvQkFDRjtnRUFBR3VEO2dCQUVIO3VEQUFPLElBQU1NLGFBQWFGOztZQUM1QjtRQUNGOzBDQUFHO1FBQUM5RCxXQUFXQyxNQUFNO1FBQUUwQjtRQUFXN0I7UUFBa0JDO0tBQXFCO0lBRXpFLG9EQUFvRDtJQUNwRFYsZ0RBQVNBOzJDQUFDO1lBQ1IsSUFBSVcsV0FBV0MsTUFBTSxLQUFLLGFBQWE7Z0JBQ3JDNEMscUJBQXFCTyxPQUFPLEdBQUc7WUFDakM7UUFDRjswQ0FBRztRQUFDcEQsV0FBV0MsTUFBTTtLQUFDO0lBRXRCLGtDQUFrQztJQUNsQyxNQUFNZ0Usa0JBQWtCM0Usa0RBQVdBOzhEQUFDO1lBQ2xDLE9BQU80QztRQUNUOzZEQUFHO1FBQUNBO0tBQXNCO0lBRTFCLE1BQU1nQyxnQkFBZ0I1RSxrREFBV0E7NERBQUM7WUFDaEMsT0FBTzZDO1FBQ1Q7MkRBQUc7UUFBQ0E7S0FBb0I7SUFFeEIsTUFBTWdDLHNCQUFzQjdFLGtEQUFXQTtrRUFBQyxDQUFDK0M7WUFDdkMsT0FBT0QsMEJBQTBCQztRQUNuQztpRUFBRztRQUFDRDtLQUEwQjtJQUU5QixNQUFNZ0Msa0JBQWtCOUUsa0RBQVdBOzhEQUFDLENBQUNrRDtZQUNuQyxPQUFPRCxzQkFBc0JDO1FBQy9COzZEQUFHO1FBQUNEO0tBQXNCO0lBRTFCLDRCQUE0QjtJQUM1QixNQUFNOEIsZ0JBQWdCL0Usa0RBQVdBOzREQUFDLENBQUN5QjtZQUNqQ2lCLFdBQVdqQjtRQUNiOzJEQUFHO1FBQUNpQjtRQUFZbEI7S0FBRztJQUVuQixNQUFNd0QsYUFBYWhGLGtEQUFXQTt5REFBQyxDQUFDMkIsT0FBZUM7WUFDN0NlLGNBQWNoQixPQUFPQztRQUN2Qjt3REFBRztRQUFDZTtLQUFjO0lBRWxCLDBCQUEwQjtJQUMxQixNQUFNc0MsWUFBWWpGLGtEQUFXQTt3REFBQyxDQUFDK0M7WUFDN0JJLHNCQUFzQko7UUFDeEI7dURBQUc7UUFBQ0k7S0FBc0I7SUFFMUIsTUFBTStCLGNBQWNsRixrREFBV0E7MERBQUMsQ0FBQytDO1lBQy9CSywwQkFBMEJMO1FBQzVCO3lEQUFHO1FBQUNLO0tBQTBCO0lBRTlCLHdCQUF3QjtJQUN4QixNQUFNK0IsaUJBQWlCbkYsa0RBQVdBOzZEQUFDO1lBQ2pDLElBQUk7Z0JBQ0Z1RCxxQkFBcUJPLE9BQU8sR0FBRyxHQUFHLHNDQUFzQztnQkFDeEUsTUFBTXpCO1lBQ1IsRUFBRSxPQUFPeEIsT0FBTztnQkFDZGtELFFBQVFsRCxLQUFLLENBQUMsNkNBQTZDQTtnQkFDM0QsTUFBTUE7WUFDUjtRQUNGOzREQUFHO1FBQUN3QjtLQUFVO0lBRWQsTUFBTStDLGVBQWVwRixrREFBV0E7MkRBQUM7WUFDL0IsSUFBSTtnQkFDRixNQUFNc0M7WUFDUixFQUFFLE9BQU96QixPQUFPO2dCQUNka0QsUUFBUWxELEtBQUssQ0FBQywwQ0FBMENBO2dCQUN4RCxNQUFNQTtZQUNSO1FBQ0Y7MERBQUc7UUFBQ3lCO0tBQWM7SUFFbEIsTUFBTStDLFlBQVlyRixrREFBV0E7d0RBQUM7WUFDNUIsSUFBSTtnQkFDRixNQUFNdUMsWUFBWTtZQUNwQixFQUFFLE9BQU8xQixPQUFPO2dCQUNka0QsUUFBUWxELEtBQUssQ0FBQyx1Q0FBdUNBO2dCQUNyRCxNQUFNQTtZQUNSO1FBQ0Y7dURBQUc7UUFBQzBCO0tBQVk7SUFFaEIsNEJBQTRCO0lBQzVCLE1BQU0rQyxRQUFRO1FBQ1pDLGtCQUFrQjlCLGdCQUFnQlMsTUFBTTtRQUN4Q3NCLHNCQUFzQi9CLGdCQUFnQmdDLE1BQU0sQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBS0MsR0FBRyxJQUFJRCxLQUFLQyxHQUFHLEdBQUcsR0FBR3pCLE1BQU07UUFDckYwQixZQUFZO1FBQ1pDLFdBQVcxRSxNQUFNRSxjQUFjO1FBQy9CTCxrQkFBa0JOLFdBQVdJLGVBQWUsQ0FBQ0UsZ0JBQWdCO1FBQzdEOEUsa0JBQWtCcEYsV0FBV0ksZUFBZSxDQUFDQyxhQUFhO1FBQzFEZ0YsbUJBQW1CeEMscUJBQXFCTyxPQUFPO1FBQy9Da0MsY0FBYzFDLG9CQUFvQlEsT0FBTyxLQUFLO0lBQ2hEO0lBRUEsT0FBTztRQUNMLE9BQU87UUFDUG1DLFlBQVl4QztRQUNaeEM7UUFDQWlGLGNBQWN2QjtRQUNkd0IsWUFBWXZCO1FBRVosbUJBQW1CO1FBQ25CaEUsYUFBYUYsV0FBV0UsV0FBVztRQUNuQ3dGLGtCQUFrQjFGLFdBQVdDLE1BQU07UUFDbkMwRixpQkFBaUIzRixXQUFXRyxLQUFLO1FBQ2pDQyxpQkFBaUJKLFdBQVdJLGVBQWU7UUFFM0MsY0FBYztRQUNkd0YsYUFBYW5GLE1BQU1DLFFBQVE7UUFDM0JtRixlQUFlcEYsTUFBTUcsY0FBYyxHQUFHO1FBQ3RDQyxpQkFBaUJKLE1BQU1JLGVBQWU7UUFFdEMsV0FBVztRQUNYRSxTQUFTRCxHQUFHQyxPQUFPO1FBQ25CQyxZQUFZRixHQUFHRSxVQUFVO1FBQ3pCRyxxQkFBcUJMLEdBQUdLLG1CQUFtQjtRQUMzQ0UsVUFBVVAsR0FBR08sUUFBUTtRQUNyQkMsYUFBYVIsR0FBR1EsV0FBVztRQUUzQixpQkFBaUI7UUFDakJDO1FBQ0F1RSxnQkFBZ0IsQ0FBQ2hELGtCQUFrQk0sT0FBTztRQUUxQyx3QkFBd0I7UUFDeEJlO1FBQ0FDO1FBQ0FIO1FBQ0FDO1FBRUEsVUFBVTtRQUNWRztRQUNBQztRQUNBQztRQUNBQztRQUVBLHdCQUF3QjtRQUN4Qi9DO1FBQ0FDO1FBQ0FDLFdBQVc4QztRQUVYLG1CQUFtQjtRQUNuQnNCLFNBQVNyQjtRQUNUc0IsTUFBTXJCO1FBQ041QztRQUVBLFVBQVU7UUFDVlk7UUFDQWlDO1FBRUEsc0NBQXNDO1FBQ3RDcUIsUUFBUTtZQUNOakU7WUFDQUM7WUFDQVE7WUFDQUM7UUFFRjtJQUNGO0FBQ0YsRUFBRTtBQUVGLGlFQUFlakQscUJBQXFCQSxFQUFDIiwic291cmNlcyI6WyJEOlxcbG92ZVxcZGFzaGJvYXJkXFxjc3YtbWFya2V0LWRhc2hib2FyZFxcc3JjXFxob29rc1xcdXNlRW5oYW5jZWRNYXJrZXREYXRhLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxyXG4gKiBFbmhhbmNlZCBNYXJrZXQgRGF0YSBIb29rXHJcbiAqIFByb3ZpZGVzIG9wdGltaXplZCBhY2Nlc3MgdG8gbWFya2V0IGRhdGEgd2l0aCBhdXRvbWF0aWMgaW5pdGlhbGl6YXRpb25cclxuICogRmVhdHVyZXM6XHJcbiAqIC0gQXV0b21hdGljIFdlYlNvY2tldCBjb25uZWN0aW9uIG1hbmFnZW1lbnRcclxuICogLSBPcHRpbWl6ZWQgcmUtcmVuZGVyaW5nIHdpdGggc2VsZWN0aXZlIHN1YnNjcmlwdGlvbnNcclxuICogLSBBdXRvLXNhdmUgZnVuY3Rpb25hbGl0eVxyXG4gKiAtIEVycm9yIHJlY292ZXJ5IGFuZCByZWNvbm5lY3Rpb25cclxuICovXHJcblxyXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrLCB1c2VSZWYsIHVzZU1lbW8gfSBmcm9tICdyZWFjdCc7XHJcbi8vIFRlbXBvcmFyaWx5IGRpc2FibGVkIGVuaGFuY2VkIHN0b3JlIGltcG9ydFxyXG4vLyBpbXBvcnQgeyB1c2VFbmhhbmNlZE1hcmtldFN0b3JlLCBzZWxlY3RDb25uZWN0aW9uLCBzZWxlY3RNYXJrZXREYXRhLCBzZWxlY3RDYWNoZSwgc2VsZWN0VUksIHNlbGVjdElzTG9hZGluZyB9IGZyb20gJ0Avc3RvcmUvZW5oYW5jZWRNYXJrZXRTdG9yZSc7XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIFVzZUVuaGFuY2VkTWFya2V0RGF0YU9wdGlvbnMge1xyXG4gIGF1dG9Db25uZWN0PzogYm9vbGVhbjtcclxuICBhdXRvTG9hZENhY2hlPzogYm9vbGVhbjtcclxuICBhdXRvU2F2ZUludGVydmFsPzogbnVtYmVyO1xyXG4gIHJlY29ubmVjdE9uRXJyb3I/OiBib29sZWFuO1xyXG4gIG1heFJlY29ubmVjdEF0dGVtcHRzPzogbnVtYmVyO1xyXG59XHJcblxyXG5leHBvcnQgY29uc3QgdXNlRW5oYW5jZWRNYXJrZXREYXRhID0gKG9wdGlvbnM6IFVzZUVuaGFuY2VkTWFya2V0RGF0YU9wdGlvbnMgPSB7fSkgPT4ge1xyXG4gIGNvbnN0IHtcclxuICAgIGF1dG9Db25uZWN0ID0gdHJ1ZSxcclxuICAgIGF1dG9Mb2FkQ2FjaGUgPSB0cnVlLFxyXG4gICAgYXV0b1NhdmVJbnRlcnZhbCA9IDMwMDAwLCAvLyAzMCBzZWNvbmRzXHJcbiAgICByZWNvbm5lY3RPbkVycm9yID0gdHJ1ZSxcclxuICAgIG1heFJlY29ubmVjdEF0dGVtcHRzID0gNSxcclxuICB9ID0gb3B0aW9ucztcclxuXHJcbiAgLy8gVGVtcG9yYXJ5IGZhbGxiYWNrIGltcGxlbWVudGF0aW9uXHJcbiAgY29uc3QgY29ubmVjdGlvbiA9IHtcclxuICAgIHN0YXR1czogJ2Rpc2Nvbm5lY3RlZCcgYXMgYW55LFxyXG4gICAgaXNDb25uZWN0ZWQ6IGZhbHNlLFxyXG4gICAgZXJyb3I6IG51bGwsXHJcbiAgICBjb25uZWN0aW9uU3RhdHM6IHtcclxuICAgICAgdG90YWxNZXNzYWdlczogMCxcclxuICAgICAgY29ubmVjdGlvblVwdGltZTogMCxcclxuICAgIH0sXHJcbiAgfTtcclxuICBjb25zdCBtYXJrZXREYXRhTWFwID0gbmV3IE1hcCgpO1xyXG4gIGNvbnN0IGNhY2hlID0ge1xyXG4gICAgaXNMb2FkZWQ6IGZhbHNlLFxyXG4gICAgdG90YWxDYWNoZVNpemU6IDAsXHJcbiAgICBwZW5kaW5nVXBkYXRlczogMCxcclxuICAgIGxhc3RDYWNoZVVwZGF0ZTogbnVsbCxcclxuICB9O1xyXG4gIGNvbnN0IHVpID0gdXNlTWVtbygoKSA9PiAoe1xyXG4gICAgZmlsdGVyczoge30sXHJcbiAgICBzb3J0Q29uZmlnOiB7IGZpZWxkOiAnc3ltYm9sJywgZGlyZWN0aW9uOiAnYXNjJyBhcyBhbnkgfSxcclxuICAgIHNlbGVjdGVkSW5zdHJ1bWVudHM6IG5ldyBTZXQoKSxcclxuICAgIHZpZXdNb2RlOiAndGFibGUnIGFzIGFueSxcclxuICAgIGF1dG9SZWZyZXNoOiB0cnVlLFxyXG4gIH0pLCBbXSk7XHJcbiAgY29uc3QgaXNMb2FkaW5nID0gZmFsc2U7XHJcblxyXG4gIC8vIFN0b3JlIGFjdGlvbnMgLSB0ZW1wb3JhcnkgbW9jayBmdW5jdGlvbnMgd2l0aCB1c2VDYWxsYmFjayB0byBmaXggZGVwZW5kZW5jeSB3YXJuaW5nc1xyXG4gIGNvbnN0IGluaXRpYWxpemVDb25uZWN0aW9uID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge30sIFtdKTtcclxuICBjb25zdCBjb25uZWN0ID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge30sIFtdKTtcclxuICBjb25zdCBkaXNjb25uZWN0ID0gdXNlQ2FsbGJhY2soKCkgPT4ge30sIFtdKTtcclxuICBjb25zdCByZWNvbm5lY3QgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7fSwgW10pO1xyXG4gIGNvbnN0IGxvYWRGcm9tQ2FjaGUgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7fSwgW10pO1xyXG4gIGNvbnN0IHNhdmVUb0NhY2hlID0gdXNlQ2FsbGJhY2soYXN5bmMgKGZvcmNlPzogYm9vbGVhbikgPT4ge30sIFtdKTtcclxuICBjb25zdCBjbGVhckNhY2hlID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge30sIFtdKTtcclxuICBjb25zdCBzZXRGaWx0ZXJzID0gdXNlQ2FsbGJhY2soKGZpbHRlcnM6IGFueSkgPT4ge30sIFtdKTtcclxuICBjb25zdCBzZXRTb3J0Q29uZmlnID0gdXNlQ2FsbGJhY2soKGZpZWxkOiBzdHJpbmcsIGRpcmVjdGlvbj86ICdhc2MnIHwgJ2Rlc2MnKSA9PiB7fSwgW10pO1xyXG4gIGNvbnN0IGdldEZpbHRlcmVkTWFya2V0RGF0YSA9IHVzZUNhbGxiYWNrKCgpID0+IFtdLCBbXSk7XHJcbiAgY29uc3QgZ2V0U29ydGVkTWFya2V0RGF0YSA9IHVzZUNhbGxiYWNrKCgpID0+IFtdLCBbXSk7XHJcbiAgY29uc3QgZ2V0TWFya2V0RGF0YUJ5U2VjdXJpdHlJZCA9IHVzZUNhbGxiYWNrKChzZWN1cml0eUlkOiBzdHJpbmcpID0+IHVuZGVmaW5lZCwgW10pO1xyXG4gIGNvbnN0IGdldE1hcmtldERhdGFCeVN5bWJvbCA9IHVzZUNhbGxiYWNrKChzeW1ib2w6IHN0cmluZykgPT4gdW5kZWZpbmVkLCBbXSk7XHJcbiAgY29uc3Qgc3Vic2NyaWJlVG9JbnN0cnVtZW50ID0gdXNlQ2FsbGJhY2soKHNlY3VyaXR5SWQ6IHN0cmluZykgPT4ge30sIFtdKTtcclxuICBjb25zdCB1bnN1YnNjcmliZUZyb21JbnN0cnVtZW50ID0gdXNlQ2FsbGJhY2soKHNlY3VyaXR5SWQ6IHN0cmluZykgPT4ge30sIFtdKTtcclxuICBjb25zdCByZXNldCA9IHVzZUNhbGxiYWNrKCgpID0+IHt9LCBbXSk7XHJcblxyXG4gIC8vIFJlZnMgZm9yIGludGVydmFscyBhbmQgdHJhY2tpbmdcclxuICBjb25zdCBhdXRvU2F2ZUludGVydmFsUmVmID0gdXNlUmVmPE5vZGVKUy5UaW1lb3V0IHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgcmVjb25uZWN0QXR0ZW1wdHNSZWYgPSB1c2VSZWYoMCk7XHJcbiAgY29uc3QgaW5pdGlhbGl6YXRpb25SZWYgPSB1c2VSZWYoZmFsc2UpO1xyXG5cclxuICAvLyBDb252ZXJ0IE1hcCB0byBBcnJheSBmb3IgY29tcG9uZW50cyB0aGF0IGV4cGVjdCBhcnJheXNcclxuICBjb25zdCBtYXJrZXREYXRhQXJyYXkgPSBBcnJheS5mcm9tKG1hcmtldERhdGFNYXAudmFsdWVzKCkpO1xyXG5cclxuICAvLyBJbml0aWFsaXplIGNvbm5lY3Rpb24gYW5kIGNhY2hlIG9uIG1vdW50XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGluaXRpYWxpemUgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGlmIChpbml0aWFsaXphdGlvblJlZi5jdXJyZW50KSByZXR1cm47XHJcbiAgICAgIGluaXRpYWxpemF0aW9uUmVmLmN1cnJlbnQgPSB0cnVlO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coJ/CfmoAgRW5oYW5jZWQgSG9vazogSW5pdGlhbGl6aW5nLi4uJyk7XHJcblxyXG4gICAgICB0cnkge1xyXG4gICAgICAgIC8vIExvYWQgY2FjaGUgZmlyc3QgZm9yIGluc3RhbnQgZGF0YSBkaXNwbGF5XHJcbiAgICAgICAgaWYgKGF1dG9Mb2FkQ2FjaGUgJiYgIWNhY2hlLmlzTG9hZGVkKSB7XHJcbiAgICAgICAgICBhd2FpdCBsb2FkRnJvbUNhY2hlKCk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBJbml0aWFsaXplIFdlYlNvY2tldCBjb25uZWN0aW9uXHJcbiAgICAgICAgaWYgKGF1dG9Db25uZWN0ICYmIGNvbm5lY3Rpb24uc3RhdHVzID09PSAnZGlzY29ubmVjdGVkJykge1xyXG4gICAgICAgICAgYXdhaXQgaW5pdGlhbGl6ZUNvbm5lY3Rpb24oKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEVuaGFuY2VkIEhvb2s6IEluaXRpYWxpemF0aW9uIGZhaWxlZDonLCBlcnJvcik7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgaW5pdGlhbGl6ZSgpO1xyXG5cclxuICAgIC8vIENsZWFudXAgb24gdW5tb3VudFxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgaWYgKGF1dG9TYXZlSW50ZXJ2YWxSZWYuY3VycmVudCkge1xyXG4gICAgICAgIGNsZWFySW50ZXJ2YWwoYXV0b1NhdmVJbnRlcnZhbFJlZi5jdXJyZW50KTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuICB9LCBbYXV0b0Nvbm5lY3QsIGF1dG9Mb2FkQ2FjaGUsIGNhY2hlLmlzTG9hZGVkLCBjb25uZWN0aW9uLnN0YXR1cywgaW5pdGlhbGl6ZUNvbm5lY3Rpb24sIGxvYWRGcm9tQ2FjaGVdKTtcclxuXHJcbiAgLy8gQXV0by1zYXZlIGZ1bmN0aW9uYWxpdHlcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKGF1dG9TYXZlSW50ZXJ2YWwgPiAwICYmIG1hcmtldERhdGFBcnJheS5sZW5ndGggPiAwKSB7XHJcbiAgICAgIGlmIChhdXRvU2F2ZUludGVydmFsUmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBjbGVhckludGVydmFsKGF1dG9TYXZlSW50ZXJ2YWxSZWYuY3VycmVudCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGF1dG9TYXZlSW50ZXJ2YWxSZWYuY3VycmVudCA9IHNldEludGVydmFsKCgpID0+IHtcclxuICAgICAgICBzYXZlVG9DYWNoZSgpO1xyXG4gICAgICB9LCBhdXRvU2F2ZUludGVydmFsKTtcclxuXHJcbiAgICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgICAgaWYgKGF1dG9TYXZlSW50ZXJ2YWxSZWYuY3VycmVudCkge1xyXG4gICAgICAgICAgY2xlYXJJbnRlcnZhbChhdXRvU2F2ZUludGVydmFsUmVmLmN1cnJlbnQpO1xyXG4gICAgICAgIH1cclxuICAgICAgfTtcclxuICAgIH1cclxuICB9LCBbYXV0b1NhdmVJbnRlcnZhbCwgbWFya2V0RGF0YUFycmF5Lmxlbmd0aCwgc2F2ZVRvQ2FjaGVdKTtcclxuXHJcbiAgLy8gQXV0by1yZWNvbm5lY3Qgb24gZXJyb3JcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKFxyXG4gICAgICByZWNvbm5lY3RPbkVycm9yICYmXHJcbiAgICAgIGNvbm5lY3Rpb24uc3RhdHVzID09PSAnZXJyb3InICYmXHJcbiAgICAgIHJlY29ubmVjdEF0dGVtcHRzUmVmLmN1cnJlbnQgPCBtYXhSZWNvbm5lY3RBdHRlbXB0c1xyXG4gICAgKSB7XHJcbiAgICAgIGNvbnN0IHJldHJ5RGVsYXkgPSBNYXRoLm1pbigxMDAwICogTWF0aC5wb3coMiwgcmVjb25uZWN0QXR0ZW1wdHNSZWYuY3VycmVudCksIDMwMDAwKTtcclxuICAgICAgXHJcbiAgICAgIGNvbnNvbGUubG9nKFxyXG4gICAgICAgIGDwn5SEIEVuaGFuY2VkIEhvb2s6IEF1dG8tcmVjb25uZWN0aW5nIGluICR7cmV0cnlEZWxheX1tcyAoYXR0ZW1wdCAke3JlY29ubmVjdEF0dGVtcHRzUmVmLmN1cnJlbnQgKyAxfS8ke21heFJlY29ubmVjdEF0dGVtcHRzfSlgXHJcbiAgICAgICk7XHJcblxyXG4gICAgICBjb25zdCB0aW1lb3V0SWQgPSBzZXRUaW1lb3V0KGFzeW5jICgpID0+IHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgcmVjb25uZWN0QXR0ZW1wdHNSZWYuY3VycmVudCsrO1xyXG4gICAgICAgICAgYXdhaXQgcmVjb25uZWN0KCk7XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFbmhhbmNlZCBIb29rOiBBdXRvLXJlY29ubmVjdCBmYWlsZWQ6JywgZXJyb3IpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSwgcmV0cnlEZWxheSk7XHJcblxyXG4gICAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVvdXRJZCk7XHJcbiAgICB9XHJcbiAgfSwgW2Nvbm5lY3Rpb24uc3RhdHVzLCByZWNvbm5lY3QsIHJlY29ubmVjdE9uRXJyb3IsIG1heFJlY29ubmVjdEF0dGVtcHRzXSk7XHJcblxyXG4gIC8vIFJlc2V0IHJlY29ubmVjdCBhdHRlbXB0cyBvbiBzdWNjZXNzZnVsIGNvbm5lY3Rpb25cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKGNvbm5lY3Rpb24uc3RhdHVzID09PSAnY29ubmVjdGVkJykge1xyXG4gICAgICByZWNvbm5lY3RBdHRlbXB0c1JlZi5jdXJyZW50ID0gMDtcclxuICAgIH1cclxuICB9LCBbY29ubmVjdGlvbi5zdGF0dXNdKTtcclxuXHJcbiAgLy8gT3B0aW1pemVkIGRhdGEgYWNjZXNzIGZ1bmN0aW9uc1xyXG4gIGNvbnN0IGdldEZpbHRlcmVkRGF0YSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgIHJldHVybiBnZXRGaWx0ZXJlZE1hcmtldERhdGEoKTtcclxuICB9LCBbZ2V0RmlsdGVyZWRNYXJrZXREYXRhXSk7XHJcblxyXG4gIGNvbnN0IGdldFNvcnRlZERhdGEgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICByZXR1cm4gZ2V0U29ydGVkTWFya2V0RGF0YSgpO1xyXG4gIH0sIFtnZXRTb3J0ZWRNYXJrZXREYXRhXSk7XHJcblxyXG4gIGNvbnN0IGdldERhdGFCeVNlY3VyaXR5SWQgPSB1c2VDYWxsYmFjaygoc2VjdXJpdHlJZDogc3RyaW5nKSA9PiB7XHJcbiAgICByZXR1cm4gZ2V0TWFya2V0RGF0YUJ5U2VjdXJpdHlJZChzZWN1cml0eUlkKTtcclxuICB9LCBbZ2V0TWFya2V0RGF0YUJ5U2VjdXJpdHlJZF0pO1xyXG5cclxuICBjb25zdCBnZXREYXRhQnlTeW1ib2wgPSB1c2VDYWxsYmFjaygoc3ltYm9sOiBzdHJpbmcpID0+IHtcclxuICAgIHJldHVybiBnZXRNYXJrZXREYXRhQnlTeW1ib2woc3ltYm9sKTtcclxuICB9LCBbZ2V0TWFya2V0RGF0YUJ5U3ltYm9sXSk7XHJcblxyXG4gIC8vIEVuaGFuY2VkIGZpbHRlciBmdW5jdGlvbnNcclxuICBjb25zdCB1cGRhdGVGaWx0ZXJzID0gdXNlQ2FsbGJhY2soKGZpbHRlcnM6IFBhcnRpYWw8dHlwZW9mIHVpLmZpbHRlcnM+KSA9PiB7XHJcbiAgICBzZXRGaWx0ZXJzKGZpbHRlcnMpO1xyXG4gIH0sIFtzZXRGaWx0ZXJzLCB1aV0pO1xyXG5cclxuICBjb25zdCB1cGRhdGVTb3J0ID0gdXNlQ2FsbGJhY2soKGZpZWxkOiBzdHJpbmcsIGRpcmVjdGlvbj86ICdhc2MnIHwgJ2Rlc2MnKSA9PiB7XHJcbiAgICBzZXRTb3J0Q29uZmlnKGZpZWxkLCBkaXJlY3Rpb24pO1xyXG4gIH0sIFtzZXRTb3J0Q29uZmlnXSk7XHJcblxyXG4gIC8vIFN1YnNjcmlwdGlvbiBtYW5hZ2VtZW50XHJcbiAgY29uc3Qgc3Vic2NyaWJlID0gdXNlQ2FsbGJhY2soKHNlY3VyaXR5SWQ6IHN0cmluZykgPT4ge1xyXG4gICAgc3Vic2NyaWJlVG9JbnN0cnVtZW50KHNlY3VyaXR5SWQpO1xyXG4gIH0sIFtzdWJzY3JpYmVUb0luc3RydW1lbnRdKTtcclxuXHJcbiAgY29uc3QgdW5zdWJzY3JpYmUgPSB1c2VDYWxsYmFjaygoc2VjdXJpdHlJZDogc3RyaW5nKSA9PiB7XHJcbiAgICB1bnN1YnNjcmliZUZyb21JbnN0cnVtZW50KHNlY3VyaXR5SWQpO1xyXG4gIH0sIFt1bnN1YnNjcmliZUZyb21JbnN0cnVtZW50XSk7XHJcblxyXG4gIC8vIENvbm5lY3Rpb24gbWFuYWdlbWVudFxyXG4gIGNvbnN0IGZvcmNlUmVjb25uZWN0ID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgcmVjb25uZWN0QXR0ZW1wdHNSZWYuY3VycmVudCA9IDA7IC8vIFJlc2V0IGF0dGVtcHRzIGZvciBtYW51YWwgcmVjb25uZWN0XHJcbiAgICAgIGF3YWl0IHJlY29ubmVjdCgpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign4p2MIEVuaGFuY2VkIEhvb2s6IE1hbnVhbCByZWNvbm5lY3QgZmFpbGVkOicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfSwgW3JlY29ubmVjdF0pO1xyXG5cclxuICBjb25zdCBmb3JjZVJlZnJlc2ggPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBhd2FpdCBsb2FkRnJvbUNhY2hlKCk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRW5oYW5jZWQgSG9vazogRm9yY2UgcmVmcmVzaCBmYWlsZWQ6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9LCBbbG9hZEZyb21DYWNoZV0pO1xyXG5cclxuICBjb25zdCBmb3JjZVNhdmUgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBhd2FpdCBzYXZlVG9DYWNoZSh0cnVlKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFbmhhbmNlZCBIb29rOiBGb3JjZSBzYXZlIGZhaWxlZDonLCBlcnJvcik7XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG4gIH0sIFtzYXZlVG9DYWNoZV0pO1xyXG5cclxuICAvLyBTdGF0cyBhbmQgY29tcHV0ZWQgdmFsdWVzXHJcbiAgY29uc3Qgc3RhdHMgPSB7XHJcbiAgICB0b3RhbEluc3RydW1lbnRzOiBtYXJrZXREYXRhQXJyYXkubGVuZ3RoLFxyXG4gICAgY29ubmVjdGVkSW5zdHJ1bWVudHM6IG1hcmtldERhdGFBcnJheS5maWx0ZXIoaXRlbSA9PiBpdGVtLmx0cCAmJiBpdGVtLmx0cCA+IDApLmxlbmd0aCxcclxuICAgIGxhc3RVcGRhdGU6IG51bGwsIC8vIFRlbXBvcmFyeSBtb2NrXHJcbiAgICBjYWNoZVNpemU6IGNhY2hlLnRvdGFsQ2FjaGVTaXplLFxyXG4gICAgY29ubmVjdGlvblVwdGltZTogY29ubmVjdGlvbi5jb25uZWN0aW9uU3RhdHMuY29ubmVjdGlvblVwdGltZSxcclxuICAgIG1lc3NhZ2VzUmVjZWl2ZWQ6IGNvbm5lY3Rpb24uY29ubmVjdGlvblN0YXRzLnRvdGFsTWVzc2FnZXMsXHJcbiAgICByZWNvbm5lY3RBdHRlbXB0czogcmVjb25uZWN0QXR0ZW1wdHNSZWYuY3VycmVudCxcclxuICAgIGlzQXV0b1NhdmluZzogYXV0b1NhdmVJbnRlcnZhbFJlZi5jdXJyZW50ICE9PSBudWxsLFxyXG4gIH07XHJcblxyXG4gIHJldHVybiB7XHJcbiAgICAvLyBEYXRhXHJcbiAgICBtYXJrZXREYXRhOiBtYXJrZXREYXRhQXJyYXksXHJcbiAgICBtYXJrZXREYXRhTWFwLFxyXG4gICAgZmlsdGVyZWREYXRhOiBnZXRGaWx0ZXJlZERhdGEoKSxcclxuICAgIHNvcnRlZERhdGE6IGdldFNvcnRlZERhdGEoKSxcclxuXHJcbiAgICAvLyBDb25uZWN0aW9uIHN0YXRlXHJcbiAgICBpc0Nvbm5lY3RlZDogY29ubmVjdGlvbi5pc0Nvbm5lY3RlZCxcclxuICAgIGNvbm5lY3Rpb25TdGF0dXM6IGNvbm5lY3Rpb24uc3RhdHVzLFxyXG4gICAgY29ubmVjdGlvbkVycm9yOiBjb25uZWN0aW9uLmVycm9yLFxyXG4gICAgY29ubmVjdGlvblN0YXRzOiBjb25uZWN0aW9uLmNvbm5lY3Rpb25TdGF0cyxcclxuXHJcbiAgICAvLyBDYWNoZSBzdGF0ZVxyXG4gICAgY2FjaGVMb2FkZWQ6IGNhY2hlLmlzTG9hZGVkLFxyXG4gICAgY2FjaGVVcGRhdGluZzogY2FjaGUucGVuZGluZ1VwZGF0ZXMgPiAwLFxyXG4gICAgbGFzdENhY2hlVXBkYXRlOiBjYWNoZS5sYXN0Q2FjaGVVcGRhdGUsXHJcblxyXG4gICAgLy8gVUkgc3RhdGVcclxuICAgIGZpbHRlcnM6IHVpLmZpbHRlcnMsXHJcbiAgICBzb3J0Q29uZmlnOiB1aS5zb3J0Q29uZmlnLFxyXG4gICAgc2VsZWN0ZWRJbnN0cnVtZW50czogdWkuc2VsZWN0ZWRJbnN0cnVtZW50cyxcclxuICAgIHZpZXdNb2RlOiB1aS52aWV3TW9kZSxcclxuICAgIGF1dG9SZWZyZXNoOiB1aS5hdXRvUmVmcmVzaCxcclxuXHJcbiAgICAvLyBMb2FkaW5nIHN0YXRlc1xyXG4gICAgaXNMb2FkaW5nLFxyXG4gICAgaXNJbml0aWFsaXppbmc6ICFpbml0aWFsaXphdGlvblJlZi5jdXJyZW50LFxyXG5cclxuICAgIC8vIERhdGEgYWNjZXNzIGZ1bmN0aW9uc1xyXG4gICAgZ2V0RGF0YUJ5U2VjdXJpdHlJZCxcclxuICAgIGdldERhdGFCeVN5bWJvbCxcclxuICAgIGdldEZpbHRlcmVkRGF0YSxcclxuICAgIGdldFNvcnRlZERhdGEsXHJcblxyXG4gICAgLy8gQWN0aW9uc1xyXG4gICAgdXBkYXRlRmlsdGVycyxcclxuICAgIHVwZGF0ZVNvcnQsXHJcbiAgICBzdWJzY3JpYmUsXHJcbiAgICB1bnN1YnNjcmliZSxcclxuXHJcbiAgICAvLyBDb25uZWN0aW9uIG1hbmFnZW1lbnRcclxuICAgIGNvbm5lY3QsXHJcbiAgICBkaXNjb25uZWN0LFxyXG4gICAgcmVjb25uZWN0OiBmb3JjZVJlY29ubmVjdCxcclxuXHJcbiAgICAvLyBDYWNoZSBtYW5hZ2VtZW50XHJcbiAgICByZWZyZXNoOiBmb3JjZVJlZnJlc2gsXHJcbiAgICBzYXZlOiBmb3JjZVNhdmUsXHJcbiAgICBjbGVhckNhY2hlLFxyXG5cclxuICAgIC8vIFV0aWxpdHlcclxuICAgIHJlc2V0LFxyXG4gICAgc3RhdHMsXHJcblxyXG4gICAgLy8gQWR2YW5jZWQgYWN0aW9ucyAoZXhwb3NlIGlmIG5lZWRlZClcclxuICAgIF9zdG9yZToge1xyXG4gICAgICBzZXRGaWx0ZXJzLFxyXG4gICAgICBzZXRTb3J0Q29uZmlnLFxyXG4gICAgICBzdWJzY3JpYmVUb0luc3RydW1lbnQsXHJcbiAgICAgIHVuc3Vic2NyaWJlRnJvbUluc3RydW1lbnQsXHJcbiAgICAgIC8vIEFkZCBtb3JlIGlmIG5lZWRlZFxyXG4gICAgfSxcclxuICB9O1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgdXNlRW5oYW5jZWRNYXJrZXREYXRhO1xyXG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlQ2FsbGJhY2siLCJ1c2VSZWYiLCJ1c2VNZW1vIiwidXNlRW5oYW5jZWRNYXJrZXREYXRhIiwib3B0aW9ucyIsImF1dG9Db25uZWN0IiwiYXV0b0xvYWRDYWNoZSIsImF1dG9TYXZlSW50ZXJ2YWwiLCJyZWNvbm5lY3RPbkVycm9yIiwibWF4UmVjb25uZWN0QXR0ZW1wdHMiLCJjb25uZWN0aW9uIiwic3RhdHVzIiwiaXNDb25uZWN0ZWQiLCJlcnJvciIsImNvbm5lY3Rpb25TdGF0cyIsInRvdGFsTWVzc2FnZXMiLCJjb25uZWN0aW9uVXB0aW1lIiwibWFya2V0RGF0YU1hcCIsIk1hcCIsImNhY2hlIiwiaXNMb2FkZWQiLCJ0b3RhbENhY2hlU2l6ZSIsInBlbmRpbmdVcGRhdGVzIiwibGFzdENhY2hlVXBkYXRlIiwidWkiLCJmaWx0ZXJzIiwic29ydENvbmZpZyIsImZpZWxkIiwiZGlyZWN0aW9uIiwic2VsZWN0ZWRJbnN0cnVtZW50cyIsIlNldCIsInZpZXdNb2RlIiwiYXV0b1JlZnJlc2giLCJpc0xvYWRpbmciLCJpbml0aWFsaXplQ29ubmVjdGlvbiIsImNvbm5lY3QiLCJkaXNjb25uZWN0IiwicmVjb25uZWN0IiwibG9hZEZyb21DYWNoZSIsInNhdmVUb0NhY2hlIiwiZm9yY2UiLCJjbGVhckNhY2hlIiwic2V0RmlsdGVycyIsInNldFNvcnRDb25maWciLCJnZXRGaWx0ZXJlZE1hcmtldERhdGEiLCJnZXRTb3J0ZWRNYXJrZXREYXRhIiwiZ2V0TWFya2V0RGF0YUJ5U2VjdXJpdHlJZCIsInNlY3VyaXR5SWQiLCJ1bmRlZmluZWQiLCJnZXRNYXJrZXREYXRhQnlTeW1ib2wiLCJzeW1ib2wiLCJzdWJzY3JpYmVUb0luc3RydW1lbnQiLCJ1bnN1YnNjcmliZUZyb21JbnN0cnVtZW50IiwicmVzZXQiLCJhdXRvU2F2ZUludGVydmFsUmVmIiwicmVjb25uZWN0QXR0ZW1wdHNSZWYiLCJpbml0aWFsaXphdGlvblJlZiIsIm1hcmtldERhdGFBcnJheSIsIkFycmF5IiwiZnJvbSIsInZhbHVlcyIsImluaXRpYWxpemUiLCJjdXJyZW50IiwiY29uc29sZSIsImxvZyIsImNsZWFySW50ZXJ2YWwiLCJsZW5ndGgiLCJzZXRJbnRlcnZhbCIsInJldHJ5RGVsYXkiLCJNYXRoIiwibWluIiwicG93IiwidGltZW91dElkIiwic2V0VGltZW91dCIsImNsZWFyVGltZW91dCIsImdldEZpbHRlcmVkRGF0YSIsImdldFNvcnRlZERhdGEiLCJnZXREYXRhQnlTZWN1cml0eUlkIiwiZ2V0RGF0YUJ5U3ltYm9sIiwidXBkYXRlRmlsdGVycyIsInVwZGF0ZVNvcnQiLCJzdWJzY3JpYmUiLCJ1bnN1YnNjcmliZSIsImZvcmNlUmVjb25uZWN0IiwiZm9yY2VSZWZyZXNoIiwiZm9yY2VTYXZlIiwic3RhdHMiLCJ0b3RhbEluc3RydW1lbnRzIiwiY29ubmVjdGVkSW5zdHJ1bWVudHMiLCJmaWx0ZXIiLCJpdGVtIiwibHRwIiwibGFzdFVwZGF0ZSIsImNhY2hlU2l6ZSIsIm1lc3NhZ2VzUmVjZWl2ZWQiLCJyZWNvbm5lY3RBdHRlbXB0cyIsImlzQXV0b1NhdmluZyIsIm1hcmtldERhdGEiLCJmaWx0ZXJlZERhdGEiLCJzb3J0ZWREYXRhIiwiY29ubmVjdGlvblN0YXR1cyIsImNvbm5lY3Rpb25FcnJvciIsImNhY2hlTG9hZGVkIiwiY2FjaGVVcGRhdGluZyIsImlzSW5pdGlhbGl6aW5nIiwicmVmcmVzaCIsInNhdmUiLCJfc3RvcmUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useEnhancedMarketData.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/constants.ts":
/*!******************************!*\
  !*** ./src/lib/constants.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: () => (/* binding */ API_CONFIG),\n/* harmony export */   CACHE_CONFIG: () => (/* binding */ CACHE_CONFIG),\n/* harmony export */   CHART_CONFIG: () => (/* binding */ CHART_CONFIG),\n/* harmony export */   COLORS: () => (/* binding */ COLORS),\n/* harmony export */   DEFAULTS: () => (/* binding */ DEFAULTS),\n/* harmony export */   DHAN_CONFIG: () => (/* binding */ DHAN_CONFIG),\n/* harmony export */   ENV_CONFIG: () => (/* binding */ ENV_CONFIG),\n/* harmony export */   ERROR_MESSAGES: () => (/* binding */ ERROR_MESSAGES),\n/* harmony export */   EXCHANGE_SEGMENTS: () => (/* binding */ EXCHANGE_SEGMENTS),\n/* harmony export */   FEATURE_FLAGS: () => (/* binding */ FEATURE_FLAGS),\n/* harmony export */   INSTRUMENT_TYPES: () => (/* binding */ INSTRUMENT_TYPES),\n/* harmony export */   LOGGING_CONFIG: () => (/* binding */ LOGGING_CONFIG),\n/* harmony export */   MARKET_CONFIG: () => (/* binding */ MARKET_CONFIG),\n/* harmony export */   OPTION_TYPES: () => (/* binding */ OPTION_TYPES),\n/* harmony export */   PACKET_TYPES: () => (/* binding */ PACKET_TYPES),\n/* harmony export */   PERFORMANCE_CONFIG: () => (/* binding */ PERFORMANCE_CONFIG),\n/* harmony export */   REQUEST_CODES: () => (/* binding */ REQUEST_CODES),\n/* harmony export */   SECURITY_CONFIG: () => (/* binding */ SECURITY_CONFIG),\n/* harmony export */   STORAGE_KEYS: () => (/* binding */ STORAGE_KEYS),\n/* harmony export */   SUCCESS_MESSAGES: () => (/* binding */ SUCCESS_MESSAGES),\n/* harmony export */   UI_CONFIG: () => (/* binding */ UI_CONFIG),\n/* harmony export */   VALIDATION: () => (/* binding */ VALIDATION),\n/* harmony export */   WEBSOCKET_CONFIG: () => (/* binding */ WEBSOCKET_CONFIG)\n/* harmony export */ });\n/**\r\n * Application Constants\r\n */ // API Configuration\nconst API_CONFIG = {\n    BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080',\n    TIMEOUT: 30000,\n    RETRY_ATTEMPTS: 3,\n    RETRY_DELAY: 1000\n};\n// WebSocket Configuration\nconst WEBSOCKET_CONFIG = {\n    RECONNECT_INTERVAL: 3000,\n    MAX_RECONNECT_ATTEMPTS: 15,\n    PING_INTERVAL: 25000,\n    PONG_TIMEOUT: 10000,\n    CONNECTION_TIMEOUT: 20000,\n    HEARTBEAT_INTERVAL: 30000,\n    MAX_LISTENERS_PER_EVENT: 10,\n    CLEANUP_INTERVAL: 60000\n};\n// Dhan API Configuration\nconst DHAN_CONFIG = {\n    BASE_URL: 'https://api.dhan.co',\n    WEBSOCKET_URL: 'wss://api.dhan.co/v2/wsapi',\n    MAX_INSTRUMENTS_PER_CONNECTION: 5000,\n    MAX_INSTRUMENTS_PER_MESSAGE: 100,\n    RATE_LIMIT: {\n        REQUESTS_PER_SECOND: 10,\n        REQUESTS_PER_MINUTE: 600\n    }\n};\n// Market Configuration\nconst MARKET_CONFIG = {\n    TRADING_HOURS: {\n        START: {\n            hour: 9,\n            minute: 15\n        },\n        END: {\n            hour: 15,\n            minute: 30\n        }\n    },\n    TRADING_DAYS: [\n        1,\n        2,\n        3,\n        4,\n        5\n    ],\n    REFRESH_INTERVAL: 1000,\n    BATCH_SIZE: 100\n};\n// Instrument Types\nconst INSTRUMENT_TYPES = {\n    EQUITY: 'EQUITY',\n    INDEX: 'INDEX',\n    FUTIDX: 'FUTIDX',\n    OPTIDX: 'OPTIDX',\n    FUTSTK: 'FUTSTK',\n    OPTSTK: 'OPTSTK',\n    FUTCUR: 'FUTCUR',\n    OPTCUR: 'OPTCUR',\n    FUTCOM: 'FUTCOM',\n    OPTFUT: 'OPTFUT'\n};\n// Exchange Segments\nconst EXCHANGE_SEGMENTS = {\n    NSE_EQ: 'NSE_EQ',\n    NSE_FNO: 'NSE_FNO',\n    BSE_EQ: 'BSE_EQ',\n    MCX_COMM: 'MCX_COMM',\n    IDX_I: 'IDX_I'\n};\n// Option Types\nconst OPTION_TYPES = {\n    CALL: 'CE',\n    PUT: 'PE'\n};\n// Market Data Packet Types\nconst PACKET_TYPES = {\n    TOUCHLINE: 1,\n    QUOTE: 2,\n    SNAP_QUOTE: 3,\n    FULL_PACKET: 4,\n    OI: 5\n};\n// WebSocket Request Codes\nconst REQUEST_CODES = {\n    SUBSCRIBE: 21,\n    UNSUBSCRIBE: 22,\n    SNAP_QUOTE: 23\n};\n// UI Configuration\nconst UI_CONFIG = {\n    DEBOUNCE_DELAY: 300,\n    THROTTLE_DELAY: 100,\n    ANIMATION_DURATION: 200,\n    TOAST_DURATION: 3000,\n    PAGINATION: {\n        DEFAULT_PAGE_SIZE: 50,\n        PAGE_SIZE_OPTIONS: [\n            25,\n            50,\n            100,\n            200\n        ]\n    }\n};\n// Color Schemes\nconst COLORS = {\n    SUCCESS: '#10B981',\n    ERROR: '#EF4444',\n    WARNING: '#F59E0B',\n    INFO: '#3B82F6',\n    NEUTRAL: '#6B7280',\n    BID: '#10B981',\n    ASK: '#EF4444',\n    SPOT: '#3B82F6'\n};\n// Chart Configuration\nconst CHART_CONFIG = {\n    DEFAULT_HEIGHT: 400,\n    COLORS: {\n        PRIMARY: '#3B82F6',\n        SECONDARY: '#10B981',\n        ACCENT: '#F59E0B',\n        GRID: '#E5E7EB',\n        TEXT: '#374151'\n    },\n    ANIMATION: {\n        DURATION: 300,\n        EASING: 'ease-in-out'\n    }\n};\n// Data Validation\nconst VALIDATION = {\n    MIN_STRIKE_PRICE: 1,\n    MAX_STRIKE_PRICE: 100000,\n    MIN_OPTION_PRICE: 0.01,\n    MAX_OPTION_PRICE: 50000,\n    MIN_VOLUME: 0,\n    MAX_VOLUME: 10000000\n};\n// Error Messages\nconst ERROR_MESSAGES = {\n    NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',\n    API_ERROR: 'API request failed. Please try again later.',\n    WEBSOCKET_ERROR: 'WebSocket connection failed. Attempting to reconnect...',\n    DATA_PARSING_ERROR: 'Failed to parse market data. Please refresh the page.',\n    SUBSCRIPTION_ERROR: 'Failed to subscribe to market data. Please try again.',\n    INVALID_INSTRUMENT: 'Invalid instrument selected.',\n    MARKET_CLOSED: 'Market is currently closed.',\n    RATE_LIMIT_EXCEEDED: 'Too many requests. Please wait before trying again.'\n};\n// Success Messages\nconst SUCCESS_MESSAGES = {\n    CONNECTION_ESTABLISHED: 'Successfully connected to market data feed.',\n    SUBSCRIPTION_SUCCESS: 'Successfully subscribed to market data.',\n    DATA_UPDATED: 'Market data updated successfully.',\n    SETTINGS_SAVED: 'Settings saved successfully.'\n};\n// Local Storage Keys\nconst STORAGE_KEYS = {\n    USER_PREFERENCES: 'csv_market_dashboard_preferences',\n    SELECTED_INSTRUMENTS: 'csv_market_dashboard_selected_instruments',\n    THEME: 'csv_market_dashboard_theme',\n    LAYOUT: 'csv_market_dashboard_layout'\n};\n// Feature Flags\nconst FEATURE_FLAGS = {\n    ENABLE_CHARTS: true,\n    ENABLE_ALERTS: true,\n    ENABLE_EXPORT: true,\n    ENABLE_DARK_MODE: true,\n    ENABLE_REAL_TIME_UPDATES: true,\n    ENABLE_OPTION_GREEKS: false\n};\n// Performance Monitoring\nconst PERFORMANCE_CONFIG = {\n    MEMORY_WARNING_THRESHOLD: 100 * 1024 * 1024,\n    CPU_WARNING_THRESHOLD: 80,\n    NETWORK_TIMEOUT_WARNING: 5000,\n    MAX_CONCURRENT_REQUESTS: 10\n};\n// Logging Configuration\nconst LOGGING_CONFIG = {\n    LEVELS: {\n        ERROR: 0,\n        WARN: 1,\n        INFO: 2,\n        DEBUG: 3\n    },\n    MAX_LOG_SIZE: 10 * 1024 * 1024,\n    MAX_LOG_FILES: 5,\n    LOG_ROTATION_INTERVAL: 24 * 60 * 60 * 1000\n};\n// Cache Configuration\nconst CACHE_CONFIG = {\n    DEFAULT_TTL: 5 * 60 * 1000,\n    MARKET_DATA_TTL: 10 * 60 * 1000,\n    STATIC_DATA_TTL: 30 * 60 * 1000,\n    EXPIRY_CHECK_INTERVAL: 60 * 1000,\n    MAX_CACHE_SIZE: 50 * 1024 * 1024,\n    KEYS: {\n        INSTRUMENTS: 'instruments',\n        MARKET_DATA: 'market_data',\n        OPTION_CHAIN: 'option_chain',\n        EXPIRY_DATES: 'expiry_dates',\n        NIFTY_SPOT: 'nifty_spot',\n        USER_SETTINGS: 'user_settings',\n        USER_PREFERENCES: 'user_preferences'\n    }\n};\n// Security Configuration\nconst SECURITY_CONFIG = {\n    RATE_LIMITING: {\n        WINDOW_MS: 15 * 60 * 1000,\n        MAX_REQUESTS: 1000\n    },\n    CORS: {\n        ORIGIN:  false ? 0 : [\n            'http://localhost:3000',\n            'http://localhost:3001'\n        ],\n        CREDENTIALS: true\n    },\n    HEADERS: {\n        CONTENT_SECURITY_POLICY: \"default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';\",\n        X_FRAME_OPTIONS: 'DENY',\n        X_CONTENT_TYPE_OPTIONS: 'nosniff'\n    }\n};\n// Environment Configuration\nconst ENV_CONFIG = {\n    NODE_ENV: \"development\" || 0,\n    PORT: parseInt(process.env.PORT || '8080'),\n    NEXT_PORT: parseInt(process.env.NEXT_PORT || '3000'),\n    LOG_LEVEL: process.env.LOG_LEVEL || 'info',\n    ENABLE_METRICS: process.env.ENABLE_METRICS === 'true'\n};\n// Default Values\nconst DEFAULTS = {\n    NIFTY_SPOT_PRICE: 24850,\n    SELECTED_EXPIRY: '2025-06-19',\n    STRIKES_TO_SHOW: 20,\n    REFRESH_INTERVAL: 1000,\n    CHART_TIMEFRAME: '1D',\n    TABLE_PAGE_SIZE: 50\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/constants.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/data-cache.ts":
/*!*******************************!*\
  !*** ./src/lib/data-cache.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MARKET_DATA_CACHE_KEYS: () => (/* binding */ MARKET_DATA_CACHE_KEYS),\n/* harmony export */   cacheHelpers: () => (/* binding */ cacheHelpers),\n/* harmony export */   dataCache: () => (/* binding */ dataCache)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(ssr)/./src/lib/constants.ts\");\n/**\r\n * Data Caching System for Market Data Persistence\r\n * Handles localStorage/sessionStorage with TTL and compression\r\n */ \nclass DataCache {\n    static{\n        this.instance = null;\n    }\n    /**\r\n   * Check if we're in a browser environment\r\n   */ isBrowser() {\n        return  false && 0;\n    }\n    static getInstance() {\n        if (!DataCache.instance) {\n            DataCache.instance = new DataCache();\n        }\n        return DataCache.instance;\n    }\n    /**\r\n   * Initialize cache for client-side usage\r\n   * Call this method when the component mounts on the client side\r\n   */ initializeClientSide() {\n        if (this.isBrowser()) {\n            console.log('🚀 DataCache: Initializing client-side cache');\n            // Perform any client-side specific initialization here\n            this.cleanupExpired() // Run initial cleanup\n            ;\n        }\n    }\n    constructor(){\n        this.version = '1.0.0';\n        this.compressionSupported = false;\n        this.checkCompressionSupport();\n        this.startCleanupInterval();\n    }\n    /**\r\n   * Check if compression is supported\r\n   */ checkCompressionSupport() {\n        try {\n            // Check if CompressionStream is available (modern browsers)\n            this.compressionSupported = typeof CompressionStream !== 'undefined';\n        } catch  {\n            this.compressionSupported = false;\n        }\n    }\n    /**\r\n   * Set data in cache with TTL\r\n   */ async set(key, data, options = {}) {\n        try {\n            // Skip caching on server side\n            if (!this.isBrowser()) {\n                console.log(`⚠️ DataCache: Skipping cache on server side for ${key}`);\n                return false;\n            }\n            const { ttl = _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.DEFAULT_TTL, useCompression = false, storage = 'localStorage' } = options;\n            const entry = {\n                data,\n                timestamp: Date.now(),\n                ttl,\n                version: this.version\n            };\n            let serializedData = JSON.stringify(entry);\n            // Apply compression if requested and supported\n            if (useCompression && this.compressionSupported) {\n                serializedData = await this.compress(serializedData);\n            }\n            const storageObj = storage === 'localStorage' ? localStorage : sessionStorage;\n            const fullKey = this.getFullKey(key);\n            storageObj.setItem(fullKey, serializedData);\n            console.log(`💾 DataCache: Cached ${key} (${serializedData.length} bytes)`);\n            return true;\n        } catch (error) {\n            console.error(`❌ DataCache: Failed to cache ${key}:`, error);\n            return false;\n        }\n    }\n    /**\r\n   * Get data from cache\r\n   */ async get(key, options = {}) {\n        try {\n            // Return null on server side\n            if (!this.isBrowser()) {\n                return null;\n            }\n            const { storage = 'localStorage' } = options;\n            const storageObj = storage === 'localStorage' ? localStorage : sessionStorage;\n            const fullKey = this.getFullKey(key);\n            const serializedData = storageObj.getItem(fullKey);\n            if (!serializedData) {\n                return null;\n            }\n            let decompressedData = serializedData;\n            // Try to decompress if it looks compressed\n            if (this.compressionSupported && this.isCompressed(serializedData)) {\n                decompressedData = await this.decompress(serializedData);\n            }\n            const entry = JSON.parse(decompressedData);\n            // Check version compatibility\n            if (entry.version !== this.version) {\n                console.warn(`⚠️ DataCache: Version mismatch for ${key}, removing`);\n                this.remove(key, options);\n                return null;\n            }\n            // Check TTL\n            if (Date.now() - entry.timestamp > entry.ttl) {\n                console.log(`⏰ DataCache: ${key} expired, removing`);\n                this.remove(key, options);\n                return null;\n            }\n            console.log(`📖 DataCache: Retrieved ${key} from cache`);\n            return entry.data;\n        } catch (error) {\n            console.error(`❌ DataCache: Failed to retrieve ${key}:`, error);\n            // Remove corrupted entry\n            this.remove(key, options);\n            return null;\n        }\n    }\n    /**\r\n   * Remove data from cache\r\n   */ remove(key, options = {}) {\n        try {\n            // Skip on server side\n            if (!this.isBrowser()) {\n                return;\n            }\n            const { storage = 'localStorage' } = options;\n            const storageObj = storage === 'localStorage' ? localStorage : sessionStorage;\n            const fullKey = this.getFullKey(key);\n            storageObj.removeItem(fullKey);\n            console.log(`🗑️ DataCache: Removed ${key}`);\n        } catch (error) {\n            console.error(`❌ DataCache: Failed to remove ${key}:`, error);\n        }\n    }\n    /**\r\n   * Clear all cache entries\r\n   */ clear(storage = 'localStorage') {\n        try {\n            // Skip on server side\n            if (!this.isBrowser()) {\n                return;\n            }\n            const storageObj = storage === 'localStorage' ? localStorage : sessionStorage;\n            const keysToRemove = [];\n            // Find all our cache keys\n            for(let i = 0; i < storageObj.length; i++){\n                const key = storageObj.key(i);\n                if (key && key.startsWith('csv_market_dashboard_cache_')) {\n                    keysToRemove.push(key);\n                }\n            }\n            // Remove them\n            keysToRemove.forEach((key)=>storageObj.removeItem(key));\n            console.log(`🧹 DataCache: Cleared ${keysToRemove.length} entries from ${storage}`);\n        } catch (error) {\n            console.error(`❌ DataCache: Failed to clear ${storage}:`, error);\n        }\n    }\n    /**\r\n   * Get cache statistics\r\n   */ getStats(storage = 'localStorage') {\n        // Return empty stats on server side\n        if (!this.isBrowser()) {\n            return {\n                totalEntries: 0,\n                totalSize: 0,\n                oldestEntry: null,\n                newestEntry: null\n            };\n        }\n        const storageObj = storage === 'localStorage' ? localStorage : sessionStorage;\n        let totalEntries = 0;\n        let totalSize = 0;\n        let oldestTimestamp = Infinity;\n        let newestTimestamp = 0;\n        try {\n            for(let i = 0; i < storageObj.length; i++){\n                const key = storageObj.key(i);\n                if (key && key.startsWith('csv_market_dashboard_cache_')) {\n                    const value = storageObj.getItem(key);\n                    if (value) {\n                        totalEntries++;\n                        totalSize += value.length;\n                        try {\n                            const entry = JSON.parse(value);\n                            if (entry.timestamp) {\n                                oldestTimestamp = Math.min(oldestTimestamp, entry.timestamp);\n                                newestTimestamp = Math.max(newestTimestamp, entry.timestamp);\n                            }\n                        } catch  {\n                        // Ignore parsing errors for stats\n                        }\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('❌ DataCache: Failed to get stats:', error);\n        }\n        return {\n            totalEntries,\n            totalSize,\n            oldestEntry: oldestTimestamp === Infinity ? null : new Date(oldestTimestamp),\n            newestEntry: newestTimestamp === 0 ? null : new Date(newestTimestamp)\n        };\n    }\n    /**\r\n   * Start cleanup interval to remove expired entries\r\n   */ startCleanupInterval() {\n        // Only start cleanup interval on client side\n        if (this.isBrowser()) {\n            setInterval(()=>{\n                this.cleanupExpired();\n            }, _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.EXPIRY_CHECK_INTERVAL);\n        }\n    }\n    /**\r\n   * Clean up expired entries\r\n   */ cleanupExpired() {\n        try {\n            // Skip cleanup on server side\n            if (!this.isBrowser()) {\n                return;\n            }\n            const storages = [\n                'localStorage',\n                'sessionStorage'\n            ];\n            storages.forEach((storageType)=>{\n                const storageObj = storageType === 'localStorage' ? localStorage : sessionStorage;\n                const keysToRemove = [];\n                for(let i = 0; i < storageObj.length; i++){\n                    const key = storageObj.key(i);\n                    if (key && key.startsWith('csv_market_dashboard_cache_')) {\n                        try {\n                            const value = storageObj.getItem(key);\n                            if (value) {\n                                const entry = JSON.parse(value);\n                                if (Date.now() - entry.timestamp > entry.ttl) {\n                                    keysToRemove.push(key);\n                                }\n                            }\n                        } catch  {\n                            // Remove corrupted entries\n                            keysToRemove.push(key);\n                        }\n                    }\n                }\n                keysToRemove.forEach((key)=>storageObj.removeItem(key));\n                if (keysToRemove.length > 0) {\n                    console.log(`🧹 DataCache: Cleaned up ${keysToRemove.length} expired entries from ${storageType}`);\n                }\n            });\n        } catch (error) {\n            console.error('❌ DataCache: Cleanup failed:', error);\n        }\n    }\n    /**\r\n   * Generate full cache key\r\n   */ getFullKey(key) {\n        return `csv_market_dashboard_cache_${key}`;\n    }\n    /**\r\n   * Check if data is compressed\r\n   */ isCompressed(data) {\n        // Simple heuristic: compressed data usually starts with specific bytes\n        return data.startsWith('H4sI') || data.startsWith('eJy') // Common compression signatures\n        ;\n    }\n    /**\r\n   * Compress data (placeholder for future implementation)\r\n   */ async compress(data) {\n        // For now, return as-is. Can implement actual compression later\n        return data;\n    }\n    /**\r\n   * Decompress data (placeholder for future implementation)\r\n   */ async decompress(data) {\n        // For now, return as-is. Can implement actual decompression later\n        return data;\n    }\n}\n// Export singleton instance\nconst dataCache = DataCache.getInstance();\n// Export cache keys for market data\nconst MARKET_DATA_CACHE_KEYS = _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.KEYS;\n// Export helper functions\nconst cacheHelpers = {\n    /**\r\n   * Cache market data with medium TTL for page refresh persistence\r\n   */ cacheMarketData: async (key, data)=>{\n        return dataCache.set(key, data, {\n            ttl: _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.MARKET_DATA_TTL,\n            storage: 'localStorage' // Use localStorage for page refresh persistence\n        });\n    },\n    /**\r\n   * Cache static data with long TTL\r\n   */ cacheStaticData: async (key, data)=>{\n        return dataCache.set(key, data, {\n            ttl: _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.STATIC_DATA_TTL,\n            storage: 'localStorage' // Use local storage for persistent data\n        });\n    },\n    /**\r\n   * Get cached market data\r\n   */ getCachedMarketData: async (key)=>{\n        return dataCache.get(key, {\n            storage: 'localStorage'\n        });\n    },\n    /**\r\n   * Get cached static data\r\n   */ getCachedStaticData: async (key)=>{\n        return dataCache.get(key, {\n            storage: 'localStorage'\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2RhdGEtY2FjaGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBOzs7Q0FHQyxHQUV5QztBQWUxQyxNQUFNQzs7YUFDV0MsV0FBNkI7O0lBSTVDOztHQUVDLEdBQ0QsWUFBNkI7UUFDM0IsT0FBTyxNQUE2QixJQUFJLENBQTBDO0lBQ3BGO0lBRUEsT0FBT0ksY0FBeUI7UUFDOUIsSUFBSSxDQUFDTCxVQUFVQyxRQUFRLEVBQUU7WUFDdkJELFVBQVVDLFFBQVEsR0FBRyxJQUFJRDtRQUMzQjtRQUNBLE9BQU9BLFVBQVVDLFFBQVE7SUFDM0I7SUFFQTs7O0dBR0MsR0FDREssdUJBQTZCO1FBQzNCLElBQUksSUFBSSxDQUFDSixTQUFTLElBQUk7WUFDcEJLLFFBQVFDLEdBQUcsQ0FBQztZQUNaLHVEQUF1RDtZQUN2RCxJQUFJLENBQUNDLGNBQWMsR0FBRyxzQkFBc0I7O1FBQzlDO0lBQ0Y7SUFFQSxhQUFzQjthQTdCTEMsVUFBVTthQUNuQkMsdUJBQXVCO1FBNkI3QixJQUFJLENBQUNDLHVCQUF1QjtRQUM1QixJQUFJLENBQUNDLG9CQUFvQjtJQUMzQjtJQUVBOztHQUVDLEdBQ0QsMEJBQXdDO1FBQ3RDLElBQUk7WUFDRiw0REFBNEQ7WUFDNUQsSUFBSSxDQUFDRixvQkFBb0IsR0FBRyxPQUFPRyxzQkFBc0I7UUFDM0QsRUFBRSxPQUFNO1lBQ04sSUFBSSxDQUFDSCxvQkFBb0IsR0FBRztRQUM5QjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNSSxJQUNKQyxHQUFXLEVBQ1hDLElBQU8sRUFDUEMsVUFBd0IsQ0FBQyxDQUFDLEVBQ1I7UUFDbEIsSUFBSTtZQUNGLDhCQUE4QjtZQUM5QixJQUFJLENBQUMsSUFBSSxDQUFDaEIsU0FBUyxJQUFJO2dCQUNyQkssUUFBUUMsR0FBRyxDQUFDLENBQUMsZ0RBQWdELEVBQUVRLEtBQUs7Z0JBQ3BFLE9BQU87WUFDVDtZQUVBLE1BQU0sRUFDSkcsTUFBTXBCLG9EQUFZQSxDQUFDcUIsV0FBVyxFQUM5QkMsaUJBQWlCLEtBQUssRUFDdEJDLFVBQVUsY0FBYyxFQUN6QixHQUFHSjtZQUVKLE1BQU1LLFFBQXVCO2dCQUMzQk47Z0JBQ0FPLFdBQVdDLEtBQUtDLEdBQUc7Z0JBQ25CUDtnQkFDQVQsU0FBUyxJQUFJLENBQUNBLE9BQU87WUFDdkI7WUFFQSxJQUFJaUIsaUJBQWlCQyxLQUFLQyxTQUFTLENBQUNOO1lBRXBDLCtDQUErQztZQUMvQyxJQUFJRixrQkFBa0IsSUFBSSxDQUFDVixvQkFBb0IsRUFBRTtnQkFDL0NnQixpQkFBaUIsTUFBTSxJQUFJLENBQUNHLFFBQVEsQ0FBQ0g7WUFDdkM7WUFFQSxNQUFNSSxhQUFhVCxZQUFZLGlCQUFpQmxCLGVBQWU0QjtZQUMvRCxNQUFNQyxVQUFVLElBQUksQ0FBQ0MsVUFBVSxDQUFDbEI7WUFFaENlLFdBQVdJLE9BQU8sQ0FBQ0YsU0FBU047WUFFNUJwQixRQUFRQyxHQUFHLENBQUMsQ0FBQyxxQkFBcUIsRUFBRVEsSUFBSSxFQUFFLEVBQUVXLGVBQWVTLE1BQU0sQ0FBQyxPQUFPLENBQUM7WUFDMUUsT0FBTztRQUNULEVBQUUsT0FBT0MsT0FBTztZQUNkOUIsUUFBUThCLEtBQUssQ0FBQyxDQUFDLDZCQUE2QixFQUFFckIsSUFBSSxDQUFDLENBQUMsRUFBRXFCO1lBQ3RELE9BQU87UUFDVDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNQyxJQUNKdEIsR0FBVyxFQUNYRSxVQUF3QixDQUFDLENBQUMsRUFDUDtRQUNuQixJQUFJO1lBQ0YsNkJBQTZCO1lBQzdCLElBQUksQ0FBQyxJQUFJLENBQUNoQixTQUFTLElBQUk7Z0JBQ3JCLE9BQU87WUFDVDtZQUVBLE1BQU0sRUFDSm9CLFVBQVUsY0FBYyxFQUN6QixHQUFHSjtZQUVKLE1BQU1hLGFBQWFULFlBQVksaUJBQWlCbEIsZUFBZTRCO1lBQy9ELE1BQU1DLFVBQVUsSUFBSSxDQUFDQyxVQUFVLENBQUNsQjtZQUNoQyxNQUFNVyxpQkFBaUJJLFdBQVdRLE9BQU8sQ0FBQ047WUFFMUMsSUFBSSxDQUFDTixnQkFBZ0I7Z0JBQ25CLE9BQU87WUFDVDtZQUVBLElBQUlhLG1CQUFtQmI7WUFFdkIsMkNBQTJDO1lBQzNDLElBQUksSUFBSSxDQUFDaEIsb0JBQW9CLElBQUksSUFBSSxDQUFDOEIsWUFBWSxDQUFDZCxpQkFBaUI7Z0JBQ2xFYSxtQkFBbUIsTUFBTSxJQUFJLENBQUNFLFVBQVUsQ0FBQ2Y7WUFDM0M7WUFFQSxNQUFNSixRQUF1QkssS0FBS2UsS0FBSyxDQUFDSDtZQUV4Qyw4QkFBOEI7WUFDOUIsSUFBSWpCLE1BQU1iLE9BQU8sS0FBSyxJQUFJLENBQUNBLE9BQU8sRUFBRTtnQkFDbENILFFBQVFxQyxJQUFJLENBQUMsQ0FBQyxtQ0FBbUMsRUFBRTVCLElBQUksVUFBVSxDQUFDO2dCQUNsRSxJQUFJLENBQUM2QixNQUFNLENBQUM3QixLQUFLRTtnQkFDakIsT0FBTztZQUNUO1lBRUEsWUFBWTtZQUNaLElBQUlPLEtBQUtDLEdBQUcsS0FBS0gsTUFBTUMsU0FBUyxHQUFHRCxNQUFNSixHQUFHLEVBQUU7Z0JBQzVDWixRQUFRQyxHQUFHLENBQUMsQ0FBQyxhQUFhLEVBQUVRLElBQUksa0JBQWtCLENBQUM7Z0JBQ25ELElBQUksQ0FBQzZCLE1BQU0sQ0FBQzdCLEtBQUtFO2dCQUNqQixPQUFPO1lBQ1Q7WUFFQVgsUUFBUUMsR0FBRyxDQUFDLENBQUMsd0JBQXdCLEVBQUVRLElBQUksV0FBVyxDQUFDO1lBQ3ZELE9BQU9PLE1BQU1OLElBQUk7UUFDbkIsRUFBRSxPQUFPb0IsT0FBTztZQUNkOUIsUUFBUThCLEtBQUssQ0FBQyxDQUFDLGdDQUFnQyxFQUFFckIsSUFBSSxDQUFDLENBQUMsRUFBRXFCO1lBQ3pELHlCQUF5QjtZQUN6QixJQUFJLENBQUNRLE1BQU0sQ0FBQzdCLEtBQUtFO1lBQ2pCLE9BQU87UUFDVDtJQUNGO0lBRUE7O0dBRUMsR0FDRDJCLE9BQU83QixHQUFXLEVBQUVFLFVBQXdCLENBQUMsQ0FBQyxFQUFRO1FBQ3BELElBQUk7WUFDRixzQkFBc0I7WUFDdEIsSUFBSSxDQUFDLElBQUksQ0FBQ2hCLFNBQVMsSUFBSTtnQkFDckI7WUFDRjtZQUVBLE1BQU0sRUFBRW9CLFVBQVUsY0FBYyxFQUFFLEdBQUdKO1lBQ3JDLE1BQU1hLGFBQWFULFlBQVksaUJBQWlCbEIsZUFBZTRCO1lBQy9ELE1BQU1DLFVBQVUsSUFBSSxDQUFDQyxVQUFVLENBQUNsQjtZQUVoQ2UsV0FBV2UsVUFBVSxDQUFDYjtZQUN0QjFCLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHVCQUF1QixFQUFFUSxLQUFLO1FBQzdDLEVBQUUsT0FBT3FCLE9BQU87WUFDZDlCLFFBQVE4QixLQUFLLENBQUMsQ0FBQyw4QkFBOEIsRUFBRXJCLElBQUksQ0FBQyxDQUFDLEVBQUVxQjtRQUN6RDtJQUNGO0lBRUE7O0dBRUMsR0FDRFUsTUFBTXpCLFVBQTZDLGNBQWMsRUFBUTtRQUN2RSxJQUFJO1lBQ0Ysc0JBQXNCO1lBQ3RCLElBQUksQ0FBQyxJQUFJLENBQUNwQixTQUFTLElBQUk7Z0JBQ3JCO1lBQ0Y7WUFFQSxNQUFNNkIsYUFBYVQsWUFBWSxpQkFBaUJsQixlQUFlNEI7WUFDL0QsTUFBTWdCLGVBQXlCLEVBQUU7WUFFakMsMEJBQTBCO1lBQzFCLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJbEIsV0FBV0ssTUFBTSxFQUFFYSxJQUFLO2dCQUMxQyxNQUFNakMsTUFBTWUsV0FBV2YsR0FBRyxDQUFDaUM7Z0JBQzNCLElBQUlqQyxPQUFPQSxJQUFJa0MsVUFBVSxDQUFDLGdDQUFnQztvQkFDeERGLGFBQWFHLElBQUksQ0FBQ25DO2dCQUNwQjtZQUNGO1lBRUEsY0FBYztZQUNkZ0MsYUFBYUksT0FBTyxDQUFDcEMsQ0FBQUEsTUFBT2UsV0FBV2UsVUFBVSxDQUFDOUI7WUFFbERULFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHNCQUFzQixFQUFFd0MsYUFBYVosTUFBTSxDQUFDLGNBQWMsRUFBRWQsU0FBUztRQUNwRixFQUFFLE9BQU9lLE9BQU87WUFDZDlCLFFBQVE4QixLQUFLLENBQUMsQ0FBQyw2QkFBNkIsRUFBRWYsUUFBUSxDQUFDLENBQUMsRUFBRWU7UUFDNUQ7SUFDRjtJQUVBOztHQUVDLEdBQ0RnQixTQUFTL0IsVUFBNkMsY0FBYyxFQUtsRTtRQUNBLG9DQUFvQztRQUNwQyxJQUFJLENBQUMsSUFBSSxDQUFDcEIsU0FBUyxJQUFJO1lBQ3JCLE9BQU87Z0JBQ0xvRCxjQUFjO2dCQUNkQyxXQUFXO2dCQUNYQyxhQUFhO2dCQUNiQyxhQUFhO1lBQ2Y7UUFDRjtRQUVBLE1BQU0xQixhQUFhVCxZQUFZLGlCQUFpQmxCLGVBQWU0QjtRQUMvRCxJQUFJc0IsZUFBZTtRQUNuQixJQUFJQyxZQUFZO1FBQ2hCLElBQUlHLGtCQUFrQkM7UUFDdEIsSUFBSUMsa0JBQWtCO1FBRXRCLElBQUk7WUFDRixJQUFLLElBQUlYLElBQUksR0FBR0EsSUFBSWxCLFdBQVdLLE1BQU0sRUFBRWEsSUFBSztnQkFDMUMsTUFBTWpDLE1BQU1lLFdBQVdmLEdBQUcsQ0FBQ2lDO2dCQUMzQixJQUFJakMsT0FBT0EsSUFBSWtDLFVBQVUsQ0FBQyxnQ0FBZ0M7b0JBQ3hELE1BQU1XLFFBQVE5QixXQUFXUSxPQUFPLENBQUN2QjtvQkFDakMsSUFBSTZDLE9BQU87d0JBQ1RQO3dCQUNBQyxhQUFhTSxNQUFNekIsTUFBTTt3QkFFekIsSUFBSTs0QkFDRixNQUFNYixRQUFRSyxLQUFLZSxLQUFLLENBQUNrQjs0QkFDekIsSUFBSXRDLE1BQU1DLFNBQVMsRUFBRTtnQ0FDbkJrQyxrQkFBa0JJLEtBQUtDLEdBQUcsQ0FBQ0wsaUJBQWlCbkMsTUFBTUMsU0FBUztnQ0FDM0RvQyxrQkFBa0JFLEtBQUtFLEdBQUcsQ0FBQ0osaUJBQWlCckMsTUFBTUMsU0FBUzs0QkFDN0Q7d0JBQ0YsRUFBRSxPQUFNO3dCQUNOLGtDQUFrQzt3QkFDcEM7b0JBQ0Y7Z0JBQ0Y7WUFDRjtRQUNGLEVBQUUsT0FBT2EsT0FBTztZQUNkOUIsUUFBUThCLEtBQUssQ0FBQyxxQ0FBcUNBO1FBQ3JEO1FBRUEsT0FBTztZQUNMaUI7WUFDQUM7WUFDQUMsYUFBYUUsb0JBQW9CQyxXQUFXLE9BQU8sSUFBSWxDLEtBQUtpQztZQUM1REQsYUFBYUcsb0JBQW9CLElBQUksT0FBTyxJQUFJbkMsS0FBS21DO1FBQ3ZEO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELHVCQUFxQztRQUNuQyw2Q0FBNkM7UUFDN0MsSUFBSSxJQUFJLENBQUMxRCxTQUFTLElBQUk7WUFDcEIrRCxZQUFZO2dCQUNWLElBQUksQ0FBQ3hELGNBQWM7WUFDckIsR0FBR1Ysb0RBQVlBLENBQUNtRSxxQkFBcUI7UUFDdkM7SUFDRjtJQUVBOztHQUVDLEdBQ0QsaUJBQStCO1FBQzdCLElBQUk7WUFDRiw4QkFBOEI7WUFDOUIsSUFBSSxDQUFDLElBQUksQ0FBQ2hFLFNBQVMsSUFBSTtnQkFDckI7WUFDRjtZQUVBLE1BQU1pRSxXQUFrRDtnQkFBQztnQkFBZ0I7YUFBaUI7WUFFMUZBLFNBQVNmLE9BQU8sQ0FBQ2dCLENBQUFBO2dCQUNmLE1BQU1yQyxhQUFhcUMsZ0JBQWdCLGlCQUFpQmhFLGVBQWU0QjtnQkFDbkUsTUFBTWdCLGVBQXlCLEVBQUU7Z0JBRWpDLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJbEIsV0FBV0ssTUFBTSxFQUFFYSxJQUFLO29CQUMxQyxNQUFNakMsTUFBTWUsV0FBV2YsR0FBRyxDQUFDaUM7b0JBQzNCLElBQUlqQyxPQUFPQSxJQUFJa0MsVUFBVSxDQUFDLGdDQUFnQzt3QkFDeEQsSUFBSTs0QkFDRixNQUFNVyxRQUFROUIsV0FBV1EsT0FBTyxDQUFDdkI7NEJBQ2pDLElBQUk2QyxPQUFPO2dDQUNULE1BQU10QyxRQUFRSyxLQUFLZSxLQUFLLENBQUNrQjtnQ0FDekIsSUFBSXBDLEtBQUtDLEdBQUcsS0FBS0gsTUFBTUMsU0FBUyxHQUFHRCxNQUFNSixHQUFHLEVBQUU7b0NBQzVDNkIsYUFBYUcsSUFBSSxDQUFDbkM7Z0NBQ3BCOzRCQUNGO3dCQUNGLEVBQUUsT0FBTTs0QkFDTiwyQkFBMkI7NEJBQzNCZ0MsYUFBYUcsSUFBSSxDQUFDbkM7d0JBQ3BCO29CQUNGO2dCQUNGO2dCQUVBZ0MsYUFBYUksT0FBTyxDQUFDcEMsQ0FBQUEsTUFBT2UsV0FBV2UsVUFBVSxDQUFDOUI7Z0JBRWxELElBQUlnQyxhQUFhWixNQUFNLEdBQUcsR0FBRztvQkFDM0I3QixRQUFRQyxHQUFHLENBQUMsQ0FBQyx5QkFBeUIsRUFBRXdDLGFBQWFaLE1BQU0sQ0FBQyxzQkFBc0IsRUFBRWdDLGFBQWE7Z0JBQ25HO1lBQ0Y7UUFDRixFQUFFLE9BQU8vQixPQUFPO1lBQ2Q5QixRQUFROEIsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDaEQ7SUFDRjtJQUVBOztHQUVDLEdBQ0QsV0FBbUJyQixHQUFXLEVBQVU7UUFDdEMsT0FBTyxDQUFDLDJCQUEyQixFQUFFQSxLQUFLO0lBQzVDO0lBRUE7O0dBRUMsR0FDRCxhQUFxQkMsSUFBWSxFQUFXO1FBQzFDLHVFQUF1RTtRQUN2RSxPQUFPQSxLQUFLaUMsVUFBVSxDQUFDLFdBQVdqQyxLQUFLaUMsVUFBVSxDQUFDLE9BQU8sZ0NBQWdDOztJQUMzRjtJQUVBOztHQUVDLEdBQ0QsTUFBY3BCLFNBQVNiLElBQVksRUFBbUI7UUFDcEQsZ0VBQWdFO1FBQ2hFLE9BQU9BO0lBQ1Q7SUFFQTs7R0FFQyxHQUNELE1BQWN5QixXQUFXekIsSUFBWSxFQUFtQjtRQUN0RCxrRUFBa0U7UUFDbEUsT0FBT0E7SUFDVDtBQUNGO0FBRUEsNEJBQTRCO0FBQ3JCLE1BQU1vRCxZQUFZckUsVUFBVUssV0FBVyxHQUFFO0FBRWhELG9DQUFvQztBQUM3QixNQUFNaUUseUJBQXlCdkUsb0RBQVlBLENBQUN3RSxJQUFJO0FBRXZELDBCQUEwQjtBQUNuQixNQUFNQyxlQUFlO0lBQzFCOztHQUVDLEdBQ0RDLGlCQUFpQixPQUFPekQsS0FBYUM7UUFDbkMsT0FBT29ELFVBQVV0RCxHQUFHLENBQUNDLEtBQUtDLE1BQU07WUFDOUJFLEtBQUtwQixvREFBWUEsQ0FBQzJFLGVBQWU7WUFDakNwRCxTQUFTLGVBQWUsZ0RBQWdEO1FBQzFFO0lBQ0Y7SUFFQTs7R0FFQyxHQUNEcUQsaUJBQWlCLE9BQU8zRCxLQUFhQztRQUNuQyxPQUFPb0QsVUFBVXRELEdBQUcsQ0FBQ0MsS0FBS0MsTUFBTTtZQUM5QkUsS0FBS3BCLG9EQUFZQSxDQUFDNkUsZUFBZTtZQUNqQ3RELFNBQVMsZUFBZSx3Q0FBd0M7UUFDbEU7SUFDRjtJQUVBOztHQUVDLEdBQ0R1RCxxQkFBcUIsT0FBTzdEO1FBQzFCLE9BQU9xRCxVQUFVL0IsR0FBRyxDQUFDdEIsS0FBSztZQUFFTSxTQUFTO1FBQWU7SUFDdEQ7SUFFQTs7R0FFQyxHQUNEd0QscUJBQXFCLE9BQU85RDtRQUMxQixPQUFPcUQsVUFBVS9CLEdBQUcsQ0FBQ3RCLEtBQUs7WUFBRU0sU0FBUztRQUFlO0lBQ3REO0FBQ0YsRUFBQyIsInNvdXJjZXMiOlsiRDpcXGxvdmVcXGRhc2hib2FyZFxcY3N2LW1hcmtldC1kYXNoYm9hcmRcXHNyY1xcbGliXFxkYXRhLWNhY2hlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxyXG4gKiBEYXRhIENhY2hpbmcgU3lzdGVtIGZvciBNYXJrZXQgRGF0YSBQZXJzaXN0ZW5jZVxyXG4gKiBIYW5kbGVzIGxvY2FsU3RvcmFnZS9zZXNzaW9uU3RvcmFnZSB3aXRoIFRUTCBhbmQgY29tcHJlc3Npb25cclxuICovXHJcblxyXG5pbXBvcnQgeyBDQUNIRV9DT05GSUcgfSBmcm9tICcuL2NvbnN0YW50cydcclxuXHJcbmludGVyZmFjZSBDYWNoZUVudHJ5PFQ+IHtcclxuICBkYXRhOiBUXHJcbiAgdGltZXN0YW1wOiBudW1iZXJcclxuICB0dGw6IG51bWJlclxyXG4gIHZlcnNpb246IHN0cmluZ1xyXG59XHJcblxyXG5pbnRlcmZhY2UgQ2FjaGVPcHRpb25zIHtcclxuICB0dGw/OiBudW1iZXJcclxuICB1c2VDb21wcmVzc2lvbj86IGJvb2xlYW5cclxuICBzdG9yYWdlPzogJ2xvY2FsU3RvcmFnZScgfCAnc2Vzc2lvblN0b3JhZ2UnXHJcbn1cclxuXHJcbmNsYXNzIERhdGFDYWNoZSB7XHJcbiAgcHJpdmF0ZSBzdGF0aWMgaW5zdGFuY2U6IERhdGFDYWNoZSB8IG51bGwgPSBudWxsXHJcbiAgcHJpdmF0ZSByZWFkb25seSB2ZXJzaW9uID0gJzEuMC4wJ1xyXG4gIHByaXZhdGUgY29tcHJlc3Npb25TdXBwb3J0ZWQgPSBmYWxzZVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiB3ZSdyZSBpbiBhIGJyb3dzZXIgZW52aXJvbm1lbnRcclxuICAgKi9cclxuICBwcml2YXRlIGlzQnJvd3NlcigpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB0eXBlb2Ygd2luZG93LmxvY2FsU3RvcmFnZSAhPT0gJ3VuZGVmaW5lZCdcclxuICB9XHJcblxyXG4gIHN0YXRpYyBnZXRJbnN0YW5jZSgpOiBEYXRhQ2FjaGUge1xyXG4gICAgaWYgKCFEYXRhQ2FjaGUuaW5zdGFuY2UpIHtcclxuICAgICAgRGF0YUNhY2hlLmluc3RhbmNlID0gbmV3IERhdGFDYWNoZSgpXHJcbiAgICB9XHJcbiAgICByZXR1cm4gRGF0YUNhY2hlLmluc3RhbmNlXHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBJbml0aWFsaXplIGNhY2hlIGZvciBjbGllbnQtc2lkZSB1c2FnZVxyXG4gICAqIENhbGwgdGhpcyBtZXRob2Qgd2hlbiB0aGUgY29tcG9uZW50IG1vdW50cyBvbiB0aGUgY2xpZW50IHNpZGVcclxuICAgKi9cclxuICBpbml0aWFsaXplQ2xpZW50U2lkZSgpOiB2b2lkIHtcclxuICAgIGlmICh0aGlzLmlzQnJvd3NlcigpKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfwn5qAIERhdGFDYWNoZTogSW5pdGlhbGl6aW5nIGNsaWVudC1zaWRlIGNhY2hlJylcclxuICAgICAgLy8gUGVyZm9ybSBhbnkgY2xpZW50LXNpZGUgc3BlY2lmaWMgaW5pdGlhbGl6YXRpb24gaGVyZVxyXG4gICAgICB0aGlzLmNsZWFudXBFeHBpcmVkKCkgLy8gUnVuIGluaXRpYWwgY2xlYW51cFxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBjb25zdHJ1Y3RvcigpIHtcclxuICAgIHRoaXMuY2hlY2tDb21wcmVzc2lvblN1cHBvcnQoKVxyXG4gICAgdGhpcy5zdGFydENsZWFudXBJbnRlcnZhbCgpXHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBjb21wcmVzc2lvbiBpcyBzdXBwb3J0ZWRcclxuICAgKi9cclxuICBwcml2YXRlIGNoZWNrQ29tcHJlc3Npb25TdXBwb3J0KCk6IHZvaWQge1xyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gQ2hlY2sgaWYgQ29tcHJlc3Npb25TdHJlYW0gaXMgYXZhaWxhYmxlIChtb2Rlcm4gYnJvd3NlcnMpXHJcbiAgICAgIHRoaXMuY29tcHJlc3Npb25TdXBwb3J0ZWQgPSB0eXBlb2YgQ29tcHJlc3Npb25TdHJlYW0gIT09ICd1bmRlZmluZWQnXHJcbiAgICB9IGNhdGNoIHtcclxuICAgICAgdGhpcy5jb21wcmVzc2lvblN1cHBvcnRlZCA9IGZhbHNlXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBTZXQgZGF0YSBpbiBjYWNoZSB3aXRoIFRUTFxyXG4gICAqL1xyXG4gIGFzeW5jIHNldDxUPihcclxuICAgIGtleTogc3RyaW5nLFxyXG4gICAgZGF0YTogVCxcclxuICAgIG9wdGlvbnM6IENhY2hlT3B0aW9ucyA9IHt9XHJcbiAgKTogUHJvbWlzZTxib29sZWFuPiB7XHJcbiAgICB0cnkge1xyXG4gICAgICAvLyBTa2lwIGNhY2hpbmcgb24gc2VydmVyIHNpZGVcclxuICAgICAgaWYgKCF0aGlzLmlzQnJvd3NlcigpKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coYOKaoO+4jyBEYXRhQ2FjaGU6IFNraXBwaW5nIGNhY2hlIG9uIHNlcnZlciBzaWRlIGZvciAke2tleX1gKVxyXG4gICAgICAgIHJldHVybiBmYWxzZVxyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCB7XHJcbiAgICAgICAgdHRsID0gQ0FDSEVfQ09ORklHLkRFRkFVTFRfVFRMLFxyXG4gICAgICAgIHVzZUNvbXByZXNzaW9uID0gZmFsc2UsXHJcbiAgICAgICAgc3RvcmFnZSA9ICdsb2NhbFN0b3JhZ2UnXHJcbiAgICAgIH0gPSBvcHRpb25zXHJcblxyXG4gICAgICBjb25zdCBlbnRyeTogQ2FjaGVFbnRyeTxUPiA9IHtcclxuICAgICAgICBkYXRhLFxyXG4gICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcclxuICAgICAgICB0dGwsXHJcbiAgICAgICAgdmVyc2lvbjogdGhpcy52ZXJzaW9uXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGxldCBzZXJpYWxpemVkRGF0YSA9IEpTT04uc3RyaW5naWZ5KGVudHJ5KVxyXG5cclxuICAgICAgLy8gQXBwbHkgY29tcHJlc3Npb24gaWYgcmVxdWVzdGVkIGFuZCBzdXBwb3J0ZWRcclxuICAgICAgaWYgKHVzZUNvbXByZXNzaW9uICYmIHRoaXMuY29tcHJlc3Npb25TdXBwb3J0ZWQpIHtcclxuICAgICAgICBzZXJpYWxpemVkRGF0YSA9IGF3YWl0IHRoaXMuY29tcHJlc3Moc2VyaWFsaXplZERhdGEpXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IHN0b3JhZ2VPYmogPSBzdG9yYWdlID09PSAnbG9jYWxTdG9yYWdlJyA/IGxvY2FsU3RvcmFnZSA6IHNlc3Npb25TdG9yYWdlXHJcbiAgICAgIGNvbnN0IGZ1bGxLZXkgPSB0aGlzLmdldEZ1bGxLZXkoa2V5KVxyXG5cclxuICAgICAgc3RvcmFnZU9iai5zZXRJdGVtKGZ1bGxLZXksIHNlcmlhbGl6ZWREYXRhKVxyXG5cclxuICAgICAgY29uc29sZS5sb2coYPCfkr4gRGF0YUNhY2hlOiBDYWNoZWQgJHtrZXl9ICgke3NlcmlhbGl6ZWREYXRhLmxlbmd0aH0gYnl0ZXMpYClcclxuICAgICAgcmV0dXJuIHRydWVcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoYOKdjCBEYXRhQ2FjaGU6IEZhaWxlZCB0byBjYWNoZSAke2tleX06YCwgZXJyb3IpXHJcbiAgICAgIHJldHVybiBmYWxzZVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IGRhdGEgZnJvbSBjYWNoZVxyXG4gICAqL1xyXG4gIGFzeW5jIGdldDxUPihcclxuICAgIGtleTogc3RyaW5nLFxyXG4gICAgb3B0aW9uczogQ2FjaGVPcHRpb25zID0ge31cclxuICApOiBQcm9taXNlPFQgfCBudWxsPiB7XHJcbiAgICB0cnkge1xyXG4gICAgICAvLyBSZXR1cm4gbnVsbCBvbiBzZXJ2ZXIgc2lkZVxyXG4gICAgICBpZiAoIXRoaXMuaXNCcm93c2VyKCkpIHtcclxuICAgICAgICByZXR1cm4gbnVsbFxyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCB7XHJcbiAgICAgICAgc3RvcmFnZSA9ICdsb2NhbFN0b3JhZ2UnXHJcbiAgICAgIH0gPSBvcHRpb25zXHJcblxyXG4gICAgICBjb25zdCBzdG9yYWdlT2JqID0gc3RvcmFnZSA9PT0gJ2xvY2FsU3RvcmFnZScgPyBsb2NhbFN0b3JhZ2UgOiBzZXNzaW9uU3RvcmFnZVxyXG4gICAgICBjb25zdCBmdWxsS2V5ID0gdGhpcy5nZXRGdWxsS2V5KGtleSlcclxuICAgICAgY29uc3Qgc2VyaWFsaXplZERhdGEgPSBzdG9yYWdlT2JqLmdldEl0ZW0oZnVsbEtleSlcclxuXHJcbiAgICAgIGlmICghc2VyaWFsaXplZERhdGEpIHtcclxuICAgICAgICByZXR1cm4gbnVsbFxyXG4gICAgICB9XHJcblxyXG4gICAgICBsZXQgZGVjb21wcmVzc2VkRGF0YSA9IHNlcmlhbGl6ZWREYXRhXHJcblxyXG4gICAgICAvLyBUcnkgdG8gZGVjb21wcmVzcyBpZiBpdCBsb29rcyBjb21wcmVzc2VkXHJcbiAgICAgIGlmICh0aGlzLmNvbXByZXNzaW9uU3VwcG9ydGVkICYmIHRoaXMuaXNDb21wcmVzc2VkKHNlcmlhbGl6ZWREYXRhKSkge1xyXG4gICAgICAgIGRlY29tcHJlc3NlZERhdGEgPSBhd2FpdCB0aGlzLmRlY29tcHJlc3Moc2VyaWFsaXplZERhdGEpXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IGVudHJ5OiBDYWNoZUVudHJ5PFQ+ID0gSlNPTi5wYXJzZShkZWNvbXByZXNzZWREYXRhKVxyXG5cclxuICAgICAgLy8gQ2hlY2sgdmVyc2lvbiBjb21wYXRpYmlsaXR5XHJcbiAgICAgIGlmIChlbnRyeS52ZXJzaW9uICE9PSB0aGlzLnZlcnNpb24pIHtcclxuICAgICAgICBjb25zb2xlLndhcm4oYOKaoO+4jyBEYXRhQ2FjaGU6IFZlcnNpb24gbWlzbWF0Y2ggZm9yICR7a2V5fSwgcmVtb3ZpbmdgKVxyXG4gICAgICAgIHRoaXMucmVtb3ZlKGtleSwgb3B0aW9ucylcclxuICAgICAgICByZXR1cm4gbnVsbFxyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBDaGVjayBUVExcclxuICAgICAgaWYgKERhdGUubm93KCkgLSBlbnRyeS50aW1lc3RhbXAgPiBlbnRyeS50dGwpIHtcclxuICAgICAgICBjb25zb2xlLmxvZyhg4o+wIERhdGFDYWNoZTogJHtrZXl9IGV4cGlyZWQsIHJlbW92aW5nYClcclxuICAgICAgICB0aGlzLnJlbW92ZShrZXksIG9wdGlvbnMpXHJcbiAgICAgICAgcmV0dXJuIG51bGxcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc29sZS5sb2coYPCfk5YgRGF0YUNhY2hlOiBSZXRyaWV2ZWQgJHtrZXl9IGZyb20gY2FjaGVgKVxyXG4gICAgICByZXR1cm4gZW50cnkuZGF0YVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihg4p2MIERhdGFDYWNoZTogRmFpbGVkIHRvIHJldHJpZXZlICR7a2V5fTpgLCBlcnJvcilcclxuICAgICAgLy8gUmVtb3ZlIGNvcnJ1cHRlZCBlbnRyeVxyXG4gICAgICB0aGlzLnJlbW92ZShrZXksIG9wdGlvbnMpXHJcbiAgICAgIHJldHVybiBudWxsXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBSZW1vdmUgZGF0YSBmcm9tIGNhY2hlXHJcbiAgICovXHJcbiAgcmVtb3ZlKGtleTogc3RyaW5nLCBvcHRpb25zOiBDYWNoZU9wdGlvbnMgPSB7fSk6IHZvaWQge1xyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gU2tpcCBvbiBzZXJ2ZXIgc2lkZVxyXG4gICAgICBpZiAoIXRoaXMuaXNCcm93c2VyKCkpIHtcclxuICAgICAgICByZXR1cm5cclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgeyBzdG9yYWdlID0gJ2xvY2FsU3RvcmFnZScgfSA9IG9wdGlvbnNcclxuICAgICAgY29uc3Qgc3RvcmFnZU9iaiA9IHN0b3JhZ2UgPT09ICdsb2NhbFN0b3JhZ2UnID8gbG9jYWxTdG9yYWdlIDogc2Vzc2lvblN0b3JhZ2VcclxuICAgICAgY29uc3QgZnVsbEtleSA9IHRoaXMuZ2V0RnVsbEtleShrZXkpXHJcblxyXG4gICAgICBzdG9yYWdlT2JqLnJlbW92ZUl0ZW0oZnVsbEtleSlcclxuICAgICAgY29uc29sZS5sb2coYPCfl5HvuI8gRGF0YUNhY2hlOiBSZW1vdmVkICR7a2V5fWApXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKGDinYwgRGF0YUNhY2hlOiBGYWlsZWQgdG8gcmVtb3ZlICR7a2V5fTpgLCBlcnJvcilcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENsZWFyIGFsbCBjYWNoZSBlbnRyaWVzXHJcbiAgICovXHJcbiAgY2xlYXIoc3RvcmFnZTogJ2xvY2FsU3RvcmFnZScgfCAnc2Vzc2lvblN0b3JhZ2UnID0gJ2xvY2FsU3RvcmFnZScpOiB2b2lkIHtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIFNraXAgb24gc2VydmVyIHNpZGVcclxuICAgICAgaWYgKCF0aGlzLmlzQnJvd3NlcigpKSB7XHJcbiAgICAgICAgcmV0dXJuXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IHN0b3JhZ2VPYmogPSBzdG9yYWdlID09PSAnbG9jYWxTdG9yYWdlJyA/IGxvY2FsU3RvcmFnZSA6IHNlc3Npb25TdG9yYWdlXHJcbiAgICAgIGNvbnN0IGtleXNUb1JlbW92ZTogc3RyaW5nW10gPSBbXVxyXG5cclxuICAgICAgLy8gRmluZCBhbGwgb3VyIGNhY2hlIGtleXNcclxuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzdG9yYWdlT2JqLmxlbmd0aDsgaSsrKSB7XHJcbiAgICAgICAgY29uc3Qga2V5ID0gc3RvcmFnZU9iai5rZXkoaSlcclxuICAgICAgICBpZiAoa2V5ICYmIGtleS5zdGFydHNXaXRoKCdjc3ZfbWFya2V0X2Rhc2hib2FyZF9jYWNoZV8nKSkge1xyXG4gICAgICAgICAga2V5c1RvUmVtb3ZlLnB1c2goa2V5KVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gUmVtb3ZlIHRoZW1cclxuICAgICAga2V5c1RvUmVtb3ZlLmZvckVhY2goa2V5ID0+IHN0b3JhZ2VPYmoucmVtb3ZlSXRlbShrZXkpKVxyXG5cclxuICAgICAgY29uc29sZS5sb2coYPCfp7kgRGF0YUNhY2hlOiBDbGVhcmVkICR7a2V5c1RvUmVtb3ZlLmxlbmd0aH0gZW50cmllcyBmcm9tICR7c3RvcmFnZX1gKVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihg4p2MIERhdGFDYWNoZTogRmFpbGVkIHRvIGNsZWFyICR7c3RvcmFnZX06YCwgZXJyb3IpXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgY2FjaGUgc3RhdGlzdGljc1xyXG4gICAqL1xyXG4gIGdldFN0YXRzKHN0b3JhZ2U6ICdsb2NhbFN0b3JhZ2UnIHwgJ3Nlc3Npb25TdG9yYWdlJyA9ICdsb2NhbFN0b3JhZ2UnKToge1xyXG4gICAgdG90YWxFbnRyaWVzOiBudW1iZXJcclxuICAgIHRvdGFsU2l6ZTogbnVtYmVyXHJcbiAgICBvbGRlc3RFbnRyeTogRGF0ZSB8IG51bGxcclxuICAgIG5ld2VzdEVudHJ5OiBEYXRlIHwgbnVsbFxyXG4gIH0ge1xyXG4gICAgLy8gUmV0dXJuIGVtcHR5IHN0YXRzIG9uIHNlcnZlciBzaWRlXHJcbiAgICBpZiAoIXRoaXMuaXNCcm93c2VyKCkpIHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICB0b3RhbEVudHJpZXM6IDAsXHJcbiAgICAgICAgdG90YWxTaXplOiAwLFxyXG4gICAgICAgIG9sZGVzdEVudHJ5OiBudWxsLFxyXG4gICAgICAgIG5ld2VzdEVudHJ5OiBudWxsXHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBzdG9yYWdlT2JqID0gc3RvcmFnZSA9PT0gJ2xvY2FsU3RvcmFnZScgPyBsb2NhbFN0b3JhZ2UgOiBzZXNzaW9uU3RvcmFnZVxyXG4gICAgbGV0IHRvdGFsRW50cmllcyA9IDBcclxuICAgIGxldCB0b3RhbFNpemUgPSAwXHJcbiAgICBsZXQgb2xkZXN0VGltZXN0YW1wID0gSW5maW5pdHlcclxuICAgIGxldCBuZXdlc3RUaW1lc3RhbXAgPSAwXHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzdG9yYWdlT2JqLmxlbmd0aDsgaSsrKSB7XHJcbiAgICAgICAgY29uc3Qga2V5ID0gc3RvcmFnZU9iai5rZXkoaSlcclxuICAgICAgICBpZiAoa2V5ICYmIGtleS5zdGFydHNXaXRoKCdjc3ZfbWFya2V0X2Rhc2hib2FyZF9jYWNoZV8nKSkge1xyXG4gICAgICAgICAgY29uc3QgdmFsdWUgPSBzdG9yYWdlT2JqLmdldEl0ZW0oa2V5KVxyXG4gICAgICAgICAgaWYgKHZhbHVlKSB7XHJcbiAgICAgICAgICAgIHRvdGFsRW50cmllcysrXHJcbiAgICAgICAgICAgIHRvdGFsU2l6ZSArPSB2YWx1ZS5sZW5ndGhcclxuXHJcbiAgICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgICAgY29uc3QgZW50cnkgPSBKU09OLnBhcnNlKHZhbHVlKVxyXG4gICAgICAgICAgICAgIGlmIChlbnRyeS50aW1lc3RhbXApIHtcclxuICAgICAgICAgICAgICAgIG9sZGVzdFRpbWVzdGFtcCA9IE1hdGgubWluKG9sZGVzdFRpbWVzdGFtcCwgZW50cnkudGltZXN0YW1wKVxyXG4gICAgICAgICAgICAgICAgbmV3ZXN0VGltZXN0YW1wID0gTWF0aC5tYXgobmV3ZXN0VGltZXN0YW1wLCBlbnRyeS50aW1lc3RhbXApXHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9IGNhdGNoIHtcclxuICAgICAgICAgICAgICAvLyBJZ25vcmUgcGFyc2luZyBlcnJvcnMgZm9yIHN0YXRzXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBEYXRhQ2FjaGU6IEZhaWxlZCB0byBnZXQgc3RhdHM6JywgZXJyb3IpXHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgdG90YWxFbnRyaWVzLFxyXG4gICAgICB0b3RhbFNpemUsXHJcbiAgICAgIG9sZGVzdEVudHJ5OiBvbGRlc3RUaW1lc3RhbXAgPT09IEluZmluaXR5ID8gbnVsbCA6IG5ldyBEYXRlKG9sZGVzdFRpbWVzdGFtcCksXHJcbiAgICAgIG5ld2VzdEVudHJ5OiBuZXdlc3RUaW1lc3RhbXAgPT09IDAgPyBudWxsIDogbmV3IERhdGUobmV3ZXN0VGltZXN0YW1wKVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogU3RhcnQgY2xlYW51cCBpbnRlcnZhbCB0byByZW1vdmUgZXhwaXJlZCBlbnRyaWVzXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBzdGFydENsZWFudXBJbnRlcnZhbCgpOiB2b2lkIHtcclxuICAgIC8vIE9ubHkgc3RhcnQgY2xlYW51cCBpbnRlcnZhbCBvbiBjbGllbnQgc2lkZVxyXG4gICAgaWYgKHRoaXMuaXNCcm93c2VyKCkpIHtcclxuICAgICAgc2V0SW50ZXJ2YWwoKCkgPT4ge1xyXG4gICAgICAgIHRoaXMuY2xlYW51cEV4cGlyZWQoKVxyXG4gICAgICB9LCBDQUNIRV9DT05GSUcuRVhQSVJZX0NIRUNLX0lOVEVSVkFMKVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2xlYW4gdXAgZXhwaXJlZCBlbnRyaWVzXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBjbGVhbnVwRXhwaXJlZCgpOiB2b2lkIHtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIFNraXAgY2xlYW51cCBvbiBzZXJ2ZXIgc2lkZVxyXG4gICAgICBpZiAoIXRoaXMuaXNCcm93c2VyKCkpIHtcclxuICAgICAgICByZXR1cm5cclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3Qgc3RvcmFnZXM6ICgnbG9jYWxTdG9yYWdlJyB8ICdzZXNzaW9uU3RvcmFnZScpW10gPSBbJ2xvY2FsU3RvcmFnZScsICdzZXNzaW9uU3RvcmFnZSddXHJcblxyXG4gICAgICBzdG9yYWdlcy5mb3JFYWNoKHN0b3JhZ2VUeXBlID0+IHtcclxuICAgICAgICBjb25zdCBzdG9yYWdlT2JqID0gc3RvcmFnZVR5cGUgPT09ICdsb2NhbFN0b3JhZ2UnID8gbG9jYWxTdG9yYWdlIDogc2Vzc2lvblN0b3JhZ2VcclxuICAgICAgICBjb25zdCBrZXlzVG9SZW1vdmU6IHN0cmluZ1tdID0gW11cclxuXHJcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzdG9yYWdlT2JqLmxlbmd0aDsgaSsrKSB7XHJcbiAgICAgICAgICBjb25zdCBrZXkgPSBzdG9yYWdlT2JqLmtleShpKVxyXG4gICAgICAgICAgaWYgKGtleSAmJiBrZXkuc3RhcnRzV2l0aCgnY3N2X21hcmtldF9kYXNoYm9hcmRfY2FjaGVfJykpIHtcclxuICAgICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgICBjb25zdCB2YWx1ZSA9IHN0b3JhZ2VPYmouZ2V0SXRlbShrZXkpXHJcbiAgICAgICAgICAgICAgaWYgKHZhbHVlKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBlbnRyeSA9IEpTT04ucGFyc2UodmFsdWUpXHJcbiAgICAgICAgICAgICAgICBpZiAoRGF0ZS5ub3coKSAtIGVudHJ5LnRpbWVzdGFtcCA+IGVudHJ5LnR0bCkge1xyXG4gICAgICAgICAgICAgICAgICBrZXlzVG9SZW1vdmUucHVzaChrZXkpXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9IGNhdGNoIHtcclxuICAgICAgICAgICAgICAvLyBSZW1vdmUgY29ycnVwdGVkIGVudHJpZXNcclxuICAgICAgICAgICAgICBrZXlzVG9SZW1vdmUucHVzaChrZXkpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGtleXNUb1JlbW92ZS5mb3JFYWNoKGtleSA9PiBzdG9yYWdlT2JqLnJlbW92ZUl0ZW0oa2V5KSlcclxuICAgICAgICBcclxuICAgICAgICBpZiAoa2V5c1RvUmVtb3ZlLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKGDwn6e5IERhdGFDYWNoZTogQ2xlYW5lZCB1cCAke2tleXNUb1JlbW92ZS5sZW5ndGh9IGV4cGlyZWQgZW50cmllcyBmcm9tICR7c3RvcmFnZVR5cGV9YClcclxuICAgICAgICB9XHJcbiAgICAgIH0pXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRGF0YUNhY2hlOiBDbGVhbnVwIGZhaWxlZDonLCBlcnJvcilcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdlbmVyYXRlIGZ1bGwgY2FjaGUga2V5XHJcbiAgICovXHJcbiAgcHJpdmF0ZSBnZXRGdWxsS2V5KGtleTogc3RyaW5nKTogc3RyaW5nIHtcclxuICAgIHJldHVybiBgY3N2X21hcmtldF9kYXNoYm9hcmRfY2FjaGVfJHtrZXl9YFxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgZGF0YSBpcyBjb21wcmVzc2VkXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBpc0NvbXByZXNzZWQoZGF0YTogc3RyaW5nKTogYm9vbGVhbiB7XHJcbiAgICAvLyBTaW1wbGUgaGV1cmlzdGljOiBjb21wcmVzc2VkIGRhdGEgdXN1YWxseSBzdGFydHMgd2l0aCBzcGVjaWZpYyBieXRlc1xyXG4gICAgcmV0dXJuIGRhdGEuc3RhcnRzV2l0aCgnSDRzSScpIHx8IGRhdGEuc3RhcnRzV2l0aCgnZUp5JykgLy8gQ29tbW9uIGNvbXByZXNzaW9uIHNpZ25hdHVyZXNcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENvbXByZXNzIGRhdGEgKHBsYWNlaG9sZGVyIGZvciBmdXR1cmUgaW1wbGVtZW50YXRpb24pXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBhc3luYyBjb21wcmVzcyhkYXRhOiBzdHJpbmcpOiBQcm9taXNlPHN0cmluZz4ge1xyXG4gICAgLy8gRm9yIG5vdywgcmV0dXJuIGFzLWlzLiBDYW4gaW1wbGVtZW50IGFjdHVhbCBjb21wcmVzc2lvbiBsYXRlclxyXG4gICAgcmV0dXJuIGRhdGFcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIERlY29tcHJlc3MgZGF0YSAocGxhY2Vob2xkZXIgZm9yIGZ1dHVyZSBpbXBsZW1lbnRhdGlvbilcclxuICAgKi9cclxuICBwcml2YXRlIGFzeW5jIGRlY29tcHJlc3MoZGF0YTogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmc+IHtcclxuICAgIC8vIEZvciBub3csIHJldHVybiBhcy1pcy4gQ2FuIGltcGxlbWVudCBhY3R1YWwgZGVjb21wcmVzc2lvbiBsYXRlclxyXG4gICAgcmV0dXJuIGRhdGFcclxuICB9XHJcbn1cclxuXHJcbi8vIEV4cG9ydCBzaW5nbGV0b24gaW5zdGFuY2VcclxuZXhwb3J0IGNvbnN0IGRhdGFDYWNoZSA9IERhdGFDYWNoZS5nZXRJbnN0YW5jZSgpXHJcblxyXG4vLyBFeHBvcnQgY2FjaGUga2V5cyBmb3IgbWFya2V0IGRhdGFcclxuZXhwb3J0IGNvbnN0IE1BUktFVF9EQVRBX0NBQ0hFX0tFWVMgPSBDQUNIRV9DT05GSUcuS0VZU1xyXG5cclxuLy8gRXhwb3J0IGhlbHBlciBmdW5jdGlvbnNcclxuZXhwb3J0IGNvbnN0IGNhY2hlSGVscGVycyA9IHtcclxuICAvKipcclxuICAgKiBDYWNoZSBtYXJrZXQgZGF0YSB3aXRoIG1lZGl1bSBUVEwgZm9yIHBhZ2UgcmVmcmVzaCBwZXJzaXN0ZW5jZVxyXG4gICAqL1xyXG4gIGNhY2hlTWFya2V0RGF0YTogYXN5bmMgKGtleTogc3RyaW5nLCBkYXRhOiBhbnkpID0+IHtcclxuICAgIHJldHVybiBkYXRhQ2FjaGUuc2V0KGtleSwgZGF0YSwge1xyXG4gICAgICB0dGw6IENBQ0hFX0NPTkZJRy5NQVJLRVRfREFUQV9UVEwsIC8vIDEwIG1pbnV0ZXMgZm9yIG1hcmtldCBkYXRhIHBlcnNpc3RlbmNlXHJcbiAgICAgIHN0b3JhZ2U6ICdsb2NhbFN0b3JhZ2UnIC8vIFVzZSBsb2NhbFN0b3JhZ2UgZm9yIHBhZ2UgcmVmcmVzaCBwZXJzaXN0ZW5jZVxyXG4gICAgfSlcclxuICB9LFxyXG5cclxuICAvKipcclxuICAgKiBDYWNoZSBzdGF0aWMgZGF0YSB3aXRoIGxvbmcgVFRMXHJcbiAgICovXHJcbiAgY2FjaGVTdGF0aWNEYXRhOiBhc3luYyAoa2V5OiBzdHJpbmcsIGRhdGE6IGFueSkgPT4ge1xyXG4gICAgcmV0dXJuIGRhdGFDYWNoZS5zZXQoa2V5LCBkYXRhLCB7XHJcbiAgICAgIHR0bDogQ0FDSEVfQ09ORklHLlNUQVRJQ19EQVRBX1RUTCwgLy8gMTAgbWludXRlcyBmb3Igc3RhdGljIGRhdGFcclxuICAgICAgc3RvcmFnZTogJ2xvY2FsU3RvcmFnZScgLy8gVXNlIGxvY2FsIHN0b3JhZ2UgZm9yIHBlcnNpc3RlbnQgZGF0YVxyXG4gICAgfSlcclxuICB9LFxyXG5cclxuICAvKipcclxuICAgKiBHZXQgY2FjaGVkIG1hcmtldCBkYXRhXHJcbiAgICovXHJcbiAgZ2V0Q2FjaGVkTWFya2V0RGF0YTogYXN5bmMgKGtleTogc3RyaW5nKSA9PiB7XHJcbiAgICByZXR1cm4gZGF0YUNhY2hlLmdldChrZXksIHsgc3RvcmFnZTogJ2xvY2FsU3RvcmFnZScgfSlcclxuICB9LFxyXG5cclxuICAvKipcclxuICAgKiBHZXQgY2FjaGVkIHN0YXRpYyBkYXRhXHJcbiAgICovXHJcbiAgZ2V0Q2FjaGVkU3RhdGljRGF0YTogYXN5bmMgKGtleTogc3RyaW5nKSA9PiB7XHJcbiAgICByZXR1cm4gZGF0YUNhY2hlLmdldChrZXksIHsgc3RvcmFnZTogJ2xvY2FsU3RvcmFnZScgfSlcclxuICB9XHJcbn1cclxuIl0sIm5hbWVzIjpbIkNBQ0hFX0NPTkZJRyIsIkRhdGFDYWNoZSIsImluc3RhbmNlIiwiaXNCcm93c2VyIiwid2luZG93IiwibG9jYWxTdG9yYWdlIiwiZ2V0SW5zdGFuY2UiLCJpbml0aWFsaXplQ2xpZW50U2lkZSIsImNvbnNvbGUiLCJsb2ciLCJjbGVhbnVwRXhwaXJlZCIsInZlcnNpb24iLCJjb21wcmVzc2lvblN1cHBvcnRlZCIsImNoZWNrQ29tcHJlc3Npb25TdXBwb3J0Iiwic3RhcnRDbGVhbnVwSW50ZXJ2YWwiLCJDb21wcmVzc2lvblN0cmVhbSIsInNldCIsImtleSIsImRhdGEiLCJvcHRpb25zIiwidHRsIiwiREVGQVVMVF9UVEwiLCJ1c2VDb21wcmVzc2lvbiIsInN0b3JhZ2UiLCJlbnRyeSIsInRpbWVzdGFtcCIsIkRhdGUiLCJub3ciLCJzZXJpYWxpemVkRGF0YSIsIkpTT04iLCJzdHJpbmdpZnkiLCJjb21wcmVzcyIsInN0b3JhZ2VPYmoiLCJzZXNzaW9uU3RvcmFnZSIsImZ1bGxLZXkiLCJnZXRGdWxsS2V5Iiwic2V0SXRlbSIsImxlbmd0aCIsImVycm9yIiwiZ2V0IiwiZ2V0SXRlbSIsImRlY29tcHJlc3NlZERhdGEiLCJpc0NvbXByZXNzZWQiLCJkZWNvbXByZXNzIiwicGFyc2UiLCJ3YXJuIiwicmVtb3ZlIiwicmVtb3ZlSXRlbSIsImNsZWFyIiwia2V5c1RvUmVtb3ZlIiwiaSIsInN0YXJ0c1dpdGgiLCJwdXNoIiwiZm9yRWFjaCIsImdldFN0YXRzIiwidG90YWxFbnRyaWVzIiwidG90YWxTaXplIiwib2xkZXN0RW50cnkiLCJuZXdlc3RFbnRyeSIsIm9sZGVzdFRpbWVzdGFtcCIsIkluZmluaXR5IiwibmV3ZXN0VGltZXN0YW1wIiwidmFsdWUiLCJNYXRoIiwibWluIiwibWF4Iiwic2V0SW50ZXJ2YWwiLCJFWFBJUllfQ0hFQ0tfSU5URVJWQUwiLCJzdG9yYWdlcyIsInN0b3JhZ2VUeXBlIiwiZGF0YUNhY2hlIiwiTUFSS0VUX0RBVEFfQ0FDSEVfS0VZUyIsIktFWVMiLCJjYWNoZUhlbHBlcnMiLCJjYWNoZU1hcmtldERhdGEiLCJNQVJLRVRfREFUQV9UVEwiLCJjYWNoZVN0YXRpY0RhdGEiLCJTVEFUSUNfREFUQV9UVEwiLCJnZXRDYWNoZWRNYXJrZXREYXRhIiwiZ2V0Q2FjaGVkU3RhdGljRGF0YSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/data-cache.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-hot-toast","vendor-chunks/react-error-boundary","vendor-chunks/goober","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();