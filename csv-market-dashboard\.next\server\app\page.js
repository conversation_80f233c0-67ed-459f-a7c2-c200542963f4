/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNsb3ZlJTVDJTVDZGFzaGJvYXJkJTVDJTVDY3N2LW1hcmtldC1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBb0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jc3YtbWFya2V0LWRhc2hib2FyZC8/N2ZhYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGxvdmVcXFxcZGFzaGJvYXJkXFxcXGNzdi1tYXJrZXQtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNsb3ZlJTVDJTVDZGFzaGJvYXJkJTVDJTVDY3N2LW1hcmtldC1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQWtHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3N2LW1hcmtldC1kYXNoYm9hcmQvPzUyZDEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxsb3ZlXFxcXGRhc2hib2FyZFxcXFxjc3YtbWFya2V0LWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var react_error_boundary__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-error-boundary */ \"(ssr)/./node_modules/react-error-boundary/dist/react-error-boundary.development.esm.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Error fallback component\nfunction ErrorFallback({ error, resetErrorBoundary }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg border border-red-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-600 text-lg\",\n                                children: \"⚠\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Application Error\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-4\",\n                    children: \"An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"cursor-pointer text-sm text-gray-500 hover:text-gray-700\",\n                            children: \"Technical Details\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto max-h-32\",\n                            children: error.message\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: resetErrorBoundary,\n                            className: \"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\",\n                            children: \"Try Again\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors\",\n                            children: \"Refresh Page\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"CSV Market Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Real-time market data dashboard with enhanced WebSocket management and data caching\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_error_boundary__WEBPACK_IMPORTED_MODULE_4__.ErrorBoundary, {\n                    FallbackComponent: ErrorFallback,\n                    onError: (error, errorInfo)=>{\n                        console.error(\"Application Error:\", error, errorInfo);\n                    // Here you could send the error to an error reporting service\n                    },\n                    onReset: ()=>{\n                        // Clear any state that might be causing the error\n                        localStorage.removeItem(\"enhanced-market-data\");\n                        localStorage.removeItem(\"enhanced-market-timestamp\");\n                        sessionStorage.clear();\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20\",\n                        children: [\n                            children,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                                position: \"top-right\",\n                                toastOptions: {\n                                    duration: 4000,\n                                    style: {\n                                        background: \"#363636\",\n                                        color: \"#fff\"\n                                    },\n                                    success: {\n                                        style: {\n                                            background: \"#10B981\"\n                                        }\n                                    },\n                                    error: {\n                                        style: {\n                                            background: \"#EF4444\"\n                                        }\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_InstrumentTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/InstrumentTable */ \"(ssr)/./src/components/InstrumentTable.tsx\");\n/* harmony import */ var _components_FilterPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/FilterPanel */ \"(ssr)/./src/components/FilterPanel.tsx\");\n/* harmony import */ var _components_ConnectionStatus__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ConnectionStatus */ \"(ssr)/./src/components/ConnectionStatus.tsx\");\n/* harmony import */ var _components_Stats__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Stats */ \"(ssr)/./src/components/Stats.tsx\");\n/* harmony import */ var _hooks_useEnhancedMarketData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useEnhancedMarketData */ \"(ssr)/./src/hooks/useEnhancedMarketData.ts\");\n/* harmony import */ var _lib_data_cache__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/data-cache */ \"(ssr)/./src/lib/data-cache.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Dashboard() {\n    const { marketData: marketDataArray, marketDataMap, isConnected, connectionError, connectionStatus, isLoading: wsLoading, cacheLoaded, refresh: refreshFromCache, stats, updateFilters, filters, getFilteredData, getSortedData } = (0,_hooks_useEnhancedMarketData__WEBPACK_IMPORTED_MODULE_6__.useEnhancedMarketData)();\n    const [instruments, setInstruments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [exchanges, setExchanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [instrumentTypes, setInstrumentTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Convert market data array to Map for compatibility\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const dataMap = new Map();\n        marketDataArray.forEach((item)=>{\n            if (item.securityId) {\n                dataMap.set(item.securityId, item);\n            }\n        });\n        setMarketData(dataMap);\n    }, [\n        marketDataArray\n    ]);\n    const fetchFreshInstruments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const serverUrl = \"http://localhost:8080]\" || 0;\n            console.log(\"\\uD83C\\uDF10 Dashboard: Loading fresh instruments from API...\");\n            const response = await fetch(`${serverUrl}/api/instruments`);\n            if (response.ok) {\n                const data = await response.json();\n                console.log(\"✅ Dashboard: Loaded\", data.data.instruments.length, \"instruments from API\");\n                setInstruments(data.data.instruments);\n                // Cache the instruments\n                await _lib_data_cache__WEBPACK_IMPORTED_MODULE_7__.cacheHelpers.cacheStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_7__.MARKET_DATA_CACHE_KEYS.INSTRUMENTS, data.data.instruments);\n                console.log(\"\\uD83D\\uDCBE Dashboard: Cached instruments data\");\n                setLoading(false);\n            } else {\n                console.error(\"❌ Dashboard: Failed to load instruments:\", response.statusText);\n                setError(\"Failed to load instruments\");\n                setLoading(false);\n            }\n        } catch (error) {\n            console.error(\"❌ Dashboard: Error fetching fresh instruments:\", error);\n            setError(\"Error fetching instruments\");\n            setLoading(false);\n        }\n    }, []);\n    // Load all instruments from API with caching\n    const loadAllInstruments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            setLoading(true);\n            // Check cache first\n            const cachedInstruments = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_7__.cacheHelpers.getCachedStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_7__.MARKET_DATA_CACHE_KEYS.INSTRUMENTS);\n            if (cachedInstruments && Array.isArray(cachedInstruments)) {\n                console.log(\"✅ Dashboard: Loaded instruments from cache\");\n                setInstruments(cachedInstruments);\n                setLoading(false);\n                // Still fetch fresh data in background\n                fetchFreshInstruments();\n                return;\n            }\n            // Fetch fresh data\n            await fetchFreshInstruments();\n        } catch (error) {\n            console.error(\"❌ Dashboard: Error loading instruments:\", error);\n            setError(\"Error loading instruments\");\n            setLoading(false);\n        }\n    }, [\n        fetchFreshInstruments\n    ]);\n    // Load all instruments on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAllInstruments();\n    }, [\n        loadAllInstruments\n    ]);\n    // Load additional data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadMetadata = async ()=>{\n            try {\n                const serverUrl = \"http://localhost:8080]\" || 0;\n                // Load exchanges\n                const exchangesResponse = await fetch(`${serverUrl}/api/exchanges`);\n                if (exchangesResponse.ok) {\n                    const exchangesData = await exchangesResponse.json();\n                    setExchanges(exchangesData.data);\n                }\n                // Load instrument types\n                const typesResponse = await fetch(`${serverUrl}/api/instrument-types`);\n                if (typesResponse.ok) {\n                    const typesData = await typesResponse.json();\n                    setInstrumentTypes(typesData.data);\n                }\n            } catch (error) {\n                console.error(\"❌ Error loading metadata:\", error);\n            }\n        };\n        loadMetadata();\n    }, []);\n    // Filter instruments based on current filter\n    const filteredInstruments = instruments.filter((instrument)=>{\n        if (filter.exchange && filter.exchange.length > 0 && !filter.exchange.includes(instrument.exchange)) {\n            return false;\n        }\n        if (filter.instrumentType && filter.instrumentType.length > 0 && !filter.instrumentType.includes(instrument.instrumentType)) {\n            return false;\n        }\n        if (filter.search) {\n            const searchTerm = filter.search.toLowerCase();\n            return instrument.symbol.toLowerCase().includes(searchTerm) || instrument.displayName.toLowerCase().includes(searchTerm) || instrument.isin && instrument.isin.toLowerCase().includes(searchTerm);\n        }\n        return true;\n    });\n    // Note: Subscriptions are now handled server-side automatically\n    const handleFilterChange = (newFilter)=>{\n        setFilter(newFilter);\n    };\n    const handleInstrumentSelect = (instrument)=>{\n        console.log(\"Selected instrument:\", instrument);\n    // You can add more functionality here, like showing detailed view\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner w-12 h-12 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading market data...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 169,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass rounded-2xl shadow-lg p-6 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold gradient-text\",\n                                    children: \"CSV Market Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-1\",\n                                    children: \"Real-time market data from CSV instruments\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3 space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/subscribed\",\n                                            className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors\",\n                                            children: \"\\uD83D\\uDCCA View Subscribed Data Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/option-chain\",\n                                            className: \"inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors\",\n                                            children: \"\\uD83D\\uDD17 NIFTY Option Chain\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConnectionStatus__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            connected: isConnected\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            (error || connectionError) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Error:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this),\n                    \" \",\n                    error || connectionError\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Stats__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                totalInstruments: instruments.length,\n                filteredInstruments: filteredInstruments.length,\n                marketDataCount: stats.totalInstruments,\n                connected: isConnected,\n                connectionStats: stats\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FilterPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            filter: filter,\n                            onFilterChange: handleFilterChange,\n                            exchanges: exchanges,\n                            instrumentTypes: instrumentTypes,\n                            segments: [\n                                \"C\",\n                                \"F\",\n                                \"O\"\n                            ]\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InstrumentTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            instruments: filteredInstruments.slice(0, 100),\n                            marketData: marketData,\n                            onInstrumentSelect: handleInstrumentSelect,\n                            loading: loading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 text-center text-sm text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"CSV Market Dashboard - Real-time data from \",\n                            instruments.length,\n                            \" instruments\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Last updated: \",\n                            new Date().toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUVnRTtBQUVMO0FBQ1I7QUFDVTtBQUN0QjtBQUMrQjtBQUNFO0FBR3pELFNBQVNXO0lBQ3RCLE1BQU0sRUFDSkMsWUFBWUMsZUFBZSxFQUMzQkMsYUFBYSxFQUNiQyxXQUFXLEVBQ1hDLGVBQWUsRUFDZkMsZ0JBQWdCLEVBQ2hCQyxXQUFXQyxTQUFTLEVBQ3BCQyxXQUFXLEVBQ1hDLFNBQVNDLGdCQUFnQixFQUN6QkMsS0FBSyxFQUNMQyxhQUFhLEVBQ2JDLE9BQU8sRUFDUEMsZUFBZSxFQUNmQyxhQUFhLEVBQ2QsR0FBR25CLG1GQUFxQkE7SUFFekIsTUFBTSxDQUFDb0IsYUFBYUMsZUFBZSxHQUFHNUIsK0NBQVFBLENBQWUsRUFBRTtJQUMvRCxNQUFNLENBQUNXLFlBQVlrQixjQUFjLEdBQUc3QiwrQ0FBUUEsQ0FBMEIsSUFBSThCO0lBQzFFLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHaEMsK0NBQVFBLENBQW1CLENBQUM7SUFDeEQsTUFBTSxDQUFDaUMsU0FBU0MsV0FBVyxHQUFHbEMsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDbUMsT0FBT0MsU0FBUyxHQUFHcEMsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQ3FDLFdBQVdDLGFBQWEsR0FBR3RDLCtDQUFRQSxDQUFXLEVBQUU7SUFDdkQsTUFBTSxDQUFDdUMsaUJBQWlCQyxtQkFBbUIsR0FBR3hDLCtDQUFRQSxDQUFXLEVBQUU7SUFFbkUscURBQXFEO0lBQ3JEQyxnREFBU0EsQ0FBQztRQUNSLE1BQU13QyxVQUFVLElBQUlYO1FBQ3BCbEIsZ0JBQWdCOEIsT0FBTyxDQUFDQyxDQUFBQTtZQUN0QixJQUFJQSxLQUFLQyxVQUFVLEVBQUU7Z0JBQ25CSCxRQUFRSSxHQUFHLENBQUNGLEtBQUtDLFVBQVUsRUFBRUQ7WUFDL0I7UUFDRjtRQUNBZCxjQUFjWTtJQUNoQixHQUFHO1FBQUM3QjtLQUFnQjtJQUVwQixNQUFNa0Msd0JBQXdCNUMsa0RBQVdBLENBQUM7UUFDeEMsSUFBSTtZQUNGLE1BQU02QyxZQUFZQyx3QkFBa0MsSUFBSTtZQUN4REcsUUFBUUMsR0FBRyxDQUFDO1lBRVosTUFBTUMsV0FBVyxNQUFNQyxNQUFNLENBQUMsRUFBRVAsVUFBVSxnQkFBZ0IsQ0FBQztZQUMzRCxJQUFJTSxTQUFTRSxFQUFFLEVBQUU7Z0JBQ2YsTUFBTUMsT0FBTyxNQUFNSCxTQUFTSSxJQUFJO2dCQUNoQ04sUUFBUUMsR0FBRyxDQUFDLHVCQUF1QkksS0FBS0EsSUFBSSxDQUFDN0IsV0FBVyxDQUFDK0IsTUFBTSxFQUFFO2dCQUNqRTlCLGVBQWU0QixLQUFLQSxJQUFJLENBQUM3QixXQUFXO2dCQUVwQyx3QkFBd0I7Z0JBQ3hCLE1BQU1uQix5REFBWUEsQ0FBQ21ELGVBQWUsQ0FBQ2xELG1FQUFzQkEsQ0FBQ21ELFdBQVcsRUFBRUosS0FBS0EsSUFBSSxDQUFDN0IsV0FBVztnQkFDNUZ3QixRQUFRQyxHQUFHLENBQUM7Z0JBRVpsQixXQUFXO1lBQ2IsT0FBTztnQkFDTGlCLFFBQVFoQixLQUFLLENBQUMsNENBQTRDa0IsU0FBU1EsVUFBVTtnQkFDN0V6QixTQUFTO2dCQUNURixXQUFXO1lBQ2I7UUFDRixFQUFFLE9BQU9DLE9BQU87WUFDZGdCLFFBQVFoQixLQUFLLENBQUMsa0RBQWtEQTtZQUNoRUMsU0FBUztZQUNURixXQUFXO1FBQ2I7SUFDRixHQUFHLEVBQUU7SUFFTCw2Q0FBNkM7SUFDN0MsTUFBTTRCLHFCQUFxQjVELGtEQUFXQSxDQUFDO1FBQ3JDLElBQUk7WUFDRmdDLFdBQVc7WUFFWCxvQkFBb0I7WUFDcEIsTUFBTTZCLG9CQUFvQixNQUFNdkQseURBQVlBLENBQUN3RCxtQkFBbUIsQ0FBQ3ZELG1FQUFzQkEsQ0FBQ21ELFdBQVc7WUFDbkcsSUFBSUcscUJBQXFCRSxNQUFNQyxPQUFPLENBQUNILG9CQUFvQjtnQkFDekRaLFFBQVFDLEdBQUcsQ0FBQztnQkFDWnhCLGVBQWVtQztnQkFDZjdCLFdBQVc7Z0JBRVgsdUNBQXVDO2dCQUN2Q1k7Z0JBQ0E7WUFDRjtZQUVBLG1CQUFtQjtZQUNuQixNQUFNQTtRQUNSLEVBQUUsT0FBT1gsT0FBTztZQUNkZ0IsUUFBUWhCLEtBQUssQ0FBQywyQ0FBMkNBO1lBQ3pEQyxTQUFTO1lBQ1RGLFdBQVc7UUFDYjtJQUNGLEdBQUc7UUFBQ1k7S0FBc0I7SUFFMUIsZ0NBQWdDO0lBQ2hDN0MsZ0RBQVNBLENBQUM7UUFDUjZEO0lBQ0YsR0FBRztRQUFDQTtLQUFtQjtJQUl2Qix1QkFBdUI7SUFDdkI3RCxnREFBU0EsQ0FBQztRQUNSLE1BQU1rRSxlQUFlO1lBQ25CLElBQUk7Z0JBQ0YsTUFBTXBCLFlBQVlDLHdCQUFrQyxJQUFJO2dCQUV4RCxpQkFBaUI7Z0JBQ2pCLE1BQU1vQixvQkFBb0IsTUFBTWQsTUFBTSxDQUFDLEVBQUVQLFVBQVUsY0FBYyxDQUFDO2dCQUNsRSxJQUFJcUIsa0JBQWtCYixFQUFFLEVBQUU7b0JBQ3hCLE1BQU1jLGdCQUFnQixNQUFNRCxrQkFBa0JYLElBQUk7b0JBQ2xEbkIsYUFBYStCLGNBQWNiLElBQUk7Z0JBQ2pDO2dCQUVBLHdCQUF3QjtnQkFDeEIsTUFBTWMsZ0JBQWdCLE1BQU1oQixNQUFNLENBQUMsRUFBRVAsVUFBVSxxQkFBcUIsQ0FBQztnQkFDckUsSUFBSXVCLGNBQWNmLEVBQUUsRUFBRTtvQkFDcEIsTUFBTWdCLFlBQVksTUFBTUQsY0FBY2IsSUFBSTtvQkFDMUNqQixtQkFBbUIrQixVQUFVZixJQUFJO2dCQUNuQztZQUNGLEVBQUUsT0FBT3JCLE9BQU87Z0JBQ2RnQixRQUFRaEIsS0FBSyxDQUFDLDZCQUE2QkE7WUFDN0M7UUFDRjtRQUVBZ0M7SUFDRixHQUFHLEVBQUU7SUFFTCw2Q0FBNkM7SUFDN0MsTUFBTUssc0JBQXNCN0MsWUFBWUksTUFBTSxDQUFDMEMsQ0FBQUE7UUFDN0MsSUFBSTFDLE9BQU8yQyxRQUFRLElBQUkzQyxPQUFPMkMsUUFBUSxDQUFDaEIsTUFBTSxHQUFHLEtBQUssQ0FBQzNCLE9BQU8yQyxRQUFRLENBQUNDLFFBQVEsQ0FBQ0YsV0FBV0MsUUFBUSxHQUFHO1lBQ25HLE9BQU87UUFDVDtRQUNBLElBQUkzQyxPQUFPNkMsY0FBYyxJQUFJN0MsT0FBTzZDLGNBQWMsQ0FBQ2xCLE1BQU0sR0FBRyxLQUFLLENBQUMzQixPQUFPNkMsY0FBYyxDQUFDRCxRQUFRLENBQUNGLFdBQVdHLGNBQWMsR0FBRztZQUMzSCxPQUFPO1FBQ1Q7UUFDQSxJQUFJN0MsT0FBTzhDLE1BQU0sRUFBRTtZQUNqQixNQUFNQyxhQUFhL0MsT0FBTzhDLE1BQU0sQ0FBQ0UsV0FBVztZQUM1QyxPQUNFTixXQUFXTyxNQUFNLENBQUNELFdBQVcsR0FBR0osUUFBUSxDQUFDRyxlQUN6Q0wsV0FBV1EsV0FBVyxDQUFDRixXQUFXLEdBQUdKLFFBQVEsQ0FBQ0csZUFDN0NMLFdBQVdTLElBQUksSUFBSVQsV0FBV1MsSUFBSSxDQUFDSCxXQUFXLEdBQUdKLFFBQVEsQ0FBQ0c7UUFFL0Q7UUFDQSxPQUFPO0lBQ1Q7SUFFQSxnRUFBZ0U7SUFFaEUsTUFBTUsscUJBQXFCLENBQUNDO1FBQzFCcEQsVUFBVW9EO0lBQ1o7SUFFQSxNQUFNQyx5QkFBeUIsQ0FBQ1o7UUFDOUJ0QixRQUFRQyxHQUFHLENBQUMsd0JBQXdCcUI7SUFDcEMsa0VBQWtFO0lBQ3BFO0lBRUEsSUFBSXhDLFNBQVM7UUFDWCxxQkFDRSw4REFBQ3FEO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ0M7d0JBQUVELFdBQVU7a0NBQWdCOzs7Ozs7Ozs7Ozs7Ozs7OztJQUlyQztJQUVBLHFCQUNFLDhEQUFDRDtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7OzhDQUNDLDhEQUFDRztvQ0FBR0YsV0FBVTs4Q0FBbUM7Ozs7Ozs4Q0FDakQsOERBQUNDO29DQUFFRCxXQUFVOzhDQUFxQjs7Ozs7OzhDQUNsQyw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRzs0Q0FDQ0MsTUFBSzs0Q0FDTEosV0FBVTtzREFDWDs7Ozs7O3NEQUdELDhEQUFDRzs0Q0FDQ0MsTUFBSzs0Q0FDTEosV0FBVTtzREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUtMLDhEQUFDbEYsb0VBQWdCQTs0QkFBQ3VGLFdBQVc5RTs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFLL0JxQixDQUFBQSxTQUFTcEIsZUFBYyxtQkFDdkIsOERBQUN1RTtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNNO2tDQUFPOzs7Ozs7b0JBQWU7b0JBQUUxRCxTQUFTcEI7Ozs7Ozs7MEJBS3RDLDhEQUFDVCx5REFBS0E7Z0JBQ0p3RixrQkFBa0JuRSxZQUFZK0IsTUFBTTtnQkFDcENjLHFCQUFxQkEsb0JBQW9CZCxNQUFNO2dCQUMvQ3FDLGlCQUFpQnpFLE1BQU13RSxnQkFBZ0I7Z0JBQ3ZDRixXQUFXOUU7Z0JBQ1hrRixpQkFBaUIxRTs7Ozs7OzBCQUluQiw4REFBQ2dFO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNuRiwrREFBV0E7NEJBQ1YyQixRQUFRQTs0QkFDUmtFLGdCQUFnQmQ7NEJBQ2hCOUMsV0FBV0E7NEJBQ1hFLGlCQUFpQkE7NEJBQ2pCMkQsVUFBVTtnQ0FBQztnQ0FBSztnQ0FBSzs2QkFBSTs7Ozs7Ozs7Ozs7a0NBSzdCLDhEQUFDWjt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ3BGLG1FQUFlQTs0QkFDZHdCLGFBQWE2QyxvQkFBb0IyQixLQUFLLENBQUMsR0FBRzs0QkFDMUN4RixZQUFZQTs0QkFDWnlGLG9CQUFvQmY7NEJBQ3BCcEQsU0FBU0E7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU1mLDhEQUFDcUQ7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDQzs7NEJBQUU7NEJBQTRDN0QsWUFBWStCLE1BQU07NEJBQUM7Ozs7Ozs7a0NBQ2xFLDhEQUFDOEI7OzRCQUFFOzRCQUFlLElBQUlhLE9BQU9DLGtCQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUl2RCIsInNvdXJjZXMiOlsid2VicGFjazovL2Nzdi1tYXJrZXQtZGFzaGJvYXJkLy4vc3JjL2FwcC9wYWdlLnRzeD9mNjhhIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5cclxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyBJbnN0cnVtZW50LCBNYXJrZXREYXRhLCBJbnN0cnVtZW50RmlsdGVyIH0gZnJvbSAnQC90eXBlcyc7XHJcbmltcG9ydCBJbnN0cnVtZW50VGFibGUgZnJvbSAnQC9jb21wb25lbnRzL0luc3RydW1lbnRUYWJsZSc7XHJcbmltcG9ydCBGaWx0ZXJQYW5lbCBmcm9tICdAL2NvbXBvbmVudHMvRmlsdGVyUGFuZWwnO1xyXG5pbXBvcnQgQ29ubmVjdGlvblN0YXR1cyBmcm9tICdAL2NvbXBvbmVudHMvQ29ubmVjdGlvblN0YXR1cyc7XHJcbmltcG9ydCBTdGF0cyBmcm9tICdAL2NvbXBvbmVudHMvU3RhdHMnO1xyXG5pbXBvcnQgeyB1c2VFbmhhbmNlZE1hcmtldERhdGEgfSBmcm9tICdAL2hvb2tzL3VzZUVuaGFuY2VkTWFya2V0RGF0YSc7XHJcbmltcG9ydCB7IGNhY2hlSGVscGVycywgTUFSS0VUX0RBVEFfQ0FDSEVfS0VZUyB9IGZyb20gJ0AvbGliL2RhdGEtY2FjaGUnO1xyXG5pbXBvcnQgdG9hc3QgZnJvbSAncmVhY3QtaG90LXRvYXN0JztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERhc2hib2FyZCgpIHtcclxuICBjb25zdCB7XHJcbiAgICBtYXJrZXREYXRhOiBtYXJrZXREYXRhQXJyYXksXHJcbiAgICBtYXJrZXREYXRhTWFwLFxyXG4gICAgaXNDb25uZWN0ZWQsXHJcbiAgICBjb25uZWN0aW9uRXJyb3IsXHJcbiAgICBjb25uZWN0aW9uU3RhdHVzLFxyXG4gICAgaXNMb2FkaW5nOiB3c0xvYWRpbmcsXHJcbiAgICBjYWNoZUxvYWRlZCxcclxuICAgIHJlZnJlc2g6IHJlZnJlc2hGcm9tQ2FjaGUsXHJcbiAgICBzdGF0cyxcclxuICAgIHVwZGF0ZUZpbHRlcnMsXHJcbiAgICBmaWx0ZXJzLFxyXG4gICAgZ2V0RmlsdGVyZWREYXRhLFxyXG4gICAgZ2V0U29ydGVkRGF0YVxyXG4gIH0gPSB1c2VFbmhhbmNlZE1hcmtldERhdGEoKTtcclxuXHJcbiAgY29uc3QgW2luc3RydW1lbnRzLCBzZXRJbnN0cnVtZW50c10gPSB1c2VTdGF0ZTxJbnN0cnVtZW50W10+KFtdKTtcclxuICBjb25zdCBbbWFya2V0RGF0YSwgc2V0TWFya2V0RGF0YV0gPSB1c2VTdGF0ZTxNYXA8c3RyaW5nLCBNYXJrZXREYXRhPj4obmV3IE1hcCgpKTtcclxuICBjb25zdCBbZmlsdGVyLCBzZXRGaWx0ZXJdID0gdXNlU3RhdGU8SW5zdHJ1bWVudEZpbHRlcj4oe30pO1xyXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW2V4Y2hhbmdlcywgc2V0RXhjaGFuZ2VzXSA9IHVzZVN0YXRlPHN0cmluZ1tdPihbXSk7XHJcbiAgY29uc3QgW2luc3RydW1lbnRUeXBlcywgc2V0SW5zdHJ1bWVudFR5cGVzXSA9IHVzZVN0YXRlPHN0cmluZ1tdPihbXSk7XHJcblxyXG4gIC8vIENvbnZlcnQgbWFya2V0IGRhdGEgYXJyYXkgdG8gTWFwIGZvciBjb21wYXRpYmlsaXR5XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGRhdGFNYXAgPSBuZXcgTWFwKCk7XHJcbiAgICBtYXJrZXREYXRhQXJyYXkuZm9yRWFjaChpdGVtID0+IHtcclxuICAgICAgaWYgKGl0ZW0uc2VjdXJpdHlJZCkge1xyXG4gICAgICAgIGRhdGFNYXAuc2V0KGl0ZW0uc2VjdXJpdHlJZCwgaXRlbSk7XHJcbiAgICAgIH1cclxuICAgIH0pO1xyXG4gICAgc2V0TWFya2V0RGF0YShkYXRhTWFwKTtcclxuICB9LCBbbWFya2V0RGF0YUFycmF5XSk7XHJcblxyXG4gIGNvbnN0IGZldGNoRnJlc2hJbnN0cnVtZW50cyA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHNlcnZlclVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NFUlZFUl9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6ODA4MCc7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfwn4yQIERhc2hib2FyZDogTG9hZGluZyBmcmVzaCBpbnN0cnVtZW50cyBmcm9tIEFQSS4uLicpO1xyXG5cclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtzZXJ2ZXJVcmx9L2FwaS9pbnN0cnVtZW50c2ApO1xyXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcclxuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgRGFzaGJvYXJkOiBMb2FkZWQnLCBkYXRhLmRhdGEuaW5zdHJ1bWVudHMubGVuZ3RoLCAnaW5zdHJ1bWVudHMgZnJvbSBBUEknKTtcclxuICAgICAgICBzZXRJbnN0cnVtZW50cyhkYXRhLmRhdGEuaW5zdHJ1bWVudHMpO1xyXG5cclxuICAgICAgICAvLyBDYWNoZSB0aGUgaW5zdHJ1bWVudHNcclxuICAgICAgICBhd2FpdCBjYWNoZUhlbHBlcnMuY2FjaGVTdGF0aWNEYXRhKE1BUktFVF9EQVRBX0NBQ0hFX0tFWVMuSU5TVFJVTUVOVFMsIGRhdGEuZGF0YS5pbnN0cnVtZW50cyk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfkr4gRGFzaGJvYXJkOiBDYWNoZWQgaW5zdHJ1bWVudHMgZGF0YScpO1xyXG5cclxuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgRGFzaGJvYXJkOiBGYWlsZWQgdG8gbG9hZCBpbnN0cnVtZW50czonLCByZXNwb25zZS5zdGF0dXNUZXh0KTtcclxuICAgICAgICBzZXRFcnJvcignRmFpbGVkIHRvIGxvYWQgaW5zdHJ1bWVudHMnKTtcclxuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign4p2MIERhc2hib2FyZDogRXJyb3IgZmV0Y2hpbmcgZnJlc2ggaW5zdHJ1bWVudHM6JywgZXJyb3IpO1xyXG4gICAgICBzZXRFcnJvcignRXJyb3IgZmV0Y2hpbmcgaW5zdHJ1bWVudHMnKTtcclxuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfSwgW10pO1xyXG5cclxuICAvLyBMb2FkIGFsbCBpbnN0cnVtZW50cyBmcm9tIEFQSSB3aXRoIGNhY2hpbmdcclxuICBjb25zdCBsb2FkQWxsSW5zdHJ1bWVudHMgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xyXG5cclxuICAgICAgLy8gQ2hlY2sgY2FjaGUgZmlyc3RcclxuICAgICAgY29uc3QgY2FjaGVkSW5zdHJ1bWVudHMgPSBhd2FpdCBjYWNoZUhlbHBlcnMuZ2V0Q2FjaGVkU3RhdGljRGF0YShNQVJLRVRfREFUQV9DQUNIRV9LRVlTLklOU1RSVU1FTlRTKTtcclxuICAgICAgaWYgKGNhY2hlZEluc3RydW1lbnRzICYmIEFycmF5LmlzQXJyYXkoY2FjaGVkSW5zdHJ1bWVudHMpKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBEYXNoYm9hcmQ6IExvYWRlZCBpbnN0cnVtZW50cyBmcm9tIGNhY2hlJyk7XHJcbiAgICAgICAgc2V0SW5zdHJ1bWVudHMoY2FjaGVkSW5zdHJ1bWVudHMpO1xyXG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG5cclxuICAgICAgICAvLyBTdGlsbCBmZXRjaCBmcmVzaCBkYXRhIGluIGJhY2tncm91bmRcclxuICAgICAgICBmZXRjaEZyZXNoSW5zdHJ1bWVudHMoKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEZldGNoIGZyZXNoIGRhdGFcclxuICAgICAgYXdhaXQgZmV0Y2hGcmVzaEluc3RydW1lbnRzKCk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRGFzaGJvYXJkOiBFcnJvciBsb2FkaW5nIGluc3RydW1lbnRzOicsIGVycm9yKTtcclxuICAgICAgc2V0RXJyb3IoJ0Vycm9yIGxvYWRpbmcgaW5zdHJ1bWVudHMnKTtcclxuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfSwgW2ZldGNoRnJlc2hJbnN0cnVtZW50c10pO1xyXG5cclxuICAvLyBMb2FkIGFsbCBpbnN0cnVtZW50cyBvbiBtb3VudFxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBsb2FkQWxsSW5zdHJ1bWVudHMoKTtcclxuICB9LCBbbG9hZEFsbEluc3RydW1lbnRzXSk7XHJcblxyXG5cclxuXHJcbiAgLy8gTG9hZCBhZGRpdGlvbmFsIGRhdGFcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgbG9hZE1ldGFkYXRhID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IHNlcnZlclVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NFUlZFUl9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6ODA4MCc7XHJcbiAgICAgICAgXHJcbiAgICAgICAgLy8gTG9hZCBleGNoYW5nZXNcclxuICAgICAgICBjb25zdCBleGNoYW5nZXNSZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke3NlcnZlclVybH0vYXBpL2V4Y2hhbmdlc2ApO1xyXG4gICAgICAgIGlmIChleGNoYW5nZXNSZXNwb25zZS5vaykge1xyXG4gICAgICAgICAgY29uc3QgZXhjaGFuZ2VzRGF0YSA9IGF3YWl0IGV4Y2hhbmdlc1Jlc3BvbnNlLmpzb24oKTtcclxuICAgICAgICAgIHNldEV4Y2hhbmdlcyhleGNoYW5nZXNEYXRhLmRhdGEpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gTG9hZCBpbnN0cnVtZW50IHR5cGVzXHJcbiAgICAgICAgY29uc3QgdHlwZXNSZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke3NlcnZlclVybH0vYXBpL2luc3RydW1lbnQtdHlwZXNgKTtcclxuICAgICAgICBpZiAodHlwZXNSZXNwb25zZS5vaykge1xyXG4gICAgICAgICAgY29uc3QgdHlwZXNEYXRhID0gYXdhaXQgdHlwZXNSZXNwb25zZS5qc29uKCk7XHJcbiAgICAgICAgICBzZXRJbnN0cnVtZW50VHlwZXModHlwZXNEYXRhLmRhdGEpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJyb3IgbG9hZGluZyBtZXRhZGF0YTonLCBlcnJvcik7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgbG9hZE1ldGFkYXRhKCk7XHJcbiAgfSwgW10pO1xyXG5cclxuICAvLyBGaWx0ZXIgaW5zdHJ1bWVudHMgYmFzZWQgb24gY3VycmVudCBmaWx0ZXJcclxuICBjb25zdCBmaWx0ZXJlZEluc3RydW1lbnRzID0gaW5zdHJ1bWVudHMuZmlsdGVyKGluc3RydW1lbnQgPT4ge1xyXG4gICAgaWYgKGZpbHRlci5leGNoYW5nZSAmJiBmaWx0ZXIuZXhjaGFuZ2UubGVuZ3RoID4gMCAmJiAhZmlsdGVyLmV4Y2hhbmdlLmluY2x1ZGVzKGluc3RydW1lbnQuZXhjaGFuZ2UpKSB7XHJcbiAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH1cclxuICAgIGlmIChmaWx0ZXIuaW5zdHJ1bWVudFR5cGUgJiYgZmlsdGVyLmluc3RydW1lbnRUeXBlLmxlbmd0aCA+IDAgJiYgIWZpbHRlci5pbnN0cnVtZW50VHlwZS5pbmNsdWRlcyhpbnN0cnVtZW50Lmluc3RydW1lbnRUeXBlKSkge1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcbiAgICBpZiAoZmlsdGVyLnNlYXJjaCkge1xyXG4gICAgICBjb25zdCBzZWFyY2hUZXJtID0gZmlsdGVyLnNlYXJjaC50b0xvd2VyQ2FzZSgpO1xyXG4gICAgICByZXR1cm4gKFxyXG4gICAgICAgIGluc3RydW1lbnQuc3ltYm9sLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybSkgfHxcclxuICAgICAgICBpbnN0cnVtZW50LmRpc3BsYXlOYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybSkgfHxcclxuICAgICAgICAoaW5zdHJ1bWVudC5pc2luICYmIGluc3RydW1lbnQuaXNpbi50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0pKVxyXG4gICAgICApO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIHRydWU7XHJcbiAgfSk7XHJcblxyXG4gIC8vIE5vdGU6IFN1YnNjcmlwdGlvbnMgYXJlIG5vdyBoYW5kbGVkIHNlcnZlci1zaWRlIGF1dG9tYXRpY2FsbHlcclxuXHJcbiAgY29uc3QgaGFuZGxlRmlsdGVyQ2hhbmdlID0gKG5ld0ZpbHRlcjogSW5zdHJ1bWVudEZpbHRlcikgPT4ge1xyXG4gICAgc2V0RmlsdGVyKG5ld0ZpbHRlcik7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlSW5zdHJ1bWVudFNlbGVjdCA9IChpbnN0cnVtZW50OiBJbnN0cnVtZW50KSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZygnU2VsZWN0ZWQgaW5zdHJ1bWVudDonLCBpbnN0cnVtZW50KTtcclxuICAgIC8vIFlvdSBjYW4gYWRkIG1vcmUgZnVuY3Rpb25hbGl0eSBoZXJlLCBsaWtlIHNob3dpbmcgZGV0YWlsZWQgdmlld1xyXG4gIH07XHJcblxyXG4gIGlmIChsb2FkaW5nKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3Bpbm5lciB3LTEyIGgtMTIgbXgtYXV0byBtYi00XCI+PC9kaXY+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+TG9hZGluZyBtYXJrZXQgZGF0YS4uLjwvcD5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIHAtNlwiPlxyXG4gICAgICB7LyogSGVhZGVyICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdsYXNzIHJvdW5kZWQtMnhsIHNoYWRvdy1sZyBwLTYgbWItNlwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIGdyYWRpZW50LXRleHRcIj5DU1YgTWFya2V0IERhc2hib2FyZDwvaDE+XHJcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbXQtMVwiPlJlYWwtdGltZSBtYXJrZXQgZGF0YSBmcm9tIENTViBpbnN0cnVtZW50czwvcD5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0zIHNwYWNlLXgtM1wiPlxyXG4gICAgICAgICAgICAgIDxhXHJcbiAgICAgICAgICAgICAgICBocmVmPVwiL3N1YnNjcmliZWRcIlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMiBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHRleHQtc20gZm9udC1tZWRpdW0gcm91bmRlZC1tZCBob3ZlcjpiZy1ibHVlLTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAg8J+TiiBWaWV3IFN1YnNjcmliZWQgRGF0YSBEYXNoYm9hcmRcclxuICAgICAgICAgICAgICA8L2E+XHJcbiAgICAgICAgICAgICAgPGFcclxuICAgICAgICAgICAgICAgIGhyZWY9XCIvb3B0aW9uLWNoYWluXCJcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTIgYmctZ3JlZW4tNjAwIHRleHQtd2hpdGUgdGV4dC1zbSBmb250LW1lZGl1bSByb3VuZGVkLW1kIGhvdmVyOmJnLWdyZWVuLTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAg8J+UlyBOSUZUWSBPcHRpb24gQ2hhaW5cclxuICAgICAgICAgICAgICA8L2E+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8Q29ubmVjdGlvblN0YXR1cyBjb25uZWN0ZWQ9e2lzQ29ubmVjdGVkfSAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHsvKiBFcnJvciBNZXNzYWdlICovfVxyXG4gICAgICB7KGVycm9yIHx8IGNvbm5lY3Rpb25FcnJvcikgJiYgKFxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcmVkLTEwMCBib3JkZXIgYm9yZGVyLXJlZC00MDAgdGV4dC1yZWQtNzAwIHB4LTQgcHktMyByb3VuZGVkIG1iLTZcIj5cclxuICAgICAgICAgIDxzdHJvbmc+RXJyb3I6PC9zdHJvbmc+IHtlcnJvciB8fCBjb25uZWN0aW9uRXJyb3J9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICB7LyogU3RhdHMgKi99XHJcbiAgICAgIDxTdGF0cyBcclxuICAgICAgICB0b3RhbEluc3RydW1lbnRzPXtpbnN0cnVtZW50cy5sZW5ndGh9XHJcbiAgICAgICAgZmlsdGVyZWRJbnN0cnVtZW50cz17ZmlsdGVyZWRJbnN0cnVtZW50cy5sZW5ndGh9XHJcbiAgICAgICAgbWFya2V0RGF0YUNvdW50PXtzdGF0cy50b3RhbEluc3RydW1lbnRzfVxyXG4gICAgICAgIGNvbm5lY3RlZD17aXNDb25uZWN0ZWR9XHJcbiAgICAgICAgY29ubmVjdGlvblN0YXRzPXtzdGF0c31cclxuICAgICAgLz5cclxuXHJcbiAgICAgIHsvKiBNYWluIENvbnRlbnQgKi99XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtNCBnYXAtNlwiPlxyXG4gICAgICAgIHsvKiBGaWx0ZXIgUGFuZWwgKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0xXCI+XHJcbiAgICAgICAgICA8RmlsdGVyUGFuZWxcclxuICAgICAgICAgICAgZmlsdGVyPXtmaWx0ZXJ9XHJcbiAgICAgICAgICAgIG9uRmlsdGVyQ2hhbmdlPXtoYW5kbGVGaWx0ZXJDaGFuZ2V9XHJcbiAgICAgICAgICAgIGV4Y2hhbmdlcz17ZXhjaGFuZ2VzfVxyXG4gICAgICAgICAgICBpbnN0cnVtZW50VHlwZXM9e2luc3RydW1lbnRUeXBlc31cclxuICAgICAgICAgICAgc2VnbWVudHM9e1snQycsICdGJywgJ08nXX0gLy8gQ29tbW9uIHNlZ21lbnRzXHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7LyogSW5zdHJ1bWVudCBUYWJsZSAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTNcIj5cclxuICAgICAgICAgIDxJbnN0cnVtZW50VGFibGVcclxuICAgICAgICAgICAgaW5zdHJ1bWVudHM9e2ZpbHRlcmVkSW5zdHJ1bWVudHMuc2xpY2UoMCwgMTAwKX0gLy8gU2hvdyBtb3JlIGluc3RydW1lbnRzXHJcbiAgICAgICAgICAgIG1hcmtldERhdGE9e21hcmtldERhdGF9XHJcbiAgICAgICAgICAgIG9uSW5zdHJ1bWVudFNlbGVjdD17aGFuZGxlSW5zdHJ1bWVudFNlbGVjdH1cclxuICAgICAgICAgICAgbG9hZGluZz17bG9hZGluZ31cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIEZvb3RlciAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC04IHRleHQtY2VudGVyIHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgIDxwPkNTViBNYXJrZXQgRGFzaGJvYXJkIC0gUmVhbC10aW1lIGRhdGEgZnJvbSB7aW5zdHJ1bWVudHMubGVuZ3RofSBpbnN0cnVtZW50czwvcD5cclxuICAgICAgICA8cD5MYXN0IHVwZGF0ZWQ6IHtuZXcgRGF0ZSgpLnRvTG9jYWxlVGltZVN0cmluZygpfTwvcD5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQ2FsbGJhY2siLCJJbnN0cnVtZW50VGFibGUiLCJGaWx0ZXJQYW5lbCIsIkNvbm5lY3Rpb25TdGF0dXMiLCJTdGF0cyIsInVzZUVuaGFuY2VkTWFya2V0RGF0YSIsImNhY2hlSGVscGVycyIsIk1BUktFVF9EQVRBX0NBQ0hFX0tFWVMiLCJEYXNoYm9hcmQiLCJtYXJrZXREYXRhIiwibWFya2V0RGF0YUFycmF5IiwibWFya2V0RGF0YU1hcCIsImlzQ29ubmVjdGVkIiwiY29ubmVjdGlvbkVycm9yIiwiY29ubmVjdGlvblN0YXR1cyIsImlzTG9hZGluZyIsIndzTG9hZGluZyIsImNhY2hlTG9hZGVkIiwicmVmcmVzaCIsInJlZnJlc2hGcm9tQ2FjaGUiLCJzdGF0cyIsInVwZGF0ZUZpbHRlcnMiLCJmaWx0ZXJzIiwiZ2V0RmlsdGVyZWREYXRhIiwiZ2V0U29ydGVkRGF0YSIsImluc3RydW1lbnRzIiwic2V0SW5zdHJ1bWVudHMiLCJzZXRNYXJrZXREYXRhIiwiTWFwIiwiZmlsdGVyIiwic2V0RmlsdGVyIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwiZXhjaGFuZ2VzIiwic2V0RXhjaGFuZ2VzIiwiaW5zdHJ1bWVudFR5cGVzIiwic2V0SW5zdHJ1bWVudFR5cGVzIiwiZGF0YU1hcCIsImZvckVhY2giLCJpdGVtIiwic2VjdXJpdHlJZCIsInNldCIsImZldGNoRnJlc2hJbnN0cnVtZW50cyIsInNlcnZlclVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TRVJWRVJfVVJMIiwiY29uc29sZSIsImxvZyIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsImRhdGEiLCJqc29uIiwibGVuZ3RoIiwiY2FjaGVTdGF0aWNEYXRhIiwiSU5TVFJVTUVOVFMiLCJzdGF0dXNUZXh0IiwibG9hZEFsbEluc3RydW1lbnRzIiwiY2FjaGVkSW5zdHJ1bWVudHMiLCJnZXRDYWNoZWRTdGF0aWNEYXRhIiwiQXJyYXkiLCJpc0FycmF5IiwibG9hZE1ldGFkYXRhIiwiZXhjaGFuZ2VzUmVzcG9uc2UiLCJleGNoYW5nZXNEYXRhIiwidHlwZXNSZXNwb25zZSIsInR5cGVzRGF0YSIsImZpbHRlcmVkSW5zdHJ1bWVudHMiLCJpbnN0cnVtZW50IiwiZXhjaGFuZ2UiLCJpbmNsdWRlcyIsImluc3RydW1lbnRUeXBlIiwic2VhcmNoIiwic2VhcmNoVGVybSIsInRvTG93ZXJDYXNlIiwic3ltYm9sIiwiZGlzcGxheU5hbWUiLCJpc2luIiwiaGFuZGxlRmlsdGVyQ2hhbmdlIiwibmV3RmlsdGVyIiwiaGFuZGxlSW5zdHJ1bWVudFNlbGVjdCIsImRpdiIsImNsYXNzTmFtZSIsInAiLCJoMSIsImEiLCJocmVmIiwiY29ubmVjdGVkIiwic3Ryb25nIiwidG90YWxJbnN0cnVtZW50cyIsIm1hcmtldERhdGFDb3VudCIsImNvbm5lY3Rpb25TdGF0cyIsIm9uRmlsdGVyQ2hhbmdlIiwic2VnbWVudHMiLCJzbGljZSIsIm9uSW5zdHJ1bWVudFNlbGVjdCIsIkRhdGUiLCJ0b0xvY2FsZVRpbWVTdHJpbmciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ConnectionStatus.tsx":
/*!*********************************************!*\
  !*** ./src/components/ConnectionStatus.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst ConnectionStatus = ({ connected })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `w-3 h-3 rounded-full ${connected ? \"bg-green-500 animate-pulse\" : \"bg-red-500\"}`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-medium ${connected ? \"text-green-700\" : \"text-red-700\"}`,\n                        children: connected ? \"Connected\" : \"Disconnected\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `px-3 py-1 rounded-full text-xs font-semibold ${connected ? \"bg-green-100 text-green-800\" : \"bg-red-100 text-red-800\"}`,\n                children: connected ? \"LIVE\" : \"OFFLINE\"\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ConnectionStatus);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ConnectionStatus.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FilterPanel.tsx":
/*!****************************************!*\
  !*** ./src/components/FilterPanel.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst FilterPanel = ({ filter, onFilterChange, exchanges, instrumentTypes, segments })=>{\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(filter.search || \"\");\n    const handleExchangeChange = (exchange, checked)=>{\n        const currentExchanges = filter.exchange || [];\n        const newExchanges = checked ? [\n            ...currentExchanges,\n            exchange\n        ] : currentExchanges.filter((e)=>e !== exchange);\n        onFilterChange({\n            ...filter,\n            exchange: newExchanges.length > 0 ? newExchanges : undefined\n        });\n    };\n    const handleInstrumentTypeChange = (type, checked)=>{\n        const currentTypes = filter.instrumentType || [];\n        const newTypes = checked ? [\n            ...currentTypes,\n            type\n        ] : currentTypes.filter((t)=>t !== type);\n        onFilterChange({\n            ...filter,\n            instrumentType: newTypes.length > 0 ? newTypes : undefined\n        });\n    };\n    const handleSegmentChange = (segment, checked)=>{\n        const currentSegments = filter.segment || [];\n        const newSegments = checked ? [\n            ...currentSegments,\n            segment\n        ] : currentSegments.filter((s)=>s !== segment);\n        onFilterChange({\n            ...filter,\n            segment: newSegments.length > 0 ? newSegments : undefined\n        });\n    };\n    const handleSearchChange = (value)=>{\n        setSearchTerm(value);\n        onFilterChange({\n            ...filter,\n            search: value || undefined\n        });\n    };\n    const handleClearFilters = ()=>{\n        setSearchTerm(\"\");\n        onFilterChange({});\n    };\n    const isFilterActive = ()=>{\n        return !!(filter.exchange?.length || filter.instrumentType?.length || filter.segment?.length || filter.search || filter.isActive !== undefined || filter.hasExpiry !== undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"filter-panel\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Filters\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    isFilterActive() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleClearFilters,\n                        className: \"text-sm text-blue-600 hover:text-blue-800 font-medium\",\n                        children: \"Clear All\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Search\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        placeholder: \"Search by symbol, name, or ISIN...\",\n                        value: searchTerm,\n                        onChange: (e)=>handleSearchChange(e.target.value),\n                        className: \"filter-input\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Exchanges\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 max-h-32 overflow-y-auto custom-scrollbar\",\n                        children: exchanges.map((exchange)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filter.exchange?.includes(exchange) || false,\n                                        onChange: (e)=>handleExchangeChange(exchange, e.target.checked),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: exchange\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, exchange, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Instrument Types\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 max-h-32 overflow-y-auto custom-scrollbar\",\n                        children: instrumentTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filter.instrumentType?.includes(type) || false,\n                                        onChange: (e)=>handleInstrumentTypeChange(type, e.target.checked),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: type\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, type, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Segments\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: segments.map((segment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filter.segment?.includes(segment) || false,\n                                        onChange: (e)=>handleSegmentChange(segment, e.target.checked),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: segment === \"C\" ? \"Cash (C)\" : segment === \"F\" ? \"Futures (F)\" : segment === \"O\" ? \"Options (O)\" : segment\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, segment, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Additional Filters\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filter.isActive === true,\n                                        onChange: (e)=>onFilterChange({\n                                                ...filter,\n                                                isActive: e.target.checked ? true : undefined\n                                            }),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: \"Active Only\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filter.hasExpiry === true,\n                                        onChange: (e)=>onFilterChange({\n                                                ...filter,\n                                                hasExpiry: e.target.checked ? true : undefined\n                                            }),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: \"Has Expiry\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Lot Size Range\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"number\",\n                                placeholder: \"Min\",\n                                value: filter.minLotSize || \"\",\n                                onChange: (e)=>onFilterChange({\n                                        ...filter,\n                                        minLotSize: e.target.value ? parseInt(e.target.value) : undefined\n                                    }),\n                                className: \"filter-input text-sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"number\",\n                                placeholder: \"Max\",\n                                value: filter.maxLotSize || \"\",\n                                onChange: (e)=>onFilterChange({\n                                        ...filter,\n                                        maxLotSize: e.target.value ? parseInt(e.target.value) : undefined\n                                    }),\n                                className: \"filter-input text-sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, undefined),\n            isFilterActive() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-blue-900 mb-2\",\n                        children: \"Active Filters:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1 text-xs text-blue-700\",\n                        children: [\n                            filter.exchange?.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Exchanges: \",\n                                    filter.exchange.join(\", \")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.instrumentType?.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Types: \",\n                                    filter.instrumentType.join(\", \")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.segment?.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Segments: \",\n                                    filter.segment.join(\", \")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.search && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Search: “\",\n                                    filter.search,\n                                    \"”\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Active instruments only\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.hasExpiry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"With expiry date\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, undefined),\n                            (filter.minLotSize || filter.maxLotSize) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Lot size: \",\n                                    filter.minLotSize || 0,\n                                    \" - \",\n                                    filter.maxLotSize || \"∞\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 220,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FilterPanel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FilterPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/InstrumentTable.tsx":
/*!********************************************!*\
  !*** ./src/components/InstrumentTable.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst InstrumentTable = ({ instruments, marketData, onInstrumentSelect, loading = false })=>{\n    const [sortField, setSortField] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"symbol\");\n    const [sortDirection, setSortDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"asc\");\n    // Sort instruments\n    const sortedInstruments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return [\n            ...instruments\n        ].sort((a, b)=>{\n            const aValue = a[sortField];\n            const bValue = b[sortField];\n            // Handle undefined values\n            if (aValue === undefined && bValue === undefined) return 0;\n            if (aValue === undefined) return 1;\n            if (bValue === undefined) return -1;\n            if (aValue === bValue) return 0;\n            const comparison = aValue < bValue ? -1 : 1;\n            return sortDirection === \"asc\" ? comparison : -comparison;\n        });\n    }, [\n        instruments,\n        sortField,\n        sortDirection\n    ]);\n    const handleSort = (field)=>{\n        if (sortField === field) {\n            setSortDirection(sortDirection === \"asc\" ? \"desc\" : \"asc\");\n        } else {\n            setSortField(field);\n            setSortDirection(\"asc\");\n        }\n    };\n    const formatPrice = (price)=>{\n        if (price === undefined || price === 0) return \"-\";\n        return `₹${price.toFixed(2)}`;\n    };\n    const formatChange = (change, changePercent)=>{\n        if (change === undefined || changePercent === undefined) return \"-\";\n        const sign = change >= 0 ? \"+\" : \"\";\n        return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`;\n    };\n    const getChangeColor = (change)=>{\n        if (change === undefined || change === 0) return \"text-gray-600\";\n        return change > 0 ? \"text-green-600\" : \"text-red-600\";\n    };\n    const formatVolume = (volume)=>{\n        if (volume === undefined || volume === 0) return \"-\";\n        if (volume >= 10000000) return `${(volume / 10000000).toFixed(1)}Cr`;\n        if (volume >= 100000) return `${(volume / 100000).toFixed(1)}L`;\n        if (volume >= 1000) return `${(volume / 1000).toFixed(1)}K`;\n        return volume.toString();\n    };\n    const SortIcon = ({ field })=>{\n        if (sortField !== field) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-gray-400\",\n                children: \"↕\"\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 68,\n                columnNumber: 14\n            }, undefined);\n        }\n        return sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-blue-600\",\n            children: \"↑\"\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 70,\n            columnNumber: 38\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-blue-600\",\n            children: \"↓\"\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 70,\n            columnNumber: 81\n        }, undefined);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"glass rounded-2xl shadow-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner w-8 h-8 mr-3\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading instruments...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (instruments.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"glass rounded-2xl shadow-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-lg\",\n                        children: \"No instruments found\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 text-sm mt-2\",\n                        children: \"Try adjusting your filters\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"glass rounded-2xl shadow-lg overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900\",\n                        children: \"Market Instruments\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-sm mt-1\",\n                        children: [\n                            \"Showing \",\n                            sortedInstruments.length,\n                            \" instruments\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto custom-scrollbar\",\n                style: {\n                    maxHeight: \"600px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"market-table\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"sticky top-0 bg-gray-50 z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort(\"securityId\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Security ID\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"securityId\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort(\"symbol\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Symbol\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"symbol\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort(\"displayName\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Name\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"displayName\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort(\"exchange\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Exchange\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"exchange\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort(\"instrumentType\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Type\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"instrumentType\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right\",\n                                        children: \"LTP\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right\",\n                                        children: \"Change\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right\",\n                                        children: \"Volume\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort(\"lotSize\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Lot Size\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"lotSize\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            children: sortedInstruments.map((instrument)=>{\n                                const data = marketData.get(instrument.securityId);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-gray-50 cursor-pointer transition-colors\",\n                                    onClick: ()=>onInstrumentSelect?.(instrument),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"font-mono text-sm text-gray-700\",\n                                            children: instrument.securityId\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"font-medium text-blue-600\",\n                                            children: instrument.symbol\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"max-w-xs truncate\",\n                                            title: instrument.displayName,\n                                            children: instrument.displayName\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                children: instrument.exchange\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                children: instrument.instrumentType\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"text-right font-medium\",\n                                            children: formatPrice(data?.ltp)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: `text-right font-medium ${getChangeColor(data?.change)}`,\n                                            children: formatChange(data?.change, data?.changePercent)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"text-right\",\n                                            children: formatVolume(data?.volume)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"text-right\",\n                                            children: instrument.lotSize.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, instrument.securityId, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, undefined),\n            sortedInstruments.length > 100 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-gray-50 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600 text-center\",\n                    children: \"Showing first 100 instruments. Use filters to narrow down results.\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InstrumentTable);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/InstrumentTable.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Stats.tsx":
/*!**********************************!*\
  !*** ./src/components/Stats.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Stats = ({ totalInstruments, filteredInstruments, marketDataCount, connected, connectionStats })=>{\n    const formatUptime = (seconds)=>{\n        if (seconds < 60) return `${seconds}s`;\n        if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;\n        const hours = Math.floor(seconds / 3600);\n        const minutes = Math.floor(seconds % 3600 / 60);\n        return `${hours}h ${minutes}m`;\n    };\n    const formatLastUpdate = (date)=>{\n        if (!date) return \"Never\";\n        const now = new Date();\n        const diffMs = now.getTime() - date.getTime();\n        const diffSeconds = Math.floor(diffMs / 1000);\n        if (diffSeconds < 60) return `${diffSeconds}s ago`;\n        if (diffSeconds < 3600) return `${Math.floor(diffSeconds / 60)}m ago`;\n        return date.toLocaleTimeString();\n    };\n    const basicStats = [\n        {\n            label: \"Total Instruments\",\n            value: totalInstruments.toLocaleString(),\n            icon: \"\\uD83D\\uDCCA\",\n            color: \"bg-blue-500\",\n            description: \"Available instruments\"\n        },\n        {\n            label: \"Filtered Results\",\n            value: filteredInstruments.toLocaleString(),\n            icon: \"\\uD83D\\uDD0D\",\n            color: \"bg-purple-500\",\n            description: \"Matching filters\"\n        },\n        {\n            label: \"Live Data\",\n            value: marketDataCount.toLocaleString(),\n            icon: \"\\uD83D\\uDCC8\",\n            color: connected ? \"bg-green-500\" : \"bg-gray-500\",\n            description: connectionStats ? `${connectionStats.connectedInstruments} active` : \"Market data points\"\n        },\n        {\n            label: \"Connection\",\n            value: connected ? \"Active\" : \"Inactive\",\n            icon: connected ? \"\\uD83D\\uDFE2\" : \"\\uD83D\\uDD34\",\n            color: connected ? \"bg-green-500\" : \"bg-red-500\",\n            description: connectionStats ? `${connectionStats.messagesReceived} messages` : \"WebSocket status\"\n        }\n    ];\n    const enhancedStats = connectionStats ? [\n        {\n            label: \"Cache Size\",\n            value: connectionStats.cacheSize.toLocaleString(),\n            icon: \"\\uD83D\\uDCBE\",\n            color: \"bg-indigo-500\",\n            description: connectionStats.isAutoSaving ? \"Auto-saving\" : \"Manual save\"\n        },\n        {\n            label: \"Last Update\",\n            value: formatLastUpdate(connectionStats.lastUpdate),\n            icon: \"\\uD83D\\uDD52\",\n            color: \"bg-orange-500\",\n            description: \"Data freshness\"\n        },\n        {\n            label: \"Uptime\",\n            value: formatUptime(connectionStats.connectionUptime),\n            icon: \"⏱️\",\n            color: \"bg-teal-500\",\n            description: \"Connection stability\"\n        },\n        {\n            label: \"Reconnects\",\n            value: connectionStats.reconnectAttempts.toString(),\n            icon: connectionStats.reconnectAttempts > 0 ? \"\\uD83D\\uDD04\" : \"✅\",\n            color: connectionStats.reconnectAttempts > 0 ? \"bg-yellow-500\" : \"bg-green-500\",\n            description: \"Connection reliability\"\n        }\n    ] : [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: basicStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glass rounded-xl shadow-lg p-4 card-hover\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: stat.label\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900 mt-1\",\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: stat.description\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl ml-3\",\n                                        children: stat.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `mt-3 h-1 rounded-full ${stat.color}`\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined),\n            enhancedStats.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800 mb-3 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl mr-2\",\n                                children: \"⚡\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Real-time Connection Stats\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: enhancedStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"glass rounded-xl shadow-lg p-4 card-hover border-l-4 border-l-blue-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: stat.label\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-gray-900 mt-1\",\n                                                        children: stat.value\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: stat.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl ml-3\",\n                                                children: stat.icon\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `mt-3 h-1 rounded-full ${stat.color}`\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Stats);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TdGF0cy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRTBCO0FBcUIxQixNQUFNQyxRQUE4QixDQUFDLEVBQ25DQyxnQkFBZ0IsRUFDaEJDLG1CQUFtQixFQUNuQkMsZUFBZSxFQUNmQyxTQUFTLEVBQ1RDLGVBQWUsRUFDaEI7SUFDQyxNQUFNQyxlQUFlLENBQUNDO1FBQ3BCLElBQUlBLFVBQVUsSUFBSSxPQUFPLENBQUMsRUFBRUEsUUFBUSxDQUFDLENBQUM7UUFDdEMsSUFBSUEsVUFBVSxNQUFNLE9BQU8sQ0FBQyxFQUFFQyxLQUFLQyxLQUFLLENBQUNGLFVBQVUsSUFBSSxFQUFFLEVBQUVBLFVBQVUsR0FBRyxDQUFDLENBQUM7UUFDMUUsTUFBTUcsUUFBUUYsS0FBS0MsS0FBSyxDQUFDRixVQUFVO1FBQ25DLE1BQU1JLFVBQVVILEtBQUtDLEtBQUssQ0FBQyxVQUFXLE9BQVE7UUFDOUMsT0FBTyxDQUFDLEVBQUVDLE1BQU0sRUFBRSxFQUFFQyxRQUFRLENBQUMsQ0FBQztJQUNoQztJQUVBLE1BQU1DLG1CQUFtQixDQUFDQztRQUN4QixJQUFJLENBQUNBLE1BQU0sT0FBTztRQUNsQixNQUFNQyxNQUFNLElBQUlDO1FBQ2hCLE1BQU1DLFNBQVNGLElBQUlHLE9BQU8sS0FBS0osS0FBS0ksT0FBTztRQUMzQyxNQUFNQyxjQUFjVixLQUFLQyxLQUFLLENBQUNPLFNBQVM7UUFFeEMsSUFBSUUsY0FBYyxJQUFJLE9BQU8sQ0FBQyxFQUFFQSxZQUFZLEtBQUssQ0FBQztRQUNsRCxJQUFJQSxjQUFjLE1BQU0sT0FBTyxDQUFDLEVBQUVWLEtBQUtDLEtBQUssQ0FBQ1MsY0FBYyxJQUFJLEtBQUssQ0FBQztRQUNyRSxPQUFPTCxLQUFLTSxrQkFBa0I7SUFDaEM7SUFFQSxNQUFNQyxhQUFhO1FBQ2pCO1lBQ0VDLE9BQU87WUFDUEMsT0FBT3JCLGlCQUFpQnNCLGNBQWM7WUFDdENDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxhQUFhO1FBQ2Y7UUFDQTtZQUNFTCxPQUFPO1lBQ1BDLE9BQU9wQixvQkFBb0JxQixjQUFjO1lBQ3pDQyxNQUFNO1lBQ05DLE9BQU87WUFDUEMsYUFBYTtRQUNmO1FBQ0E7WUFDRUwsT0FBTztZQUNQQyxPQUFPbkIsZ0JBQWdCb0IsY0FBYztZQUNyQ0MsTUFBTTtZQUNOQyxPQUFPckIsWUFBWSxpQkFBaUI7WUFDcENzQixhQUFhckIsa0JBQWtCLENBQUMsRUFBRUEsZ0JBQWdCc0Isb0JBQW9CLENBQUMsT0FBTyxDQUFDLEdBQUc7UUFDcEY7UUFDQTtZQUNFTixPQUFPO1lBQ1BDLE9BQU9sQixZQUFZLFdBQVc7WUFDOUJvQixNQUFNcEIsWUFBWSxpQkFBTztZQUN6QnFCLE9BQU9yQixZQUFZLGlCQUFpQjtZQUNwQ3NCLGFBQWFyQixrQkFBa0IsQ0FBQyxFQUFFQSxnQkFBZ0J1QixnQkFBZ0IsQ0FBQyxTQUFTLENBQUMsR0FBRztRQUNsRjtLQUNEO0lBRUQsTUFBTUMsZ0JBQWdCeEIsa0JBQWtCO1FBQ3RDO1lBQ0VnQixPQUFPO1lBQ1BDLE9BQU9qQixnQkFBZ0J5QixTQUFTLENBQUNQLGNBQWM7WUFDL0NDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxhQUFhckIsZ0JBQWdCMEIsWUFBWSxHQUFHLGdCQUFnQjtRQUM5RDtRQUNBO1lBQ0VWLE9BQU87WUFDUEMsT0FBT1YsaUJBQWlCUCxnQkFBZ0IyQixVQUFVO1lBQ2xEUixNQUFNO1lBQ05DLE9BQU87WUFDUEMsYUFBYTtRQUNmO1FBQ0E7WUFDRUwsT0FBTztZQUNQQyxPQUFPaEIsYUFBYUQsZ0JBQWdCNEIsZ0JBQWdCO1lBQ3BEVCxNQUFNO1lBQ05DLE9BQU87WUFDUEMsYUFBYTtRQUNmO1FBQ0E7WUFDRUwsT0FBTztZQUNQQyxPQUFPakIsZ0JBQWdCNkIsaUJBQWlCLENBQUNDLFFBQVE7WUFDakRYLE1BQU1uQixnQkFBZ0I2QixpQkFBaUIsR0FBRyxJQUFJLGlCQUFPO1lBQ3JEVCxPQUFPcEIsZ0JBQWdCNkIsaUJBQWlCLEdBQUcsSUFBSSxrQkFBa0I7WUFDakVSLGFBQWE7UUFDZjtLQUNELEdBQUcsRUFBRTtJQUVOLHFCQUNFLDhEQUFDVTtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ1pqQixXQUFXa0IsR0FBRyxDQUFDLENBQUNDLE1BQU1DLHNCQUNyQiw4REFBQ0o7d0JBQWdCQyxXQUFVOzswQ0FDekIsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDSTtnREFBRUosV0FBVTswREFBcUNFLEtBQUtsQixLQUFLOzs7Ozs7MERBQzVELDhEQUFDb0I7Z0RBQUVKLFdBQVU7MERBQXlDRSxLQUFLakIsS0FBSzs7Ozs7OzBEQUNoRSw4REFBQ21CO2dEQUFFSixXQUFVOzBEQUE4QkUsS0FBS2IsV0FBVzs7Ozs7Ozs7Ozs7O2tEQUU3RCw4REFBQ1U7d0NBQUlDLFdBQVU7a0RBQWlCRSxLQUFLZixJQUFJOzs7Ozs7Ozs7Ozs7MENBRTNDLDhEQUFDWTtnQ0FBSUMsV0FBVyxDQUFDLHNCQUFzQixFQUFFRSxLQUFLZCxLQUFLLENBQUMsQ0FBQzs7Ozs7Ozt1QkFUN0NlOzs7Ozs7Ozs7O1lBZWJYLGNBQWNhLE1BQU0sR0FBRyxtQkFDdEIsOERBQUNOOztrQ0FDQyw4REFBQ087d0JBQUdOLFdBQVU7OzBDQUNaLDhEQUFDTztnQ0FBS1AsV0FBVTswQ0FBZTs7Ozs7OzRCQUFROzs7Ozs7O2tDQUd6Qyw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ1pSLGNBQWNTLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxzQkFDeEIsOERBQUNKO2dDQUFnQkMsV0FBVTs7a0RBQ3pCLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0k7d0RBQUVKLFdBQVU7a0VBQXFDRSxLQUFLbEIsS0FBSzs7Ozs7O2tFQUM1RCw4REFBQ29CO3dEQUFFSixXQUFVO2tFQUF3Q0UsS0FBS2pCLEtBQUs7Ozs7OztrRUFDL0QsOERBQUNtQjt3REFBRUosV0FBVTtrRUFBOEJFLEtBQUtiLFdBQVc7Ozs7Ozs7Ozs7OzswREFFN0QsOERBQUNVO2dEQUFJQyxXQUFVOzBEQUFnQkUsS0FBS2YsSUFBSTs7Ozs7Ozs7Ozs7O2tEQUUxQyw4REFBQ1k7d0NBQUlDLFdBQVcsQ0FBQyxzQkFBc0IsRUFBRUUsS0FBS2QsS0FBSyxDQUFDLENBQUM7Ozs7Ozs7K0JBVDdDZTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQWlCeEI7QUFFQSxpRUFBZXhDLEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jc3YtbWFya2V0LWRhc2hib2FyZC8uL3NyYy9jb21wb25lbnRzL1N0YXRzLnRzeD9kNTI4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5cclxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcclxuXHJcbmludGVyZmFjZSBDb25uZWN0aW9uU3RhdHMge1xyXG4gIHRvdGFsSW5zdHJ1bWVudHM6IG51bWJlcjtcclxuICBjb25uZWN0ZWRJbnN0cnVtZW50czogbnVtYmVyO1xyXG4gIGxhc3RVcGRhdGU6IERhdGUgfCBudWxsO1xyXG4gIGNhY2hlU2l6ZTogbnVtYmVyO1xyXG4gIGNvbm5lY3Rpb25VcHRpbWU6IG51bWJlcjtcclxuICBtZXNzYWdlc1JlY2VpdmVkOiBudW1iZXI7XHJcbiAgcmVjb25uZWN0QXR0ZW1wdHM6IG51bWJlcjtcclxuICBpc0F1dG9TYXZpbmc6IGJvb2xlYW47XHJcbn1cclxuXHJcbmludGVyZmFjZSBTdGF0c1Byb3BzIHtcclxuICB0b3RhbEluc3RydW1lbnRzOiBudW1iZXI7XHJcbiAgZmlsdGVyZWRJbnN0cnVtZW50czogbnVtYmVyO1xyXG4gIG1hcmtldERhdGFDb3VudDogbnVtYmVyO1xyXG4gIGNvbm5lY3RlZDogYm9vbGVhbjtcclxuICBjb25uZWN0aW9uU3RhdHM/OiBDb25uZWN0aW9uU3RhdHM7XHJcbn1cclxuXHJcbmNvbnN0IFN0YXRzOiBSZWFjdC5GQzxTdGF0c1Byb3BzPiA9ICh7XHJcbiAgdG90YWxJbnN0cnVtZW50cyxcclxuICBmaWx0ZXJlZEluc3RydW1lbnRzLFxyXG4gIG1hcmtldERhdGFDb3VudCxcclxuICBjb25uZWN0ZWQsXHJcbiAgY29ubmVjdGlvblN0YXRzLFxyXG59KSA9PiB7XHJcbiAgY29uc3QgZm9ybWF0VXB0aW1lID0gKHNlY29uZHM6IG51bWJlcik6IHN0cmluZyA9PiB7XHJcbiAgICBpZiAoc2Vjb25kcyA8IDYwKSByZXR1cm4gYCR7c2Vjb25kc31zYDtcclxuICAgIGlmIChzZWNvbmRzIDwgMzYwMCkgcmV0dXJuIGAke01hdGguZmxvb3Ioc2Vjb25kcyAvIDYwKX1tICR7c2Vjb25kcyAlIDYwfXNgO1xyXG4gICAgY29uc3QgaG91cnMgPSBNYXRoLmZsb29yKHNlY29uZHMgLyAzNjAwKTtcclxuICAgIGNvbnN0IG1pbnV0ZXMgPSBNYXRoLmZsb29yKChzZWNvbmRzICUgMzYwMCkgLyA2MCk7XHJcbiAgICByZXR1cm4gYCR7aG91cnN9aCAke21pbnV0ZXN9bWA7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZm9ybWF0TGFzdFVwZGF0ZSA9IChkYXRlOiBEYXRlIHwgbnVsbCk6IHN0cmluZyA9PiB7XHJcbiAgICBpZiAoIWRhdGUpIHJldHVybiAnTmV2ZXInO1xyXG4gICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTtcclxuICAgIGNvbnN0IGRpZmZNcyA9IG5vdy5nZXRUaW1lKCkgLSBkYXRlLmdldFRpbWUoKTtcclxuICAgIGNvbnN0IGRpZmZTZWNvbmRzID0gTWF0aC5mbG9vcihkaWZmTXMgLyAxMDAwKTtcclxuICAgIFxyXG4gICAgaWYgKGRpZmZTZWNvbmRzIDwgNjApIHJldHVybiBgJHtkaWZmU2Vjb25kc31zIGFnb2A7XHJcbiAgICBpZiAoZGlmZlNlY29uZHMgPCAzNjAwKSByZXR1cm4gYCR7TWF0aC5mbG9vcihkaWZmU2Vjb25kcyAvIDYwKX1tIGFnb2A7XHJcbiAgICByZXR1cm4gZGF0ZS50b0xvY2FsZVRpbWVTdHJpbmcoKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBiYXNpY1N0YXRzID0gW1xyXG4gICAge1xyXG4gICAgICBsYWJlbDogJ1RvdGFsIEluc3RydW1lbnRzJyxcclxuICAgICAgdmFsdWU6IHRvdGFsSW5zdHJ1bWVudHMudG9Mb2NhbGVTdHJpbmcoKSxcclxuICAgICAgaWNvbjogJ/Cfk4onLFxyXG4gICAgICBjb2xvcjogJ2JnLWJsdWUtNTAwJyxcclxuICAgICAgZGVzY3JpcHRpb246ICdBdmFpbGFibGUgaW5zdHJ1bWVudHMnLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgbGFiZWw6ICdGaWx0ZXJlZCBSZXN1bHRzJyxcclxuICAgICAgdmFsdWU6IGZpbHRlcmVkSW5zdHJ1bWVudHMudG9Mb2NhbGVTdHJpbmcoKSxcclxuICAgICAgaWNvbjogJ/CflI0nLFxyXG4gICAgICBjb2xvcjogJ2JnLXB1cnBsZS01MDAnLFxyXG4gICAgICBkZXNjcmlwdGlvbjogJ01hdGNoaW5nIGZpbHRlcnMnLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgbGFiZWw6ICdMaXZlIERhdGEnLFxyXG4gICAgICB2YWx1ZTogbWFya2V0RGF0YUNvdW50LnRvTG9jYWxlU3RyaW5nKCksXHJcbiAgICAgIGljb246ICfwn5OIJyxcclxuICAgICAgY29sb3I6IGNvbm5lY3RlZCA/ICdiZy1ncmVlbi01MDAnIDogJ2JnLWdyYXktNTAwJyxcclxuICAgICAgZGVzY3JpcHRpb246IGNvbm5lY3Rpb25TdGF0cyA/IGAke2Nvbm5lY3Rpb25TdGF0cy5jb25uZWN0ZWRJbnN0cnVtZW50c30gYWN0aXZlYCA6ICdNYXJrZXQgZGF0YSBwb2ludHMnLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgbGFiZWw6ICdDb25uZWN0aW9uJyxcclxuICAgICAgdmFsdWU6IGNvbm5lY3RlZCA/ICdBY3RpdmUnIDogJ0luYWN0aXZlJyxcclxuICAgICAgaWNvbjogY29ubmVjdGVkID8gJ/Cfn6InIDogJ/CflLQnLFxyXG4gICAgICBjb2xvcjogY29ubmVjdGVkID8gJ2JnLWdyZWVuLTUwMCcgOiAnYmctcmVkLTUwMCcsXHJcbiAgICAgIGRlc2NyaXB0aW9uOiBjb25uZWN0aW9uU3RhdHMgPyBgJHtjb25uZWN0aW9uU3RhdHMubWVzc2FnZXNSZWNlaXZlZH0gbWVzc2FnZXNgIDogJ1dlYlNvY2tldCBzdGF0dXMnLFxyXG4gICAgfSxcclxuICBdO1xyXG5cclxuICBjb25zdCBlbmhhbmNlZFN0YXRzID0gY29ubmVjdGlvblN0YXRzID8gW1xyXG4gICAge1xyXG4gICAgICBsYWJlbDogJ0NhY2hlIFNpemUnLFxyXG4gICAgICB2YWx1ZTogY29ubmVjdGlvblN0YXRzLmNhY2hlU2l6ZS50b0xvY2FsZVN0cmluZygpLFxyXG4gICAgICBpY29uOiAn8J+SvicsXHJcbiAgICAgIGNvbG9yOiAnYmctaW5kaWdvLTUwMCcsXHJcbiAgICAgIGRlc2NyaXB0aW9uOiBjb25uZWN0aW9uU3RhdHMuaXNBdXRvU2F2aW5nID8gJ0F1dG8tc2F2aW5nJyA6ICdNYW51YWwgc2F2ZScsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBsYWJlbDogJ0xhc3QgVXBkYXRlJyxcclxuICAgICAgdmFsdWU6IGZvcm1hdExhc3RVcGRhdGUoY29ubmVjdGlvblN0YXRzLmxhc3RVcGRhdGUpLFxyXG4gICAgICBpY29uOiAn8J+VkicsXHJcbiAgICAgIGNvbG9yOiAnYmctb3JhbmdlLTUwMCcsXHJcbiAgICAgIGRlc2NyaXB0aW9uOiAnRGF0YSBmcmVzaG5lc3MnLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgbGFiZWw6ICdVcHRpbWUnLFxyXG4gICAgICB2YWx1ZTogZm9ybWF0VXB0aW1lKGNvbm5lY3Rpb25TdGF0cy5jb25uZWN0aW9uVXB0aW1lKSxcclxuICAgICAgaWNvbjogJ+KPse+4jycsXHJcbiAgICAgIGNvbG9yOiAnYmctdGVhbC01MDAnLFxyXG4gICAgICBkZXNjcmlwdGlvbjogJ0Nvbm5lY3Rpb24gc3RhYmlsaXR5JyxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGxhYmVsOiAnUmVjb25uZWN0cycsXHJcbiAgICAgIHZhbHVlOiBjb25uZWN0aW9uU3RhdHMucmVjb25uZWN0QXR0ZW1wdHMudG9TdHJpbmcoKSxcclxuICAgICAgaWNvbjogY29ubmVjdGlvblN0YXRzLnJlY29ubmVjdEF0dGVtcHRzID4gMCA/ICfwn5SEJyA6ICfinIUnLFxyXG4gICAgICBjb2xvcjogY29ubmVjdGlvblN0YXRzLnJlY29ubmVjdEF0dGVtcHRzID4gMCA/ICdiZy15ZWxsb3ctNTAwJyA6ICdiZy1ncmVlbi01MDAnLFxyXG4gICAgICBkZXNjcmlwdGlvbjogJ0Nvbm5lY3Rpb24gcmVsaWFiaWxpdHknLFxyXG4gICAgfSxcclxuICBdIDogW107XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNiBtYi02XCI+XHJcbiAgICAgIHsvKiBCYXNpYyBTdGF0cyAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC00XCI+XHJcbiAgICAgICAge2Jhc2ljU3RhdHMubWFwKChzdGF0LCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJnbGFzcyByb3VuZGVkLXhsIHNoYWRvdy1sZyBwLTQgY2FyZC1ob3ZlclwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDBcIj57c3RhdC5sYWJlbH08L3A+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtdC0xXCI+e3N0YXQudmFsdWV9PC9wPlxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIG10LTFcIj57c3RhdC5kZXNjcmlwdGlvbn08L3A+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBtbC0zXCI+e3N0YXQuaWNvbn08L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgbXQtMyBoLTEgcm91bmRlZC1mdWxsICR7c3RhdC5jb2xvcn1gfSAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKSl9XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIEVuaGFuY2VkIFN0YXRzICh3aGVuIGF2YWlsYWJsZSkgKi99XHJcbiAgICAgIHtlbmhhbmNlZFN0YXRzLmxlbmd0aCA+IDAgJiYgKFxyXG4gICAgICAgIDxkaXY+XHJcbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS04MDAgbWItMyBmbGV4IGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhsIG1yLTJcIj7imqE8L3NwYW4+XHJcbiAgICAgICAgICAgIFJlYWwtdGltZSBDb25uZWN0aW9uIFN0YXRzXHJcbiAgICAgICAgICA8L2gzPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC00XCI+XHJcbiAgICAgICAgICAgIHtlbmhhbmNlZFN0YXRzLm1hcCgoc3RhdCwgaW5kZXgpID0+IChcclxuICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImdsYXNzIHJvdW5kZWQteGwgc2hhZG93LWxnIHAtNCBjYXJkLWhvdmVyIGJvcmRlci1sLTQgYm9yZGVyLWwtYmx1ZS00MDBcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwXCI+e3N0YXQubGFiZWx9PC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbXQtMVwiPntzdGF0LnZhbHVlfTwvcD5cclxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMVwiPntzdGF0LmRlc2NyaXB0aW9ufTwvcD5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14bCBtbC0zXCI+e3N0YXQuaWNvbn08L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BtdC0zIGgtMSByb3VuZGVkLWZ1bGwgJHtzdGF0LmNvbG9yfWB9IC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICl9XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgU3RhdHM7XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlN0YXRzIiwidG90YWxJbnN0cnVtZW50cyIsImZpbHRlcmVkSW5zdHJ1bWVudHMiLCJtYXJrZXREYXRhQ291bnQiLCJjb25uZWN0ZWQiLCJjb25uZWN0aW9uU3RhdHMiLCJmb3JtYXRVcHRpbWUiLCJzZWNvbmRzIiwiTWF0aCIsImZsb29yIiwiaG91cnMiLCJtaW51dGVzIiwiZm9ybWF0TGFzdFVwZGF0ZSIsImRhdGUiLCJub3ciLCJEYXRlIiwiZGlmZk1zIiwiZ2V0VGltZSIsImRpZmZTZWNvbmRzIiwidG9Mb2NhbGVUaW1lU3RyaW5nIiwiYmFzaWNTdGF0cyIsImxhYmVsIiwidmFsdWUiLCJ0b0xvY2FsZVN0cmluZyIsImljb24iLCJjb2xvciIsImRlc2NyaXB0aW9uIiwiY29ubmVjdGVkSW5zdHJ1bWVudHMiLCJtZXNzYWdlc1JlY2VpdmVkIiwiZW5oYW5jZWRTdGF0cyIsImNhY2hlU2l6ZSIsImlzQXV0b1NhdmluZyIsImxhc3RVcGRhdGUiLCJjb25uZWN0aW9uVXB0aW1lIiwicmVjb25uZWN0QXR0ZW1wdHMiLCJ0b1N0cmluZyIsImRpdiIsImNsYXNzTmFtZSIsIm1hcCIsInN0YXQiLCJpbmRleCIsInAiLCJsZW5ndGgiLCJoMyIsInNwYW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Stats.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useEnhancedMarketData.ts":
/*!********************************************!*\
  !*** ./src/hooks/useEnhancedMarketData.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useEnhancedMarketData: () => (/* binding */ useEnhancedMarketData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * Enhanced Market Data Hook\r\n * Provides optimized access to market data with automatic initialization\r\n * Features:\r\n * - Automatic WebSocket connection management\r\n * - Optimized re-rendering with selective subscriptions\r\n * - Auto-save functionality\r\n * - Error recovery and reconnection\r\n */ \nconst useEnhancedMarketData = (options = {})=>{\n    const { autoConnect = true, autoLoadCache = true, autoSaveInterval = 30000, reconnectOnError = true, maxReconnectAttempts = 5 } = options;\n    // Temporary fallback implementation\n    const connection = {\n        status: \"disconnected\",\n        isConnected: false,\n        error: null,\n        connectionStats: {\n            totalMessages: 0,\n            connectionUptime: 0\n        }\n    };\n    const marketDataMap = new Map();\n    const cache = {\n        isLoaded: false,\n        totalCacheSize: 0,\n        pendingUpdates: 0,\n        lastCacheUpdate: null\n    };\n    const ui = {\n        filters: {},\n        sortConfig: {\n            field: \"symbol\",\n            direction: \"asc\"\n        },\n        selectedInstruments: new Set(),\n        viewMode: \"table\",\n        autoRefresh: true\n    };\n    const isLoading = false;\n    // Store actions - temporary mock functions with useCallback to fix dependency warnings\n    const initializeConnection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{}, []);\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{}, []);\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{}, []);\n    const reconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{}, []);\n    const loadFromCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{}, []);\n    const saveToCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (force)=>{}, []);\n    const clearCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{}, []);\n    const setFilters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((filters)=>{}, []);\n    const setSortConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((field, direction)=>{}, []);\n    const getFilteredMarketData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>[], []);\n    const getSortedMarketData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>[], []);\n    const getMarketDataBySecurityId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((securityId)=>undefined, []);\n    const getMarketDataBySymbol = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((symbol)=>undefined, []);\n    const subscribeToInstrument = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((securityId)=>{}, []);\n    const unsubscribeFromInstrument = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((securityId)=>{}, []);\n    const reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{}, []);\n    // Refs for intervals and tracking\n    const autoSaveIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const initializationRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Convert Map to Array for components that expect arrays\n    const marketDataArray = Array.from(marketDataMap.values());\n    // Initialize connection and cache on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const initialize = async ()=>{\n            if (initializationRef.current) return;\n            initializationRef.current = true;\n            console.log(\"\\uD83D\\uDE80 Enhanced Hook: Initializing...\");\n            try {\n                // Load cache first for instant data display\n                if (autoLoadCache && !cache.isLoaded) {\n                    await loadFromCache();\n                }\n                // Initialize WebSocket connection\n                if (autoConnect && connection.status === \"disconnected\") {\n                    await initializeConnection();\n                }\n            } catch (error) {\n                console.error(\"❌ Enhanced Hook: Initialization failed:\", error);\n            }\n        };\n        initialize();\n        // Cleanup on unmount\n        return ()=>{\n            if (autoSaveIntervalRef.current) {\n                clearInterval(autoSaveIntervalRef.current);\n            }\n        };\n    }, [\n        autoConnect,\n        autoLoadCache,\n        cache.isLoaded,\n        connection.status,\n        initializeConnection,\n        loadFromCache\n    ]);\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (autoSaveInterval > 0 && marketDataArray.length > 0) {\n            if (autoSaveIntervalRef.current) {\n                clearInterval(autoSaveIntervalRef.current);\n            }\n            autoSaveIntervalRef.current = setInterval(()=>{\n                saveToCache();\n            }, autoSaveInterval);\n            return ()=>{\n                if (autoSaveIntervalRef.current) {\n                    clearInterval(autoSaveIntervalRef.current);\n                }\n            };\n        }\n    }, [\n        autoSaveInterval,\n        marketDataArray.length,\n        saveToCache\n    ]);\n    // Auto-reconnect on error\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (reconnectOnError && connection.status === \"error\" && reconnectAttemptsRef.current < maxReconnectAttempts) {\n            const retryDelay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);\n            console.log(`🔄 Enhanced Hook: Auto-reconnecting in ${retryDelay}ms (attempt ${reconnectAttemptsRef.current + 1}/${maxReconnectAttempts})`);\n            const timeoutId = setTimeout(async ()=>{\n                try {\n                    reconnectAttemptsRef.current++;\n                    await reconnect();\n                } catch (error) {\n                    console.error(\"❌ Enhanced Hook: Auto-reconnect failed:\", error);\n                }\n            }, retryDelay);\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        connection.status,\n        reconnect,\n        reconnectOnError,\n        maxReconnectAttempts\n    ]);\n    // Reset reconnect attempts on successful connection\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (connection.status === \"connected\") {\n            reconnectAttemptsRef.current = 0;\n        }\n    }, [\n        connection.status\n    ]);\n    // Optimized data access functions\n    const getFilteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        return getFilteredMarketData();\n    }, [\n        getFilteredMarketData\n    ]);\n    const getSortedData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        return getSortedMarketData();\n    }, [\n        getSortedMarketData\n    ]);\n    const getDataBySecurityId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((securityId)=>{\n        return getMarketDataBySecurityId(securityId);\n    }, [\n        getMarketDataBySecurityId\n    ]);\n    const getDataBySymbol = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((symbol)=>{\n        return getMarketDataBySymbol(symbol);\n    }, [\n        getMarketDataBySymbol\n    ]);\n    // Enhanced filter functions\n    const updateFilters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((filters)=>{\n        setFilters(filters);\n    }, [\n        setFilters,\n        ui\n    ]);\n    const updateSort = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((field, direction)=>{\n        setSortConfig(field, direction);\n    }, [\n        setSortConfig\n    ]);\n    // Subscription management\n    const subscribe = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((securityId)=>{\n        subscribeToInstrument(securityId);\n    }, [\n        subscribeToInstrument\n    ]);\n    const unsubscribe = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((securityId)=>{\n        unsubscribeFromInstrument(securityId);\n    }, [\n        unsubscribeFromInstrument\n    ]);\n    // Connection management\n    const forceReconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        try {\n            reconnectAttemptsRef.current = 0; // Reset attempts for manual reconnect\n            await reconnect();\n        } catch (error) {\n            console.error(\"❌ Enhanced Hook: Manual reconnect failed:\", error);\n            throw error;\n        }\n    }, [\n        reconnect\n    ]);\n    const forceRefresh = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        try {\n            await loadFromCache();\n        } catch (error) {\n            console.error(\"❌ Enhanced Hook: Force refresh failed:\", error);\n            throw error;\n        }\n    }, [\n        loadFromCache\n    ]);\n    const forceSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        try {\n            await saveToCache(true);\n        } catch (error) {\n            console.error(\"❌ Enhanced Hook: Force save failed:\", error);\n            throw error;\n        }\n    }, [\n        saveToCache\n    ]);\n    // Stats and computed values\n    const stats = {\n        totalInstruments: marketDataArray.length,\n        connectedInstruments: marketDataArray.filter((item)=>item.ltp && item.ltp > 0).length,\n        lastUpdate: null,\n        cacheSize: cache.totalCacheSize,\n        connectionUptime: connection.connectionStats.connectionUptime,\n        messagesReceived: connection.connectionStats.totalMessages,\n        reconnectAttempts: reconnectAttemptsRef.current,\n        isAutoSaving: autoSaveIntervalRef.current !== null\n    };\n    return {\n        // Data\n        marketData: marketDataArray,\n        marketDataMap,\n        filteredData: getFilteredData(),\n        sortedData: getSortedData(),\n        // Connection state\n        isConnected: connection.isConnected,\n        connectionStatus: connection.status,\n        connectionError: connection.error,\n        connectionStats: connection.connectionStats,\n        // Cache state\n        cacheLoaded: cache.isLoaded,\n        cacheUpdating: cache.pendingUpdates > 0,\n        lastCacheUpdate: cache.lastCacheUpdate,\n        // UI state\n        filters: ui.filters,\n        sortConfig: ui.sortConfig,\n        selectedInstruments: ui.selectedInstruments,\n        viewMode: ui.viewMode,\n        autoRefresh: ui.autoRefresh,\n        // Loading states\n        isLoading,\n        isInitializing: !initializationRef.current,\n        // Data access functions\n        getDataBySecurityId,\n        getDataBySymbol,\n        getFilteredData,\n        getSortedData,\n        // Actions\n        updateFilters,\n        updateSort,\n        subscribe,\n        unsubscribe,\n        // Connection management\n        connect,\n        disconnect,\n        reconnect: forceReconnect,\n        // Cache management\n        refresh: forceRefresh,\n        save: forceSave,\n        clearCache,\n        // Utility\n        reset,\n        stats,\n        // Advanced actions (expose if needed)\n        _store: {\n            setFilters,\n            setSortConfig,\n            subscribeToInstrument,\n            unsubscribeFromInstrument\n        }\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useEnhancedMarketData);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useEnhancedMarketData.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/constants.ts":
/*!******************************!*\
  !*** ./src/lib/constants.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: () => (/* binding */ API_CONFIG),\n/* harmony export */   CACHE_CONFIG: () => (/* binding */ CACHE_CONFIG),\n/* harmony export */   CHART_CONFIG: () => (/* binding */ CHART_CONFIG),\n/* harmony export */   COLORS: () => (/* binding */ COLORS),\n/* harmony export */   DEFAULTS: () => (/* binding */ DEFAULTS),\n/* harmony export */   DHAN_CONFIG: () => (/* binding */ DHAN_CONFIG),\n/* harmony export */   ENV_CONFIG: () => (/* binding */ ENV_CONFIG),\n/* harmony export */   ERROR_MESSAGES: () => (/* binding */ ERROR_MESSAGES),\n/* harmony export */   EXCHANGE_SEGMENTS: () => (/* binding */ EXCHANGE_SEGMENTS),\n/* harmony export */   FEATURE_FLAGS: () => (/* binding */ FEATURE_FLAGS),\n/* harmony export */   INSTRUMENT_TYPES: () => (/* binding */ INSTRUMENT_TYPES),\n/* harmony export */   LOGGING_CONFIG: () => (/* binding */ LOGGING_CONFIG),\n/* harmony export */   MARKET_CONFIG: () => (/* binding */ MARKET_CONFIG),\n/* harmony export */   OPTION_TYPES: () => (/* binding */ OPTION_TYPES),\n/* harmony export */   PACKET_TYPES: () => (/* binding */ PACKET_TYPES),\n/* harmony export */   PERFORMANCE_CONFIG: () => (/* binding */ PERFORMANCE_CONFIG),\n/* harmony export */   REQUEST_CODES: () => (/* binding */ REQUEST_CODES),\n/* harmony export */   SECURITY_CONFIG: () => (/* binding */ SECURITY_CONFIG),\n/* harmony export */   STORAGE_KEYS: () => (/* binding */ STORAGE_KEYS),\n/* harmony export */   SUCCESS_MESSAGES: () => (/* binding */ SUCCESS_MESSAGES),\n/* harmony export */   UI_CONFIG: () => (/* binding */ UI_CONFIG),\n/* harmony export */   VALIDATION: () => (/* binding */ VALIDATION),\n/* harmony export */   WEBSOCKET_CONFIG: () => (/* binding */ WEBSOCKET_CONFIG)\n/* harmony export */ });\n/**\r\n * Application Constants\r\n */ // API Configuration\nconst API_CONFIG = {\n    BASE_URL: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8080\",\n    TIMEOUT: 30000,\n    RETRY_ATTEMPTS: 3,\n    RETRY_DELAY: 1000\n};\n// WebSocket Configuration\nconst WEBSOCKET_CONFIG = {\n    RECONNECT_INTERVAL: 3000,\n    MAX_RECONNECT_ATTEMPTS: 15,\n    PING_INTERVAL: 25000,\n    PONG_TIMEOUT: 10000,\n    CONNECTION_TIMEOUT: 20000,\n    HEARTBEAT_INTERVAL: 30000,\n    MAX_LISTENERS_PER_EVENT: 10,\n    CLEANUP_INTERVAL: 60000\n};\n// Dhan API Configuration\nconst DHAN_CONFIG = {\n    BASE_URL: \"https://api.dhan.co\",\n    WEBSOCKET_URL: \"wss://api.dhan.co/v2/wsapi\",\n    MAX_INSTRUMENTS_PER_CONNECTION: 5000,\n    MAX_INSTRUMENTS_PER_MESSAGE: 100,\n    RATE_LIMIT: {\n        REQUESTS_PER_SECOND: 10,\n        REQUESTS_PER_MINUTE: 600\n    }\n};\n// Market Configuration\nconst MARKET_CONFIG = {\n    TRADING_HOURS: {\n        START: {\n            hour: 9,\n            minute: 15\n        },\n        END: {\n            hour: 15,\n            minute: 30\n        }\n    },\n    TRADING_DAYS: [\n        1,\n        2,\n        3,\n        4,\n        5\n    ],\n    REFRESH_INTERVAL: 1000,\n    BATCH_SIZE: 100\n};\n// Instrument Types\nconst INSTRUMENT_TYPES = {\n    EQUITY: \"EQUITY\",\n    INDEX: \"INDEX\",\n    FUTIDX: \"FUTIDX\",\n    OPTIDX: \"OPTIDX\",\n    FUTSTK: \"FUTSTK\",\n    OPTSTK: \"OPTSTK\",\n    FUTCUR: \"FUTCUR\",\n    OPTCUR: \"OPTCUR\",\n    FUTCOM: \"FUTCOM\",\n    OPTFUT: \"OPTFUT\"\n};\n// Exchange Segments\nconst EXCHANGE_SEGMENTS = {\n    NSE_EQ: \"NSE_EQ\",\n    NSE_FNO: \"NSE_FNO\",\n    BSE_EQ: \"BSE_EQ\",\n    MCX_COMM: \"MCX_COMM\",\n    IDX_I: \"IDX_I\"\n};\n// Option Types\nconst OPTION_TYPES = {\n    CALL: \"CE\",\n    PUT: \"PE\"\n};\n// Market Data Packet Types\nconst PACKET_TYPES = {\n    TOUCHLINE: 1,\n    QUOTE: 2,\n    SNAP_QUOTE: 3,\n    FULL_PACKET: 4,\n    OI: 5\n};\n// WebSocket Request Codes\nconst REQUEST_CODES = {\n    SUBSCRIBE: 21,\n    UNSUBSCRIBE: 22,\n    SNAP_QUOTE: 23\n};\n// UI Configuration\nconst UI_CONFIG = {\n    DEBOUNCE_DELAY: 300,\n    THROTTLE_DELAY: 100,\n    ANIMATION_DURATION: 200,\n    TOAST_DURATION: 3000,\n    PAGINATION: {\n        DEFAULT_PAGE_SIZE: 50,\n        PAGE_SIZE_OPTIONS: [\n            25,\n            50,\n            100,\n            200\n        ]\n    }\n};\n// Color Schemes\nconst COLORS = {\n    SUCCESS: \"#10B981\",\n    ERROR: \"#EF4444\",\n    WARNING: \"#F59E0B\",\n    INFO: \"#3B82F6\",\n    NEUTRAL: \"#6B7280\",\n    BID: \"#10B981\",\n    ASK: \"#EF4444\",\n    SPOT: \"#3B82F6\"\n};\n// Chart Configuration\nconst CHART_CONFIG = {\n    DEFAULT_HEIGHT: 400,\n    COLORS: {\n        PRIMARY: \"#3B82F6\",\n        SECONDARY: \"#10B981\",\n        ACCENT: \"#F59E0B\",\n        GRID: \"#E5E7EB\",\n        TEXT: \"#374151\"\n    },\n    ANIMATION: {\n        DURATION: 300,\n        EASING: \"ease-in-out\"\n    }\n};\n// Data Validation\nconst VALIDATION = {\n    MIN_STRIKE_PRICE: 1,\n    MAX_STRIKE_PRICE: 100000,\n    MIN_OPTION_PRICE: 0.01,\n    MAX_OPTION_PRICE: 50000,\n    MIN_VOLUME: 0,\n    MAX_VOLUME: 10000000\n};\n// Error Messages\nconst ERROR_MESSAGES = {\n    NETWORK_ERROR: \"Network connection failed. Please check your internet connection.\",\n    API_ERROR: \"API request failed. Please try again later.\",\n    WEBSOCKET_ERROR: \"WebSocket connection failed. Attempting to reconnect...\",\n    DATA_PARSING_ERROR: \"Failed to parse market data. Please refresh the page.\",\n    SUBSCRIPTION_ERROR: \"Failed to subscribe to market data. Please try again.\",\n    INVALID_INSTRUMENT: \"Invalid instrument selected.\",\n    MARKET_CLOSED: \"Market is currently closed.\",\n    RATE_LIMIT_EXCEEDED: \"Too many requests. Please wait before trying again.\"\n};\n// Success Messages\nconst SUCCESS_MESSAGES = {\n    CONNECTION_ESTABLISHED: \"Successfully connected to market data feed.\",\n    SUBSCRIPTION_SUCCESS: \"Successfully subscribed to market data.\",\n    DATA_UPDATED: \"Market data updated successfully.\",\n    SETTINGS_SAVED: \"Settings saved successfully.\"\n};\n// Local Storage Keys\nconst STORAGE_KEYS = {\n    USER_PREFERENCES: \"csv_market_dashboard_preferences\",\n    SELECTED_INSTRUMENTS: \"csv_market_dashboard_selected_instruments\",\n    THEME: \"csv_market_dashboard_theme\",\n    LAYOUT: \"csv_market_dashboard_layout\"\n};\n// Feature Flags\nconst FEATURE_FLAGS = {\n    ENABLE_CHARTS: true,\n    ENABLE_ALERTS: true,\n    ENABLE_EXPORT: true,\n    ENABLE_DARK_MODE: true,\n    ENABLE_REAL_TIME_UPDATES: true,\n    ENABLE_OPTION_GREEKS: false\n};\n// Performance Monitoring\nconst PERFORMANCE_CONFIG = {\n    MEMORY_WARNING_THRESHOLD: 100 * 1024 * 1024,\n    CPU_WARNING_THRESHOLD: 80,\n    NETWORK_TIMEOUT_WARNING: 5000,\n    MAX_CONCURRENT_REQUESTS: 10\n};\n// Logging Configuration\nconst LOGGING_CONFIG = {\n    LEVELS: {\n        ERROR: 0,\n        WARN: 1,\n        INFO: 2,\n        DEBUG: 3\n    },\n    MAX_LOG_SIZE: 10 * 1024 * 1024,\n    MAX_LOG_FILES: 5,\n    LOG_ROTATION_INTERVAL: 24 * 60 * 60 * 1000\n};\n// Cache Configuration\nconst CACHE_CONFIG = {\n    DEFAULT_TTL: 5 * 60 * 1000,\n    MARKET_DATA_TTL: 10 * 60 * 1000,\n    STATIC_DATA_TTL: 30 * 60 * 1000,\n    EXPIRY_CHECK_INTERVAL: 60 * 1000,\n    MAX_CACHE_SIZE: 50 * 1024 * 1024,\n    KEYS: {\n        INSTRUMENTS: \"instruments\",\n        MARKET_DATA: \"market_data\",\n        OPTION_CHAIN: \"option_chain\",\n        EXPIRY_DATES: \"expiry_dates\",\n        NIFTY_SPOT: \"nifty_spot\",\n        USER_SETTINGS: \"user_settings\",\n        USER_PREFERENCES: \"user_preferences\"\n    }\n};\n// Security Configuration\nconst SECURITY_CONFIG = {\n    RATE_LIMITING: {\n        WINDOW_MS: 15 * 60 * 1000,\n        MAX_REQUESTS: 1000\n    },\n    CORS: {\n        ORIGIN:  false ? 0 : [\n            \"http://localhost:3000\",\n            \"http://localhost:3001\"\n        ],\n        CREDENTIALS: true\n    },\n    HEADERS: {\n        CONTENT_SECURITY_POLICY: \"default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';\",\n        X_FRAME_OPTIONS: \"DENY\",\n        X_CONTENT_TYPE_OPTIONS: \"nosniff\"\n    }\n};\n// Environment Configuration\nconst ENV_CONFIG = {\n    NODE_ENV: \"development\" || 0,\n    PORT: parseInt(process.env.PORT || \"8080\"),\n    NEXT_PORT: parseInt(process.env.NEXT_PORT || \"3000\"),\n    LOG_LEVEL: process.env.LOG_LEVEL || \"info\",\n    ENABLE_METRICS: process.env.ENABLE_METRICS === \"true\"\n};\n// Default Values\nconst DEFAULTS = {\n    NIFTY_SPOT_PRICE: 24850,\n    SELECTED_EXPIRY: \"2025-06-19\",\n    STRIKES_TO_SHOW: 20,\n    REFRESH_INTERVAL: 1000,\n    CHART_TIMEFRAME: \"1D\",\n    TABLE_PAGE_SIZE: 50\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/constants.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/data-cache.ts":
/*!*******************************!*\
  !*** ./src/lib/data-cache.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MARKET_DATA_CACHE_KEYS: () => (/* binding */ MARKET_DATA_CACHE_KEYS),\n/* harmony export */   cacheHelpers: () => (/* binding */ cacheHelpers),\n/* harmony export */   dataCache: () => (/* binding */ dataCache)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(ssr)/./src/lib/constants.ts\");\n/**\r\n * Data Caching System for Market Data Persistence\r\n * Handles localStorage/sessionStorage with TTL and compression\r\n */ \nclass DataCache {\n    static{\n        this.instance = null;\n    }\n    /**\r\n   * Check if we're in a browser environment\r\n   */ isBrowser() {\n        return  false && 0;\n    }\n    static getInstance() {\n        if (!DataCache.instance) {\n            DataCache.instance = new DataCache();\n        }\n        return DataCache.instance;\n    }\n    /**\r\n   * Initialize cache for client-side usage\r\n   * Call this method when the component mounts on the client side\r\n   */ initializeClientSide() {\n        if (this.isBrowser()) {\n            console.log(\"\\uD83D\\uDE80 DataCache: Initializing client-side cache\");\n            // Perform any client-side specific initialization here\n            this.cleanupExpired() // Run initial cleanup\n            ;\n        }\n    }\n    constructor(){\n        this.version = \"1.0.0\";\n        this.compressionSupported = false;\n        this.checkCompressionSupport();\n        this.startCleanupInterval();\n    }\n    /**\r\n   * Check if compression is supported\r\n   */ checkCompressionSupport() {\n        try {\n            // Check if CompressionStream is available (modern browsers)\n            this.compressionSupported = typeof CompressionStream !== \"undefined\";\n        } catch  {\n            this.compressionSupported = false;\n        }\n    }\n    /**\r\n   * Set data in cache with TTL\r\n   */ async set(key, data, options = {}) {\n        try {\n            // Skip caching on server side\n            if (!this.isBrowser()) {\n                console.log(`⚠️ DataCache: Skipping cache on server side for ${key}`);\n                return false;\n            }\n            const { ttl = _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.DEFAULT_TTL, useCompression = false, storage = \"localStorage\" } = options;\n            const entry = {\n                data,\n                timestamp: Date.now(),\n                ttl,\n                version: this.version\n            };\n            let serializedData = JSON.stringify(entry);\n            // Apply compression if requested and supported\n            if (useCompression && this.compressionSupported) {\n                serializedData = await this.compress(serializedData);\n            }\n            const storageObj = storage === \"localStorage\" ? localStorage : sessionStorage;\n            const fullKey = this.getFullKey(key);\n            storageObj.setItem(fullKey, serializedData);\n            console.log(`💾 DataCache: Cached ${key} (${serializedData.length} bytes)`);\n            return true;\n        } catch (error) {\n            console.error(`❌ DataCache: Failed to cache ${key}:`, error);\n            return false;\n        }\n    }\n    /**\r\n   * Get data from cache\r\n   */ async get(key, options = {}) {\n        try {\n            // Return null on server side\n            if (!this.isBrowser()) {\n                return null;\n            }\n            const { storage = \"localStorage\" } = options;\n            const storageObj = storage === \"localStorage\" ? localStorage : sessionStorage;\n            const fullKey = this.getFullKey(key);\n            const serializedData = storageObj.getItem(fullKey);\n            if (!serializedData) {\n                return null;\n            }\n            let decompressedData = serializedData;\n            // Try to decompress if it looks compressed\n            if (this.compressionSupported && this.isCompressed(serializedData)) {\n                decompressedData = await this.decompress(serializedData);\n            }\n            const entry = JSON.parse(decompressedData);\n            // Check version compatibility\n            if (entry.version !== this.version) {\n                console.warn(`⚠️ DataCache: Version mismatch for ${key}, removing`);\n                this.remove(key, options);\n                return null;\n            }\n            // Check TTL\n            if (Date.now() - entry.timestamp > entry.ttl) {\n                console.log(`⏰ DataCache: ${key} expired, removing`);\n                this.remove(key, options);\n                return null;\n            }\n            console.log(`📖 DataCache: Retrieved ${key} from cache`);\n            return entry.data;\n        } catch (error) {\n            console.error(`❌ DataCache: Failed to retrieve ${key}:`, error);\n            // Remove corrupted entry\n            this.remove(key, options);\n            return null;\n        }\n    }\n    /**\r\n   * Remove data from cache\r\n   */ remove(key, options = {}) {\n        try {\n            // Skip on server side\n            if (!this.isBrowser()) {\n                return;\n            }\n            const { storage = \"localStorage\" } = options;\n            const storageObj = storage === \"localStorage\" ? localStorage : sessionStorage;\n            const fullKey = this.getFullKey(key);\n            storageObj.removeItem(fullKey);\n            console.log(`🗑️ DataCache: Removed ${key}`);\n        } catch (error) {\n            console.error(`❌ DataCache: Failed to remove ${key}:`, error);\n        }\n    }\n    /**\r\n   * Clear all cache entries\r\n   */ clear(storage = \"localStorage\") {\n        try {\n            // Skip on server side\n            if (!this.isBrowser()) {\n                return;\n            }\n            const storageObj = storage === \"localStorage\" ? localStorage : sessionStorage;\n            const keysToRemove = [];\n            // Find all our cache keys\n            for(let i = 0; i < storageObj.length; i++){\n                const key = storageObj.key(i);\n                if (key && key.startsWith(\"csv_market_dashboard_cache_\")) {\n                    keysToRemove.push(key);\n                }\n            }\n            // Remove them\n            keysToRemove.forEach((key)=>storageObj.removeItem(key));\n            console.log(`🧹 DataCache: Cleared ${keysToRemove.length} entries from ${storage}`);\n        } catch (error) {\n            console.error(`❌ DataCache: Failed to clear ${storage}:`, error);\n        }\n    }\n    /**\r\n   * Get cache statistics\r\n   */ getStats(storage = \"localStorage\") {\n        // Return empty stats on server side\n        if (!this.isBrowser()) {\n            return {\n                totalEntries: 0,\n                totalSize: 0,\n                oldestEntry: null,\n                newestEntry: null\n            };\n        }\n        const storageObj = storage === \"localStorage\" ? localStorage : sessionStorage;\n        let totalEntries = 0;\n        let totalSize = 0;\n        let oldestTimestamp = Infinity;\n        let newestTimestamp = 0;\n        try {\n            for(let i = 0; i < storageObj.length; i++){\n                const key = storageObj.key(i);\n                if (key && key.startsWith(\"csv_market_dashboard_cache_\")) {\n                    const value = storageObj.getItem(key);\n                    if (value) {\n                        totalEntries++;\n                        totalSize += value.length;\n                        try {\n                            const entry = JSON.parse(value);\n                            if (entry.timestamp) {\n                                oldestTimestamp = Math.min(oldestTimestamp, entry.timestamp);\n                                newestTimestamp = Math.max(newestTimestamp, entry.timestamp);\n                            }\n                        } catch  {\n                        // Ignore parsing errors for stats\n                        }\n                    }\n                }\n            }\n        } catch (error) {\n            console.error(\"❌ DataCache: Failed to get stats:\", error);\n        }\n        return {\n            totalEntries,\n            totalSize,\n            oldestEntry: oldestTimestamp === Infinity ? null : new Date(oldestTimestamp),\n            newestEntry: newestTimestamp === 0 ? null : new Date(newestTimestamp)\n        };\n    }\n    /**\r\n   * Start cleanup interval to remove expired entries\r\n   */ startCleanupInterval() {\n        // Only start cleanup interval on client side\n        if (this.isBrowser()) {\n            setInterval(()=>{\n                this.cleanupExpired();\n            }, _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.EXPIRY_CHECK_INTERVAL);\n        }\n    }\n    /**\r\n   * Clean up expired entries\r\n   */ cleanupExpired() {\n        try {\n            // Skip cleanup on server side\n            if (!this.isBrowser()) {\n                return;\n            }\n            const storages = [\n                \"localStorage\",\n                \"sessionStorage\"\n            ];\n            storages.forEach((storageType)=>{\n                const storageObj = storageType === \"localStorage\" ? localStorage : sessionStorage;\n                const keysToRemove = [];\n                for(let i = 0; i < storageObj.length; i++){\n                    const key = storageObj.key(i);\n                    if (key && key.startsWith(\"csv_market_dashboard_cache_\")) {\n                        try {\n                            const value = storageObj.getItem(key);\n                            if (value) {\n                                const entry = JSON.parse(value);\n                                if (Date.now() - entry.timestamp > entry.ttl) {\n                                    keysToRemove.push(key);\n                                }\n                            }\n                        } catch  {\n                            // Remove corrupted entries\n                            keysToRemove.push(key);\n                        }\n                    }\n                }\n                keysToRemove.forEach((key)=>storageObj.removeItem(key));\n                if (keysToRemove.length > 0) {\n                    console.log(`🧹 DataCache: Cleaned up ${keysToRemove.length} expired entries from ${storageType}`);\n                }\n            });\n        } catch (error) {\n            console.error(\"❌ DataCache: Cleanup failed:\", error);\n        }\n    }\n    /**\r\n   * Generate full cache key\r\n   */ getFullKey(key) {\n        return `csv_market_dashboard_cache_${key}`;\n    }\n    /**\r\n   * Check if data is compressed\r\n   */ isCompressed(data) {\n        // Simple heuristic: compressed data usually starts with specific bytes\n        return data.startsWith(\"H4sI\") || data.startsWith(\"eJy\") // Common compression signatures\n        ;\n    }\n    /**\r\n   * Compress data (placeholder for future implementation)\r\n   */ async compress(data) {\n        // For now, return as-is. Can implement actual compression later\n        return data;\n    }\n    /**\r\n   * Decompress data (placeholder for future implementation)\r\n   */ async decompress(data) {\n        // For now, return as-is. Can implement actual decompression later\n        return data;\n    }\n}\n// Export singleton instance\nconst dataCache = DataCache.getInstance();\n// Export cache keys for market data\nconst MARKET_DATA_CACHE_KEYS = _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.KEYS;\n// Export helper functions\nconst cacheHelpers = {\n    /**\r\n   * Cache market data with medium TTL for page refresh persistence\r\n   */ cacheMarketData: async (key, data)=>{\n        return dataCache.set(key, data, {\n            ttl: _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.MARKET_DATA_TTL,\n            storage: \"localStorage\" // Use localStorage for page refresh persistence\n        });\n    },\n    /**\r\n   * Cache static data with long TTL\r\n   */ cacheStaticData: async (key, data)=>{\n        return dataCache.set(key, data, {\n            ttl: _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.STATIC_DATA_TTL,\n            storage: \"localStorage\" // Use local storage for persistent data\n        });\n    },\n    /**\r\n   * Get cached market data\r\n   */ getCachedMarketData: async (key)=>{\n        return dataCache.get(key, {\n            storage: \"localStorage\"\n        });\n    },\n    /**\r\n   * Get cached static data\r\n   */ getCachedStaticData: async (key)=>{\n        return dataCache.get(key, {\n            storage: \"localStorage\"\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/data-cache.ts\n");

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"146531be5b05\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3N2LW1hcmtldC1kYXNoYm9hcmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2VlZGYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxNDY1MzFiZTViMDVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\love\dashboard\csv-market-dashboard\src\app\layout.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\love\dashboard\csv-market-dashboard\src\app\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/react-hot-toast","vendor-chunks/react-error-boundary","vendor-chunks/goober"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();