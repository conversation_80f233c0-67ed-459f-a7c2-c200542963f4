/**
 * Application Constants
 */
export declare const API_CONFIG: {
    readonly BASE_URL: string;
    readonly TIMEOUT: 30000;
    readonly RETRY_ATTEMPTS: 3;
    readonly RETRY_DELAY: 1000;
};
export declare const WEBSOCKET_CONFIG: {
    readonly RECONNECT_INTERVAL: 3000;
    readonly MAX_RECONNECT_ATTEMPTS: 15;
    readonly PING_INTERVAL: 25000;
    readonly PONG_TIMEOUT: 10000;
    readonly CONNECTION_TIMEOUT: 20000;
    readonly HEARTBEAT_INTERVAL: 30000;
    readonly MAX_LISTENERS_PER_EVENT: 10;
    readonly CLEANUP_INTERVAL: 60000;
};
export declare const DHAN_CONFIG: {
    readonly BASE_URL: "https://api.dhan.co";
    readonly WEBSOCKET_URL: "wss://api.dhan.co/v2/wsapi";
    readonly MAX_INSTRUMENTS_PER_CONNECTION: 5000;
    readonly MAX_INSTRUMENTS_PER_MESSAGE: 100;
    readonly RATE_LIMIT: {
        readonly REQUESTS_PER_SECOND: 10;
        readonly REQUESTS_PER_MINUTE: 600;
    };
};
export declare const MARKET_CONFIG: {
    readonly TRADING_HOURS: {
        readonly START: {
            readonly hour: 9;
            readonly minute: 15;
        };
        readonly END: {
            readonly hour: 15;
            readonly minute: 30;
        };
    };
    readonly TRADING_DAYS: readonly [1, 2, 3, 4, 5];
    readonly REFRESH_INTERVAL: 1000;
    readonly BATCH_SIZE: 100;
};
export declare const INSTRUMENT_TYPES: {
    readonly EQUITY: "EQUITY";
    readonly INDEX: "INDEX";
    readonly FUTIDX: "FUTIDX";
    readonly OPTIDX: "OPTIDX";
    readonly FUTSTK: "FUTSTK";
    readonly OPTSTK: "OPTSTK";
    readonly FUTCUR: "FUTCUR";
    readonly OPTCUR: "OPTCUR";
    readonly FUTCOM: "FUTCOM";
    readonly OPTFUT: "OPTFUT";
};
export declare const EXCHANGE_SEGMENTS: {
    readonly NSE_EQ: "NSE_EQ";
    readonly NSE_FNO: "NSE_FNO";
    readonly BSE_EQ: "BSE_EQ";
    readonly MCX_COMM: "MCX_COMM";
    readonly IDX_I: "IDX_I";
};
export declare const OPTION_TYPES: {
    readonly CALL: "CE";
    readonly PUT: "PE";
};
export declare const PACKET_TYPES: {
    readonly TOUCHLINE: 1;
    readonly QUOTE: 2;
    readonly SNAP_QUOTE: 3;
    readonly FULL_PACKET: 4;
    readonly OI: 5;
};
export declare const REQUEST_CODES: {
    readonly SUBSCRIBE: 21;
    readonly UNSUBSCRIBE: 22;
    readonly SNAP_QUOTE: 23;
};
export declare const UI_CONFIG: {
    readonly DEBOUNCE_DELAY: 300;
    readonly THROTTLE_DELAY: 100;
    readonly ANIMATION_DURATION: 200;
    readonly TOAST_DURATION: 3000;
    readonly PAGINATION: {
        readonly DEFAULT_PAGE_SIZE: 50;
        readonly PAGE_SIZE_OPTIONS: readonly [25, 50, 100, 200];
    };
};
export declare const COLORS: {
    readonly SUCCESS: "#10B981";
    readonly ERROR: "#EF4444";
    readonly WARNING: "#F59E0B";
    readonly INFO: "#3B82F6";
    readonly NEUTRAL: "#6B7280";
    readonly BID: "#10B981";
    readonly ASK: "#EF4444";
    readonly SPOT: "#3B82F6";
};
export declare const CHART_CONFIG: {
    readonly DEFAULT_HEIGHT: 400;
    readonly COLORS: {
        readonly PRIMARY: "#3B82F6";
        readonly SECONDARY: "#10B981";
        readonly ACCENT: "#F59E0B";
        readonly GRID: "#E5E7EB";
        readonly TEXT: "#374151";
    };
    readonly ANIMATION: {
        readonly DURATION: 300;
        readonly EASING: "ease-in-out";
    };
};
export declare const VALIDATION: {
    readonly MIN_STRIKE_PRICE: 1;
    readonly MAX_STRIKE_PRICE: 100000;
    readonly MIN_OPTION_PRICE: 0.01;
    readonly MAX_OPTION_PRICE: 50000;
    readonly MIN_VOLUME: 0;
    readonly MAX_VOLUME: 10000000;
};
export declare const ERROR_MESSAGES: {
    readonly NETWORK_ERROR: "Network connection failed. Please check your internet connection.";
    readonly API_ERROR: "API request failed. Please try again later.";
    readonly WEBSOCKET_ERROR: "WebSocket connection failed. Attempting to reconnect...";
    readonly DATA_PARSING_ERROR: "Failed to parse market data. Please refresh the page.";
    readonly SUBSCRIPTION_ERROR: "Failed to subscribe to market data. Please try again.";
    readonly INVALID_INSTRUMENT: "Invalid instrument selected.";
    readonly MARKET_CLOSED: "Market is currently closed.";
    readonly RATE_LIMIT_EXCEEDED: "Too many requests. Please wait before trying again.";
};
export declare const SUCCESS_MESSAGES: {
    readonly CONNECTION_ESTABLISHED: "Successfully connected to market data feed.";
    readonly SUBSCRIPTION_SUCCESS: "Successfully subscribed to market data.";
    readonly DATA_UPDATED: "Market data updated successfully.";
    readonly SETTINGS_SAVED: "Settings saved successfully.";
};
export declare const STORAGE_KEYS: {
    readonly USER_PREFERENCES: "csv_market_dashboard_preferences";
    readonly SELECTED_INSTRUMENTS: "csv_market_dashboard_selected_instruments";
    readonly THEME: "csv_market_dashboard_theme";
    readonly LAYOUT: "csv_market_dashboard_layout";
};
export declare const FEATURE_FLAGS: {
    readonly ENABLE_CHARTS: true;
    readonly ENABLE_ALERTS: true;
    readonly ENABLE_EXPORT: true;
    readonly ENABLE_DARK_MODE: true;
    readonly ENABLE_REAL_TIME_UPDATES: true;
    readonly ENABLE_OPTION_GREEKS: false;
};
export declare const PERFORMANCE_CONFIG: {
    readonly MEMORY_WARNING_THRESHOLD: number;
    readonly CPU_WARNING_THRESHOLD: 80;
    readonly NETWORK_TIMEOUT_WARNING: 5000;
    readonly MAX_CONCURRENT_REQUESTS: 10;
};
export declare const LOGGING_CONFIG: {
    readonly LEVELS: {
        readonly ERROR: 0;
        readonly WARN: 1;
        readonly INFO: 2;
        readonly DEBUG: 3;
    };
    readonly MAX_LOG_SIZE: number;
    readonly MAX_LOG_FILES: 5;
    readonly LOG_ROTATION_INTERVAL: number;
};
export declare const CACHE_CONFIG: {
    readonly DEFAULT_TTL: number;
    readonly MARKET_DATA_TTL: number;
    readonly STATIC_DATA_TTL: number;
    readonly EXPIRY_CHECK_INTERVAL: number;
    readonly MAX_CACHE_SIZE: number;
    readonly KEYS: {
        readonly INSTRUMENTS: "instruments";
        readonly MARKET_DATA: "market_data";
        readonly OPTION_CHAIN: "option_chain";
        readonly EXPIRY_DATES: "expiry_dates";
        readonly NIFTY_SPOT: "nifty_spot";
        readonly USER_SETTINGS: "user_settings";
        readonly USER_PREFERENCES: "user_preferences";
    };
};
export declare const SECURITY_CONFIG: {
    readonly RATE_LIMITING: {
        readonly WINDOW_MS: number;
        readonly MAX_REQUESTS: 1000;
    };
    readonly CORS: {
        readonly ORIGIN: string[];
        readonly CREDENTIALS: true;
    };
    readonly HEADERS: {
        readonly CONTENT_SECURITY_POLICY: "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';";
        readonly X_FRAME_OPTIONS: "DENY";
        readonly X_CONTENT_TYPE_OPTIONS: "nosniff";
    };
};
export declare const ENV_CONFIG: {
    readonly NODE_ENV: string;
    readonly PORT: number;
    readonly NEXT_PORT: number;
    readonly LOG_LEVEL: string;
    readonly ENABLE_METRICS: boolean;
};
export declare const DEFAULTS: {
    readonly NIFTY_SPOT_PRICE: 24850;
    readonly SELECTED_EXPIRY: "2025-06-19";
    readonly STRIKES_TO_SHOW: 20;
    readonly REFRESH_INTERVAL: 1000;
    readonly CHART_TIMEFRAME: "1D";
    readonly TABLE_PAGE_SIZE: 50;
};
//# sourceMappingURL=constants.d.ts.map