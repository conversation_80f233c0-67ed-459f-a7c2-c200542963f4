/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(app-pages-browser)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q2xvdmUlNUMlNUNkYXNoYm9hcmQlNUMlNUNjc3YtbWFya2V0LWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQWtHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/YmFkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGxvdmVcXFxcZGFzaGJvYXJkXFxcXGNzdi1tYXJrZXQtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/build/polyfills/process.js ***!
  \***********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nvar _global_process, _global_process1;\nmodule.exports = ((_global_process = __webpack_require__.g.process) == null ? void 0 : _global_process.env) && typeof ((_global_process1 = __webpack_require__.g.process) == null ? void 0 : _global_process1.env) === \"object\" ? __webpack_require__.g.process : __webpack_require__(/*! next/dist/compiled/process */ \"(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js\");\n\n//# sourceMappingURL=process.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvcG9seWZpbGxzL3Byb2Nlc3MuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLHFDQUFxQyxxQkFBTSxpRkFBaUYscUJBQU0sa0VBQWtFLHFCQUFNLFdBQVcsbUJBQU8sQ0FBQyw0R0FBNEI7O0FBRXpQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvcG9seWZpbGxzL3Byb2Nlc3MuanM/YzQwMCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfZ2xvYmFsX3Byb2Nlc3MsIF9nbG9iYWxfcHJvY2VzczE7XG5tb2R1bGUuZXhwb3J0cyA9ICgoX2dsb2JhbF9wcm9jZXNzID0gZ2xvYmFsLnByb2Nlc3MpID09IG51bGwgPyB2b2lkIDAgOiBfZ2xvYmFsX3Byb2Nlc3MuZW52KSAmJiB0eXBlb2YgKChfZ2xvYmFsX3Byb2Nlc3MxID0gZ2xvYmFsLnByb2Nlc3MpID09IG51bGwgPyB2b2lkIDAgOiBfZ2xvYmFsX3Byb2Nlc3MxLmVudikgPT09IFwib2JqZWN0XCIgPyBnbG9iYWwucHJvY2VzcyA6IHJlcXVpcmUoXCJuZXh0L2Rpc3QvY29tcGlsZWQvcHJvY2Vzc1wiKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cHJvY2Vzcy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/compiled/process/browser.js ***!
  \************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(function(){var e={229:function(e){var t=e.exports={};var r;var n;function defaultSetTimout(){throw new Error(\"setTimeout has not been defined\")}function defaultClearTimeout(){throw new Error(\"clearTimeout has not been defined\")}(function(){try{if(typeof setTimeout===\"function\"){r=setTimeout}else{r=defaultSetTimout}}catch(e){r=defaultSetTimout}try{if(typeof clearTimeout===\"function\"){n=clearTimeout}else{n=defaultClearTimeout}}catch(e){n=defaultClearTimeout}})();function runTimeout(e){if(r===setTimeout){return setTimeout(e,0)}if((r===defaultSetTimout||!r)&&setTimeout){r=setTimeout;return setTimeout(e,0)}try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}function runClearTimeout(e){if(n===clearTimeout){return clearTimeout(e)}if((n===defaultClearTimeout||!n)&&clearTimeout){n=clearTimeout;return clearTimeout(e)}try{return n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}var i=[];var o=false;var u;var a=-1;function cleanUpNextTick(){if(!o||!u){return}o=false;if(u.length){i=u.concat(i)}else{a=-1}if(i.length){drainQueue()}}function drainQueue(){if(o){return}var e=runTimeout(cleanUpNextTick);o=true;var t=i.length;while(t){u=i;i=[];while(++a<t){if(u){u[a].run()}}a=-1;t=i.length}u=null;o=false;runClearTimeout(e)}t.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1){for(var r=1;r<arguments.length;r++){t[r-1]=arguments[r]}}i.push(new Item(e,t));if(i.length===1&&!o){runTimeout(drainQueue)}};function Item(e,t){this.fun=e;this.array=t}Item.prototype.run=function(){this.fun.apply(null,this.array)};t.title=\"browser\";t.browser=true;t.env={};t.argv=[];t.version=\"\";t.versions={};function noop(){}t.on=noop;t.addListener=noop;t.once=noop;t.off=noop;t.removeListener=noop;t.removeAllListeners=noop;t.emit=noop;t.prependListener=noop;t.prependOnceListener=noop;t.listeners=function(e){return[]};t.binding=function(e){throw new Error(\"process.binding is not supported\")};t.cwd=function(){return\"/\"};t.chdir=function(e){throw new Error(\"process.chdir is not supported\")};t.umask=function(){return 0}}};var t={};function __nccwpck_require__(r){var n=t[r];if(n!==undefined){return n.exports}var i=t[r]={exports:{}};var o=true;try{e[r](i,i.exports,__nccwpck_require__);o=false}finally{if(o)delete t[r]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var r=__nccwpck_require__(229);module.exports=r})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Dashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_InstrumentTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/InstrumentTable */ \"(app-pages-browser)/./src/components/InstrumentTable.tsx\");\n/* harmony import */ var _components_FilterPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/FilterPanel */ \"(app-pages-browser)/./src/components/FilterPanel.tsx\");\n/* harmony import */ var _components_ConnectionStatus__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ConnectionStatus */ \"(app-pages-browser)/./src/components/ConnectionStatus.tsx\");\n/* harmony import */ var _components_Stats__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Stats */ \"(app-pages-browser)/./src/components/Stats.tsx\");\n/* harmony import */ var _hooks_useEnhancedMarketData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useEnhancedMarketData */ \"(app-pages-browser)/./src/hooks/useEnhancedMarketData.ts\");\n/* harmony import */ var _lib_data_cache__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/data-cache */ \"(app-pages-browser)/./src/lib/data-cache.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const { marketData: marketDataArray, marketDataMap, isConnected, connectionError, connectionStatus, isLoading: wsLoading, cacheLoaded, refresh: refreshFromCache, stats, updateFilters, filters, getFilteredData, getSortedData } = (0,_hooks_useEnhancedMarketData__WEBPACK_IMPORTED_MODULE_6__.useEnhancedMarketData)();\n    const [instruments, setInstruments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [exchanges, setExchanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [instrumentTypes, setInstrumentTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Convert market data array to Map for compatibility\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const dataMap = new Map();\n        marketDataArray.forEach((item)=>{\n            if (item.securityId) {\n                dataMap.set(item.securityId, item);\n            }\n        });\n        setMarketData(dataMap);\n    }, [\n        marketDataArray\n    ]);\n    const fetchFreshInstruments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const serverUrl = \"http://localhost:8080]\" || 0;\n            console.log(\"\\uD83C\\uDF10 Dashboard: Loading fresh instruments from API...\");\n            const response = await fetch(\"\".concat(serverUrl, \"/api/instruments\"));\n            if (response.ok) {\n                const data = await response.json();\n                console.log(\"✅ Dashboard: Loaded\", data.data.instruments.length, \"instruments from API\");\n                setInstruments(data.data.instruments);\n                // Cache the instruments\n                await _lib_data_cache__WEBPACK_IMPORTED_MODULE_7__.cacheHelpers.cacheStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_7__.MARKET_DATA_CACHE_KEYS.INSTRUMENTS, data.data.instruments);\n                console.log(\"\\uD83D\\uDCBE Dashboard: Cached instruments data\");\n                setLoading(false);\n            } else {\n                console.error(\"❌ Dashboard: Failed to load instruments:\", response.statusText);\n                setError(\"Failed to load instruments\");\n                setLoading(false);\n            }\n        } catch (error) {\n            console.error(\"❌ Dashboard: Error fetching fresh instruments:\", error);\n            setError(\"Error fetching instruments\");\n            setLoading(false);\n        }\n    }, []);\n    // Load all instruments from API with caching\n    const loadAllInstruments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            setLoading(true);\n            // Check cache first\n            const cachedInstruments = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_7__.cacheHelpers.getCachedStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_7__.MARKET_DATA_CACHE_KEYS.INSTRUMENTS);\n            if (cachedInstruments && Array.isArray(cachedInstruments)) {\n                console.log(\"✅ Dashboard: Loaded instruments from cache\");\n                setInstruments(cachedInstruments);\n                setLoading(false);\n                // Still fetch fresh data in background\n                fetchFreshInstruments();\n                return;\n            }\n            // Fetch fresh data\n            await fetchFreshInstruments();\n        } catch (error) {\n            console.error(\"❌ Dashboard: Error loading instruments:\", error);\n            setError(\"Error loading instruments\");\n            setLoading(false);\n        }\n    }, [\n        fetchFreshInstruments\n    ]);\n    // Load all instruments on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAllInstruments();\n    }, [\n        loadAllInstruments\n    ]);\n    // Load additional data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadMetadata = async ()=>{\n            try {\n                const serverUrl = \"http://localhost:8080]\" || 0;\n                // Load exchanges\n                const exchangesResponse = await fetch(\"\".concat(serverUrl, \"/api/exchanges\"));\n                if (exchangesResponse.ok) {\n                    const exchangesData = await exchangesResponse.json();\n                    setExchanges(exchangesData.data);\n                }\n                // Load instrument types\n                const typesResponse = await fetch(\"\".concat(serverUrl, \"/api/instrument-types\"));\n                if (typesResponse.ok) {\n                    const typesData = await typesResponse.json();\n                    setInstrumentTypes(typesData.data);\n                }\n            } catch (error) {\n                console.error(\"❌ Error loading metadata:\", error);\n            }\n        };\n        loadMetadata();\n    }, []);\n    // Filter instruments based on current filter\n    const filteredInstruments = instruments.filter((instrument)=>{\n        if (filter.exchange && filter.exchange.length > 0 && !filter.exchange.includes(instrument.exchange)) {\n            return false;\n        }\n        if (filter.instrumentType && filter.instrumentType.length > 0 && !filter.instrumentType.includes(instrument.instrumentType)) {\n            return false;\n        }\n        if (filter.search) {\n            const searchTerm = filter.search.toLowerCase();\n            return instrument.symbol.toLowerCase().includes(searchTerm) || instrument.displayName.toLowerCase().includes(searchTerm) || instrument.isin && instrument.isin.toLowerCase().includes(searchTerm);\n        }\n        return true;\n    });\n    // Note: Subscriptions are now handled server-side automatically\n    const handleFilterChange = (newFilter)=>{\n        setFilter(newFilter);\n    };\n    const handleInstrumentSelect = (instrument)=>{\n        console.log(\"Selected instrument:\", instrument);\n    // You can add more functionality here, like showing detailed view\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner w-12 h-12 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading market data...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 169,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass rounded-2xl shadow-lg p-6 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold gradient-text\",\n                                    children: \"CSV Market Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-1\",\n                                    children: \"Real-time market data from CSV instruments\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3 space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/subscribed\",\n                                            className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors\",\n                                            children: \"\\uD83D\\uDCCA View Subscribed Data Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/option-chain\",\n                                            className: \"inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors\",\n                                            children: \"\\uD83D\\uDD17 NIFTY Option Chain\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConnectionStatus__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            connected: isConnected\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            (error || connectionError) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Error:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this),\n                    \" \",\n                    error || connectionError\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Stats__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                totalInstruments: instruments.length,\n                filteredInstruments: filteredInstruments.length,\n                marketDataCount: stats.totalInstruments,\n                connected: isConnected,\n                connectionStats: stats\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FilterPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            filter: filter,\n                            onFilterChange: handleFilterChange,\n                            exchanges: exchanges,\n                            instrumentTypes: instrumentTypes,\n                            segments: [\n                                \"C\",\n                                \"F\",\n                                \"O\"\n                            ]\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InstrumentTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            instruments: filteredInstruments.slice(0, 100),\n                            marketData: marketData,\n                            onInstrumentSelect: handleInstrumentSelect,\n                            loading: loading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 text-center text-sm text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"CSV Market Dashboard - Real-time data from \",\n                            instruments.length,\n                            \" instruments\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Last updated: \",\n                            new Date().toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"wOUSZNBu5Zzu0TU5leUfa8hlk1s=\", false, function() {\n    return [\n        _hooks_useEnhancedMarketData__WEBPACK_IMPORTED_MODULE_6__.useEnhancedMarketData\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ConnectionStatus.tsx":
/*!*********************************************!*\
  !*** ./src/components/ConnectionStatus.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst ConnectionStatus = (param)=>{\n    let { connected } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-3 h-3 rounded-full \".concat(connected ? \"bg-green-500 animate-pulse\" : \"bg-red-500\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium \".concat(connected ? \"text-green-700\" : \"text-red-700\"),\n                        children: connected ? \"Connected\" : \"Disconnected\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-3 py-1 rounded-full text-xs font-semibold \".concat(connected ? \"bg-green-100 text-green-800\" : \"bg-red-100 text-red-800\"),\n                children: connected ? \"LIVE\" : \"OFFLINE\"\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ConnectionStatus;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ConnectionStatus);\nvar _c;\n$RefreshReg$(_c, \"ConnectionStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ConnectionStatus.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/FilterPanel.tsx":
/*!****************************************!*\
  !*** ./src/components/FilterPanel.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst FilterPanel = (param)=>{\n    let { filter, onFilterChange, exchanges, instrumentTypes, segments } = param;\n    var _filter_exchange, _filter_instrumentType, _filter_segment;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(filter.search || \"\");\n    const handleExchangeChange = (exchange, checked)=>{\n        const currentExchanges = filter.exchange || [];\n        const newExchanges = checked ? [\n            ...currentExchanges,\n            exchange\n        ] : currentExchanges.filter((e)=>e !== exchange);\n        onFilterChange({\n            ...filter,\n            exchange: newExchanges.length > 0 ? newExchanges : undefined\n        });\n    };\n    const handleInstrumentTypeChange = (type, checked)=>{\n        const currentTypes = filter.instrumentType || [];\n        const newTypes = checked ? [\n            ...currentTypes,\n            type\n        ] : currentTypes.filter((t)=>t !== type);\n        onFilterChange({\n            ...filter,\n            instrumentType: newTypes.length > 0 ? newTypes : undefined\n        });\n    };\n    const handleSegmentChange = (segment, checked)=>{\n        const currentSegments = filter.segment || [];\n        const newSegments = checked ? [\n            ...currentSegments,\n            segment\n        ] : currentSegments.filter((s)=>s !== segment);\n        onFilterChange({\n            ...filter,\n            segment: newSegments.length > 0 ? newSegments : undefined\n        });\n    };\n    const handleSearchChange = (value)=>{\n        setSearchTerm(value);\n        onFilterChange({\n            ...filter,\n            search: value || undefined\n        });\n    };\n    const handleClearFilters = ()=>{\n        setSearchTerm(\"\");\n        onFilterChange({});\n    };\n    const isFilterActive = ()=>{\n        var _filter_exchange, _filter_instrumentType, _filter_segment;\n        return !!(((_filter_exchange = filter.exchange) === null || _filter_exchange === void 0 ? void 0 : _filter_exchange.length) || ((_filter_instrumentType = filter.instrumentType) === null || _filter_instrumentType === void 0 ? void 0 : _filter_instrumentType.length) || ((_filter_segment = filter.segment) === null || _filter_segment === void 0 ? void 0 : _filter_segment.length) || filter.search || filter.isActive !== undefined || filter.hasExpiry !== undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"filter-panel\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Filters\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    isFilterActive() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleClearFilters,\n                        className: \"text-sm text-blue-600 hover:text-blue-800 font-medium\",\n                        children: \"Clear All\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Search\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        placeholder: \"Search by symbol, name, or ISIN...\",\n                        value: searchTerm,\n                        onChange: (e)=>handleSearchChange(e.target.value),\n                        className: \"filter-input\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Exchanges\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 max-h-32 overflow-y-auto custom-scrollbar\",\n                        children: exchanges.map((exchange)=>{\n                            var _filter_exchange;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: ((_filter_exchange = filter.exchange) === null || _filter_exchange === void 0 ? void 0 : _filter_exchange.includes(exchange)) || false,\n                                        onChange: (e)=>handleExchangeChange(exchange, e.target.checked),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: exchange\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, exchange, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Instrument Types\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 max-h-32 overflow-y-auto custom-scrollbar\",\n                        children: instrumentTypes.map((type)=>{\n                            var _filter_instrumentType;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: ((_filter_instrumentType = filter.instrumentType) === null || _filter_instrumentType === void 0 ? void 0 : _filter_instrumentType.includes(type)) || false,\n                                        onChange: (e)=>handleInstrumentTypeChange(type, e.target.checked),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: type\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, type, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Segments\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: segments.map((segment)=>{\n                            var _filter_segment;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: ((_filter_segment = filter.segment) === null || _filter_segment === void 0 ? void 0 : _filter_segment.includes(segment)) || false,\n                                        onChange: (e)=>handleSegmentChange(segment, e.target.checked),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: segment === \"C\" ? \"Cash (C)\" : segment === \"F\" ? \"Futures (F)\" : segment === \"O\" ? \"Options (O)\" : segment\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, segment, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Additional Filters\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filter.isActive === true,\n                                        onChange: (e)=>onFilterChange({\n                                                ...filter,\n                                                isActive: e.target.checked ? true : undefined\n                                            }),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: \"Active Only\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filter.hasExpiry === true,\n                                        onChange: (e)=>onFilterChange({\n                                                ...filter,\n                                                hasExpiry: e.target.checked ? true : undefined\n                                            }),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: \"Has Expiry\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Lot Size Range\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"number\",\n                                placeholder: \"Min\",\n                                value: filter.minLotSize || \"\",\n                                onChange: (e)=>onFilterChange({\n                                        ...filter,\n                                        minLotSize: e.target.value ? parseInt(e.target.value) : undefined\n                                    }),\n                                className: \"filter-input text-sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"number\",\n                                placeholder: \"Max\",\n                                value: filter.maxLotSize || \"\",\n                                onChange: (e)=>onFilterChange({\n                                        ...filter,\n                                        maxLotSize: e.target.value ? parseInt(e.target.value) : undefined\n                                    }),\n                                className: \"filter-input text-sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, undefined),\n            isFilterActive() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-blue-900 mb-2\",\n                        children: \"Active Filters:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1 text-xs text-blue-700\",\n                        children: [\n                            ((_filter_exchange = filter.exchange) === null || _filter_exchange === void 0 ? void 0 : _filter_exchange.length) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Exchanges: \",\n                                    filter.exchange.join(\", \")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, undefined),\n                            ((_filter_instrumentType = filter.instrumentType) === null || _filter_instrumentType === void 0 ? void 0 : _filter_instrumentType.length) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Types: \",\n                                    filter.instrumentType.join(\", \")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, undefined),\n                            ((_filter_segment = filter.segment) === null || _filter_segment === void 0 ? void 0 : _filter_segment.length) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Segments: \",\n                                    filter.segment.join(\", \")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.search && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Search: “\",\n                                    filter.search,\n                                    \"”\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Active instruments only\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.hasExpiry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"With expiry date\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, undefined),\n                            (filter.minLotSize || filter.maxLotSize) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Lot size: \",\n                                    filter.minLotSize || 0,\n                                    \" - \",\n                                    filter.maxLotSize || \"∞\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 220,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FilterPanel, \"ViotEHOY83tXzCBEuSV/ICV6Rsw=\");\n_c = FilterPanel;\n/* harmony default export */ __webpack_exports__[\"default\"] = (FilterPanel);\nvar _c;\n$RefreshReg$(_c, \"FilterPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FilterPanel.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/InstrumentTable.tsx":
/*!********************************************!*\
  !*** ./src/components/InstrumentTable.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst InstrumentTable = (param)=>{\n    let { instruments, marketData, onInstrumentSelect, loading = false } = param;\n    _s();\n    const [sortField, setSortField] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"symbol\");\n    const [sortDirection, setSortDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"asc\");\n    // Sort instruments\n    const sortedInstruments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return [\n            ...instruments\n        ].sort((a, b)=>{\n            const aValue = a[sortField];\n            const bValue = b[sortField];\n            // Handle undefined values\n            if (aValue === undefined && bValue === undefined) return 0;\n            if (aValue === undefined) return 1;\n            if (bValue === undefined) return -1;\n            if (aValue === bValue) return 0;\n            const comparison = aValue < bValue ? -1 : 1;\n            return sortDirection === \"asc\" ? comparison : -comparison;\n        });\n    }, [\n        instruments,\n        sortField,\n        sortDirection\n    ]);\n    const handleSort = (field)=>{\n        if (sortField === field) {\n            setSortDirection(sortDirection === \"asc\" ? \"desc\" : \"asc\");\n        } else {\n            setSortField(field);\n            setSortDirection(\"asc\");\n        }\n    };\n    const formatPrice = (price)=>{\n        if (price === undefined || price === 0) return \"-\";\n        return \"₹\".concat(price.toFixed(2));\n    };\n    const formatChange = (change, changePercent)=>{\n        if (change === undefined || changePercent === undefined) return \"-\";\n        const sign = change >= 0 ? \"+\" : \"\";\n        return \"\".concat(sign).concat(change.toFixed(2), \" (\").concat(sign).concat(changePercent.toFixed(2), \"%)\");\n    };\n    const getChangeColor = (change)=>{\n        if (change === undefined || change === 0) return \"text-gray-600\";\n        return change > 0 ? \"text-green-600\" : \"text-red-600\";\n    };\n    const formatVolume = (volume)=>{\n        if (volume === undefined || volume === 0) return \"-\";\n        if (volume >= 10000000) return \"\".concat((volume / 10000000).toFixed(1), \"Cr\");\n        if (volume >= 100000) return \"\".concat((volume / 100000).toFixed(1), \"L\");\n        if (volume >= 1000) return \"\".concat((volume / 1000).toFixed(1), \"K\");\n        return volume.toString();\n    };\n    const SortIcon = (param)=>{\n        let { field } = param;\n        if (sortField !== field) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-gray-400\",\n                children: \"↕\"\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 68,\n                columnNumber: 14\n            }, undefined);\n        }\n        return sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-blue-600\",\n            children: \"↑\"\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 70,\n            columnNumber: 38\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-blue-600\",\n            children: \"↓\"\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 70,\n            columnNumber: 81\n        }, undefined);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"glass rounded-2xl shadow-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner w-8 h-8 mr-3\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading instruments...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (instruments.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"glass rounded-2xl shadow-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-lg\",\n                        children: \"No instruments found\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 text-sm mt-2\",\n                        children: \"Try adjusting your filters\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"glass rounded-2xl shadow-lg overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900\",\n                        children: \"Market Instruments\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-sm mt-1\",\n                        children: [\n                            \"Showing \",\n                            sortedInstruments.length,\n                            \" instruments\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto custom-scrollbar\",\n                style: {\n                    maxHeight: \"600px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"market-table\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"sticky top-0 bg-gray-50 z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort(\"securityId\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Security ID\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"securityId\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort(\"symbol\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Symbol\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"symbol\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort(\"displayName\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Name\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"displayName\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort(\"exchange\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Exchange\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"exchange\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort(\"instrumentType\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Type\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"instrumentType\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right\",\n                                        children: \"LTP\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right\",\n                                        children: \"Change\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right\",\n                                        children: \"Volume\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort(\"lotSize\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Lot Size\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"lotSize\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            children: sortedInstruments.map((instrument)=>{\n                                const data = marketData.get(instrument.securityId);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-gray-50 cursor-pointer transition-colors\",\n                                    onClick: ()=>onInstrumentSelect === null || onInstrumentSelect === void 0 ? void 0 : onInstrumentSelect(instrument),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"font-mono text-sm text-gray-700\",\n                                            children: instrument.securityId\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"font-medium text-blue-600\",\n                                            children: instrument.symbol\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"max-w-xs truncate\",\n                                            title: instrument.displayName,\n                                            children: instrument.displayName\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                children: instrument.exchange\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                children: instrument.instrumentType\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"text-right font-medium\",\n                                            children: formatPrice(data === null || data === void 0 ? void 0 : data.ltp)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"text-right font-medium \".concat(getChangeColor(data === null || data === void 0 ? void 0 : data.change)),\n                                            children: formatChange(data === null || data === void 0 ? void 0 : data.change, data === null || data === void 0 ? void 0 : data.changePercent)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"text-right\",\n                                            children: formatVolume(data === null || data === void 0 ? void 0 : data.volume)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"text-right\",\n                                            children: instrument.lotSize.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, instrument.securityId, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, undefined),\n            sortedInstruments.length > 100 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-gray-50 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600 text-center\",\n                    children: \"Showing first 100 instruments. Use filters to narrow down results.\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, undefined);\n};\n_s(InstrumentTable, \"AUdWssv40DkD69fxRdafzsbHvFc=\");\n_c = InstrumentTable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (InstrumentTable);\nvar _c;\n$RefreshReg$(_c, \"InstrumentTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0luc3RydW1lbnRUYWJsZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBRWlEO0FBR2pELE1BQU1HLGtCQUFrRDtRQUFDLEVBQ3ZEQyxXQUFXLEVBQ1hDLFVBQVUsRUFDVkMsa0JBQWtCLEVBQ2xCQyxVQUFVLEtBQUssRUFDaEI7O0lBQ0MsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUdSLCtDQUFRQSxDQUFtQjtJQUM3RCxNQUFNLENBQUNTLGVBQWVDLGlCQUFpQixHQUFHViwrQ0FBUUEsQ0FBaUI7SUFFbkUsbUJBQW1CO0lBQ25CLE1BQU1XLG9CQUFvQlYsOENBQU9BLENBQUM7UUFDaEMsT0FBTztlQUFJRTtTQUFZLENBQUNTLElBQUksQ0FBQyxDQUFDQyxHQUFHQztZQUMvQixNQUFNQyxTQUFTRixDQUFDLENBQUNOLFVBQVU7WUFDM0IsTUFBTVMsU0FBU0YsQ0FBQyxDQUFDUCxVQUFVO1lBRTNCLDBCQUEwQjtZQUMxQixJQUFJUSxXQUFXRSxhQUFhRCxXQUFXQyxXQUFXLE9BQU87WUFDekQsSUFBSUYsV0FBV0UsV0FBVyxPQUFPO1lBQ2pDLElBQUlELFdBQVdDLFdBQVcsT0FBTyxDQUFDO1lBRWxDLElBQUlGLFdBQVdDLFFBQVEsT0FBTztZQUU5QixNQUFNRSxhQUFhSCxTQUFTQyxTQUFTLENBQUMsSUFBSTtZQUMxQyxPQUFPUCxrQkFBa0IsUUFBUVMsYUFBYSxDQUFDQTtRQUNqRDtJQUNGLEdBQUc7UUFBQ2Y7UUFBYUk7UUFBV0U7S0FBYztJQUUxQyxNQUFNVSxhQUFhLENBQUNDO1FBQ2xCLElBQUliLGNBQWNhLE9BQU87WUFDdkJWLGlCQUFpQkQsa0JBQWtCLFFBQVEsU0FBUztRQUN0RCxPQUFPO1lBQ0xELGFBQWFZO1lBQ2JWLGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsTUFBTVcsY0FBYyxDQUFDQztRQUNuQixJQUFJQSxVQUFVTCxhQUFhSyxVQUFVLEdBQUcsT0FBTztRQUMvQyxPQUFPLElBQXFCLE9BQWpCQSxNQUFNQyxPQUFPLENBQUM7SUFDM0I7SUFFQSxNQUFNQyxlQUFlLENBQUNDLFFBQTRCQztRQUNoRCxJQUFJRCxXQUFXUixhQUFhUyxrQkFBa0JULFdBQVcsT0FBTztRQUNoRSxNQUFNVSxPQUFPRixVQUFVLElBQUksTUFBTTtRQUNqQyxPQUFPLEdBQVVBLE9BQVBFLE1BQTZCQSxPQUF0QkYsT0FBT0YsT0FBTyxDQUFDLElBQUcsTUFBV0csT0FBUEMsTUFBZ0MsT0FBekJELGNBQWNILE9BQU8sQ0FBQyxJQUFHO0lBQ3pFO0lBRUEsTUFBTUssaUJBQWlCLENBQUNIO1FBQ3RCLElBQUlBLFdBQVdSLGFBQWFRLFdBQVcsR0FBRyxPQUFPO1FBQ2pELE9BQU9BLFNBQVMsSUFBSSxtQkFBbUI7SUFDekM7SUFFQSxNQUFNSSxlQUFlLENBQUNDO1FBQ3BCLElBQUlBLFdBQVdiLGFBQWFhLFdBQVcsR0FBRyxPQUFPO1FBQ2pELElBQUlBLFVBQVUsVUFBVSxPQUFPLEdBQWtDLE9BQS9CLENBQUNBLFNBQVMsUUFBTyxFQUFHUCxPQUFPLENBQUMsSUFBRztRQUNqRSxJQUFJTyxVQUFVLFFBQVEsT0FBTyxHQUFnQyxPQUE3QixDQUFDQSxTQUFTLE1BQUssRUFBR1AsT0FBTyxDQUFDLElBQUc7UUFDN0QsSUFBSU8sVUFBVSxNQUFNLE9BQU8sR0FBOEIsT0FBM0IsQ0FBQ0EsU0FBUyxJQUFHLEVBQUdQLE9BQU8sQ0FBQyxJQUFHO1FBQ3pELE9BQU9PLE9BQU9DLFFBQVE7SUFDeEI7SUFFQSxNQUFNQyxXQUFXO1lBQUMsRUFBRVosS0FBSyxFQUErQjtRQUN0RCxJQUFJYixjQUFjYSxPQUFPO1lBQ3ZCLHFCQUFPLDhEQUFDYTtnQkFBS0MsV0FBVTswQkFBZ0I7Ozs7OztRQUN6QztRQUNBLE9BQU96QixrQkFBa0Isc0JBQVEsOERBQUN3QjtZQUFLQyxXQUFVO3NCQUFnQjs7Ozs7c0NBQVcsOERBQUNEO1lBQUtDLFdBQVU7c0JBQWdCOzs7Ozs7SUFDOUc7SUFFQSxJQUFJNUIsU0FBUztRQUNYLHFCQUNFLDhEQUFDNkI7WUFBSUQsV0FBVTtzQkFDYiw0RUFBQ0M7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDQzt3QkFBSUQsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDRDt3QkFBS0MsV0FBVTtrQ0FBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSXhDO0lBRUEsSUFBSS9CLFlBQVlpQyxNQUFNLEtBQUssR0FBRztRQUM1QixxQkFDRSw4REFBQ0Q7WUFBSUQsV0FBVTtzQkFDYiw0RUFBQ0M7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDRzt3QkFBRUgsV0FBVTtrQ0FBd0I7Ozs7OztrQ0FDckMsOERBQUNHO3dCQUFFSCxXQUFVO2tDQUE2Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJbEQ7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUQsV0FBVTs7MEJBQ2IsOERBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0k7d0JBQUdKLFdBQVU7a0NBQWtDOzs7Ozs7a0NBQ2hELDhEQUFDRzt3QkFBRUgsV0FBVTs7NEJBQTZCOzRCQUMvQnZCLGtCQUFrQnlCLE1BQU07NEJBQUM7Ozs7Ozs7Ozs7Ozs7MEJBSXRDLDhEQUFDRDtnQkFBSUQsV0FBVTtnQkFBbUNLLE9BQU87b0JBQUVDLFdBQVc7Z0JBQVE7MEJBQzVFLDRFQUFDQztvQkFBTVAsV0FBVTs7c0NBQ2YsOERBQUNROzRCQUFNUixXQUFVO3NDQUNmLDRFQUFDUzs7a0RBQ0MsOERBQUNDO3dDQUNDVixXQUFVO3dDQUNWVyxTQUFTLElBQU0xQixXQUFXO2tEQUUxQiw0RUFBQ2dCOzRDQUFJRCxXQUFVOztnREFBb0M7OERBRWpELDhEQUFDRjtvREFBU1osT0FBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBR3BCLDhEQUFDd0I7d0NBQ0NWLFdBQVU7d0NBQ1ZXLFNBQVMsSUFBTTFCLFdBQVc7a0RBRTFCLDRFQUFDZ0I7NENBQUlELFdBQVU7O2dEQUFvQzs4REFFakQsOERBQUNGO29EQUFTWixPQUFNOzs7Ozs7Ozs7Ozs7Ozs7OztrREFHcEIsOERBQUN3Qjt3Q0FDQ1YsV0FBVTt3Q0FDVlcsU0FBUyxJQUFNMUIsV0FBVztrREFFMUIsNEVBQUNnQjs0Q0FBSUQsV0FBVTs7Z0RBQW9DOzhEQUVqRCw4REFBQ0Y7b0RBQVNaLE9BQU07Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUdwQiw4REFBQ3dCO3dDQUNDVixXQUFVO3dDQUNWVyxTQUFTLElBQU0xQixXQUFXO2tEQUUxQiw0RUFBQ2dCOzRDQUFJRCxXQUFVOztnREFBb0M7OERBRWpELDhEQUFDRjtvREFBU1osT0FBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBR3BCLDhEQUFDd0I7d0NBQ0NWLFdBQVU7d0NBQ1ZXLFNBQVMsSUFBTTFCLFdBQVc7a0RBRTFCLDRFQUFDZ0I7NENBQUlELFdBQVU7O2dEQUFvQzs4REFFakQsOERBQUNGO29EQUFTWixPQUFNOzs7Ozs7Ozs7Ozs7Ozs7OztrREFHcEIsOERBQUN3Qjt3Q0FBR1YsV0FBVTtrREFBYTs7Ozs7O2tEQUMzQiw4REFBQ1U7d0NBQUdWLFdBQVU7a0RBQWE7Ozs7OztrREFDM0IsOERBQUNVO3dDQUFHVixXQUFVO2tEQUFhOzs7Ozs7a0RBQzNCLDhEQUFDVTt3Q0FDQ1YsV0FBVTt3Q0FDVlcsU0FBUyxJQUFNMUIsV0FBVztrREFFMUIsNEVBQUNnQjs0Q0FBSUQsV0FBVTs7Z0RBQW9DOzhEQUVqRCw4REFBQ0Y7b0RBQVNaLE9BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBS3hCLDhEQUFDMEI7c0NBQ0VuQyxrQkFBa0JvQyxHQUFHLENBQUMsQ0FBQ0M7Z0NBQ3RCLE1BQU1DLE9BQU83QyxXQUFXOEMsR0FBRyxDQUFDRixXQUFXRyxVQUFVO2dDQUNqRCxxQkFDRSw4REFBQ1I7b0NBRUNULFdBQVU7b0NBQ1ZXLFNBQVMsSUFBTXhDLCtCQUFBQSx5Q0FBQUEsbUJBQXFCMkM7O3NEQUVwQyw4REFBQ0k7NENBQUdsQixXQUFVO3NEQUNYYyxXQUFXRyxVQUFVOzs7Ozs7c0RBRXhCLDhEQUFDQzs0Q0FBR2xCLFdBQVU7c0RBQ1hjLFdBQVdLLE1BQU07Ozs7OztzREFFcEIsOERBQUNEOzRDQUFHbEIsV0FBVTs0Q0FBb0JvQixPQUFPTixXQUFXTyxXQUFXO3NEQUM1RFAsV0FBV08sV0FBVzs7Ozs7O3NEQUV6Qiw4REFBQ0g7c0RBQ0MsNEVBQUNuQjtnREFBS0MsV0FBVTswREFDYmMsV0FBV1EsUUFBUTs7Ozs7Ozs7Ozs7c0RBR3hCLDhEQUFDSjtzREFDQyw0RUFBQ25CO2dEQUFLQyxXQUFVOzBEQUNiYyxXQUFXUyxjQUFjOzs7Ozs7Ozs7OztzREFHOUIsOERBQUNMOzRDQUFHbEIsV0FBVTtzREFDWGIsWUFBWTRCLGlCQUFBQSwyQkFBQUEsS0FBTVMsR0FBRzs7Ozs7O3NEQUV4Qiw4REFBQ047NENBQUdsQixXQUFXLDBCQUF1RCxPQUE3Qk4sZUFBZXFCLGlCQUFBQSwyQkFBQUEsS0FBTXhCLE1BQU07c0RBQ2pFRCxhQUFheUIsaUJBQUFBLDJCQUFBQSxLQUFNeEIsTUFBTSxFQUFFd0IsaUJBQUFBLDJCQUFBQSxLQUFNdkIsYUFBYTs7Ozs7O3NEQUVqRCw4REFBQzBCOzRDQUFHbEIsV0FBVTtzREFDWEwsYUFBYW9CLGlCQUFBQSwyQkFBQUEsS0FBTW5CLE1BQU07Ozs7OztzREFFNUIsOERBQUNzQjs0Q0FBR2xCLFdBQVU7c0RBQ1hjLFdBQVdXLE9BQU8sQ0FBQ0MsY0FBYzs7Ozs7OzttQ0FqQy9CWixXQUFXRyxVQUFVOzs7Ozs0QkFxQ2hDOzs7Ozs7Ozs7Ozs7Ozs7OztZQUtMeEMsa0JBQWtCeUIsTUFBTSxHQUFHLHFCQUMxQiw4REFBQ0Q7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNHO29CQUFFSCxXQUFVOzhCQUFvQzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPM0Q7R0F6Tk1oQztLQUFBQTtBQTJOTiwrREFBZUEsZUFBZUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9JbnN0cnVtZW50VGFibGUudHN4PzJhZmMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZU1lbW8gfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IEluc3RydW1lbnQsIE1hcmtldERhdGEsIEluc3RydW1lbnRUYWJsZVByb3BzIH0gZnJvbSAnQC90eXBlcyc7XHJcblxyXG5jb25zdCBJbnN0cnVtZW50VGFibGU6IFJlYWN0LkZDPEluc3RydW1lbnRUYWJsZVByb3BzPiA9ICh7XHJcbiAgaW5zdHJ1bWVudHMsXHJcbiAgbWFya2V0RGF0YSxcclxuICBvbkluc3RydW1lbnRTZWxlY3QsXHJcbiAgbG9hZGluZyA9IGZhbHNlLFxyXG59KSA9PiB7XHJcbiAgY29uc3QgW3NvcnRGaWVsZCwgc2V0U29ydEZpZWxkXSA9IHVzZVN0YXRlPGtleW9mIEluc3RydW1lbnQ+KCdzeW1ib2wnKTtcclxuICBjb25zdCBbc29ydERpcmVjdGlvbiwgc2V0U29ydERpcmVjdGlvbl0gPSB1c2VTdGF0ZTwnYXNjJyB8ICdkZXNjJz4oJ2FzYycpO1xyXG5cclxuICAvLyBTb3J0IGluc3RydW1lbnRzXHJcbiAgY29uc3Qgc29ydGVkSW5zdHJ1bWVudHMgPSB1c2VNZW1vKCgpID0+IHtcclxuICAgIHJldHVybiBbLi4uaW5zdHJ1bWVudHNdLnNvcnQoKGEsIGIpID0+IHtcclxuICAgICAgY29uc3QgYVZhbHVlID0gYVtzb3J0RmllbGRdO1xyXG4gICAgICBjb25zdCBiVmFsdWUgPSBiW3NvcnRGaWVsZF07XHJcblxyXG4gICAgICAvLyBIYW5kbGUgdW5kZWZpbmVkIHZhbHVlc1xyXG4gICAgICBpZiAoYVZhbHVlID09PSB1bmRlZmluZWQgJiYgYlZhbHVlID09PSB1bmRlZmluZWQpIHJldHVybiAwO1xyXG4gICAgICBpZiAoYVZhbHVlID09PSB1bmRlZmluZWQpIHJldHVybiAxO1xyXG4gICAgICBpZiAoYlZhbHVlID09PSB1bmRlZmluZWQpIHJldHVybiAtMTtcclxuXHJcbiAgICAgIGlmIChhVmFsdWUgPT09IGJWYWx1ZSkgcmV0dXJuIDA7XHJcblxyXG4gICAgICBjb25zdCBjb21wYXJpc29uID0gYVZhbHVlIDwgYlZhbHVlID8gLTEgOiAxO1xyXG4gICAgICByZXR1cm4gc29ydERpcmVjdGlvbiA9PT0gJ2FzYycgPyBjb21wYXJpc29uIDogLWNvbXBhcmlzb247XHJcbiAgICB9KTtcclxuICB9LCBbaW5zdHJ1bWVudHMsIHNvcnRGaWVsZCwgc29ydERpcmVjdGlvbl0pO1xyXG5cclxuICBjb25zdCBoYW5kbGVTb3J0ID0gKGZpZWxkOiBrZXlvZiBJbnN0cnVtZW50KSA9PiB7XHJcbiAgICBpZiAoc29ydEZpZWxkID09PSBmaWVsZCkge1xyXG4gICAgICBzZXRTb3J0RGlyZWN0aW9uKHNvcnREaXJlY3Rpb24gPT09ICdhc2MnID8gJ2Rlc2MnIDogJ2FzYycpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgc2V0U29ydEZpZWxkKGZpZWxkKTtcclxuICAgICAgc2V0U29ydERpcmVjdGlvbignYXNjJyk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZm9ybWF0UHJpY2UgPSAocHJpY2U6IG51bWJlciB8IHVuZGVmaW5lZCk6IHN0cmluZyA9PiB7XHJcbiAgICBpZiAocHJpY2UgPT09IHVuZGVmaW5lZCB8fCBwcmljZSA9PT0gMCkgcmV0dXJuICctJztcclxuICAgIHJldHVybiBg4oK5JHtwcmljZS50b0ZpeGVkKDIpfWA7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZm9ybWF0Q2hhbmdlID0gKGNoYW5nZTogbnVtYmVyIHwgdW5kZWZpbmVkLCBjaGFuZ2VQZXJjZW50OiBudW1iZXIgfCB1bmRlZmluZWQpOiBzdHJpbmcgPT4ge1xyXG4gICAgaWYgKGNoYW5nZSA9PT0gdW5kZWZpbmVkIHx8IGNoYW5nZVBlcmNlbnQgPT09IHVuZGVmaW5lZCkgcmV0dXJuICctJztcclxuICAgIGNvbnN0IHNpZ24gPSBjaGFuZ2UgPj0gMCA/ICcrJyA6ICcnO1xyXG4gICAgcmV0dXJuIGAke3NpZ259JHtjaGFuZ2UudG9GaXhlZCgyKX0gKCR7c2lnbn0ke2NoYW5nZVBlcmNlbnQudG9GaXhlZCgyKX0lKWA7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZ2V0Q2hhbmdlQ29sb3IgPSAoY2hhbmdlOiBudW1iZXIgfCB1bmRlZmluZWQpOiBzdHJpbmcgPT4ge1xyXG4gICAgaWYgKGNoYW5nZSA9PT0gdW5kZWZpbmVkIHx8IGNoYW5nZSA9PT0gMCkgcmV0dXJuICd0ZXh0LWdyYXktNjAwJztcclxuICAgIHJldHVybiBjaGFuZ2UgPiAwID8gJ3RleHQtZ3JlZW4tNjAwJyA6ICd0ZXh0LXJlZC02MDAnO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGZvcm1hdFZvbHVtZSA9ICh2b2x1bWU6IG51bWJlciB8IHVuZGVmaW5lZCk6IHN0cmluZyA9PiB7XHJcbiAgICBpZiAodm9sdW1lID09PSB1bmRlZmluZWQgfHwgdm9sdW1lID09PSAwKSByZXR1cm4gJy0nO1xyXG4gICAgaWYgKHZvbHVtZSA+PSAxMDAwMDAwMCkgcmV0dXJuIGAkeyh2b2x1bWUgLyAxMDAwMDAwMCkudG9GaXhlZCgxKX1DcmA7XHJcbiAgICBpZiAodm9sdW1lID49IDEwMDAwMCkgcmV0dXJuIGAkeyh2b2x1bWUgLyAxMDAwMDApLnRvRml4ZWQoMSl9TGA7XHJcbiAgICBpZiAodm9sdW1lID49IDEwMDApIHJldHVybiBgJHsodm9sdW1lIC8gMTAwMCkudG9GaXhlZCgxKX1LYDtcclxuICAgIHJldHVybiB2b2x1bWUudG9TdHJpbmcoKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBTb3J0SWNvbiA9ICh7IGZpZWxkIH06IHsgZmllbGQ6IGtleW9mIEluc3RydW1lbnQgfSkgPT4ge1xyXG4gICAgaWYgKHNvcnRGaWVsZCAhPT0gZmllbGQpIHtcclxuICAgICAgcmV0dXJuIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj7ihpU8L3NwYW4+O1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIHNvcnREaXJlY3Rpb24gPT09ICdhc2MnID8gPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMFwiPuKGkTwvc3Bhbj4gOiA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwXCI+4oaTPC9zcGFuPjtcclxuICB9O1xyXG5cclxuICBpZiAobG9hZGluZykge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJnbGFzcyByb3VuZGVkLTJ4bCBzaGFkb3ctbGcgcC02XCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLTY0XCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwaW5uZXIgdy04IGgtOCBtci0zXCI+PC9kaXY+XHJcbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+TG9hZGluZyBpbnN0cnVtZW50cy4uLjwvc3Bhbj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgaWYgKGluc3RydW1lbnRzLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJnbGFzcyByb3VuZGVkLTJ4bCBzaGFkb3ctbGcgcC02XCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LWxnXCI+Tm8gaW5zdHJ1bWVudHMgZm91bmQ8L3A+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQtc20gbXQtMlwiPlRyeSBhZGp1c3RpbmcgeW91ciBmaWx0ZXJzPC9wPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJnbGFzcyByb3VuZGVkLTJ4bCBzaGFkb3ctbGcgb3ZlcmZsb3ctaGlkZGVuXCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxyXG4gICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+TWFya2V0IEluc3RydW1lbnRzPC9oMj5cclxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHRleHQtc20gbXQtMVwiPlxyXG4gICAgICAgICAgU2hvd2luZyB7c29ydGVkSW5zdHJ1bWVudHMubGVuZ3RofSBpbnN0cnVtZW50c1xyXG4gICAgICAgIDwvcD5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0byBjdXN0b20tc2Nyb2xsYmFyXCIgc3R5bGU9e3sgbWF4SGVpZ2h0OiAnNjAwcHgnIH19PlxyXG4gICAgICAgIDx0YWJsZSBjbGFzc05hbWU9XCJtYXJrZXQtdGFibGVcIj5cclxuICAgICAgICAgIDx0aGVhZCBjbGFzc05hbWU9XCJzdGlja3kgdG9wLTAgYmctZ3JheS01MCB6LTEwXCI+XHJcbiAgICAgICAgICAgIDx0cj5cclxuICAgICAgICAgICAgICA8dGhcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImN1cnNvci1wb2ludGVyIGhvdmVyOmJnLWdyYXktMTAwXCJcclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVNvcnQoJ3NlY3VyaXR5SWQnKX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgICAgICBTZWN1cml0eSBJRFxyXG4gICAgICAgICAgICAgICAgICA8U29ydEljb24gZmllbGQ9XCJzZWN1cml0eUlkXCIgLz5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvdGg+XHJcbiAgICAgICAgICAgICAgPHRoXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1ncmF5LTEwMFwiXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTb3J0KCdzeW1ib2wnKX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgICAgICBTeW1ib2xcclxuICAgICAgICAgICAgICAgICAgPFNvcnRJY29uIGZpZWxkPVwic3ltYm9sXCIgLz5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvdGg+XHJcbiAgICAgICAgICAgICAgPHRoXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1ncmF5LTEwMFwiXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTb3J0KCdkaXNwbGF5TmFtZScpfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICAgIE5hbWVcclxuICAgICAgICAgICAgICAgICAgPFNvcnRJY29uIGZpZWxkPVwiZGlzcGxheU5hbWVcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC90aD5cclxuICAgICAgICAgICAgICA8dGggXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1ncmF5LTEwMFwiXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTb3J0KCdleGNoYW5nZScpfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICAgIEV4Y2hhbmdlXHJcbiAgICAgICAgICAgICAgICAgIDxTb3J0SWNvbiBmaWVsZD1cImV4Y2hhbmdlXCIgLz5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvdGg+XHJcbiAgICAgICAgICAgICAgPHRoIFxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY3Vyc29yLXBvaW50ZXIgaG92ZXI6YmctZ3JheS0xMDBcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlU29ydCgnaW5zdHJ1bWVudFR5cGUnKX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgICAgICBUeXBlXHJcbiAgICAgICAgICAgICAgICAgIDxTb3J0SWNvbiBmaWVsZD1cImluc3RydW1lbnRUeXBlXCIgLz5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvdGg+XHJcbiAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5MVFA8L3RoPlxyXG4gICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+Q2hhbmdlPC90aD5cclxuICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlZvbHVtZTwvdGg+XHJcbiAgICAgICAgICAgICAgPHRoIFxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY3Vyc29yLXBvaW50ZXIgaG92ZXI6YmctZ3JheS0xMDBcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlU29ydCgnbG90U2l6ZScpfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICAgIExvdCBTaXplXHJcbiAgICAgICAgICAgICAgICAgIDxTb3J0SWNvbiBmaWVsZD1cImxvdFNpemVcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC90aD5cclxuICAgICAgICAgICAgPC90cj5cclxuICAgICAgICAgIDwvdGhlYWQ+XHJcbiAgICAgICAgICA8dGJvZHk+XHJcbiAgICAgICAgICAgIHtzb3J0ZWRJbnN0cnVtZW50cy5tYXAoKGluc3RydW1lbnQpID0+IHtcclxuICAgICAgICAgICAgICBjb25zdCBkYXRhID0gbWFya2V0RGF0YS5nZXQoaW5zdHJ1bWVudC5zZWN1cml0eUlkKTtcclxuICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgPHRyXHJcbiAgICAgICAgICAgICAgICAgIGtleT17aW5zdHJ1bWVudC5zZWN1cml0eUlkfVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJob3ZlcjpiZy1ncmF5LTUwIGN1cnNvci1wb2ludGVyIHRyYW5zaXRpb24tY29sb3JzXCJcclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb25JbnN0cnVtZW50U2VsZWN0Py4oaW5zdHJ1bWVudCl9XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJmb250LW1vbm8gdGV4dC1zbSB0ZXh0LWdyYXktNzAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAge2luc3RydW1lbnQuc2VjdXJpdHlJZH1cclxuICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtYmx1ZS02MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICB7aW5zdHJ1bWVudC5zeW1ib2x9XHJcbiAgICAgICAgICAgICAgICAgIDwvdGQ+XHJcbiAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJtYXgtdy14cyB0cnVuY2F0ZVwiIHRpdGxlPXtpbnN0cnVtZW50LmRpc3BsYXlOYW1lfT5cclxuICAgICAgICAgICAgICAgICAgICB7aW5zdHJ1bWVudC5kaXNwbGF5TmFtZX1cclxuICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICAgPHRkPlxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0yLjUgcHktMC41IHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHtpbnN0cnVtZW50LmV4Y2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICAgPHRkPlxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0yLjUgcHktMC41IHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJnLWdyYXktMTAwIHRleHQtZ3JheS04MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHtpbnN0cnVtZW50Lmluc3RydW1lbnRUeXBlfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInRleHQtcmlnaHQgZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0UHJpY2UoZGF0YT8ubHRwKX1cclxuICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT17YHRleHQtcmlnaHQgZm9udC1tZWRpdW0gJHtnZXRDaGFuZ2VDb2xvcihkYXRhPy5jaGFuZ2UpfWB9PlxyXG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXRDaGFuZ2UoZGF0YT8uY2hhbmdlLCBkYXRhPy5jaGFuZ2VQZXJjZW50KX1cclxuICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cclxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0Vm9sdW1lKGRhdGE/LnZvbHVtZSl9XHJcbiAgICAgICAgICAgICAgICAgIDwvdGQ+XHJcbiAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAge2luc3RydW1lbnQubG90U2l6ZS50b0xvY2FsZVN0cmluZygpfVxyXG4gICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgPC90cj5cclxuICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICB9KX1cclxuICAgICAgICAgIDwvdGJvZHk+XHJcbiAgICAgICAgPC90YWJsZT5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7c29ydGVkSW5zdHJ1bWVudHMubGVuZ3RoID4gMTAwICYmIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBiZy1ncmF5LTUwIGJvcmRlci10IGJvcmRlci1ncmF5LTIwMFwiPlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIHRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICAgIFNob3dpbmcgZmlyc3QgMTAwIGluc3RydW1lbnRzLiBVc2UgZmlsdGVycyB0byBuYXJyb3cgZG93biByZXN1bHRzLlxyXG4gICAgICAgICAgPC9wPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApfVxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEluc3RydW1lbnRUYWJsZTtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VNZW1vIiwiSW5zdHJ1bWVudFRhYmxlIiwiaW5zdHJ1bWVudHMiLCJtYXJrZXREYXRhIiwib25JbnN0cnVtZW50U2VsZWN0IiwibG9hZGluZyIsInNvcnRGaWVsZCIsInNldFNvcnRGaWVsZCIsInNvcnREaXJlY3Rpb24iLCJzZXRTb3J0RGlyZWN0aW9uIiwic29ydGVkSW5zdHJ1bWVudHMiLCJzb3J0IiwiYSIsImIiLCJhVmFsdWUiLCJiVmFsdWUiLCJ1bmRlZmluZWQiLCJjb21wYXJpc29uIiwiaGFuZGxlU29ydCIsImZpZWxkIiwiZm9ybWF0UHJpY2UiLCJwcmljZSIsInRvRml4ZWQiLCJmb3JtYXRDaGFuZ2UiLCJjaGFuZ2UiLCJjaGFuZ2VQZXJjZW50Iiwic2lnbiIsImdldENoYW5nZUNvbG9yIiwiZm9ybWF0Vm9sdW1lIiwidm9sdW1lIiwidG9TdHJpbmciLCJTb3J0SWNvbiIsInNwYW4iLCJjbGFzc05hbWUiLCJkaXYiLCJsZW5ndGgiLCJwIiwiaDIiLCJzdHlsZSIsIm1heEhlaWdodCIsInRhYmxlIiwidGhlYWQiLCJ0ciIsInRoIiwib25DbGljayIsInRib2R5IiwibWFwIiwiaW5zdHJ1bWVudCIsImRhdGEiLCJnZXQiLCJzZWN1cml0eUlkIiwidGQiLCJzeW1ib2wiLCJ0aXRsZSIsImRpc3BsYXlOYW1lIiwiZXhjaGFuZ2UiLCJpbnN0cnVtZW50VHlwZSIsImx0cCIsImxvdFNpemUiLCJ0b0xvY2FsZVN0cmluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/InstrumentTable.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Stats.tsx":
/*!**********************************!*\
  !*** ./src/components/Stats.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Stats = (param)=>{\n    let { totalInstruments, filteredInstruments, marketDataCount, connected, connectionStats } = param;\n    const formatUptime = (seconds)=>{\n        if (seconds < 60) return \"\".concat(seconds, \"s\");\n        if (seconds < 3600) return \"\".concat(Math.floor(seconds / 60), \"m \").concat(seconds % 60, \"s\");\n        const hours = Math.floor(seconds / 3600);\n        const minutes = Math.floor(seconds % 3600 / 60);\n        return \"\".concat(hours, \"h \").concat(minutes, \"m\");\n    };\n    const formatLastUpdate = (date)=>{\n        if (!date) return \"Never\";\n        const now = new Date();\n        const diffMs = now.getTime() - date.getTime();\n        const diffSeconds = Math.floor(diffMs / 1000);\n        if (diffSeconds < 60) return \"\".concat(diffSeconds, \"s ago\");\n        if (diffSeconds < 3600) return \"\".concat(Math.floor(diffSeconds / 60), \"m ago\");\n        return date.toLocaleTimeString();\n    };\n    const basicStats = [\n        {\n            label: \"Total Instruments\",\n            value: totalInstruments.toLocaleString(),\n            icon: \"\\uD83D\\uDCCA\",\n            color: \"bg-blue-500\",\n            description: \"Available instruments\"\n        },\n        {\n            label: \"Filtered Results\",\n            value: filteredInstruments.toLocaleString(),\n            icon: \"\\uD83D\\uDD0D\",\n            color: \"bg-purple-500\",\n            description: \"Matching filters\"\n        },\n        {\n            label: \"Live Data\",\n            value: marketDataCount.toLocaleString(),\n            icon: \"\\uD83D\\uDCC8\",\n            color: connected ? \"bg-green-500\" : \"bg-gray-500\",\n            description: connectionStats ? \"\".concat(connectionStats.connectedInstruments, \" active\") : \"Market data points\"\n        },\n        {\n            label: \"Connection\",\n            value: connected ? \"Active\" : \"Inactive\",\n            icon: connected ? \"\\uD83D\\uDFE2\" : \"\\uD83D\\uDD34\",\n            color: connected ? \"bg-green-500\" : \"bg-red-500\",\n            description: connectionStats ? \"\".concat(connectionStats.messagesReceived, \" messages\") : \"WebSocket status\"\n        }\n    ];\n    const enhancedStats = connectionStats ? [\n        {\n            label: \"Cache Size\",\n            value: connectionStats.cacheSize.toLocaleString(),\n            icon: \"\\uD83D\\uDCBE\",\n            color: \"bg-indigo-500\",\n            description: connectionStats.isAutoSaving ? \"Auto-saving\" : \"Manual save\"\n        },\n        {\n            label: \"Last Update\",\n            value: formatLastUpdate(connectionStats.lastUpdate),\n            icon: \"\\uD83D\\uDD52\",\n            color: \"bg-orange-500\",\n            description: \"Data freshness\"\n        },\n        {\n            label: \"Uptime\",\n            value: formatUptime(connectionStats.connectionUptime),\n            icon: \"⏱️\",\n            color: \"bg-teal-500\",\n            description: \"Connection stability\"\n        },\n        {\n            label: \"Reconnects\",\n            value: connectionStats.reconnectAttempts.toString(),\n            icon: connectionStats.reconnectAttempts > 0 ? \"\\uD83D\\uDD04\" : \"✅\",\n            color: connectionStats.reconnectAttempts > 0 ? \"bg-yellow-500\" : \"bg-green-500\",\n            description: \"Connection reliability\"\n        }\n    ] : [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: basicStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glass rounded-xl shadow-lg p-4 card-hover\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: stat.label\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900 mt-1\",\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: stat.description\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl ml-3\",\n                                        children: stat.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 h-1 rounded-full \".concat(stat.color)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined),\n            enhancedStats.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800 mb-3 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl mr-2\",\n                                children: \"⚡\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Real-time Connection Stats\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: enhancedStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"glass rounded-xl shadow-lg p-4 card-hover border-l-4 border-l-blue-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: stat.label\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-gray-900 mt-1\",\n                                                        children: stat.value\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: stat.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl ml-3\",\n                                                children: stat.icon\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3 h-1 rounded-full \".concat(stat.color)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Stats;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Stats);\nvar _c;\n$RefreshReg$(_c, \"Stats\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Stats.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useEnhancedMarketData.ts":
/*!********************************************!*\
  !*** ./src/hooks/useEnhancedMarketData.ts ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEnhancedMarketData: function() { return /* binding */ useEnhancedMarketData; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * Enhanced Market Data Hook\r\n * Provides optimized access to market data with automatic initialization\r\n * Features:\r\n * - Automatic WebSocket connection management\r\n * - Optimized re-rendering with selective subscriptions\r\n * - Auto-save functionality\r\n * - Error recovery and reconnection\r\n */ \nconst useEnhancedMarketData = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { autoConnect = true, autoLoadCache = true, autoSaveInterval = 30000, reconnectOnError = true, maxReconnectAttempts = 5 } = options;\n    // Temporary fallback implementation\n    const connection = {\n        status: \"disconnected\",\n        isConnected: false,\n        error: null,\n        connectionStats: {\n            totalMessages: 0,\n            connectionUptime: 0\n        }\n    };\n    const marketDataMap = new Map();\n    const cache = {\n        isLoaded: false,\n        totalCacheSize: 0,\n        pendingUpdates: 0,\n        lastCacheUpdate: null\n    };\n    const ui = {\n        filters: {},\n        sortConfig: {\n            field: \"symbol\",\n            direction: \"asc\"\n        },\n        selectedInstruments: new Set(),\n        viewMode: \"table\",\n        autoRefresh: true\n    };\n    const isLoading = false;\n    // Store actions - temporary mock functions with useCallback to fix dependency warnings\n    const initializeConnection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{}, []);\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{}, []);\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{}, []);\n    const reconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{}, []);\n    const loadFromCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{}, []);\n    const saveToCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (force)=>{}, []);\n    const clearCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{}, []);\n    const setFilters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((filters)=>{}, []);\n    const setSortConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((field, direction)=>{}, []);\n    const getFilteredMarketData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>[], []);\n    const getSortedMarketData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>[], []);\n    const getMarketDataBySecurityId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((securityId)=>undefined, []);\n    const getMarketDataBySymbol = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((symbol)=>undefined, []);\n    const subscribeToInstrument = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((securityId)=>{}, []);\n    const unsubscribeFromInstrument = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((securityId)=>{}, []);\n    const reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{}, []);\n    // Refs for intervals and tracking\n    const autoSaveIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const initializationRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Convert Map to Array for components that expect arrays\n    const marketDataArray = Array.from(marketDataMap.values());\n    // Initialize connection and cache on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const initialize = async ()=>{\n            if (initializationRef.current) return;\n            initializationRef.current = true;\n            console.log(\"\\uD83D\\uDE80 Enhanced Hook: Initializing...\");\n            try {\n                // Load cache first for instant data display\n                if (autoLoadCache && !cache.isLoaded) {\n                    await loadFromCache();\n                }\n                // Initialize WebSocket connection\n                if (autoConnect && connection.status === \"disconnected\") {\n                    await initializeConnection();\n                }\n            } catch (error) {\n                console.error(\"❌ Enhanced Hook: Initialization failed:\", error);\n            }\n        };\n        initialize();\n        // Cleanup on unmount\n        return ()=>{\n            if (autoSaveIntervalRef.current) {\n                clearInterval(autoSaveIntervalRef.current);\n            }\n        };\n    }, [\n        autoConnect,\n        autoLoadCache,\n        cache.isLoaded,\n        connection.status,\n        initializeConnection,\n        loadFromCache\n    ]);\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (autoSaveInterval > 0 && marketDataArray.length > 0) {\n            if (autoSaveIntervalRef.current) {\n                clearInterval(autoSaveIntervalRef.current);\n            }\n            autoSaveIntervalRef.current = setInterval(()=>{\n                saveToCache();\n            }, autoSaveInterval);\n            return ()=>{\n                if (autoSaveIntervalRef.current) {\n                    clearInterval(autoSaveIntervalRef.current);\n                }\n            };\n        }\n    }, [\n        autoSaveInterval,\n        marketDataArray.length,\n        saveToCache\n    ]);\n    // Auto-reconnect on error\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (reconnectOnError && connection.status === \"error\" && reconnectAttemptsRef.current < maxReconnectAttempts) {\n            const retryDelay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);\n            console.log(\"\\uD83D\\uDD04 Enhanced Hook: Auto-reconnecting in \".concat(retryDelay, \"ms (attempt \").concat(reconnectAttemptsRef.current + 1, \"/\").concat(maxReconnectAttempts, \")\"));\n            const timeoutId = setTimeout(async ()=>{\n                try {\n                    reconnectAttemptsRef.current++;\n                    await reconnect();\n                } catch (error) {\n                    console.error(\"❌ Enhanced Hook: Auto-reconnect failed:\", error);\n                }\n            }, retryDelay);\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        connection.status,\n        reconnect,\n        reconnectOnError,\n        maxReconnectAttempts\n    ]);\n    // Reset reconnect attempts on successful connection\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (connection.status === \"connected\") {\n            reconnectAttemptsRef.current = 0;\n        }\n    }, [\n        connection.status\n    ]);\n    // Optimized data access functions\n    const getFilteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        return getFilteredMarketData();\n    }, [\n        getFilteredMarketData\n    ]);\n    const getSortedData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        return getSortedMarketData();\n    }, [\n        getSortedMarketData\n    ]);\n    const getDataBySecurityId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((securityId)=>{\n        return getMarketDataBySecurityId(securityId);\n    }, [\n        getMarketDataBySecurityId\n    ]);\n    const getDataBySymbol = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((symbol)=>{\n        return getMarketDataBySymbol(symbol);\n    }, [\n        getMarketDataBySymbol\n    ]);\n    // Enhanced filter functions\n    const updateFilters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((filters)=>{\n        setFilters(filters);\n    }, [\n        setFilters\n    ]);\n    const updateSort = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((field, direction)=>{\n        setSortConfig(field, direction);\n    }, [\n        setSortConfig\n    ]);\n    // Subscription management\n    const subscribe = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((securityId)=>{\n        subscribeToInstrument(securityId);\n    }, [\n        subscribeToInstrument\n    ]);\n    const unsubscribe = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((securityId)=>{\n        unsubscribeFromInstrument(securityId);\n    }, [\n        unsubscribeFromInstrument\n    ]);\n    // Connection management\n    const forceReconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        try {\n            reconnectAttemptsRef.current = 0; // Reset attempts for manual reconnect\n            await reconnect();\n        } catch (error) {\n            console.error(\"❌ Enhanced Hook: Manual reconnect failed:\", error);\n            throw error;\n        }\n    }, [\n        reconnect\n    ]);\n    const forceRefresh = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        try {\n            await loadFromCache();\n        } catch (error) {\n            console.error(\"❌ Enhanced Hook: Force refresh failed:\", error);\n            throw error;\n        }\n    }, [\n        loadFromCache\n    ]);\n    const forceSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        try {\n            await saveToCache(true);\n        } catch (error) {\n            console.error(\"❌ Enhanced Hook: Force save failed:\", error);\n            throw error;\n        }\n    }, [\n        saveToCache\n    ]);\n    // Stats and computed values\n    const stats = {\n        totalInstruments: marketDataArray.length,\n        connectedInstruments: marketDataArray.filter((item)=>item.ltp && item.ltp > 0).length,\n        lastUpdate: null,\n        cacheSize: cache.totalCacheSize,\n        connectionUptime: connection.connectionStats.connectionUptime,\n        messagesReceived: connection.connectionStats.totalMessages,\n        reconnectAttempts: reconnectAttemptsRef.current,\n        isAutoSaving: autoSaveIntervalRef.current !== null\n    };\n    return {\n        // Data\n        marketData: marketDataArray,\n        marketDataMap,\n        filteredData: getFilteredData(),\n        sortedData: getSortedData(),\n        // Connection state\n        isConnected: connection.isConnected,\n        connectionStatus: connection.status,\n        connectionError: connection.error,\n        connectionStats: connection.connectionStats,\n        // Cache state\n        cacheLoaded: cache.isLoaded,\n        cacheUpdating: cache.pendingUpdates > 0,\n        lastCacheUpdate: cache.lastCacheUpdate,\n        // UI state\n        filters: ui.filters,\n        sortConfig: ui.sortConfig,\n        selectedInstruments: ui.selectedInstruments,\n        viewMode: ui.viewMode,\n        autoRefresh: ui.autoRefresh,\n        // Loading states\n        isLoading,\n        isInitializing: !initializationRef.current,\n        // Data access functions\n        getDataBySecurityId,\n        getDataBySymbol,\n        getFilteredData,\n        getSortedData,\n        // Actions\n        updateFilters,\n        updateSort,\n        subscribe,\n        unsubscribe,\n        // Connection management\n        connect,\n        disconnect,\n        reconnect: forceReconnect,\n        // Cache management\n        refresh: forceRefresh,\n        save: forceSave,\n        clearCache,\n        // Utility\n        reset,\n        stats,\n        // Advanced actions (expose if needed)\n        _store: {\n            setFilters,\n            setSortConfig,\n            subscribeToInstrument,\n            unsubscribeFromInstrument\n        }\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useEnhancedMarketData);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useEnhancedMarketData.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/constants.ts":
/*!******************************!*\
  !*** ./src/lib/constants.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: function() { return /* binding */ API_CONFIG; },\n/* harmony export */   CACHE_CONFIG: function() { return /* binding */ CACHE_CONFIG; },\n/* harmony export */   CHART_CONFIG: function() { return /* binding */ CHART_CONFIG; },\n/* harmony export */   COLORS: function() { return /* binding */ COLORS; },\n/* harmony export */   DEFAULTS: function() { return /* binding */ DEFAULTS; },\n/* harmony export */   DHAN_CONFIG: function() { return /* binding */ DHAN_CONFIG; },\n/* harmony export */   ENV_CONFIG: function() { return /* binding */ ENV_CONFIG; },\n/* harmony export */   ERROR_MESSAGES: function() { return /* binding */ ERROR_MESSAGES; },\n/* harmony export */   EXCHANGE_SEGMENTS: function() { return /* binding */ EXCHANGE_SEGMENTS; },\n/* harmony export */   FEATURE_FLAGS: function() { return /* binding */ FEATURE_FLAGS; },\n/* harmony export */   INSTRUMENT_TYPES: function() { return /* binding */ INSTRUMENT_TYPES; },\n/* harmony export */   LOGGING_CONFIG: function() { return /* binding */ LOGGING_CONFIG; },\n/* harmony export */   MARKET_CONFIG: function() { return /* binding */ MARKET_CONFIG; },\n/* harmony export */   OPTION_TYPES: function() { return /* binding */ OPTION_TYPES; },\n/* harmony export */   PACKET_TYPES: function() { return /* binding */ PACKET_TYPES; },\n/* harmony export */   PERFORMANCE_CONFIG: function() { return /* binding */ PERFORMANCE_CONFIG; },\n/* harmony export */   REQUEST_CODES: function() { return /* binding */ REQUEST_CODES; },\n/* harmony export */   SECURITY_CONFIG: function() { return /* binding */ SECURITY_CONFIG; },\n/* harmony export */   STORAGE_KEYS: function() { return /* binding */ STORAGE_KEYS; },\n/* harmony export */   SUCCESS_MESSAGES: function() { return /* binding */ SUCCESS_MESSAGES; },\n/* harmony export */   UI_CONFIG: function() { return /* binding */ UI_CONFIG; },\n/* harmony export */   VALIDATION: function() { return /* binding */ VALIDATION; },\n/* harmony export */   WEBSOCKET_CONFIG: function() { return /* binding */ WEBSOCKET_CONFIG; }\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/**\r\n * Application Constants\r\n */ // API Configuration\nconst API_CONFIG = {\n    BASE_URL: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8080\",\n    TIMEOUT: 30000,\n    RETRY_ATTEMPTS: 3,\n    RETRY_DELAY: 1000\n};\n// WebSocket Configuration\nconst WEBSOCKET_CONFIG = {\n    RECONNECT_INTERVAL: 3000,\n    MAX_RECONNECT_ATTEMPTS: 15,\n    PING_INTERVAL: 25000,\n    PONG_TIMEOUT: 10000,\n    CONNECTION_TIMEOUT: 20000,\n    HEARTBEAT_INTERVAL: 30000,\n    MAX_LISTENERS_PER_EVENT: 10,\n    CLEANUP_INTERVAL: 60000\n};\n// Dhan API Configuration\nconst DHAN_CONFIG = {\n    BASE_URL: \"https://api.dhan.co\",\n    WEBSOCKET_URL: \"wss://api.dhan.co/v2/wsapi\",\n    MAX_INSTRUMENTS_PER_CONNECTION: 5000,\n    MAX_INSTRUMENTS_PER_MESSAGE: 100,\n    RATE_LIMIT: {\n        REQUESTS_PER_SECOND: 10,\n        REQUESTS_PER_MINUTE: 600\n    }\n};\n// Market Configuration\nconst MARKET_CONFIG = {\n    TRADING_HOURS: {\n        START: {\n            hour: 9,\n            minute: 15\n        },\n        END: {\n            hour: 15,\n            minute: 30\n        }\n    },\n    TRADING_DAYS: [\n        1,\n        2,\n        3,\n        4,\n        5\n    ],\n    REFRESH_INTERVAL: 1000,\n    BATCH_SIZE: 100\n};\n// Instrument Types\nconst INSTRUMENT_TYPES = {\n    EQUITY: \"EQUITY\",\n    INDEX: \"INDEX\",\n    FUTIDX: \"FUTIDX\",\n    OPTIDX: \"OPTIDX\",\n    FUTSTK: \"FUTSTK\",\n    OPTSTK: \"OPTSTK\",\n    FUTCUR: \"FUTCUR\",\n    OPTCUR: \"OPTCUR\",\n    FUTCOM: \"FUTCOM\",\n    OPTFUT: \"OPTFUT\"\n};\n// Exchange Segments\nconst EXCHANGE_SEGMENTS = {\n    NSE_EQ: \"NSE_EQ\",\n    NSE_FNO: \"NSE_FNO\",\n    BSE_EQ: \"BSE_EQ\",\n    MCX_COMM: \"MCX_COMM\",\n    IDX_I: \"IDX_I\"\n};\n// Option Types\nconst OPTION_TYPES = {\n    CALL: \"CE\",\n    PUT: \"PE\"\n};\n// Market Data Packet Types\nconst PACKET_TYPES = {\n    TOUCHLINE: 1,\n    QUOTE: 2,\n    SNAP_QUOTE: 3,\n    FULL_PACKET: 4,\n    OI: 5\n};\n// WebSocket Request Codes\nconst REQUEST_CODES = {\n    SUBSCRIBE: 21,\n    UNSUBSCRIBE: 22,\n    SNAP_QUOTE: 23\n};\n// UI Configuration\nconst UI_CONFIG = {\n    DEBOUNCE_DELAY: 300,\n    THROTTLE_DELAY: 100,\n    ANIMATION_DURATION: 200,\n    TOAST_DURATION: 3000,\n    PAGINATION: {\n        DEFAULT_PAGE_SIZE: 50,\n        PAGE_SIZE_OPTIONS: [\n            25,\n            50,\n            100,\n            200\n        ]\n    }\n};\n// Color Schemes\nconst COLORS = {\n    SUCCESS: \"#10B981\",\n    ERROR: \"#EF4444\",\n    WARNING: \"#F59E0B\",\n    INFO: \"#3B82F6\",\n    NEUTRAL: \"#6B7280\",\n    BID: \"#10B981\",\n    ASK: \"#EF4444\",\n    SPOT: \"#3B82F6\"\n};\n// Chart Configuration\nconst CHART_CONFIG = {\n    DEFAULT_HEIGHT: 400,\n    COLORS: {\n        PRIMARY: \"#3B82F6\",\n        SECONDARY: \"#10B981\",\n        ACCENT: \"#F59E0B\",\n        GRID: \"#E5E7EB\",\n        TEXT: \"#374151\"\n    },\n    ANIMATION: {\n        DURATION: 300,\n        EASING: \"ease-in-out\"\n    }\n};\n// Data Validation\nconst VALIDATION = {\n    MIN_STRIKE_PRICE: 1,\n    MAX_STRIKE_PRICE: 100000,\n    MIN_OPTION_PRICE: 0.01,\n    MAX_OPTION_PRICE: 50000,\n    MIN_VOLUME: 0,\n    MAX_VOLUME: 10000000\n};\n// Error Messages\nconst ERROR_MESSAGES = {\n    NETWORK_ERROR: \"Network connection failed. Please check your internet connection.\",\n    API_ERROR: \"API request failed. Please try again later.\",\n    WEBSOCKET_ERROR: \"WebSocket connection failed. Attempting to reconnect...\",\n    DATA_PARSING_ERROR: \"Failed to parse market data. Please refresh the page.\",\n    SUBSCRIPTION_ERROR: \"Failed to subscribe to market data. Please try again.\",\n    INVALID_INSTRUMENT: \"Invalid instrument selected.\",\n    MARKET_CLOSED: \"Market is currently closed.\",\n    RATE_LIMIT_EXCEEDED: \"Too many requests. Please wait before trying again.\"\n};\n// Success Messages\nconst SUCCESS_MESSAGES = {\n    CONNECTION_ESTABLISHED: \"Successfully connected to market data feed.\",\n    SUBSCRIPTION_SUCCESS: \"Successfully subscribed to market data.\",\n    DATA_UPDATED: \"Market data updated successfully.\",\n    SETTINGS_SAVED: \"Settings saved successfully.\"\n};\n// Local Storage Keys\nconst STORAGE_KEYS = {\n    USER_PREFERENCES: \"csv_market_dashboard_preferences\",\n    SELECTED_INSTRUMENTS: \"csv_market_dashboard_selected_instruments\",\n    THEME: \"csv_market_dashboard_theme\",\n    LAYOUT: \"csv_market_dashboard_layout\"\n};\n// Feature Flags\nconst FEATURE_FLAGS = {\n    ENABLE_CHARTS: true,\n    ENABLE_ALERTS: true,\n    ENABLE_EXPORT: true,\n    ENABLE_DARK_MODE: true,\n    ENABLE_REAL_TIME_UPDATES: true,\n    ENABLE_OPTION_GREEKS: false\n};\n// Performance Monitoring\nconst PERFORMANCE_CONFIG = {\n    MEMORY_WARNING_THRESHOLD: 100 * 1024 * 1024,\n    CPU_WARNING_THRESHOLD: 80,\n    NETWORK_TIMEOUT_WARNING: 5000,\n    MAX_CONCURRENT_REQUESTS: 10\n};\n// Logging Configuration\nconst LOGGING_CONFIG = {\n    LEVELS: {\n        ERROR: 0,\n        WARN: 1,\n        INFO: 2,\n        DEBUG: 3\n    },\n    MAX_LOG_SIZE: 10 * 1024 * 1024,\n    MAX_LOG_FILES: 5,\n    LOG_ROTATION_INTERVAL: 24 * 60 * 60 * 1000\n};\n// Cache Configuration\nconst CACHE_CONFIG = {\n    DEFAULT_TTL: 5 * 60 * 1000,\n    MARKET_DATA_TTL: 10 * 60 * 1000,\n    STATIC_DATA_TTL: 30 * 60 * 1000,\n    EXPIRY_CHECK_INTERVAL: 60 * 1000,\n    MAX_CACHE_SIZE: 50 * 1024 * 1024,\n    KEYS: {\n        INSTRUMENTS: \"instruments\",\n        MARKET_DATA: \"market_data\",\n        OPTION_CHAIN: \"option_chain\",\n        EXPIRY_DATES: \"expiry_dates\",\n        NIFTY_SPOT: \"nifty_spot\",\n        USER_SETTINGS: \"user_settings\",\n        USER_PREFERENCES: \"user_preferences\"\n    }\n};\n// Security Configuration\nconst SECURITY_CONFIG = {\n    RATE_LIMITING: {\n        WINDOW_MS: 15 * 60 * 1000,\n        MAX_REQUESTS: 1000\n    },\n    CORS: {\n        ORIGIN:  false ? 0 : [\n            \"http://localhost:3000\",\n            \"http://localhost:3001\"\n        ],\n        CREDENTIALS: true\n    },\n    HEADERS: {\n        CONTENT_SECURITY_POLICY: \"default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';\",\n        X_FRAME_OPTIONS: \"DENY\",\n        X_CONTENT_TYPE_OPTIONS: \"nosniff\"\n    }\n};\n// Environment Configuration\nconst ENV_CONFIG = {\n    NODE_ENV: \"development\" || 0,\n    PORT: parseInt(process.env.PORT || \"8080\"),\n    NEXT_PORT: parseInt(process.env.NEXT_PORT || \"3000\"),\n    LOG_LEVEL: process.env.LOG_LEVEL || \"info\",\n    ENABLE_METRICS: process.env.ENABLE_METRICS === \"true\"\n};\n// Default Values\nconst DEFAULTS = {\n    NIFTY_SPOT_PRICE: 24850,\n    SELECTED_EXPIRY: \"2025-06-19\",\n    STRIKES_TO_SHOW: 20,\n    REFRESH_INTERVAL: 1000,\n    CHART_TIMEFRAME: \"1D\",\n    TABLE_PAGE_SIZE: 50\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/constants.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/data-cache.ts":
/*!*******************************!*\
  !*** ./src/lib/data-cache.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MARKET_DATA_CACHE_KEYS: function() { return /* binding */ MARKET_DATA_CACHE_KEYS; },\n/* harmony export */   cacheHelpers: function() { return /* binding */ cacheHelpers; },\n/* harmony export */   dataCache: function() { return /* binding */ dataCache; }\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(app-pages-browser)/./src/lib/constants.ts\");\n/**\r\n * Data Caching System for Market Data Persistence\r\n * Handles localStorage/sessionStorage with TTL and compression\r\n */ \nclass DataCache {\n    /**\r\n   * Check if we're in a browser environment\r\n   */ isBrowser() {\n        return  true && typeof window.localStorage !== \"undefined\";\n    }\n    static getInstance() {\n        if (!DataCache.instance) {\n            DataCache.instance = new DataCache();\n        }\n        return DataCache.instance;\n    }\n    /**\r\n   * Initialize cache for client-side usage\r\n   * Call this method when the component mounts on the client side\r\n   */ initializeClientSide() {\n        if (this.isBrowser()) {\n            console.log(\"\\uD83D\\uDE80 DataCache: Initializing client-side cache\");\n            // Perform any client-side specific initialization here\n            this.cleanupExpired() // Run initial cleanup\n            ;\n        }\n    }\n    /**\r\n   * Check if compression is supported\r\n   */ checkCompressionSupport() {\n        try {\n            // Check if CompressionStream is available (modern browsers)\n            this.compressionSupported = typeof CompressionStream !== \"undefined\";\n        } catch (e) {\n            this.compressionSupported = false;\n        }\n    }\n    /**\r\n   * Set data in cache with TTL\r\n   */ async set(key, data) {\n        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        try {\n            // Skip caching on server side\n            if (!this.isBrowser()) {\n                console.log(\"⚠️ DataCache: Skipping cache on server side for \".concat(key));\n                return false;\n            }\n            const { ttl = _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.DEFAULT_TTL, useCompression = false, storage = \"localStorage\" } = options;\n            const entry = {\n                data,\n                timestamp: Date.now(),\n                ttl,\n                version: this.version\n            };\n            let serializedData = JSON.stringify(entry);\n            // Apply compression if requested and supported\n            if (useCompression && this.compressionSupported) {\n                serializedData = await this.compress(serializedData);\n            }\n            const storageObj = storage === \"localStorage\" ? localStorage : sessionStorage;\n            const fullKey = this.getFullKey(key);\n            storageObj.setItem(fullKey, serializedData);\n            console.log(\"\\uD83D\\uDCBE DataCache: Cached \".concat(key, \" (\").concat(serializedData.length, \" bytes)\"));\n            return true;\n        } catch (error) {\n            console.error(\"❌ DataCache: Failed to cache \".concat(key, \":\"), error);\n            return false;\n        }\n    }\n    /**\r\n   * Get data from cache\r\n   */ async get(key) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        try {\n            // Return null on server side\n            if (!this.isBrowser()) {\n                return null;\n            }\n            const { storage = \"localStorage\" } = options;\n            const storageObj = storage === \"localStorage\" ? localStorage : sessionStorage;\n            const fullKey = this.getFullKey(key);\n            const serializedData = storageObj.getItem(fullKey);\n            if (!serializedData) {\n                return null;\n            }\n            let decompressedData = serializedData;\n            // Try to decompress if it looks compressed\n            if (this.compressionSupported && this.isCompressed(serializedData)) {\n                decompressedData = await this.decompress(serializedData);\n            }\n            const entry = JSON.parse(decompressedData);\n            // Check version compatibility\n            if (entry.version !== this.version) {\n                console.warn(\"⚠️ DataCache: Version mismatch for \".concat(key, \", removing\"));\n                this.remove(key, options);\n                return null;\n            }\n            // Check TTL\n            if (Date.now() - entry.timestamp > entry.ttl) {\n                console.log(\"⏰ DataCache: \".concat(key, \" expired, removing\"));\n                this.remove(key, options);\n                return null;\n            }\n            console.log(\"\\uD83D\\uDCD6 DataCache: Retrieved \".concat(key, \" from cache\"));\n            return entry.data;\n        } catch (error) {\n            console.error(\"❌ DataCache: Failed to retrieve \".concat(key, \":\"), error);\n            // Remove corrupted entry\n            this.remove(key, options);\n            return null;\n        }\n    }\n    /**\r\n   * Remove data from cache\r\n   */ remove(key) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        try {\n            // Skip on server side\n            if (!this.isBrowser()) {\n                return;\n            }\n            const { storage = \"localStorage\" } = options;\n            const storageObj = storage === \"localStorage\" ? localStorage : sessionStorage;\n            const fullKey = this.getFullKey(key);\n            storageObj.removeItem(fullKey);\n            console.log(\"\\uD83D\\uDDD1️ DataCache: Removed \".concat(key));\n        } catch (error) {\n            console.error(\"❌ DataCache: Failed to remove \".concat(key, \":\"), error);\n        }\n    }\n    /**\r\n   * Clear all cache entries\r\n   */ clear() {\n        let storage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"localStorage\";\n        try {\n            // Skip on server side\n            if (!this.isBrowser()) {\n                return;\n            }\n            const storageObj = storage === \"localStorage\" ? localStorage : sessionStorage;\n            const keysToRemove = [];\n            // Find all our cache keys\n            for(let i = 0; i < storageObj.length; i++){\n                const key = storageObj.key(i);\n                if (key && key.startsWith(\"csv_market_dashboard_cache_\")) {\n                    keysToRemove.push(key);\n                }\n            }\n            // Remove them\n            keysToRemove.forEach((key)=>storageObj.removeItem(key));\n            console.log(\"\\uD83E\\uDDF9 DataCache: Cleared \".concat(keysToRemove.length, \" entries from \").concat(storage));\n        } catch (error) {\n            console.error(\"❌ DataCache: Failed to clear \".concat(storage, \":\"), error);\n        }\n    }\n    /**\r\n   * Get cache statistics\r\n   */ getStats() {\n        let storage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"localStorage\";\n        // Return empty stats on server side\n        if (!this.isBrowser()) {\n            return {\n                totalEntries: 0,\n                totalSize: 0,\n                oldestEntry: null,\n                newestEntry: null\n            };\n        }\n        const storageObj = storage === \"localStorage\" ? localStorage : sessionStorage;\n        let totalEntries = 0;\n        let totalSize = 0;\n        let oldestTimestamp = Infinity;\n        let newestTimestamp = 0;\n        try {\n            for(let i = 0; i < storageObj.length; i++){\n                const key = storageObj.key(i);\n                if (key && key.startsWith(\"csv_market_dashboard_cache_\")) {\n                    const value = storageObj.getItem(key);\n                    if (value) {\n                        totalEntries++;\n                        totalSize += value.length;\n                        try {\n                            const entry = JSON.parse(value);\n                            if (entry.timestamp) {\n                                oldestTimestamp = Math.min(oldestTimestamp, entry.timestamp);\n                                newestTimestamp = Math.max(newestTimestamp, entry.timestamp);\n                            }\n                        } catch (e) {\n                        // Ignore parsing errors for stats\n                        }\n                    }\n                }\n            }\n        } catch (error) {\n            console.error(\"❌ DataCache: Failed to get stats:\", error);\n        }\n        return {\n            totalEntries,\n            totalSize,\n            oldestEntry: oldestTimestamp === Infinity ? null : new Date(oldestTimestamp),\n            newestEntry: newestTimestamp === 0 ? null : new Date(newestTimestamp)\n        };\n    }\n    /**\r\n   * Start cleanup interval to remove expired entries\r\n   */ startCleanupInterval() {\n        // Only start cleanup interval on client side\n        if (this.isBrowser()) {\n            setInterval(()=>{\n                this.cleanupExpired();\n            }, _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.EXPIRY_CHECK_INTERVAL);\n        }\n    }\n    /**\r\n   * Clean up expired entries\r\n   */ cleanupExpired() {\n        try {\n            // Skip cleanup on server side\n            if (!this.isBrowser()) {\n                return;\n            }\n            const storages = [\n                \"localStorage\",\n                \"sessionStorage\"\n            ];\n            storages.forEach((storageType)=>{\n                const storageObj = storageType === \"localStorage\" ? localStorage : sessionStorage;\n                const keysToRemove = [];\n                for(let i = 0; i < storageObj.length; i++){\n                    const key = storageObj.key(i);\n                    if (key && key.startsWith(\"csv_market_dashboard_cache_\")) {\n                        try {\n                            const value = storageObj.getItem(key);\n                            if (value) {\n                                const entry = JSON.parse(value);\n                                if (Date.now() - entry.timestamp > entry.ttl) {\n                                    keysToRemove.push(key);\n                                }\n                            }\n                        } catch (e) {\n                            // Remove corrupted entries\n                            keysToRemove.push(key);\n                        }\n                    }\n                }\n                keysToRemove.forEach((key)=>storageObj.removeItem(key));\n                if (keysToRemove.length > 0) {\n                    console.log(\"\\uD83E\\uDDF9 DataCache: Cleaned up \".concat(keysToRemove.length, \" expired entries from \").concat(storageType));\n                }\n            });\n        } catch (error) {\n            console.error(\"❌ DataCache: Cleanup failed:\", error);\n        }\n    }\n    /**\r\n   * Generate full cache key\r\n   */ getFullKey(key) {\n        return \"csv_market_dashboard_cache_\".concat(key);\n    }\n    /**\r\n   * Check if data is compressed\r\n   */ isCompressed(data) {\n        // Simple heuristic: compressed data usually starts with specific bytes\n        return data.startsWith(\"H4sI\") || data.startsWith(\"eJy\") // Common compression signatures\n        ;\n    }\n    /**\r\n   * Compress data (placeholder for future implementation)\r\n   */ async compress(data) {\n        // For now, return as-is. Can implement actual compression later\n        return data;\n    }\n    /**\r\n   * Decompress data (placeholder for future implementation)\r\n   */ async decompress(data) {\n        // For now, return as-is. Can implement actual decompression later\n        return data;\n    }\n    constructor(){\n        this.version = \"1.0.0\";\n        this.compressionSupported = false;\n        this.checkCompressionSupport();\n        this.startCleanupInterval();\n    }\n}\nDataCache.instance = null;\n// Export singleton instance\nconst dataCache = DataCache.getInstance();\n// Export cache keys for market data\nconst MARKET_DATA_CACHE_KEYS = _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.KEYS;\n// Export helper functions\nconst cacheHelpers = {\n    /**\r\n   * Cache market data with medium TTL for page refresh persistence\r\n   */ cacheMarketData: async (key, data)=>{\n        return dataCache.set(key, data, {\n            ttl: _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.MARKET_DATA_TTL,\n            storage: \"localStorage\" // Use localStorage for page refresh persistence\n        });\n    },\n    /**\r\n   * Cache static data with long TTL\r\n   */ cacheStaticData: async (key, data)=>{\n        return dataCache.set(key, data, {\n            ttl: _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.STATIC_DATA_TTL,\n            storage: \"localStorage\" // Use local storage for persistent data\n        });\n    },\n    /**\r\n   * Get cached market data\r\n   */ getCachedMarketData: async (key)=>{\n        return dataCache.get(key, {\n            storage: \"localStorage\"\n        });\n    },\n    /**\r\n   * Get cached static data\r\n   */ getCachedStaticData: async (key)=>{\n        return dataCache.get(key, {\n            storage: \"localStorage\"\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/data-cache.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider'); // TODO: Delete with enableRenderableContext\n\nvar REACT_CONSUMER_TYPE = Symbol.for('react.consumer');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\nvar enableRenderableContext = false;\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false;\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n}\n\nvar REACT_CLIENT_REFERENCE$2 = Symbol.for('react.client.reference'); // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  if (typeof type === 'function') {\n    if (type.$$typeof === REACT_CLIENT_REFERENCE$2) {\n      // TODO: Create a convention for naming client references with debug info.\n      return null;\n    }\n\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    {\n      if (typeof type.tag === 'number') {\n        error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n      }\n    }\n\n    switch (type.$$typeof) {\n      case REACT_PROVIDER_TYPE:\n        {\n          var provider = type;\n          return getContextName(provider._context) + '.Provider';\n        }\n\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n\n        {\n          return getContextName(context) + '.Consumer';\n        }\n\n      case REACT_CONSUMER_TYPE:\n        {\n          return null;\n        }\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n    }\n  }\n\n  return null;\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar assign = Object.assign;\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || enableRenderableContext  || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n/**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */\n\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n  /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */\n\n\n  var RunInRootFrame = {\n    DetermineComponentFrameRoot: function () {\n      var control;\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe[prop-missing]\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          } // TODO(luna): This will currently only throw if the function component\n          // tries to access React/ReactDOM/props. We should probably make this throw\n          // in simple components too\n\n\n          var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n          // component, which we don't yet support. Attach a noop catch handler to\n          // silence the error.\n          // TODO: Implement component stacks for async client components?\n\n          if (maybePromise && typeof maybePromise.catch === 'function') {\n            maybePromise.catch(function () {});\n          }\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          return [sample.stack, control.stack];\n        }\n      }\n\n      return [null, null];\n    }\n  }; // $FlowFixMe[prop-missing]\n\n  RunInRootFrame.DetermineComponentFrameRoot.displayName = 'DetermineComponentFrameRoot';\n  var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, 'name'); // Before ES6, the `name` property was not configurable.\n\n  if (namePropDescriptor && namePropDescriptor.configurable) {\n    // V8 utilizes a function's `name` property when generating a stack trace.\n    Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // Configurable properties can be updated even if its writable descriptor\n    // is set to `false`.\n    // $FlowFixMe[cannot-write]\n    'name', {\n      value: 'DetermineComponentFrameRoot'\n    });\n  }\n\n  try {\n    var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n\n    if (sampleStack && controlStack) {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sampleStack.split('\\n');\n      var controlLines = controlStack.split('\\n');\n      var s = 0;\n      var c = 0;\n\n      while (s < sampleLines.length && !sampleLines[s].includes('DetermineComponentFrameRoot')) {\n        s++;\n      }\n\n      while (c < controlLines.length && !controlLines[c].includes('DetermineComponentFrameRoot')) {\n        c++;\n      } // We couldn't find our intentionally injected common root frame, attempt\n      // to find another common root frame by search from the bottom of the\n      // control stack...\n\n\n      if (s === sampleLines.length || c === controlLines.length) {\n        s = sampleLines.length - 1;\n        c = controlLines.length - 1;\n\n        while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n          // We expect at least one stack frame to be shared.\n          // Typically this will be the root most one. However, stack frames may be\n          // cut off due to maximum stack limits. In this case, one maybe cut off\n          // earlier than the other. We assume that the sample is longer or the same\n          // and there for cut off earlier. So we should find the root most frame in\n          // the sample somewhere in the control.\n          c--;\n        }\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                if (true) {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    {\n      var warnAboutAccessingRef = function () {\n        if (!specialPropRefWarningShown) {\n          specialPropRefWarningShown = true;\n\n          error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n        }\n      };\n\n      warnAboutAccessingRef.isReactWarning = true;\n      Object.defineProperty(props, 'ref', {\n        get: warnAboutAccessingRef,\n        configurable: true\n      });\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, _ref, self, source, owner, props) {\n  var ref;\n\n  {\n    ref = _ref;\n  }\n\n  var element;\n\n  {\n    // In prod, `ref` is a regular property. It will be removed in a\n    // future release.\n    element = {\n      // This tag allows us to uniquely identify this as a React Element\n      $$typeof: REACT_ELEMENT_TYPE,\n      // Built-in properties that belong on the element\n      type: type,\n      key: key,\n      ref: ref,\n      props: props,\n      // Record the component responsible for creating this element.\n      _owner: owner\n    };\n  }\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // debugInfo contains Server Component debug information.\n\n    Object.defineProperty(element, '_debugInfo', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: null\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\nvar didWarnAboutKeySpread = {};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, isStaticChildren, source, self) {\n  {\n    if (!isValidElementType(type)) {\n      // This is an invalid element type.\n      //\n      // We warn in this case but don't throw. We expect the element creation to\n      // succeed and there will likely be errors in render.\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    } else {\n      // This is a valid element type.\n      // Skip key warning if the type isn't valid since our key validation logic\n      // doesn't expect a non-string/function type and can throw confusing\n      // errors. We don't want exception behavior to differ between dev and\n      // prod. (Rendering will throw with a helpful message and as soon as the\n      // type is fixed, the key warnings will appear.)\n      var children = config.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    } // Warn about key spread regardless of whether the type is valid.\n\n\n    if (hasOwnProperty.call(config, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(config).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      {\n        ref = config.ref;\n      }\n\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && // Skip over reserved prop names\n      propName !== 'key' && (propName !== 'ref')) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    var element = ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    }\n\n    return element;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nvar ownerHasKeyUseWarning = {};\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = getComponentNameFromType(parentType);\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  // TODO: Move this to render phase instead of at element creation.\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar jsxDEV = jsxDEV$1 ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/MzdmZSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

});