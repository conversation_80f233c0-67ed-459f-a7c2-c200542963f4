(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{721:(e,t,s)=>{"use strict";s.d(t,{Q:()=>r});var a=s(2115);let r=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{autoConnect:t=!0,autoLoadCache:s=!0,autoSaveInterval:r=3e4,reconnectOnError:l=!0,maxReconnectAttempts:n=5}=e,c={status:"disconnected",isConnected:!1,error:null,connectionStats:{totalMessages:0,connectionUptime:0}},i=new Map,o={isLoaded:!1,totalCacheSize:0,pendingUpdates:0,lastCacheUpdate:null},d=(0,a.useMemo)(()=>({filters:{},sortConfig:{field:"symbol",direction:"asc"},selectedInstruments:new Set,viewMode:"table",autoRefresh:!0}),[]),h=(0,a.useCallback)(async()=>{},[]),m=(0,a.useCallback)(async()=>{},[]),u=(0,a.useCallback)(()=>{},[]),x=(0,a.useCallback)(async()=>{},[]),g=(0,a.useCallback)(async()=>{},[]),p=(0,a.useCallback)(async e=>{},[]),v=(0,a.useCallback)(async()=>{},[]),f=(0,a.useCallback)(e=>{},[]),b=(0,a.useCallback)((e,t)=>{},[]),y=(0,a.useCallback)(()=>[],[]),N=(0,a.useCallback)(()=>[],[]),j=(0,a.useCallback)(e=>void 0,[]),S=(0,a.useCallback)(e=>void 0,[]),C=(0,a.useCallback)(e=>{},[]),E=(0,a.useCallback)(e=>{},[]),k=(0,a.useCallback)(()=>{},[]),T=(0,a.useRef)(null),w=(0,a.useRef)(0),D=(0,a.useRef)(!1),I=Array.from(i.values());(0,a.useEffect)(()=>((async()=>{if(!D.current){D.current=!0,console.log("\uD83D\uDE80 Enhanced Hook: Initializing...");try{s&&!o.isLoaded&&await g(),t&&"disconnected"===c.status&&await h()}catch(e){console.error("❌ Enhanced Hook: Initialization failed:",e)}}})(),()=>{T.current&&clearInterval(T.current)}),[t,s,o.isLoaded,c.status,h,g]),(0,a.useEffect)(()=>{if(r>0&&I.length>0)return T.current&&clearInterval(T.current),T.current=setInterval(()=>{p()},r),()=>{T.current&&clearInterval(T.current)}},[r,I.length,p]),(0,a.useEffect)(()=>{if(l&&"error"===c.status&&w.current<n){let e=Math.min(1e3*Math.pow(2,w.current),3e4);console.log("\uD83D\uDD04 Enhanced Hook: Auto-reconnecting in ".concat(e,"ms (attempt ").concat(w.current+1,"/").concat(n,")"));let t=setTimeout(async()=>{try{w.current++,await x()}catch(e){console.error("❌ Enhanced Hook: Auto-reconnect failed:",e)}},e);return()=>clearTimeout(t)}},[c.status,x,l,n]),(0,a.useEffect)(()=>{"connected"===c.status&&(w.current=0)},[c.status]);let _=(0,a.useCallback)(()=>y(),[y]),L=(0,a.useCallback)(()=>N(),[N]),A=(0,a.useCallback)(e=>j(e),[j]),R=(0,a.useCallback)(e=>S(e),[S]),M=(0,a.useCallback)(e=>{f(e)},[f,d]),F=(0,a.useCallback)((e,t)=>{b(e,t)},[b]),U=(0,a.useCallback)(e=>{C(e)},[C]),O=(0,a.useCallback)(e=>{E(e)},[E]),z=(0,a.useCallback)(async()=>{try{w.current=0,await x()}catch(e){throw console.error("❌ Enhanced Hook: Manual reconnect failed:",e),e}},[x]),P=(0,a.useCallback)(async()=>{try{await g()}catch(e){throw console.error("❌ Enhanced Hook: Force refresh failed:",e),e}},[g]),H=(0,a.useCallback)(async()=>{try{await p(!0)}catch(e){throw console.error("❌ Enhanced Hook: Force save failed:",e),e}},[p]),V={totalInstruments:I.length,connectedInstruments:I.filter(e=>e.ltp&&e.ltp>0).length,lastUpdate:null,cacheSize:o.totalCacheSize,connectionUptime:c.connectionStats.connectionUptime,messagesReceived:c.connectionStats.totalMessages,reconnectAttempts:w.current,isAutoSaving:null!==T.current};return{marketData:I,marketDataMap:i,filteredData:_(),sortedData:L(),isConnected:c.isConnected,connectionStatus:c.status,connectionError:c.error,connectionStats:c.connectionStats,cacheLoaded:o.isLoaded,cacheUpdating:o.pendingUpdates>0,lastCacheUpdate:o.lastCacheUpdate,filters:d.filters,sortConfig:d.sortConfig,selectedInstruments:d.selectedInstruments,viewMode:d.viewMode,autoRefresh:d.autoRefresh,isLoading:!1,isInitializing:!D.current,getDataBySecurityId:A,getDataBySymbol:R,getFilteredData:_,getSortedData:L,updateFilters:M,updateSort:F,subscribe:U,unsubscribe:O,connect:m,disconnect:u,reconnect:z,refresh:P,save:H,clearCache:v,reset:k,stats:V,_store:{setFilters:f,setSortConfig:b,subscribeToInstrument:C,unsubscribeFromInstrument:E}}}},942:(e,t,s)=>{"use strict";s.d(t,{aq:()=>n,ko:()=>c});var a=s(1950);class r{isBrowser(){return void 0!==window.localStorage}static getInstance(){return r.instance||(r.instance=new r),r.instance}initializeClientSide(){this.isBrowser()&&(console.log("\uD83D\uDE80 DataCache: Initializing client-side cache"),this.cleanupExpired())}checkCompressionSupport(){try{this.compressionSupported="undefined"!=typeof CompressionStream}catch(e){this.compressionSupported=!1}}async set(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};try{if(!this.isBrowser())return console.log("⚠️ DataCache: Skipping cache on server side for ".concat(e)),!1;let{ttl:r=a.WS.DEFAULT_TTL,useCompression:l=!1,storage:n="localStorage"}=s,c={data:t,timestamp:Date.now(),ttl:r,version:this.version},i=JSON.stringify(c);l&&this.compressionSupported&&(i=await this.compress(i));let o="localStorage"===n?localStorage:sessionStorage,d=this.getFullKey(e);return o.setItem(d,i),console.log("\uD83D\uDCBE DataCache: Cached ".concat(e," (").concat(i.length," bytes)")),!0}catch(t){return console.error("❌ DataCache: Failed to cache ".concat(e,":"),t),!1}}async get(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{if(!this.isBrowser())return null;let{storage:s="localStorage"}=t,a="localStorage"===s?localStorage:sessionStorage,r=this.getFullKey(e),l=a.getItem(r);if(!l)return null;let n=l;this.compressionSupported&&this.isCompressed(l)&&(n=await this.decompress(l));let c=JSON.parse(n);if(c.version!==this.version)return console.warn("⚠️ DataCache: Version mismatch for ".concat(e,", removing")),this.remove(e,t),null;if(Date.now()-c.timestamp>c.ttl)return console.log("⏰ DataCache: ".concat(e," expired, removing")),this.remove(e,t),null;return console.log("\uD83D\uDCD6 DataCache: Retrieved ".concat(e," from cache")),c.data}catch(s){return console.error("❌ DataCache: Failed to retrieve ".concat(e,":"),s),this.remove(e,t),null}}remove(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{if(!this.isBrowser())return;let{storage:s="localStorage"}=t,a="localStorage"===s?localStorage:sessionStorage,r=this.getFullKey(e);a.removeItem(r),console.log("\uD83D\uDDD1️ DataCache: Removed ".concat(e))}catch(t){console.error("❌ DataCache: Failed to remove ".concat(e,":"),t)}}clear(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"localStorage";try{if(!this.isBrowser())return;let t="localStorage"===e?localStorage:sessionStorage,s=[];for(let e=0;e<t.length;e++){let a=t.key(e);a&&a.startsWith("csv_market_dashboard_cache_")&&s.push(a)}s.forEach(e=>t.removeItem(e)),console.log("\uD83E\uDDF9 DataCache: Cleared ".concat(s.length," entries from ").concat(e))}catch(t){console.error("❌ DataCache: Failed to clear ".concat(e,":"),t)}}getStats(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"localStorage";if(!this.isBrowser())return{totalEntries:0,totalSize:0,oldestEntry:null,newestEntry:null};let t="localStorage"===e?localStorage:sessionStorage,s=0,a=0,r=1/0,l=0;try{for(let e=0;e<t.length;e++){let n=t.key(e);if(n&&n.startsWith("csv_market_dashboard_cache_")){let e=t.getItem(n);if(e){s++,a+=e.length;try{let t=JSON.parse(e);t.timestamp&&(r=Math.min(r,t.timestamp),l=Math.max(l,t.timestamp))}catch(e){}}}}}catch(e){console.error("❌ DataCache: Failed to get stats:",e)}return{totalEntries:s,totalSize:a,oldestEntry:r===1/0?null:new Date(r),newestEntry:0===l?null:new Date(l)}}startCleanupInterval(){this.isBrowser()&&setInterval(()=>{this.cleanupExpired()},a.WS.EXPIRY_CHECK_INTERVAL)}cleanupExpired(){try{if(!this.isBrowser())return;["localStorage","sessionStorage"].forEach(e=>{let t="localStorage"===e?localStorage:sessionStorage,s=[];for(let e=0;e<t.length;e++){let a=t.key(e);if(a&&a.startsWith("csv_market_dashboard_cache_"))try{let e=t.getItem(a);if(e){let t=JSON.parse(e);Date.now()-t.timestamp>t.ttl&&s.push(a)}}catch(e){s.push(a)}}s.forEach(e=>t.removeItem(e)),s.length>0&&console.log("\uD83E\uDDF9 DataCache: Cleaned up ".concat(s.length," expired entries from ").concat(e))})}catch(e){console.error("❌ DataCache: Cleanup failed:",e)}}getFullKey(e){return"csv_market_dashboard_cache_".concat(e)}isCompressed(e){return e.startsWith("H4sI")||e.startsWith("eJy")}async compress(e){return e}async decompress(e){return e}constructor(){this.version="1.0.0",this.compressionSupported=!1,this.checkCompressionSupport(),this.startCleanupInterval()}}r.instance=null;let l=r.getInstance(),n=a.WS.KEYS,c={cacheMarketData:async(e,t)=>l.set(e,t,{ttl:a.WS.MARKET_DATA_TTL,storage:"localStorage"}),cacheStaticData:async(e,t)=>l.set(e,t,{ttl:a.WS.STATIC_DATA_TTL,storage:"localStorage"}),getCachedMarketData:async e=>l.get(e,{storage:"localStorage"}),getCachedStaticData:async e=>l.get(e,{storage:"localStorage"})}},1950:(e,t,s)=>{"use strict";s.d(t,{WS:()=>n,i3:()=>r,ld:()=>l});var a=s(9509);let r={BASE_URL:a.env.NEXT_PUBLIC_API_URL||"http://localhost:8080",TIMEOUT:3e4,RETRY_ATTEMPTS:3,RETRY_DELAY:1e3},l={RECONNECT_INTERVAL:3e3,MAX_RECONNECT_ATTEMPTS:15,PING_INTERVAL:25e3,PONG_TIMEOUT:1e4,CONNECTION_TIMEOUT:2e4,HEARTBEAT_INTERVAL:3e4,MAX_LISTENERS_PER_EVENT:10,CLEANUP_INTERVAL:6e4},n={DEFAULT_TTL:3e5,MARKET_DATA_TTL:6e5,STATIC_DATA_TTL:18e5,EXPIRY_CHECK_INTERVAL:6e4,MAX_CACHE_SIZE:0x3200000,KEYS:{INSTRUMENTS:"instruments",MARKET_DATA:"market_data",OPTION_CHAIN:"option_chain",EXPIRY_DATES:"expiry_dates",NIFTY_SPOT:"nifty_spot",USER_SETTINGS:"user_settings",USER_PREFERENCES:"user_preferences"}};parseInt(a.env.PORT||"8080"),parseInt(a.env.NEXT_PORT||"3000"),a.env.LOG_LEVEL,a.env.ENABLE_METRICS},8332:(e,t,s)=>{Promise.resolve().then(s.bind(s,8886))},8886:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var a=s(5155),r=s(2115);let l=e=>{let{instruments:t,marketData:s,onInstrumentSelect:l,loading:n=!1}=e,[c,i]=(0,r.useState)("symbol"),[o,d]=(0,r.useState)("asc"),h=(0,r.useMemo)(()=>[...t].sort((e,t)=>{let s=e[c],a=t[c];if(void 0===s&&void 0===a)return 0;if(void 0===s)return 1;if(void 0===a)return -1;if(s===a)return 0;let r=s<a?-1:1;return"asc"===o?r:-r}),[t,c,o]),m=e=>{c===e?d("asc"===o?"desc":"asc"):(i(e),d("asc"))},u=e=>void 0===e||0===e?"-":"₹".concat(e.toFixed(2)),x=(e,t)=>{if(void 0===e||void 0===t)return"-";let s=e>=0?"+":"";return"".concat(s).concat(e.toFixed(2)," (").concat(s).concat(t.toFixed(2),"%)")},g=e=>void 0===e||0===e?"text-gray-600":e>0?"text-green-600":"text-red-600",p=e=>void 0===e||0===e?"-":e>=1e7?"".concat((e/1e7).toFixed(1),"Cr"):e>=1e5?"".concat((e/1e5).toFixed(1),"L"):e>=1e3?"".concat((e/1e3).toFixed(1),"K"):e.toString(),v=e=>{let{field:t}=e;return c!==t?(0,a.jsx)("span",{className:"text-gray-400",children:"↕"}):"asc"===o?(0,a.jsx)("span",{className:"text-blue-600",children:"↑"}):(0,a.jsx)("span",{className:"text-blue-600",children:"↓"})};return n?(0,a.jsx)("div",{className:"glass rounded-2xl shadow-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)("div",{className:"spinner w-8 h-8 mr-3"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Loading instruments..."})]})}):0===t.length?(0,a.jsx)("div",{className:"glass rounded-2xl shadow-lg p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"No instruments found"}),(0,a.jsx)("p",{className:"text-gray-500 text-sm mt-2",children:"Try adjusting your filters"})]})}):(0,a.jsxs)("div",{className:"glass rounded-2xl shadow-lg overflow-hidden",children:[(0,a.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Market Instruments"}),(0,a.jsxs)("p",{className:"text-gray-600 text-sm mt-1",children:["Showing ",h.length," instruments"]})]}),(0,a.jsx)("div",{className:"overflow-x-auto custom-scrollbar",style:{maxHeight:"600px"},children:(0,a.jsxs)("table",{className:"market-table",children:[(0,a.jsx)("thead",{className:"sticky top-0 bg-gray-50 z-10",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>m("securityId"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Security ID",(0,a.jsx)(v,{field:"securityId"})]})}),(0,a.jsx)("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>m("symbol"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Symbol",(0,a.jsx)(v,{field:"symbol"})]})}),(0,a.jsx)("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>m("displayName"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Name",(0,a.jsx)(v,{field:"displayName"})]})}),(0,a.jsx)("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>m("exchange"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Exchange",(0,a.jsx)(v,{field:"exchange"})]})}),(0,a.jsx)("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>m("instrumentType"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Type",(0,a.jsx)(v,{field:"instrumentType"})]})}),(0,a.jsx)("th",{className:"text-right",children:"LTP"}),(0,a.jsx)("th",{className:"text-right",children:"Change"}),(0,a.jsx)("th",{className:"text-right",children:"Volume"}),(0,a.jsx)("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>m("lotSize"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Lot Size",(0,a.jsx)(v,{field:"lotSize"})]})})]})}),(0,a.jsx)("tbody",{children:h.map(e=>{let t=s.get(e.securityId);return(0,a.jsxs)("tr",{className:"hover:bg-gray-50 cursor-pointer transition-colors",onClick:()=>null==l?void 0:l(e),children:[(0,a.jsx)("td",{className:"font-mono text-sm text-gray-700",children:e.securityId}),(0,a.jsx)("td",{className:"font-medium text-blue-600",children:e.symbol}),(0,a.jsx)("td",{className:"max-w-xs truncate",title:e.displayName,children:e.displayName}),(0,a.jsx)("td",{children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e.exchange})}),(0,a.jsx)("td",{children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:e.instrumentType})}),(0,a.jsx)("td",{className:"text-right font-medium",children:u(null==t?void 0:t.ltp)}),(0,a.jsx)("td",{className:"text-right font-medium ".concat(g(null==t?void 0:t.change)),children:x(null==t?void 0:t.change,null==t?void 0:t.changePercent)}),(0,a.jsx)("td",{className:"text-right",children:p(null==t?void 0:t.volume)}),(0,a.jsx)("td",{className:"text-right",children:e.lotSize.toLocaleString()})]},e.securityId)})})]})}),h.length>100&&(0,a.jsx)("div",{className:"p-4 bg-gray-50 border-t border-gray-200",children:(0,a.jsx)("p",{className:"text-sm text-gray-600 text-center",children:"Showing first 100 instruments. Use filters to narrow down results."})})]})},n=e=>{var t,s,l;let{filter:n,onFilterChange:c,exchanges:i,instrumentTypes:o,segments:d}=e,[h,m]=(0,r.useState)(n.search||""),u=(e,t)=>{let s=n.exchange||[],a=t?[...s,e]:s.filter(t=>t!==e);c({...n,exchange:a.length>0?a:void 0})},x=(e,t)=>{let s=n.instrumentType||[],a=t?[...s,e]:s.filter(t=>t!==e);c({...n,instrumentType:a.length>0?a:void 0})},g=(e,t)=>{let s=n.segment||[],a=t?[...s,e]:s.filter(t=>t!==e);c({...n,segment:a.length>0?a:void 0})},p=e=>{m(e),c({...n,search:e||void 0})},v=()=>{var e,t,s;return!!((null==(e=n.exchange)?void 0:e.length)||(null==(t=n.instrumentType)?void 0:t.length)||(null==(s=n.segment)?void 0:s.length)||n.search||void 0!==n.isActive||void 0!==n.hasExpiry)};return(0,a.jsxs)("div",{className:"filter-panel",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Filters"}),v()&&(0,a.jsx)("button",{onClick:()=>{m(""),c({})},className:"text-sm text-blue-600 hover:text-blue-800 font-medium",children:"Clear All"})]}),(0,a.jsxs)("div",{className:"filter-group",children:[(0,a.jsx)("label",{className:"filter-label",children:"Search"}),(0,a.jsx)("input",{type:"text",placeholder:"Search by symbol, name, or ISIN...",value:h,onChange:e=>p(e.target.value),className:"filter-input"})]}),(0,a.jsxs)("div",{className:"filter-group",children:[(0,a.jsx)("label",{className:"filter-label",children:"Exchanges"}),(0,a.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto custom-scrollbar",children:i.map(e=>{var t;return(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:(null==(t=n.exchange)?void 0:t.includes(e))||!1,onChange:t=>u(e,t.target.checked),className:"filter-checkbox"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e})]},e)})})]}),(0,a.jsxs)("div",{className:"filter-group",children:[(0,a.jsx)("label",{className:"filter-label",children:"Instrument Types"}),(0,a.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto custom-scrollbar",children:o.map(e=>{var t;return(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:(null==(t=n.instrumentType)?void 0:t.includes(e))||!1,onChange:t=>x(e,t.target.checked),className:"filter-checkbox"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e})]},e)})})]}),(0,a.jsxs)("div",{className:"filter-group",children:[(0,a.jsx)("label",{className:"filter-label",children:"Segments"}),(0,a.jsx)("div",{className:"space-y-2",children:d.map(e=>{var t;return(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:(null==(t=n.segment)?void 0:t.includes(e))||!1,onChange:t=>g(e,t.target.checked),className:"filter-checkbox"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:"C"===e?"Cash (C)":"F"===e?"Futures (F)":"O"===e?"Options (O)":e})]},e)})})]}),(0,a.jsxs)("div",{className:"filter-group",children:[(0,a.jsx)("label",{className:"filter-label",children:"Additional Filters"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:!0===n.isActive,onChange:e=>c({...n,isActive:!!e.target.checked||void 0}),className:"filter-checkbox"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:"Active Only"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:!0===n.hasExpiry,onChange:e=>c({...n,hasExpiry:!!e.target.checked||void 0}),className:"filter-checkbox"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:"Has Expiry"})]})]})]}),(0,a.jsxs)("div",{className:"filter-group",children:[(0,a.jsx)("label",{className:"filter-label",children:"Lot Size Range"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsx)("input",{type:"number",placeholder:"Min",value:n.minLotSize||"",onChange:e=>c({...n,minLotSize:e.target.value?parseInt(e.target.value):void 0}),className:"filter-input text-sm"}),(0,a.jsx)("input",{type:"number",placeholder:"Max",value:n.maxLotSize||"",onChange:e=>c({...n,maxLotSize:e.target.value?parseInt(e.target.value):void 0}),className:"filter-input text-sm"})]})]}),v()&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"Active Filters:"}),(0,a.jsxs)("div",{className:"space-y-1 text-xs text-blue-700",children:[(null==(t=n.exchange)?void 0:t.length)&&(0,a.jsxs)("div",{children:["Exchanges: ",n.exchange.join(", ")]}),(null==(s=n.instrumentType)?void 0:s.length)&&(0,a.jsxs)("div",{children:["Types: ",n.instrumentType.join(", ")]}),(null==(l=n.segment)?void 0:l.length)&&(0,a.jsxs)("div",{children:["Segments: ",n.segment.join(", ")]}),n.search&&(0,a.jsxs)("div",{children:["Search: “",n.search,"”"]}),n.isActive&&(0,a.jsx)("div",{children:"Active instruments only"}),n.hasExpiry&&(0,a.jsx)("div",{children:"With expiry date"}),(n.minLotSize||n.maxLotSize)&&(0,a.jsxs)("div",{children:["Lot size: ",n.minLotSize||0," - ",n.maxLotSize||"∞"]})]})]})]})},c=e=>{let{connected:t}=e;return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(t?"bg-green-500 animate-pulse":"bg-red-500")}),(0,a.jsx)("span",{className:"text-sm font-medium ".concat(t?"text-green-700":"text-red-700"),children:t?"Connected":"Disconnected"})]}),(0,a.jsx)("div",{className:"px-3 py-1 rounded-full text-xs font-semibold ".concat(t?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:t?"LIVE":"OFFLINE"})]})},i=e=>{let{totalInstruments:t,filteredInstruments:s,marketDataCount:r,connected:l,connectionStats:n}=e,c=[{label:"Total Instruments",value:t.toLocaleString(),icon:"\uD83D\uDCCA",color:"bg-blue-500",description:"Available instruments"},{label:"Filtered Results",value:s.toLocaleString(),icon:"\uD83D\uDD0D",color:"bg-purple-500",description:"Matching filters"},{label:"Live Data",value:r.toLocaleString(),icon:"\uD83D\uDCC8",color:l?"bg-green-500":"bg-gray-500",description:n?"".concat(n.connectedInstruments," active"):"Market data points"},{label:"Connection",value:l?"Active":"Inactive",icon:l?"\uD83D\uDFE2":"\uD83D\uDD34",color:l?"bg-green-500":"bg-red-500",description:n?"".concat(n.messagesReceived," messages"):"WebSocket status"}],i=n?[{label:"Cache Size",value:n.cacheSize.toLocaleString(),icon:"\uD83D\uDCBE",color:"bg-indigo-500",description:n.isAutoSaving?"Auto-saving":"Manual save"},{label:"Last Update",value:(e=>{if(!e)return"Never";let t=Math.floor((new Date().getTime()-e.getTime())/1e3);return t<60?"".concat(t,"s ago"):t<3600?"".concat(Math.floor(t/60),"m ago"):e.toLocaleTimeString()})(n.lastUpdate),icon:"\uD83D\uDD52",color:"bg-orange-500",description:"Data freshness"},{label:"Uptime",value:(e=>{if(e<60)return"".concat(e,"s");if(e<3600)return"".concat(Math.floor(e/60),"m ").concat(e%60,"s");let t=Math.floor(e/3600),s=Math.floor(e%3600/60);return"".concat(t,"h ").concat(s,"m")})(n.connectionUptime),icon:"⏱️",color:"bg-teal-500",description:"Connection stability"},{label:"Reconnects",value:n.reconnectAttempts.toString(),icon:n.reconnectAttempts>0?"\uD83D\uDD04":"✅",color:n.reconnectAttempts>0?"bg-yellow-500":"bg-green-500",description:"Connection reliability"}]:[];return(0,a.jsxs)("div",{className:"space-y-6 mb-6",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:c.map((e,t)=>(0,a.jsxs)("div",{className:"glass rounded-xl shadow-lg p-4 card-hover",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.label}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:e.value}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.description})]}),(0,a.jsx)("div",{className:"text-2xl ml-3",children:e.icon})]}),(0,a.jsx)("div",{className:"mt-3 h-1 rounded-full ".concat(e.color)})]},t))}),i.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 mb-3 flex items-center",children:[(0,a.jsx)("span",{className:"text-xl mr-2",children:"⚡"}),"Real-time Connection Stats"]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:i.map((e,t)=>(0,a.jsxs)("div",{className:"glass rounded-xl shadow-lg p-4 card-hover border-l-4 border-l-blue-400",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.label}),(0,a.jsx)("p",{className:"text-xl font-bold text-gray-900 mt-1",children:e.value}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.description})]}),(0,a.jsx)("div",{className:"text-xl ml-3",children:e.icon})]}),(0,a.jsx)("div",{className:"mt-3 h-1 rounded-full ".concat(e.color)})]},t))})]})]})};var o=s(721),d=s(942);function h(){let{marketData:e,marketDataMap:t,isConnected:s,connectionError:h,connectionStatus:m,isLoading:u,cacheLoaded:x,refresh:g,stats:p,updateFilters:v,filters:f,getFilteredData:b,getSortedData:y}=(0,o.Q)(),[N,j]=(0,r.useState)([]),[S,C]=(0,r.useState)(new Map),[E,k]=(0,r.useState)({}),[T,w]=(0,r.useState)(!0),[D,I]=(0,r.useState)(null),[_,L]=(0,r.useState)([]),[A,R]=(0,r.useState)([]);(0,r.useEffect)(()=>{let t=new Map;e.forEach(e=>{e.securityId&&t.set(e.securityId,e)}),C(t)},[e]);let M=(0,r.useCallback)(async()=>{try{console.log("\uD83C\uDF10 Dashboard: Loading fresh instruments from API...");let e=await fetch("".concat("http://localhost:8080]","/api/instruments"));if(e.ok){let t=await e.json();console.log("✅ Dashboard: Loaded",t.data.instruments.length,"instruments from API"),j(t.data.instruments),await d.ko.cacheStaticData(d.aq.INSTRUMENTS,t.data.instruments),console.log("\uD83D\uDCBE Dashboard: Cached instruments data"),w(!1)}else console.error("❌ Dashboard: Failed to load instruments:",e.statusText),I("Failed to load instruments"),w(!1)}catch(e){console.error("❌ Dashboard: Error fetching fresh instruments:",e),I("Error fetching instruments"),w(!1)}},[]),F=(0,r.useCallback)(async()=>{try{w(!0);let e=await d.ko.getCachedStaticData(d.aq.INSTRUMENTS);if(e&&Array.isArray(e)){console.log("✅ Dashboard: Loaded instruments from cache"),j(e),w(!1),M();return}await M()}catch(e){console.error("❌ Dashboard: Error loading instruments:",e),I("Error loading instruments"),w(!1)}},[M]);(0,r.useEffect)(()=>{F()},[F]),(0,r.useEffect)(()=>{(async()=>{try{let e="http://localhost:8080]",t=await fetch("".concat(e,"/api/exchanges"));if(t.ok){let e=await t.json();L(e.data)}let s=await fetch("".concat(e,"/api/instrument-types"));if(s.ok){let e=await s.json();R(e.data)}}catch(e){console.error("❌ Error loading metadata:",e)}})()},[]);let U=N.filter(e=>{if(E.exchange&&E.exchange.length>0&&!E.exchange.includes(e.exchange)||E.instrumentType&&E.instrumentType.length>0&&!E.instrumentType.includes(e.instrumentType))return!1;if(E.search){let t=E.search.toLowerCase();return e.symbol.toLowerCase().includes(t)||e.displayName.toLowerCase().includes(t)||e.isin&&e.isin.toLowerCase().includes(t)}return!0});return T?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading market data..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen p-6",children:[(0,a.jsx)("div",{className:"glass rounded-2xl shadow-lg p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold gradient-text",children:"CSV Market Dashboard"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Real-time market data from CSV instruments"}),(0,a.jsxs)("div",{className:"mt-3 space-x-3",children:[(0,a.jsx)("a",{href:"/subscribed",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors",children:"\uD83D\uDCCA View Subscribed Data Dashboard"}),(0,a.jsx)("a",{href:"/option-chain",className:"inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors",children:"\uD83D\uDD17 NIFTY Option Chain"})]})]}),(0,a.jsx)(c,{connected:s})]})}),(D||h)&&(0,a.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6",children:[(0,a.jsx)("strong",{children:"Error:"})," ",D||h]}),(0,a.jsx)(i,{totalInstruments:N.length,filteredInstruments:U.length,marketDataCount:p.totalInstruments,connected:s,connectionStats:p}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsx)(n,{filter:E,onFilterChange:e=>{k(e)},exchanges:_,instrumentTypes:A,segments:["C","F","O"]})}),(0,a.jsx)("div",{className:"lg:col-span-3",children:(0,a.jsx)(l,{instruments:U.slice(0,100),marketData:S,onInstrumentSelect:e=>{console.log("Selected instrument:",e)},loading:T})})]}),(0,a.jsxs)("div",{className:"mt-8 text-center text-sm text-gray-500",children:[(0,a.jsxs)("p",{children:["CSV Market Dashboard - Real-time data from ",N.length," instruments"]}),(0,a.jsxs)("p",{children:["Last updated: ",new Date().toLocaleTimeString()]})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(8332)),_N_E=e.O()}]);