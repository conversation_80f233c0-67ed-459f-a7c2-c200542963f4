require('dotenv').config({ path: '.env.database' });
const { PrismaClient } = require('./generated/prisma');

async function checkSymbols() {
  const prisma = new PrismaClient();

  try {
    console.log('🔌 Connecting to database...');
    await prisma.$connect();
    console.log('✅ Connected successfully');

    // Check distinct symbols
    const symbols = await prisma.$queryRaw`
      SELECT DISTINCT "SYMBOL_NAME", COUNT(*) as count
      FROM "Instruments" 
      WHERE "SYMBOL_NAME" LIKE '%NIFTY%'
      GROUP BY "SYMBOL_NAME"
      ORDER BY count DESC
      LIMIT 20
    `;
    
    console.log('\n📊 NIFTY-related symbols:');
    symbols.forEach(sym => {
      console.log(`  - ${sym.SYMBOL_NAME}: ${sym.count} instruments`);
    });

    // Check instrument types
    const types = await prisma.$queryRaw`
      SELECT DISTINCT "INSTRUMENT_TYPE", COUNT(*) as count
      FROM "Instruments" 
      GROUP BY "INSTRUMENT_TYPE"
      ORDER BY count DESC
    `;
    
    console.log('\n🔧 Instrument types:');
    types.forEach(type => {
      console.log(`  - ${type.INSTRUMENT_TYPE}: ${type.count} instruments`);
    });

    // Check sample data
    const sample = await prisma.$queryRaw`
      SELECT "SYMBOL_NAME", "INSTRUMENT_TYPE", "SM_EXPIRY_DATE", "STRIKE_PRICE", "OPTION_TYPE"
      FROM "Instruments" 
      WHERE "SYMBOL_NAME" LIKE '%NIFTY%'
      LIMIT 10
    `;
    
    console.log('\n📋 Sample NIFTY data:');
    sample.forEach(row => {
      console.log(`  - ${row.SYMBOL_NAME} | ${row.INSTRUMENT_TYPE} | ${row.SM_EXPIRY_DATE} | ${row.STRIKE_PRICE} | ${row.OPTION_TYPE}`);
    });

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkSymbols();
