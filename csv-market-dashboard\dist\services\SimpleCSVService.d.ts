export class SimpleCSVService {
    constructor(csvFilePath?: string);
    csvFilePath: string;
    instruments: any[];
    cache: Map<any, any>;
    loadInstruments(): Promise<any>;
    normalizeInstrument(row: any): {
        securityId: any;
        symbol: any;
        displayName: any;
        exchange: string;
        segment: any;
        instrumentType: any;
        isin: any;
        lotSize: number;
        tickSize: number;
        underlyingSymbol: any;
        expiryDate: Date | undefined;
        strikePrice: number;
        optionType: any;
        isActive: boolean;
        exchangeCode: any;
    } | null;
    getExchangeSegment(exchId: any, segment: any): "NSE_EQ" | "NSE_FNO" | "BSE_EQ" | "MCX_COMM" | "BSE_FNO";
    getExchangeCode(exchange: any): any;
    parseNumber(value: any, defaultValue?: number): number;
    parseDate(dateStr: any): Date | undefined;
    getNSEDerivativesAndIndex(): Promise<any[]>;
    getNSEDerivativesSecurityIds(): Promise<any[]>;
    getInstruments(filter?: {}): Promise<{
        instruments: any[];
        total: number;
        page: number;
        pageSize: number;
        hasMore: boolean;
    }>;
    applyFilter(instruments: any, filter: any): any;
    getExchanges(): Promise<any[]>;
    getInstrumentTypes(): Promise<any[]>;
    searchInstruments(query: any, limit?: number): Promise<any[]>;
    getInstrumentById(securityId: any): Promise<any>;
    logInstrumentStats(): void;
}
//# sourceMappingURL=SimpleCSVService.d.ts.map