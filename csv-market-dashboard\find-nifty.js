require('dotenv').config({ path: '.env.database' });
const { PrismaClient } = require('./generated/prisma');

async function findNifty() {
  const prisma = new PrismaClient();

  try {
    console.log('🔌 Connecting to database...');
    await prisma.$connect();
    console.log('✅ Connected successfully');

    // Check for any NIFTY-related symbols
    const niftySymbols = await prisma.$queryRaw`
      SELECT DISTINCT "SYMBOL_NAME", "UNDERLYING_SYMBOL", "INSTRUMENT_TYPE", COUNT(*) as count
      FROM "Instruments" 
      WHERE ("SYMBOL_NAME" LIKE '%NIFTY%' OR "UNDERLYING_SYMBOL" LIKE '%NIFTY%')
      AND "INSTRUMENT_TYPE" IN ('OPTIDX', 'OP')
      GROUP BY "SYMBOL_NAME", "UNDERLYING_SYMBOL", "INSTRUMENT_TYPE"
      ORDER BY count DESC
      LIMIT 20
    `;
    
    console.log('\n📊 NIFTY-related instruments:');
    niftySymbols.forEach(sym => {
      console.log(`  - ${sym.SYMBOL_NAME} | ${sym.UNDERLYING_SYMBOL} | ${sym.INSTRUMENT_TYPE} | ${sym.count} instruments`);
    });

    // Check for NIFTY index specifically
    const niftyIndex = await prisma.$queryRaw`
      SELECT "SYMBOL_NAME", "UNDERLYING_SYMBOL", "INSTRUMENT_TYPE", "SM_EXPIRY_DATE", "STRIKE_PRICE", "OPTION_TYPE"
      FROM "Instruments" 
      WHERE "SYMBOL_NAME" = 'NIFTY'
      LIMIT 10
    `;
    
    console.log('\n🎯 NIFTY index instruments:');
    niftyIndex.forEach(row => {
      console.log(`  - ${row.SYMBOL_NAME} | ${row.UNDERLYING_SYMBOL} | ${row.INSTRUMENT_TYPE} | ${row.SM_EXPIRY_DATE} | ${row.STRIKE_PRICE} | ${row.OPTION_TYPE}`);
    });

    // Check for NIFTY options with different patterns
    const niftyOptions = await prisma.$queryRaw`
      SELECT "SYMBOL_NAME", "UNDERLYING_SYMBOL", "INSTRUMENT_TYPE", "SM_EXPIRY_DATE", "STRIKE_PRICE", "OPTION_TYPE"
      FROM "Instruments" 
      WHERE "SYMBOL_NAME" LIKE 'NIFTY%'
      AND "INSTRUMENT_TYPE" = 'OPTIDX'
      LIMIT 10
    `;
    
    console.log('\n📋 NIFTY option patterns:');
    niftyOptions.forEach(row => {
      console.log(`  - ${row.SYMBOL_NAME} | ${row.UNDERLYING_SYMBOL} | ${row.INSTRUMENT_TYPE} | ${row.SM_EXPIRY_DATE} | ${row.STRIKE_PRICE} | ${row.OPTION_TYPE}`);
    });

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

findNifty();
