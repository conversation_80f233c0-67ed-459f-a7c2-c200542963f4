/**
 * Enhanced Redis Client for Next.js - Market Data Caching
 * Features:
 * - Automatic connection management
 * - Connection pooling
 * - Error recovery
 * - Performance monitoring
 * - Configurable TTL and compression
 */
import { RedisClientType } from 'redis';
import { MarketData } from '../types';
/**
 * Get or create Redis client instance with enhanced connection management
 */
export declare function getRedisClient(): Promise<RedisClientType | null>;
/**
 * Cache market data to Redis
 */
export declare function cacheMarketData(key: string, data: MarketData | MarketData[], ttlSeconds?: number): Promise<boolean>;
/**
 * Get market data from Redis
 */
export declare function getCachedMarketData<T = MarketData | MarketData[]>(key: string): Promise<T | null>;
/**
 * Cache bulk market data efficiently
 */
export declare function cacheBulkMarketData(data: MarketData[], ttlSeconds?: number): Promise<boolean>;
/**
 * Get bulk market data from Redis
 */
export declare function getBulkMarketData(): Promise<MarketData[]>;
/**
 * Cache individual market data entry
 */
export declare function cacheMarketDataEntry(securityId: string, data: MarketData, ttlSeconds?: number): Promise<boolean>;
/**
 * Get individual market data entry
 */
export declare function getMarketDataEntry(securityId: string): Promise<MarketData | null>;
/**
 * Remove data from Redis
 */
export declare function removeFromCache(key: string): Promise<boolean>;
/**
 * Clear all cache entries with pattern
 */
export declare function clearCache(pattern?: string): Promise<number>;
/**
 * Get Redis cache statistics
 */
export declare function getCacheStats(): Promise<{
    totalKeys: number;
    memoryUsage: string;
    connectedClients: number;
    uptime: number;
}>;
/**
 * Disconnect from Redis
 */
export declare function disconnectRedis(): Promise<void>;
/**
 * Get all latest ticks from Redis
 */
export declare function getAllLatestTicks(): Promise<MarketData[]>;
export declare const redisCacheHelpers: {
    cacheMarketData: typeof cacheMarketData;
    getCachedMarketData: typeof getCachedMarketData;
    cacheBulkMarketData: typeof cacheBulkMarketData;
    getBulkMarketData: typeof getBulkMarketData;
    cacheStaticData: (key: string, data: any) => Promise<boolean>;
};
declare const redisClient: {
    getRedisClient: typeof getRedisClient;
    cacheMarketData: typeof cacheMarketData;
    getCachedMarketData: typeof getCachedMarketData;
    cacheBulkMarketData: typeof cacheBulkMarketData;
    getBulkMarketData: typeof getBulkMarketData;
    cacheMarketDataEntry: typeof cacheMarketDataEntry;
    getMarketDataEntry: typeof getMarketDataEntry;
    removeFromCache: typeof removeFromCache;
    clearCache: typeof clearCache;
    getCacheStats: typeof getCacheStats;
    disconnectRedis: typeof disconnectRedis;
    redisCacheHelpers: {
        cacheMarketData: typeof cacheMarketData;
        getCachedMarketData: typeof getCachedMarketData;
        cacheBulkMarketData: typeof cacheBulkMarketData;
        getBulkMarketData: typeof getBulkMarketData;
        cacheStaticData: (key: string, data: any) => Promise<boolean>;
    };
    getAllLatestTicks: typeof getAllLatestTicks;
};
export default redisClient;
//# sourceMappingURL=redis-client.d.ts.map