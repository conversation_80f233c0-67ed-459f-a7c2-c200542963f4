"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/option-chain/page",{

/***/ "(app-pages-browser)/./src/components/OptionChain.tsx":
/*!****************************************!*\
  !*** ./src/components/OptionChain.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OptionChain; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/data-cache */ \"(app-pages-browser)/./src/lib/data-cache.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OptionChain(param) {\n    let { marketData } = param;\n    _s();\n    const [expiryData, setExpiryData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedExpiry, setSelectedExpiry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [optionChain, setOptionChain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [niftySpotPrice, setNiftySpotPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [cacheLoaded, setCacheLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load cached data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadCachedData = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDCD6 OptionChain: Loading cached data...\");\n                // Load cached option chain data\n                const cachedOptionChain = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.getCachedStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.OPTION_CHAIN);\n                if (cachedOptionChain && typeof cachedOptionChain === \"object\" && \"rows\" in cachedOptionChain) {\n                    setOptionChain(cachedOptionChain);\n                    console.log(\"✅ OptionChain: Loaded option chain from cache\");\n                }\n                // Load cached expiry data\n                const cachedExpiryData = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.getCachedStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.EXPIRY_DATES);\n                if (cachedExpiryData && typeof cachedExpiryData === \"object\" && \"expiries\" in cachedExpiryData && Array.isArray(cachedExpiryData.expiries)) {\n                    setExpiryData(cachedExpiryData);\n                    if (cachedExpiryData.expiries.length > 0) {\n                        setSelectedExpiry(cachedExpiryData.expiries[0]);\n                    }\n                    console.log(\"✅ OptionChain: Loaded expiry data from cache\");\n                }\n                // Load cached NIFTY spot price\n                const cachedNiftySpot = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.getCachedMarketData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.NIFTY_SPOT);\n                if (cachedNiftySpot && typeof cachedNiftySpot === \"object\" && \"ltp\" in cachedNiftySpot && typeof cachedNiftySpot.ltp === \"number\") {\n                    setNiftySpotPrice(cachedNiftySpot.ltp);\n                    console.log(\"✅ OptionChain: Loaded NIFTY spot from cache:\", cachedNiftySpot.ltp);\n                }\n                setCacheLoaded(true);\n            } catch (error) {\n                console.error(\"❌ OptionChain: Failed to load cached data:\", error);\n                setCacheLoaded(true);\n            }\n        };\n        loadCachedData();\n    }, []);\n    // ✅ ENHANCED: Get NIFTY spot price from subscribed market data with caching\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // ✅ FIXED: Direct lookup for NIFTY spot (security ID 13) - should be subscribed with IDX_I exchange\n        const niftyData = marketData.get(\"13\");\n        if (niftyData && niftyData.ltp > 0) {\n            setNiftySpotPrice(niftyData.ltp);\n            // Cache the NIFTY spot price\n            _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.cacheMarketData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.NIFTY_SPOT, niftyData);\n            console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Updated:\", niftyData.ltp, \"from security ID 13 (IDX_I)\");\n            return;\n        }\n        // ✅ ENHANCED: Fallback search for any NIFTY index data with better logging\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol === \"NIFTY\" && data.ltp > 0) {\n                setNiftySpotPrice(data.ltp);\n                // Cache the NIFTY spot price\n                _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.cacheMarketData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.NIFTY_SPOT, data);\n                console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Found:\", data.ltp, \"from security:\", securityId, \"exchange:\", data.exchange);\n                return;\n            }\n        }\n        // ✅ FIXED: Only use mock price if absolutely no real data is available and no cached data\n        if (niftySpotPrice === 0 && cacheLoaded) {\n            const mockPrice = 24850;\n            setNiftySpotPrice(mockPrice);\n            console.warn(\"[SPOT] ⚠️ Using mock NIFTY Spot Price:\", mockPrice, \"(INDEX data not available - check subscription)\");\n        }\n    }, [\n        marketData,\n        cacheLoaded,\n        niftySpotPrice\n    ]);\n    // Fetch expiry dates on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchExpiryDates();\n    }, []);\n    // Build option chain when expiry is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedExpiry && niftySpotPrice > 0) {\n            buildOptionChain();\n        }\n    }, [\n        selectedExpiry,\n        niftySpotPrice,\n        marketData\n    ]);\n    const fetchExpiryDates = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Check cache first\n            const cachedExpiryData = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.getCachedStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.EXPIRY_DATES);\n            if (cachedExpiryData && typeof cachedExpiryData === \"object\" && \"expiries\" in cachedExpiryData && Array.isArray(cachedExpiryData.expiries)) {\n                console.log(\"✅ OptionChain: Using cached expiry data\");\n                setExpiryData(cachedExpiryData);\n                if (cachedExpiryData.expiries.length > 0) {\n                    setSelectedExpiry(cachedExpiryData.expiries[0]);\n                }\n                setLoading(false);\n                return;\n            }\n            console.log(\"\\uD83C\\uDF10 OptionChain: Fetching fresh expiry data from API\");\n            const response = await fetch(\"/api/nifty-expiry\");\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch expiry dates: \".concat(response.statusText));\n            }\n            const result = await response.json();\n            if (result.success) {\n                setExpiryData(result.data);\n                // Cache the expiry data\n                await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.cacheStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.EXPIRY_DATES, result.data);\n                console.log(\"\\uD83D\\uDCBE OptionChain: Cached expiry data\");\n                // Auto-select first expiry\n                if (result.data.expiries.length > 0) {\n                    setSelectedExpiry(result.data.expiries[0]);\n                }\n            } else {\n                throw new Error(result.message || \"Failed to fetch expiry dates\");\n            }\n        } catch (err) {\n            console.error(\"❌ OptionChain: Error fetching expiry dates:\", err);\n            setError(err instanceof Error ? err.message : \"Unknown error\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const buildOptionChain = ()=>{\n        if (!selectedExpiry || niftySpotPrice <= 0) return;\n        console.log(\"\\uD83D\\uDD17 Building option chain for expiry:\", selectedExpiry, \"spot:\", niftySpotPrice);\n        // Generate strike prices around spot price\n        const strikes = generateStrikes(niftySpotPrice);\n        // Build option chain rows\n        const rows = strikes.map((strike)=>{\n            const callData = findOptionData(strike, \"CE\");\n            const putData = findOptionData(strike, \"PE\");\n            return {\n                strikePrice: strike,\n                call: callData,\n                put: putData\n            };\n        });\n        const optionChainData = {\n            underlying: \"NIFTY\",\n            spotPrice: niftySpotPrice,\n            expiry: selectedExpiry,\n            rows,\n            timestamp: Date.now()\n        };\n        setOptionChain(optionChainData);\n        // Cache the option chain data\n        _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.cacheStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.OPTION_CHAIN, optionChainData);\n        console.log(\"\\uD83D\\uDCBE OptionChain: Cached option chain data for\", selectedExpiry);\n    };\n    const generateStrikes = (spotPrice)=>{\n        // First, get all available strikes from market data for the selected expiry\n        const availableStrikes = new Set();\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol.includes(\"NIFTY\") && data.symbol.includes(formatExpiryForSymbol(selectedExpiry))) {\n                // Extract strike from symbol (e.g., NIFTY-Jun2025-24850-CE)\n                const symbolParts = data.symbol.split(\"-\");\n                if (symbolParts.length >= 4) {\n                    const strike = parseFloat(symbolParts[2]);\n                    if (!isNaN(strike)) {\n                        availableStrikes.add(strike);\n                    }\n                }\n            }\n        }\n        if (availableStrikes.size === 0) {\n            // Fallback: generate strikes around spot price\n            const strikes = [];\n            const baseStrike = Math.round(spotPrice / 50) * 50;\n            for(let i = -10; i <= 10; i++){\n                strikes.push(baseStrike + i * 50);\n            }\n            return strikes.sort((a, b)=>a - b);\n        }\n        // Convert to sorted array\n        const allStrikes = Array.from(availableStrikes).sort((a, b)=>a - b);\n        // Find ATM strike (closest to spot price)\n        const atmStrike = allStrikes.reduce((prev, curr)=>Math.abs(curr - spotPrice) < Math.abs(prev - spotPrice) ? curr : prev);\n        console.log(\"[STRIKE] \\uD83C\\uDFAF ATM Strike identified: \".concat(atmStrike, \" (Spot: \").concat(spotPrice, \")\"));\n        console.log(\"[STRIKE] \\uD83D\\uDCCA Available strikes: \".concat(allStrikes.length));\n        // ✅ ENHANCED: Select more strikes around ATM (±12 strikes for better coverage)\n        const atmIndex = allStrikes.indexOf(atmStrike);\n        const selectedStrikes = [\n            ...allStrikes.slice(Math.max(0, atmIndex - 12), atmIndex),\n            atmStrike,\n            ...allStrikes.slice(atmIndex + 1, atmIndex + 13)\n        ];\n        console.log(\"[STRIKE] ✅ Selected \".concat(selectedStrikes.length, \" strikes around ATM:\"), selectedStrikes);\n        return selectedStrikes;\n    };\n    const findOptionData = (strike, optionType)=>{\n        // ✅ FIXED: Match by exact expiry date from CSV SM_EXPIRY_DATE column\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            // Check if this is a NIFTY option with matching criteria\n            if (data.symbol.includes(\"NIFTY-\") && // Exact NIFTY options (not BANKNIFTY, etc.)\n            data.symbol.includes(\"-\".concat(strike, \"-\").concat(optionType)) && data.expiryDate === selectedExpiry) {\n                var _data_marketDepth_, _data_marketDepth, _data_marketDepth_1, _data_marketDepth1, _data_marketDepth_2, _data_marketDepth2, _data_marketDepth_3, _data_marketDepth3;\n                console.log(\"[OPTION] ✅ Found \".concat(optionType, \" \").concat(strike, \": \").concat(data.symbol, \" (Expiry: \").concat(data.expiryDate, \")\"));\n                return {\n                    securityId,\n                    symbol: data.symbol,\n                    exchange: data.exchange,\n                    strikePrice: strike,\n                    optionType,\n                    expiryDate: selectedExpiry,\n                    ltp: data.ltp || 0,\n                    change: data.change || 0,\n                    changePercent: data.changePercent || 0,\n                    volume: data.volume || 0,\n                    openInterest: data.openInterest,\n                    bid: ((_data_marketDepth = data.marketDepth) === null || _data_marketDepth === void 0 ? void 0 : (_data_marketDepth_ = _data_marketDepth[0]) === null || _data_marketDepth_ === void 0 ? void 0 : _data_marketDepth_.bidPrice) || data.bid,\n                    ask: ((_data_marketDepth1 = data.marketDepth) === null || _data_marketDepth1 === void 0 ? void 0 : (_data_marketDepth_1 = _data_marketDepth1[0]) === null || _data_marketDepth_1 === void 0 ? void 0 : _data_marketDepth_1.askPrice) || data.ask,\n                    bidQty: ((_data_marketDepth2 = data.marketDepth) === null || _data_marketDepth2 === void 0 ? void 0 : (_data_marketDepth_2 = _data_marketDepth2[0]) === null || _data_marketDepth_2 === void 0 ? void 0 : _data_marketDepth_2.bidQty) || data.bidQty,\n                    askQty: ((_data_marketDepth3 = data.marketDepth) === null || _data_marketDepth3 === void 0 ? void 0 : (_data_marketDepth_3 = _data_marketDepth3[0]) === null || _data_marketDepth_3 === void 0 ? void 0 : _data_marketDepth_3.askQty) || data.askQty,\n                    high: data.high,\n                    low: data.low,\n                    open: data.open,\n                    close: data.close,\n                    timestamp: data.timestamp\n                };\n            }\n        }\n        // Debug: Log when option not found\n        if (true) {\n            console.log(\"[OPTION] ❌ Not found \".concat(optionType, \" \").concat(strike, \" for expiry \").concat(selectedExpiry));\n            // Show available options for debugging\n            const availableOptions = Array.from(marketData.values()).filter((data)=>data.symbol.includes(\"NIFTY-\") && data.symbol.includes(\"-\".concat(optionType))).slice(0, 5);\n            console.log(\"[DEBUG] Available \".concat(optionType, \" options:\"), availableOptions.map((opt)=>\"\".concat(opt.symbol, \" (Expiry: \").concat(opt.expiryDate, \")\")));\n        }\n        return null;\n    };\n    const formatExpiryForSymbol = (expiry)=>{\n        // Convert YYYY-MM-DD to format used in symbols (e.g., Jun2025)\n        const date = new Date(expiry);\n        const months = [\n            \"Jan\",\n            \"Feb\",\n            \"Mar\",\n            \"Apr\",\n            \"May\",\n            \"Jun\",\n            \"Jul\",\n            \"Aug\",\n            \"Sep\",\n            \"Oct\",\n            \"Nov\",\n            \"Dec\"\n        ];\n        return \"\".concat(months[date.getMonth()]).concat(date.getFullYear());\n    };\n    // Use centralized formatters for consistency\n    const formatPrice = _lib_utils__WEBPACK_IMPORTED_MODULE_2__.MarketFormatters.price;\n    const formatNumber = _lib_utils__WEBPACK_IMPORTED_MODULE_2__.MarketFormatters.number;\n    // Use centralized formatters for consistency\n    const getChangeColor = (change)=>{\n        if (!change) return \"text-gray-400\";\n        return change > 0 ? \"text-green-400\" : change < 0 ? \"text-red-400\" : \"text-gray-400\";\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading option chain...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 312,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 311,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-red-800 font-medium\",\n                    children: \"Error Loading Option Chain\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 text-sm mt-1\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: fetchExpiryDates,\n                    className: \"mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700\",\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 322,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Options\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md\",\n                                            children: \"\\uD83D\\uDD0D NIFTY\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Strategy builder\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Class\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Volatility\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"By expiration | by strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: formatPrice(niftySpotPrice)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this),\n                                        niftySpotPrice === 24850 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"(Mock)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this),\n            expiryData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 overflow-x-auto pb-2\",\n                        children: expiryData.expiries.slice(0, 15).map((expiry, index)=>{\n                            const date = new Date(expiry);\n                            const isSelected = expiry === selectedExpiry;\n                            const isToday = date.toDateString() === new Date().toDateString();\n                            const isCurrentWeek = Math.abs(date.getTime() - new Date().getTime()) < 7 * 24 * 60 * 60 * 1000;\n                            // Enhanced date formatting\n                            const currentYear = new Date().getFullYear();\n                            const expiryYear = date.getFullYear();\n                            const monthShort = date.toLocaleDateString(\"en-US\", {\n                                month: \"short\"\n                            });\n                            const day = date.getDate();\n                            // Show year if different from current year\n                            const showYear = expiryYear !== currentYear;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedExpiry(expiry),\n                                className: \"flex-shrink-0 px-3 py-2 text-xs font-medium transition-colors border \".concat(isSelected ? \"bg-black text-white border-black\" : isToday ? \"bg-orange-500 text-white border-orange-500\" : isCurrentWeek ? \"bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200\" : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center min-w-[40px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-normal text-gray-600\",\n                                            children: [\n                                                monthShort,\n                                                showYear && \" \".concat(expiryYear.toString().slice(-2))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-bold text-sm\",\n                                            children: day\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 19\n                                }, this)\n                            }, expiry, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 11\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-xs text-gray-500 bg-gray-100 p-3 rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Selected Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        selectedExpiry\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" ₹\",\n                                        niftySpotPrice,\n                                        \" \",\n                                        niftySpotPrice === 24850 ? \"(Mock)\" : \"(Live)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Available Strikes:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        (optionChain === null || optionChain === void 0 ? void 0 : optionChain.rows.length) || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Market Data Size:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        marketData.size,\n                                        \" instruments\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Options:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\")).length,\n                                        \" found\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Options with Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\") && d.expiryDate).length,\n                                        \" matched\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 427,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 380,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-50 border-b border-gray-200 text-xs font-medium text-gray-700 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 font-bold\",\n                                    children: \"Calls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold\",\n                                    children: \"Strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-600 font-bold\",\n                                    children: \"Puts\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-100 border-b border-gray-200 text-xs font-medium text-gray-600 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1 border-r border-gray-200\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-2 px-1 border-r border-gray-200 font-bold\",\n                                children: \"Strike\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-100\",\n                        children: optionChain.rows.map((row, index)=>{\n                            var _row_call, _row_call1, _row_call2, _row_call3, _row_call4, _row_call5, _row_call6, _row_put, _row_put1, _row_put2, _row_put3, _row_put4, _row_put5, _row_put6;\n                            const isATM = Math.abs(row.strikePrice - niftySpotPrice) <= 50;\n                            const isITM_Call = row.strikePrice < niftySpotPrice;\n                            const isITM_Put = row.strikePrice > niftySpotPrice;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex hover:bg-gray-50 transition-colors \".concat(isATM ? \"bg-yellow-50\" : index % 2 === 0 ? \"bg-white\" : \"bg-gray-25\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_call = row.call) === null || _row_call === void 0 ? void 0 : _row_call.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_call1 = row.call) === null || _row_call1 === void 0 ? void 0 : _row_call1.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice((_row_call2 = row.call) === null || _row_call2 === void 0 ? void 0 : _row_call2.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice((_row_call3 = row.call) === null || _row_call3 === void 0 ? void 0 : _row_call3.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-medium \".concat(getChangeColor((_row_call4 = row.call) === null || _row_call4 === void 0 ? void 0 : _row_call4.change)),\n                                        children: ((_row_call5 = row.call) === null || _row_call5 === void 0 ? void 0 : _row_call5.change) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.call.change > 0 ? \"+\" : \"\",\n                                                row.call.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.call.changePercent > 0 ? \"+\" : \"\",\n                                                        row.call.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 \".concat(isITM_Call ? \"text-green-600\" : \"text-gray-700\"),\n                                        children: formatPrice((_row_call6 = row.call) === null || _row_call6 === void 0 ? void 0 : _row_call6.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 \".concat(isATM ? \"bg-yellow-100 text-yellow-800\" : \"text-gray-900\"),\n                                        children: row.strikePrice\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-bold \".concat(isITM_Put ? \"text-red-600\" : \"text-gray-700\"),\n                                        children: formatPrice((_row_put = row.put) === null || _row_put === void 0 ? void 0 : _row_put.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-medium \".concat(getChangeColor((_row_put1 = row.put) === null || _row_put1 === void 0 ? void 0 : _row_put1.change)),\n                                        children: ((_row_put2 = row.put) === null || _row_put2 === void 0 ? void 0 : _row_put2.change) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.put.change > 0 ? \"+\" : \"\",\n                                                row.put.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.put.changePercent > 0 ? \"+\" : \"\",\n                                                        row.put.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice((_row_put3 = row.put) === null || _row_put3 === void 0 ? void 0 : _row_put3.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice((_row_put4 = row.put) === null || _row_put4 === void 0 ? void 0 : _row_put4.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_put5 = row.put) === null || _row_put5 === void 0 ? void 0 : _row_put5.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_put6 = row.put) === null || _row_put6 === void 0 ? void 0 : _row_put6.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, row.strikePrice, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 482,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 442,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 border-t border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Show all\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Showing \",\n                                        optionChain.rows.length,\n                                        \" strikes around ATM\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 570,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"Last updated: \",\n                                new Date(optionChain.timestamp).toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 575,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 569,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 568,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n        lineNumber: 336,\n        columnNumber: 5\n    }, this);\n}\n_s(OptionChain, \"dEZsdkfH5U/b4Ku99uG2h6VmJP8=\");\n_c = OptionChain;\nvar _c;\n$RefreshReg$(_c, \"OptionChain\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OptionChain.tsx\n"));

/***/ })

});