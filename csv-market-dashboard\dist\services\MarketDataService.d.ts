import { EventEmitter } from 'events';
import { Instrument, MarketData, IMarketDataService } from '../types';
export declare class MarketDataService extends EventEmitter implements IMarketDataService {
    private ws;
    private isConnected;
    private accessToken;
    private clientId;
    private subscriptionType;
    private subscribedInstruments;
    private marketData;
    private reconnectAttempts;
    private maxReconnectAttempts;
    private reconnectDelay;
    private instrumentLookup;
    constructor(accessToken?: string, clientId?: string, subscriptionType?: string);
    /**
     * Initialize Redis connection for server-side caching
     */
    private initializeRedis;
    /**
     * Load cached market data from Redis on startup
     */
    private loadCachedMarketData;
    /**
     * Cache individual market data entry to Redis
     */
    private cacheMarketDataToRedis;
    /**
     * Connect to Dhan WebSocket feed
     */
    connect(): Promise<void>;
    /**
     * Setup WebSocket event handlers
     */
    private setupWebSocketHandlers;
    /**
     * Handle incoming BINARY market data from Dhan WebSocket
     */
    private handleMarketData;
    /**
     * Parse BINARY market data according to Dhan API specification
     * ✅ FIXED: Enhanced parsing with proper market depth handling
     */
    private parseBinaryMarketData;
    /**
     * ✅ FIXED: Parse Market Depth from full packet (5 levels of bid/ask)
     * According to Dhan docs: Each level is 20 bytes with structure:
     * Bytes 1-4: Bid Quantity (int32)
     * Bytes 5-8: Ask Quantity (int32)
     * Bytes 9-10: No. of Bid Orders (int16)
     * Bytes 11-12: No. of Ask Orders (int16)
     * Bytes 13-16: Bid Price (float32)
     * Bytes 17-20: Ask Price (float32)
     */
    private parseMarketDepth;
    /**
     * ✅ NEW: Generate structured log prefix for better debugging
     */
    private getLogPrefix;
    /**
     * Calculate price change and percentage
     */
    private calculatePriceChange;
    /**
     * Find instrument by security ID and exchange segment using lookup cache
     */
    private findInstrumentBySecurityId;
    /**
     * Get exchange segment name from code
     */
    private getExchangeSegmentName;
    /**
     * Subscribe to instruments (COPIED FROM WORKING PROJECT)
     */
    subscribe(instruments: Instrument[]): void;
    /**
     * Subscribe to a batch of instruments (COPIED FROM WORKING PROJECT)
     */
    private subscribeToBatch;
    /**
     * Unsubscribe from instruments
     */
    unsubscribe(instruments: Instrument[]): void;
    /**
     * Get correct exchange segment for subscription (FIXED: Use NSE_FNO for NSE derivatives)
     */
    private getCorrectExchangeSegment;
    /**
     * Get market data for a specific security
     */
    getMarketData(securityId: string): MarketData | null;
    /**
     * Get all market data
     */
    getAllMarketData(): Map<string, MarketData>;
    /**
     * Check if connected
     */
    getConnectionStatus(): boolean;
    /**
     * Get connection status as property
     */
    get connected(): boolean;
    /**
     * Get subscription count
     */
    getSubscriptionCount(): number;
    /**
     * Disconnect from market feed
     */
    disconnect(): void;
    /**
     * Handle connection errors
     */
    private handleConnectionError;
    /**
     * Start mock data mode for testing
     */
    private startMockDataMode;
    /**
     * Generate mock market data with realistic bid/ask values
     */
    private generateMockData;
}
export declare const marketDataService: MarketDataService;
//# sourceMappingURL=MarketDataService.d.ts.map