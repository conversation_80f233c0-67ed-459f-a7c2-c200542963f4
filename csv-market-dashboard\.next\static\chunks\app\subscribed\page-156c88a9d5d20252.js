(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[200],{2314:function(e,t,s){Promise.resolve().then(s.bind(s,7272))},7272:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return i},dynamic:function(){return l}});var a=s(7437),n=s(2265),c=s(3448),r=s(2553);let l="force-dynamic";function i(){let{marketData:e,isConnected:t,connectionError:s,stats:l}=(0,r.A)();l.totalInstruments;let[i,o]=(0,n.useState)(""),[d,u]=(0,n.useState)("symbol"),[x,h]=(0,n.useState)("asc"),[m,p]=(0,n.useState)("all"),g=e.filter(e=>{let t=e.symbol.toLowerCase().includes(i.toLowerCase()),s="all"===m||"options"===m&&(e.symbol.includes("-CE")||e.symbol.includes("-PE"))||"futures"===m&&!e.symbol.includes("-CE")&&!e.symbol.includes("-PE");return t&&s}).sort((e,t)=>{let s,a;switch(d){case"symbol":default:s=e.symbol,a=t.symbol;break;case"ltp":s=e.ltp||0,a=t.ltp||0;break;case"change":s=e.change||0,a=t.change||0;break;case"volume":s=e.volume||0,a=t.volume||0;break;case"openInterest":s=e.openInterest||0,a=t.openInterest||0}return"string"==typeof s?"asc"===x?s.localeCompare(a):a.localeCompare(s):"asc"===x?s-a:a-s}),b=e=>{d===e?h("asc"===x?"desc":"asc"):(u(e),h("asc"))},y=c.A1.number,f=c.A1.price,v=e=>e?e>0?"text-green-600":e<0?"text-red-600":"text-gray-500":"text-gray-500";return(0,a.jsxs)("div",{className:"container mx-auto p-4 max-w-7xl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"NSE Derivatives - Subscribed Data Dashboard"}),(0,a.jsxs)("div",{className:"space-x-3",children:[(0,a.jsx)("a",{href:"/",className:"inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 transition-colors",children:"\uD83C\uDFE0 Main Dashboard"}),(0,a.jsx)("a",{href:"/option-chain",className:"inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors",children:"\uD83D\uDD17 Option Chain"})]})]}),(0,a.jsx)("div",{className:"mb-6 p-4 bg-gray-100 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"inline-block px-3 py-1 rounded ".concat(t?"bg-green-500":"bg-red-500"," text-white"),children:t?"\uD83D\uDFE2 Connected":"\uD83D\uDD34 Disconnected"}),s&&(0,a.jsxs)("div",{className:"inline-block px-3 py-1 rounded bg-red-100 text-red-800 text-sm",children:["Error: ",s]}),(0,a.jsxs)("span",{className:"text-gray-700",children:["\uD83D\uDCCA Total Subscribed: ",e.length," instruments"]}),(0,a.jsxs)("span",{className:"text-gray-700",children:["\uD83D\uDCC8 Live Data: ",e.filter(e=>e.ltp>0).length," active"]}),(0,a.jsxs)("span",{className:"text-gray-700",children:["\uD83D\uDD22 With OI: ",e.filter(e=>e.openInterest&&e.openInterest>0).length," contracts"]})]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Server Auto-Subscription: NSE OPTIDX + FUTIDX Only"})]})}),(0,a.jsx)("div",{className:"mb-6 p-4 bg-white rounded-lg shadow",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Instruments"}),(0,a.jsx)("input",{type:"text",placeholder:"Search by symbol...",value:i,onChange:e=>o(e.target.value),className:"w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Filter by Type"}),(0,a.jsxs)("select",{value:m,onChange:e=>p(e.target.value),className:"w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Instruments"}),(0,a.jsx)("option",{value:"options",children:"Options (CE/PE)"}),(0,a.jsx)("option",{value:"futures",children:"Futures"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Showing Results"}),(0,a.jsxs)("div",{className:"p-2 bg-gray-50 rounded-md text-sm",children:[g.length," of ",e.length," instruments"]})]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsxs)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>b("symbol"),children:["Symbol ","symbol"===d&&("asc"===x?"↑":"↓")]}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Exchange"}),(0,a.jsxs)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>b("ltp"),children:["LTP ","ltp"===d&&("asc"===x?"↑":"↓")]}),(0,a.jsxs)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>b("change"),children:["Change ","change"===d&&("asc"===x?"↑":"↓")]}),(0,a.jsxs)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>b("volume"),children:["Volume ","volume"===d&&("asc"===x?"↑":"↓")]}),(0,a.jsxs)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>b("openInterest"),children:["Open Interest ","openInterest"===d&&("asc"===x?"↑":"↓")]}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"High/Low"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Open/Close"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:g.slice(0,100).map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.symbol}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.exchange}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right font-medium",children:f(e.ltp)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right font-medium ".concat(v(e.change)),children:e.change?(0,a.jsxs)(a.Fragment,{children:[e.change>0?"+":"",e.change.toFixed(2),(0,a.jsx)("br",{}),(0,a.jsxs)("span",{className:"text-xs",children:["(",e.changePercent>0?"+":"",e.changePercent.toFixed(2),"%)"]})]}):"-"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900",children:y(e.volume)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-blue-600",children:y(e.openInterest)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500",children:e.high?(0,a.jsxs)(a.Fragment,{children:[f(e.high),(0,a.jsx)("br",{}),(0,a.jsx)("span",{className:"text-xs",children:f(e.low)})]}):"-"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500",children:e.open?(0,a.jsxs)(a.Fragment,{children:[f(e.open),(0,a.jsx)("br",{}),(0,a.jsx)("span",{className:"text-xs",children:f(e.close)})]}):"-"})]},e.securityId))})]})}),0===g.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("p",{className:"text-gray-500",children:"No subscribed data available"}),(0,a.jsx)("p",{className:"text-sm text-gray-400 mt-2",children:"The server will automatically subscribe to NSE OPTIDX and FUTIDX instruments"})]}),g.length>100&&(0,a.jsxs)("div",{className:"bg-gray-50 px-6 py-3 text-sm text-gray-600",children:["Showing first 100 of ",g.length," results. Use search to narrow down."]})]})]})}},2553:function(e,t,s){"use strict";s.d(t,{A:function(){return n}});var a=s(2265);let n=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{autoConnect:t=!0,autoLoadCache:s=!0,autoSaveInterval:n=3e4,reconnectOnError:c=!0,maxReconnectAttempts:r=5}=e,l={status:"disconnected",isConnected:!1,error:null,connectionStats:{totalMessages:0,connectionUptime:0}},i=new Map,o={isLoaded:!1,totalCacheSize:0,pendingUpdates:0,lastCacheUpdate:null},d={filters:{},sortConfig:{field:"symbol",direction:"asc"},selectedInstruments:new Set,viewMode:"table",autoRefresh:!0},u=(0,a.useCallback)(async()=>{},[]),x=(0,a.useCallback)(async()=>{},[]),h=(0,a.useCallback)(()=>{},[]),m=(0,a.useCallback)(async()=>{},[]),p=(0,a.useCallback)(async()=>{},[]),g=(0,a.useCallback)(async e=>{},[]),b=(0,a.useCallback)(async()=>{},[]),y=(0,a.useCallback)(e=>{},[]),f=(0,a.useCallback)((e,t)=>{},[]),v=(0,a.useCallback)(()=>[],[]),w=(0,a.useCallback)(()=>[],[]),j=(0,a.useCallback)(e=>void 0,[]),C=(0,a.useCallback)(e=>void 0,[]),k=(0,a.useCallback)(e=>{},[]),N=(0,a.useCallback)(e=>{},[]),D=(0,a.useCallback)(()=>{},[]),S=(0,a.useRef)(null),E=(0,a.useRef)(0),I=(0,a.useRef)(!1),F=Array.from(i.values());(0,a.useEffect)(()=>((async()=>{if(!I.current){I.current=!0,console.log("\uD83D\uDE80 Enhanced Hook: Initializing...");try{s&&!o.isLoaded&&await p(),t&&"disconnected"===l.status&&await u()}catch(e){console.error("❌ Enhanced Hook: Initialization failed:",e)}}})(),()=>{S.current&&clearInterval(S.current)}),[t,s,o.isLoaded,l.status,u,p]),(0,a.useEffect)(()=>{if(n>0&&F.length>0)return S.current&&clearInterval(S.current),S.current=setInterval(()=>{g()},n),()=>{S.current&&clearInterval(S.current)}},[n,F.length,g]),(0,a.useEffect)(()=>{if(c&&"error"===l.status&&E.current<r){let e=Math.min(1e3*Math.pow(2,E.current),3e4);console.log("\uD83D\uDD04 Enhanced Hook: Auto-reconnecting in ".concat(e,"ms (attempt ").concat(E.current+1,"/").concat(r,")"));let t=setTimeout(async()=>{try{E.current++,await m()}catch(e){console.error("❌ Enhanced Hook: Auto-reconnect failed:",e)}},e);return()=>clearTimeout(t)}},[l.status,m,c,r]),(0,a.useEffect)(()=>{"connected"===l.status&&(E.current=0)},[l.status]);let L=(0,a.useCallback)(()=>v(),[v]),A=(0,a.useCallback)(()=>w(),[w]),U=(0,a.useCallback)(e=>j(e),[j]),T=(0,a.useCallback)(e=>C(e),[C]),M=(0,a.useCallback)(e=>{y(e)},[y]),O=(0,a.useCallback)((e,t)=>{f(e,t)},[f]),P=(0,a.useCallback)(e=>{k(e)},[k]),H=(0,a.useCallback)(e=>{N(e)},[N]),R=(0,a.useCallback)(async()=>{try{E.current=0,await m()}catch(e){throw console.error("❌ Enhanced Hook: Manual reconnect failed:",e),e}},[m]),_=(0,a.useCallback)(async()=>{try{await p()}catch(e){throw console.error("❌ Enhanced Hook: Force refresh failed:",e),e}},[p]),z=(0,a.useCallback)(async()=>{try{await g(!0)}catch(e){throw console.error("❌ Enhanced Hook: Force save failed:",e),e}},[g]),X={totalInstruments:F.length,connectedInstruments:F.filter(e=>e.ltp&&e.ltp>0).length,lastUpdate:null,cacheSize:o.totalCacheSize,connectionUptime:l.connectionStats.connectionUptime,messagesReceived:l.connectionStats.totalMessages,reconnectAttempts:E.current,isAutoSaving:null!==S.current};return{marketData:F,marketDataMap:i,filteredData:L(),sortedData:A(),isConnected:l.isConnected,connectionStatus:l.status,connectionError:l.error,connectionStats:l.connectionStats,cacheLoaded:o.isLoaded,cacheUpdating:o.pendingUpdates>0,lastCacheUpdate:o.lastCacheUpdate,filters:d.filters,sortConfig:d.sortConfig,selectedInstruments:d.selectedInstruments,viewMode:d.viewMode,autoRefresh:d.autoRefresh,isLoading:!1,isInitializing:!I.current,getDataBySecurityId:U,getDataBySymbol:T,getFilteredData:L,getSortedData:A,updateFilters:M,updateSort:O,subscribe:P,unsubscribe:H,connect:x,disconnect:h,reconnect:R,refresh:_,save:z,clearCache:b,reset:D,stats:X,_store:{setFilters:y,setSortConfig:f,subscribeToInstrument:k,unsubscribeFromInstrument:N}}}},3448:function(e,t,s){"use strict";s.d(t,{A1:function(){return a}});let a={price:e=>!e||e<=0?"-":"₹".concat(e.toFixed(2)),number:e=>null==e?"-":e.toLocaleString(),percentage:e=>e&&0!==e?"".concat(e>=0?"+":"").concat(e.toFixed(2),"%"):"-",change:(e,t)=>{if(!e||0===e)return"-";let s=e>0?"+":"",a=t?" (".concat(s).concat(t.toFixed(2),"%)"):"";return"".concat(s).concat(e.toFixed(2)).concat(a)},volume:e=>null==e?"-":e>=1e7?"".concat((e/1e7).toFixed(2)," Cr"):e>=1e5?"".concat((e/1e5).toFixed(2)," L"):e>=1e3?"".concat((e/1e3).toFixed(2)," K"):e.toString(),time:e=>e?new Date(e).toLocaleTimeString():"-",date:e=>e?new Date(e).toLocaleDateString():"-",bidAsk:(e,t)=>!e||e<=0?"-":"₹".concat(e.toFixed(2)).concat(t?" (".concat(t,")"):"")}}},function(e){e.O(0,[971,117,744],function(){return e(e.s=2314)}),_N_E=e.O()}]);