/**
 * useNiftySpot Hook
 * React hook for accessing live NIFTY spot price data
 */

import { useState, useEffect, useCallback } from 'react';
import { NiftySpotData, NiftySpotStatus } from '../services/NiftySpotService';

interface UseNiftySpotReturn {
  // Data
  niftyData: NiftySpotData | null;
  ltp: number;
  previousClose: number;
  changePoints: number;
  changePercent: number;
  
  // Formatted data
  formattedChange: {
    points: string;
    percent: string;
    color: string;
    isPositive: boolean;
  } | null;
  
  // Status
  isConnected: boolean;
  isReady: boolean;
  status: NiftySpotStatus | null;
  error: string | null;
  
  // Actions
  refresh: () => void;
  connect: () => Promise<void>;
  disconnect: () => void;
}

/**
 * Custom hook for NIFTY spot price data
 */
export const useNiftySpot = (): UseNiftySpotReturn => {
  const [niftyData, setNiftyData] = useState<NiftySpotData | null>(null);
  const [status, setStatus] = useState<NiftySpotStatus | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch data from API endpoint
  const fetchNiftyData = useCallback(async () => {
    try {
      const response = await fetch('/api/nifty-spot');
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          // Convert API response to NiftySpotData format
          const niftySpotData: NiftySpotData = {
            symbol: data.data.symbol || 'NIFTY',
            securityId: data.data.securityId || '13',
            ltp: data.data.ltp || 0,
            previousClose: data.data.close || 0,
            changePoints: data.data.change || 0,
            changePercent: data.data.changePercent || 0,
            lastUpdateTime: new Date(data.timestamp),
            totalUpdates: 1,
            isReady: true,
            formatted: {
              points: `${data.data.change >= 0 ? '+' : ''}₹${(data.data.change || 0).toFixed(2)}`,
              percent: `${data.data.changePercent >= 0 ? '+' : ''}${(data.data.changePercent || 0).toFixed(3)}%`,
              color: (data.data.change || 0) >= 0 ? '🟢' : '🔴',
              isPositive: (data.data.change || 0) >= 0
            }
          };
          
          setNiftyData(niftySpotData);
          setIsConnected(true);
          setError(null);
          console.log('✅ [useNiftySpot] Fetched NIFTY data from API:', niftySpotData.ltp);
        }
      } else {
        throw new Error(`API responded with status: ${response.status}`);
      }
    } catch (err) {
      console.error('❌ [useNiftySpot] Failed to fetch NIFTY data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch NIFTY data');
      setIsConnected(false);
    }
  }, []);

  // Fetch data from server market data API as fallback
  const fetchFromMarketData = useCallback(async () => {
    try {
      const serverUrl = process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:8081';
      const response = await fetch(`${serverUrl}/api/market-data`);
      
      if (response.ok) {
        const data = await response.json();
        
        // Look for NIFTY index data
        const niftyInstrument = data.data.instruments.find((item: any) => 
          item.securityId === '13' || 
          item.securityId === '2' || 
          (item.symbol === 'NIFTY' && item.instrumentType === 'INDEX')
        );

        if (niftyInstrument && niftyInstrument.ltp > 0) {
          const niftySpotData: NiftySpotData = {
            symbol: niftyInstrument.symbol,
            securityId: niftyInstrument.securityId,
            ltp: niftyInstrument.ltp,
            previousClose: niftyInstrument.close || 0,
            changePoints: niftyInstrument.change || 0,
            changePercent: niftyInstrument.changePercent || 0,
            lastUpdateTime: new Date(niftyInstrument.timestamp),
            totalUpdates: 1,
            isReady: true,
            formatted: {
              points: `${niftyInstrument.change >= 0 ? '+' : ''}₹${(niftyInstrument.change || 0).toFixed(2)}`,
              percent: `${niftyInstrument.changePercent >= 0 ? '+' : ''}${(niftyInstrument.changePercent || 0).toFixed(3)}%`,
              color: (niftyInstrument.change || 0) >= 0 ? '🟢' : '🔴',
              isPositive: (niftyInstrument.change || 0) >= 0
            }
          };
          
          setNiftyData(niftySpotData);
          setIsConnected(true);
          setError(null);
          console.log('✅ [useNiftySpot] Fetched NIFTY data from market data API:', niftySpotData.ltp);
          return true;
        }
      }
      return false;
    } catch (err) {
      console.error('❌ [useNiftySpot] Failed to fetch from market data API:', err);
      return false;
    }
  }, []);

  // Refresh data
  const refresh = useCallback(async () => {
    console.log('🔄 [useNiftySpot] Refreshing NIFTY spot data...');
    
    // Try primary API first
    await fetchNiftyData();
    
    // If no data, try market data API
    if (!niftyData || niftyData.ltp === 0) {
      await fetchFromMarketData();
    }
  }, [fetchNiftyData, fetchFromMarketData, niftyData]);

  // Connect (same as refresh for API-based approach)
  const connect = useCallback(async () => {
    await refresh();
  }, [refresh]);

  // Disconnect
  const disconnect = useCallback(() => {
    setNiftyData(null);
    setIsConnected(false);
    setError(null);
    console.log('🔌 [useNiftySpot] Disconnected');
  }, []);

  // Auto-fetch data on mount and set up polling
  useEffect(() => {
    // Initial fetch
    refresh();
    
    // Set up polling every 5 seconds during market hours
    const interval = setInterval(() => {
      const now = new Date();
      const hour = now.getHours();
      const minute = now.getMinutes();
      const timeInMinutes = hour * 60 + minute;
      
      // Market hours: 9:15 AM to 3:30 PM IST (555 to 930 minutes)
      const marketStart = 9 * 60 + 15; // 9:15 AM
      const marketEnd = 15 * 60 + 30;  // 3:30 PM
      
      if (timeInMinutes >= marketStart && timeInMinutes <= marketEnd) {
        refresh();
      }
    }, 5000); // Poll every 5 seconds

    return () => {
      clearInterval(interval);
    };
  }, [refresh]);

  return {
    // Data
    niftyData,
    ltp: niftyData?.ltp || 0,
    previousClose: niftyData?.previousClose || 0,
    changePoints: niftyData?.changePoints || 0,
    changePercent: niftyData?.changePercent || 0,
    
    // Formatted data
    formattedChange: niftyData?.formatted || null,
    
    // Status
    isConnected,
    isReady: niftyData?.isReady || false,
    status,
    error,
    
    // Actions
    refresh,
    connect,
    disconnect
  };
};

export default useNiftySpot;
