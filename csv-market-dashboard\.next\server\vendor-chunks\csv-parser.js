/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/csv-parser";
exports.ids = ["vendor-chunks/csv-parser"];
exports.modules = {

/***/ "(rsc)/./node_modules/csv-parser/index.js":
/*!******************************************!*\
  !*** ./node_modules/csv-parser/index.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { Transform } = __webpack_require__(/*! stream */ \"stream\")\n\nconst [cr] = Buffer.from('\\r')\nconst [nl] = Buffer.from('\\n')\nconst defaults = {\n  escape: '\"',\n  headers: null,\n  mapHeaders: ({ header }) => header,\n  mapValues: ({ value }) => value,\n  newline: '\\n',\n  quote: '\"',\n  raw: false,\n  separator: ',',\n  skipComments: false,\n  skipLines: null,\n  maxRowBytes: Number.MAX_SAFE_INTEGER,\n  strict: false,\n  outputByteOffset: false\n}\n\nclass CsvParser extends Transform {\n  constructor (opts = {}) {\n    super({ objectMode: true, highWaterMark: 16 })\n\n    if (Array.isArray(opts)) opts = { headers: opts }\n\n    const options = Object.assign({}, defaults, opts)\n\n    options.customNewline = options.newline !== defaults.newline\n\n    for (const key of ['newline', 'quote', 'separator']) {\n      if (typeof options[key] !== 'undefined') {\n        ([options[key]] = Buffer.from(options[key]))\n      }\n    }\n\n    // if escape is not defined on the passed options, use the end value of quote\n    options.escape = (opts || {}).escape ? Buffer.from(options.escape)[0] : options.quote\n\n    this.state = {\n      empty: options.raw ? Buffer.alloc(0) : '',\n      escaped: false,\n      first: true,\n      lineNumber: 0,\n      previousEnd: 0,\n      rowLength: 0,\n      quoted: false\n    }\n\n    this._prev = null\n\n    if (options.headers === false) {\n      // enforce, as the column length check will fail if headers:false\n      options.strict = false\n    }\n\n    if (options.headers || options.headers === false) {\n      this.state.first = false\n    }\n\n    this.options = options\n    this.headers = options.headers\n    this.bytesRead = 0\n  }\n\n  parseCell (buffer, start, end) {\n    const { escape, quote } = this.options\n    // remove quotes from quoted cells\n    if (buffer[start] === quote && buffer[end - 1] === quote) {\n      start++\n      end--\n    }\n\n    let y = start\n\n    for (let i = start; i < end; i++) {\n      // check for escape characters and skip them\n      if (buffer[i] === escape && i + 1 < end && buffer[i + 1] === quote) {\n        i++\n      }\n\n      if (y !== i) {\n        buffer[y] = buffer[i]\n      }\n      y++\n    }\n\n    return this.parseValue(buffer, start, y)\n  }\n\n  parseLine (buffer, start, end) {\n    const { customNewline, escape, mapHeaders, mapValues, quote, separator, skipComments, skipLines } = this.options\n\n    end-- // trim newline\n    if (!customNewline && buffer.length && buffer[end - 1] === cr) {\n      end--\n    }\n\n    const comma = separator\n    const cells = []\n    let isQuoted = false\n    let offset = start\n\n    if (skipComments) {\n      const char = typeof skipComments === 'string' ? skipComments : '#'\n      if (buffer[start] === Buffer.from(char)[0]) {\n        return\n      }\n    }\n\n    const mapValue = (value) => {\n      if (this.state.first) {\n        return value\n      }\n\n      const index = cells.length\n      const header = this.headers[index]\n\n      return mapValues({ header, index, value })\n    }\n\n    for (let i = start; i < end; i++) {\n      const isStartingQuote = !isQuoted && buffer[i] === quote\n      const isEndingQuote = isQuoted && buffer[i] === quote && i + 1 <= end && buffer[i + 1] === comma\n      const isEscape = isQuoted && buffer[i] === escape && i + 1 < end && buffer[i + 1] === quote\n\n      if (isStartingQuote || isEndingQuote) {\n        isQuoted = !isQuoted\n        continue\n      } else if (isEscape) {\n        i++\n        continue\n      }\n\n      if (buffer[i] === comma && !isQuoted) {\n        let value = this.parseCell(buffer, offset, i)\n        value = mapValue(value)\n        cells.push(value)\n        offset = i + 1\n      }\n    }\n\n    if (offset < end) {\n      let value = this.parseCell(buffer, offset, end)\n      value = mapValue(value)\n      cells.push(value)\n    }\n\n    if (buffer[end - 1] === comma) {\n      cells.push(mapValue(this.state.empty))\n    }\n\n    const skip = skipLines && skipLines > this.state.lineNumber\n    this.state.lineNumber++\n\n    if (this.state.first && !skip) {\n      this.state.first = false\n      this.headers = cells.map((header, index) => mapHeaders({ header, index }))\n\n      this.emit('headers', this.headers)\n      return\n    }\n\n    if (!skip && this.options.strict && cells.length !== this.headers.length) {\n      const e = new RangeError('Row length does not match headers')\n      this.emit('error', e)\n    } else {\n      if (!skip) {\n        const byteOffset = this.bytesRead - buffer.length + start\n        this.writeRow(cells, byteOffset)\n      }\n    }\n  }\n\n  parseValue (buffer, start, end) {\n    if (this.options.raw) {\n      return buffer.slice(start, end)\n    }\n\n    return buffer.toString('utf-8', start, end)\n  }\n\n  writeRow (cells, byteOffset) {\n    const headers = (this.headers === false) ? cells.map((value, index) => index) : this.headers\n\n    const row = cells.reduce((o, cell, index) => {\n      const header = headers[index]\n      if (header === null) return o // skip columns\n      if (header !== undefined) {\n        o[header] = cell\n      } else {\n        o[`_${index}`] = cell\n      }\n      return o\n    }, {})\n\n    if (this.options.outputByteOffset) {\n      this.push({ row, byteOffset })\n    } else {\n      this.push(row)\n    }\n  }\n\n  _flush (cb) {\n    if (this.state.escaped || !this._prev) return cb()\n    this.parseLine(this._prev, this.state.previousEnd, this._prev.length + 1) // plus since online -1s\n    cb()\n  }\n\n  _transform (data, enc, cb) {\n    if (typeof data === 'string') {\n      data = Buffer.from(data)\n    }\n\n    const { escape, quote } = this.options\n    let start = 0\n    let buffer = data\n    this.bytesRead += data.byteLength\n\n    if (this._prev) {\n      start = this._prev.length\n      buffer = Buffer.concat([this._prev, data])\n      this._prev = null\n    }\n\n    const bufferLength = buffer.length\n\n    for (let i = start; i < bufferLength; i++) {\n      const chr = buffer[i]\n      const nextChr = i + 1 < bufferLength ? buffer[i + 1] : null\n\n      this.state.rowLength++\n      if (this.state.rowLength > this.options.maxRowBytes) {\n        return cb(new Error('Row exceeds the maximum size'))\n      }\n\n      if (!this.state.escaped && chr === escape && nextChr === quote && i !== start) {\n        this.state.escaped = true\n        continue\n      } else if (chr === quote) {\n        if (this.state.escaped) {\n          this.state.escaped = false\n          // non-escaped quote (quoting the cell)\n        } else {\n          this.state.quoted = !this.state.quoted\n        }\n        continue\n      }\n\n      if (!this.state.quoted) {\n        if (this.state.first && !this.options.customNewline) {\n          if (chr === nl) {\n            this.options.newline = nl\n          } else if (chr === cr) {\n            if (nextChr !== nl) {\n              this.options.newline = cr\n            }\n          }\n        }\n\n        if (chr === this.options.newline) {\n          this.parseLine(buffer, this.state.previousEnd, i + 1)\n          this.state.previousEnd = i + 1\n          this.state.rowLength = 0\n        }\n      }\n    }\n\n    if (this.state.previousEnd === bufferLength) {\n      this.state.previousEnd = 0\n      return cb()\n    }\n\n    if (bufferLength - this.state.previousEnd < data.length) {\n      this._prev = data\n      this.state.previousEnd -= (bufferLength - data.length)\n      return cb()\n    }\n\n    this._prev = buffer\n    cb()\n  }\n}\n\nmodule.exports = (opts) => new CsvParser(opts)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/csv-parser/index.js\n");

/***/ })

};
;