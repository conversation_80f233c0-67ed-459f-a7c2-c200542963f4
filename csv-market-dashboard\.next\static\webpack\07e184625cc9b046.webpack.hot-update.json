{"c": ["app/page", "app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/page.tsx", "(app-pages-browser)/./src/components/ConnectionStatus.tsx", "(app-pages-browser)/./src/components/FilterPanel.tsx", "(app-pages-browser)/./src/components/InstrumentTable.tsx", "(app-pages-browser)/./src/components/Stats.tsx", "(app-pages-browser)/./src/hooks/useEnhancedMarketData.ts"]}