{"version": 3, "file": "default.replacer.js", "sourceRoot": "", "sources": ["../../src/replacers/default.replacer.ts"], "names": [], "mappings": ";;AAQA,gDAAiD;AACjD,+BAAyC;AAEzC,oCAA0C;AAE1C,SAAS,kBAAkB,CAAC,GAAW;IACrC,OAAO,GAAG,CAAC,OAAO,CAAC,0BAA0B,EAAE,MAAM,CAAC,CAAC;AACzD,CAAC;AAED,SAAS,oBAAoB,CAAC,KAAY;IACxC,OAAO,IAAI,MAAM,CACf,OAAO,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,wBAAwB,EAC/D,GAAG,CACJ,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CAAC,cAAsB,EAAE,KAAY;IAC7D,OAAO,cAAc,CAAC,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;AACjE,CAAC;AAED,SAAwB,sBAAsB,CAAC,EAC7C,IAAI,EACJ,IAAI,EACJ,MAAM,EACiB;;IACvB,MAAM,cAAc,GAAG,MAAA,MAAA,IAAI,CAAC,KAAK,CAAC,IAAA,sBAAc,GAAE,CAAC,0CAAE,MAAM,0CAAE,IAAI,CAAC;IAClE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,cAAc,CAAC,CAAC;IAC3E,MAAM,CAAC,MAAM,CAAC,MAAM,CAClB,OAAO,cAAc,IAAI,QAAQ,EACjC,uCAAuC,IAAI,EAAE,CAC9C,CAAC;IAEF,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;IACtD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;IAEzD,IAAI,CAAC,KAAK;QAAE,OAAO,IAAI,CAAC;IAExB,MAAM,OAAO,GAAG,KAAK,CAAC,uBAAuB;QAC3C,CAAC;YAEC,cAAc,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,cAAc,KAAK,KAAK,CAAC,MAAM;QAC5E,CAAC;YAIC,cAAc,KAAK,KAAK,CAAC,MAAM;gBAC/B,cAAc,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;IAElD,IAAI,OAAO,EAAE;QACX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,iBAAiB,GAAG,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAC3D,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,EACvB,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CACpB,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,KAAK,CACjB,wCAAwC,EACxC,iBAAiB,CAClB,CAAC;YAEF,IAAI,iBAAiB,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;gBACvC,IAAI,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;oBAChC,iBAAiB,GAAG,iBAAiB,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;iBAC1D;qBAAM;oBACL,SAAS;iBACV;aACF;YAGD,IACE,CAAC,MAAM,CAAC,SAAS,CAAC,mBAAmB,CACnC,KAAK,CAAC,MAAM,CAAC,MAAM,IAAI,cAAc,CAAC,MAAM;gBAC1C,CAAC,CAAC,aAAa,CAAC,iBAAiB,CAAC;gBAClC,CAAC,CAAC,aAAa,CACX,GAAG,iBAAiB,IAAI,iBAAiB,CACvC,cAAc,EACd,KAAK,CACN,EAAE,CACJ,CACN,EACD;gBACA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;gBACvD,SAAS;aACV;YAED,IAAI,iBAAiB,GAAW,aAAa,CAC3C,IAAA,eAAQ,EAAC,IAAA,cAAO,EAAC,IAAI,CAAC,EAAE,iBAAiB,CAAC,CAC3C,CAAC;YAEF,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBACtC,iBAAiB,GAAG,IAAI,GAAG,iBAAiB,CAAC;aAC9C;YACD,MAAM,CAAC,MAAM,CAAC,KAAK,CACjB,wCAAwC,EACxC,iBAAiB,CAClB,CAAC;YAEF,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACzC,MAAM,eAAe,GACnB,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC;gBACxB,iBAAiB;gBACjB,GAAG;gBACH,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,KAAK,CACjB,sCAAsC,EACtC,eAAe,CAChB,CAAC;YAEF,MAAM,UAAU,GAAG,eAAe,CAAC,KAAK,CAAC,IAAA,sBAAc,GAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;YACvE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,UAAU,CAAC,CAAC;YAEnE,OAAO,eAAe,CAAC,OAAO,CAAC,UAAU,EAAE,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC;SACvE;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AA9FD,yCA8FC", "sourcesContent": ["/**\n * @file\n *\n * The default replacer replaces the alias in an import statement\n * with the proper aliased location.\n */\n\n/** */\nimport normalizePath = require('normalize-path');\nimport { dirname, relative } from 'path';\nimport { <PERSON><PERSON>, AliasReplacerArguments } from '../interfaces';\nimport { newStringRegex } from '../utils';\n\nfunction escapeSpecialChars(str: string) {\n  return str.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&');\n}\n\nfunction getAliasPrefixRegExp(alias: <PERSON><PERSON>) {\n  return new RegExp(\n    `(?:^${escapeSpecialChars(alias.prefix)})|(?:\\\\.(js|ts|json)$)`,\n    'g'\n  );\n}\n\nfunction removeAliasPrefix(requiredModule: string, alias: Alias) {\n  return requiredModule.replace(getAliasPrefixRegExp(alias), '');\n}\n\nexport default function replaceImportStatement({\n  orig,\n  file,\n  config\n}: AliasReplacerArguments) {\n  const requiredModule = orig.match(newStringRegex())?.groups?.path;\n  config.output.debug('default replacer - requiredModule: ', requiredModule);\n  config.output.assert(\n    typeof requiredModule == 'string',\n    `Unexpected import statement pattern ${orig}`\n  );\n  // Lookup which alias should be used for this given requiredModule.\n  const alias = config.aliasTrie.search(requiredModule);\n  config.output.debug('default replacer - alias: ', alias);\n  // If an alias isn't found the original.\n  if (!alias) return orig;\n\n  const isAlias = alias.shouldPrefixMatchWildly\n    ? // if the alias is like alias*\n      // beware that typescript expects requiredModule be more than just alias\n      requiredModule.startsWith(alias.prefix) && requiredModule !== alias.prefix\n    : // need to be a bit more careful if the alias doesn't ended with a *\n      // in this case the statement must be like either\n      // require('alias') or require('alias/path');\n      // but not require('aliaspath');\n      requiredModule === alias.prefix ||\n      requiredModule.startsWith(alias.prefix + '/');\n\n  if (isAlias) {\n    for (let i = 0; i < alias.paths.length; i++) {\n      let absoluteAliasPath = config.pathCache.getAbsoluteAliasPath(\n        alias.paths[i].basePath,\n        alias.paths[i].path\n      );\n      config.output.debug(\n        'default replacer - absoluteAliasPath: ',\n        absoluteAliasPath\n      );\n\n      if (absoluteAliasPath.startsWith('---')) {\n        if (i === alias.paths.length - 1) {\n          absoluteAliasPath = absoluteAliasPath.replace('---', '');\n        } else {\n          continue;\n        }\n      }\n\n      // Check if path is valid.\n      if (\n        !config.pathCache.existsResolvedAlias(\n          alias.prefix.length == requiredModule.length\n            ? normalizePath(absoluteAliasPath)\n            : normalizePath(\n                `${absoluteAliasPath}/${removeAliasPrefix(\n                  requiredModule,\n                  alias\n                )}`\n              )\n        )\n      ) {\n        config.output.debug('default replacer - Invalid path');\n        continue;\n      }\n\n      let relativeAliasPath: string = normalizePath(\n        relative(dirname(file), absoluteAliasPath)\n      );\n\n      if (!relativeAliasPath.startsWith('.')) {\n        relativeAliasPath = './' + relativeAliasPath;\n      }\n      config.output.debug(\n        'default replacer - relativeAliasPath: ',\n        relativeAliasPath\n      );\n\n      const index = orig.indexOf(alias.prefix);\n      const newImportScript =\n        orig.substring(0, index) +\n        relativeAliasPath +\n        '/' +\n        orig.substring(index + alias.prefix.length);\n      config.output.debug(\n        'default replacer - newImportScript: ',\n        newImportScript\n      );\n\n      const modulePath = newImportScript.match(newStringRegex()).groups.path;\n      config.output.debug('default replacer - modulePath: ', modulePath);\n\n      return newImportScript.replace(modulePath, normalizePath(modulePath));\n    }\n  }\n  return orig;\n}\n"]}