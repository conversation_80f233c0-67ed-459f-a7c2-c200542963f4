require('dotenv').config({ path: '.env.database' });
const { Client } = require('pg');

async function checkNiftyOptions() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: { rejectUnauthorized: false }
  });
  
  try {
    await client.connect();
    console.log('✅ Connected to PostgreSQL');
    
    // Check for NIFTY options using correct column names
    console.log('\n🔍 Searching for NIFTY options...');
    
    const niftyOptions = await client.query(`
      SELECT COUNT(*) as count 
      FROM "Instruments" 
      WHERE "UNDERLYING_SYMBOL" = 'NIFTY' AND "INSTRUMENT_TYPE" = 'OP'
    `);
    console.log(`🎯 NIFTY options found: ${niftyOptions.rows[0].count}`);
    
    // Get sample NIFTY options
    const sampleOptions = await client.query(`
      SELECT "SECURITY_ID", "UNDERLYING_SYMBOL", "SYMBOL_NAME", "STRIKE_PRICE", "OPTION_TYPE", "SM_EXPIRY_DATE", "LOT_SIZE"
      FROM "Instruments" 
      WHERE "UNDERLYING_SYMBOL" = 'NIFTY' AND "INSTRUMENT_TYPE" = 'OP'
      ORDER BY "SM_EXPIRY_DATE", "STRIKE_PRICE"
      LIMIT 10
    `);
    
    console.log('\n📋 Sample NIFTY options:');
    sampleOptions.rows.forEach(opt => {
      console.log(`   ${opt.UNDERLYING_SYMBOL} ${opt.STRIKE_PRICE} ${opt.OPTION_TYPE} (${opt.SM_EXPIRY_DATE}) - ID: ${opt.SECURITY_ID}`);
    });
    
    // Get unique expiry dates
    const expiries = await client.query(`
      SELECT DISTINCT "SM_EXPIRY_DATE" 
      FROM "Instruments" 
      WHERE "UNDERLYING_SYMBOL" = 'NIFTY' AND "INSTRUMENT_TYPE" = 'OP'
      AND "SM_EXPIRY_DATE" IS NOT NULL 
      AND "SM_EXPIRY_DATE" != '1979-12-31'
      ORDER BY "SM_EXPIRY_DATE"
    `);
    
    console.log(`\n📅 Available NIFTY expiry dates: ${expiries.rows.length}`);
    expiries.rows.slice(0, 10).forEach(exp => {
      console.log(`   - ${exp.SM_EXPIRY_DATE}`);
    });
    
    // Check instrument types
    const instrumentTypes = await client.query(`
      SELECT "INSTRUMENT_TYPE", COUNT(*) as count 
      FROM "Instruments" 
      GROUP BY "INSTRUMENT_TYPE" 
      ORDER BY count DESC 
      LIMIT 10
    `);
    console.log('\n📊 Top instrument types:');
    instrumentTypes.rows.forEach(type => {
      console.log(`   ${type.INSTRUMENT_TYPE}: ${type.count}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await client.end();
  }
}

checkNiftyOptions();
