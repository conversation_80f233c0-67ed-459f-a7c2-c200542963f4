// ============================================================================
// CSV PARSER SCRIPT - Test and validate CSV instrument loading
// ============================================================================

import { CSVService } from '../src/services/CSVService';
import { InstrumentFilter } from '../src/types';

async function main() {
  console.log('🚀 CSV Parser Test Script');
  console.log('=' .repeat(50));

  try {
    // Initialize CSV service
    const csvService = new CSVService('./instruments.csv');

    // Load instruments
    console.log('\n📊 Loading instruments from CSV...');
    const startTime = Date.now();
    const instruments = await csvService.loadInstruments();
    const loadTime = Date.now() - startTime;

    console.log(`✅ Loaded ${instruments.length} instruments in ${loadTime}ms`);

    // Show sample instruments
    console.log('\n📋 Sample Instruments:');
    instruments.slice(0, 5).forEach((inst, index) => {
      console.log(`${index + 1}. ${inst.symbol} (${inst.exchange}) - ${inst.displayName}`);
      console.log(`   Security ID: ${inst.securityId}`);
      console.log(`   Type: ${inst.instrumentType}, Lot Size: ${inst.lotSize}`);
      if (inst.expiryDate) {
        console.log(`   Expiry: ${inst.expiryDate.toISOString().split('T')[0]}`);
      }
      console.log('');
    });

    // Get statistics
    console.log('\n📈 Statistics:');
    const exchanges = await csvService.getExchanges();
    const instrumentTypes = await csvService.getInstrumentTypes();

    console.log(`Exchanges: ${exchanges.join(', ')}`);
    console.log(`Instrument Types: ${instrumentTypes.join(', ')}`);

    // Test filtering
    console.log('\n🔍 Testing Filters:');

    // Filter by NSE equity
    const nseEquityFilter: InstrumentFilter = {
      exchange: ['NSE_EQ'],
      instrumentType: ['EQUITY']
    };
    const nseEquity = await csvService.getInstruments(nseEquityFilter);
    console.log(`NSE Equity instruments: ${nseEquity.total}`);

    // Filter by BSE
    const bseFilter: InstrumentFilter = {
      exchange: ['BSE_EQ']
    };
    const bse = await csvService.getInstruments(bseFilter);
    console.log(`BSE instruments: ${bse.total}`);

    // Filter by derivatives
    const derivativesFilter: InstrumentFilter = {
      instrumentType: ['FUTCUR', 'OPTCUR']
    };
    const derivatives = await csvService.getInstruments(derivativesFilter);
    console.log(`Derivatives: ${derivatives.total}`);

    // Test search
    console.log('\n🔎 Testing Search:');
    const searchResults = await csvService.searchInstruments('USDINR', 10);
    console.log(`Search "USDINR" found ${searchResults.length} results:`);
    searchResults.forEach((inst, index) => {
      console.log(`${index + 1}. ${inst.symbol} - ${inst.displayName} (${inst.exchange})`);
    });

    // Test specific instrument lookup
    console.log('\n🎯 Testing Specific Lookup:');
    if (instruments.length > 0) {
      const firstInstrument = instruments[0];
      const found = await csvService.getInstrumentById(firstInstrument.securityId);
      if (found) {
        console.log(`Found instrument: ${found.symbol} - ${found.displayName}`);
      } else {
        console.log('❌ Instrument not found');
      }
    }

    // Performance metrics
    console.log('\n⚡ Performance Metrics:');
    console.log(`Load time: ${loadTime}ms`);
    console.log(`Instruments per second: ${Math.round(instruments.length / (loadTime / 1000))}`);
    console.log(`Memory usage: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`);

    console.log('\n✅ CSV parsing test completed successfully!');

  } catch (error) {
    console.error('❌ Error during CSV parsing test:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}
