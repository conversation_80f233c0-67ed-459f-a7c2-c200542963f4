'use client';

import { useState, useEffect } from 'react';
import { OptionChainData, OptionChainRow, OptionData, ExpiryData, MarketData } from '../types';
import { MarketFormatters } from '../lib/utils';
import { dataCache, cacheHelpers, MARKET_DATA_CACHE_KEYS } from '../lib/data-cache';

interface OptionChainProps {
  marketData: Map<string, MarketData>;
}

export default function OptionChain({ marketData }: OptionChainProps) {
  const [expiryData, setExpiryData] = useState<ExpiryData | null>(null);
  const [selectedExpiry, setSelectedExpiry] = useState<string>('');
  const [optionChain, setOptionChain] = useState<OptionChainData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [niftySpotPrice, setNiftySpotPrice] = useState<number>(0);
  const [cacheLoaded, setCacheLoaded] = useState(false);

  // Load cached data on component mount
  useEffect(() => {
    const loadCachedData = async () => {
      try {
        console.log('📖 OptionChain: Loading cached data...');

        // Load cached option chain data
        const cachedOptionChain = await cacheHelpers.getCachedStaticData(MARKET_DATA_CACHE_KEYS.OPTION_CHAIN);
        if (cachedOptionChain && typeof cachedOptionChain === 'object' && 'rows' in cachedOptionChain) {
          setOptionChain(cachedOptionChain as OptionChainData);
          console.log('✅ OptionChain: Loaded option chain from cache');
        }

        // Load cached expiry data
        const cachedExpiryData = await cacheHelpers.getCachedStaticData(MARKET_DATA_CACHE_KEYS.EXPIRY_DATES);
        if (cachedExpiryData && typeof cachedExpiryData === 'object' && 'expiries' in cachedExpiryData && Array.isArray(cachedExpiryData.expiries)) {
          setExpiryData(cachedExpiryData as ExpiryData);
          if (cachedExpiryData.expiries.length > 0) {
            setSelectedExpiry(cachedExpiryData.expiries[0]);
          }
          console.log('✅ OptionChain: Loaded expiry data from cache');
        }

        // Load cached NIFTY spot price
        const cachedNiftySpot = await cacheHelpers.getCachedMarketData(MARKET_DATA_CACHE_KEYS.NIFTY_SPOT);
        if (cachedNiftySpot && typeof cachedNiftySpot === 'object' && 'ltp' in cachedNiftySpot && typeof cachedNiftySpot.ltp === 'number') {
          setNiftySpotPrice(cachedNiftySpot.ltp);
          console.log('✅ OptionChain: Loaded NIFTY spot from cache:', cachedNiftySpot.ltp);
        }

        setCacheLoaded(true);
      } catch (error) {
        console.error('❌ OptionChain: Failed to load cached data:', error);
        setCacheLoaded(true);
      }
    };

    loadCachedData();
  }, []);

  // ✅ ENHANCED: Get NIFTY spot price from subscribed market data with caching
  useEffect(() => {
    // ✅ FIXED: Direct lookup for NIFTY spot (security ID 13) - should be subscribed with IDX_I exchange
    const niftyData = marketData.get('13');
    if (niftyData && niftyData.ltp > 0) {
      setNiftySpotPrice(niftyData.ltp);
      // Cache the NIFTY spot price
      cacheHelpers.cacheMarketData(MARKET_DATA_CACHE_KEYS.NIFTY_SPOT, niftyData);
      console.log('[SPOT] 🎯 NIFTY Spot Price Updated:', niftyData.ltp, 'from security ID 13 (IDX_I)');
      return;
    }

    // ✅ ENHANCED: Fallback search for any NIFTY index data with better logging
    for (const [securityId, data] of Array.from(marketData.entries())) {
      if (data.symbol === 'NIFTY' && data.ltp > 0) {
        setNiftySpotPrice(data.ltp);
        // Cache the NIFTY spot price
        cacheHelpers.cacheMarketData(MARKET_DATA_CACHE_KEYS.NIFTY_SPOT, data);
        console.log('[SPOT] 🎯 NIFTY Spot Price Found:', data.ltp, 'from security:', securityId, 'exchange:', data.exchange);
        return;
      }
    }

    // ✅ FIXED: Only use mock price if absolutely no real data is available and no cached data
    if (niftySpotPrice === 0 && cacheLoaded) {
      const mockPrice = 24850;
      setNiftySpotPrice(mockPrice);
      console.warn('[SPOT] ⚠️ Using mock NIFTY Spot Price:', mockPrice, '(INDEX data not available - check subscription)');
    }
  }, [marketData, cacheLoaded]);

  // Fetch expiry dates on component mount
  useEffect(() => {
    fetchExpiryDates();
  }, []);

  // Build option chain when expiry is selected
  useEffect(() => {
    if (selectedExpiry && niftySpotPrice > 0) {
      buildOptionChain();
    }
  }, [selectedExpiry, niftySpotPrice, marketData]);

  const fetchExpiryDates = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check cache first
      const cachedExpiryData = await cacheHelpers.getCachedStaticData(MARKET_DATA_CACHE_KEYS.EXPIRY_DATES);
      if (cachedExpiryData && typeof cachedExpiryData === 'object' && 'expiries' in cachedExpiryData && Array.isArray(cachedExpiryData.expiries)) {
        console.log('✅ OptionChain: Using cached expiry data');
        setExpiryData(cachedExpiryData as ExpiryData);
        if (cachedExpiryData.expiries.length > 0) {
          setSelectedExpiry(cachedExpiryData.expiries[0]);
        }
        setLoading(false);
        return;
      }

      console.log('🌐 OptionChain: Fetching fresh expiry data from API');
      const response = await fetch('/api/nifty-expiry');
      if (!response.ok) {
        throw new Error(`Failed to fetch expiry dates: ${response.statusText}`);
      }

      const result = await response.json();
      if (result.success) {
        setExpiryData(result.data);

        // Cache the expiry data
        await cacheHelpers.cacheStaticData(MARKET_DATA_CACHE_KEYS.EXPIRY_DATES, result.data);
        console.log('💾 OptionChain: Cached expiry data');

        // Auto-select first expiry
        if (result.data.expiries.length > 0) {
          setSelectedExpiry(result.data.expiries[0]);
        }
      } else {
        throw new Error(result.message || 'Failed to fetch expiry dates');
      }
    } catch (err) {
      console.error('❌ OptionChain: Error fetching expiry dates:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const buildOptionChain = () => {
    if (!selectedExpiry || niftySpotPrice <= 0) return;

    console.log('🔗 Building option chain for expiry:', selectedExpiry, 'spot:', niftySpotPrice);

    // Generate strike prices around spot price
    const strikes = generateStrikes(niftySpotPrice);
    
    // Build option chain rows
    const rows: OptionChainRow[] = strikes.map(strike => {
      const callData = findOptionData(strike, 'CE');
      const putData = findOptionData(strike, 'PE');
      
      return {
        strikePrice: strike,
        call: callData,
        put: putData
      };
    });

    const optionChainData = {
      underlying: 'NIFTY',
      spotPrice: niftySpotPrice,
      expiry: selectedExpiry,
      rows,
      timestamp: Date.now()
    };

    setOptionChain(optionChainData);

    // Cache the option chain data
    cacheHelpers.cacheStaticData(MARKET_DATA_CACHE_KEYS.OPTION_CHAIN, optionChainData);
    console.log('💾 OptionChain: Cached option chain data for', selectedExpiry);
  };

  const generateStrikes = (spotPrice: number): number[] => {
    // First, get all available strikes from market data for the selected expiry
    const availableStrikes = new Set<number>();

    for (const [securityId, data] of Array.from(marketData.entries())) {
      if (data.symbol.includes('NIFTY') &&
          data.symbol.includes(formatExpiryForSymbol(selectedExpiry))) {

        // Extract strike from symbol (e.g., NIFTY-Jun2025-24850-CE)
        const symbolParts = data.symbol.split('-');
        if (symbolParts.length >= 4) {
          const strike = parseFloat(symbolParts[2]);
          if (!isNaN(strike)) {
            availableStrikes.add(strike);
          }
        }
      }
    }

    if (availableStrikes.size === 0) {
      // Fallback: generate strikes around spot price
      const strikes: number[] = [];
      const baseStrike = Math.round(spotPrice / 50) * 50;
      for (let i = -10; i <= 10; i++) {
        strikes.push(baseStrike + (i * 50));
      }
      return strikes.sort((a, b) => a - b);
    }

    // Convert to sorted array
    const allStrikes = Array.from(availableStrikes).sort((a, b) => a - b);

    // Find ATM strike (closest to spot price)
    const atmStrike = allStrikes.reduce((prev, curr) =>
      Math.abs(curr - spotPrice) < Math.abs(prev - spotPrice) ? curr : prev
    );

    console.log(`[STRIKE] 🎯 ATM Strike identified: ${atmStrike} (Spot: ${spotPrice})`);
    console.log(`[STRIKE] 📊 Available strikes: ${allStrikes.length}`);

    // ✅ ENHANCED: Select more strikes around ATM (±12 strikes for better coverage)
    const atmIndex = allStrikes.indexOf(atmStrike);
    const selectedStrikes = [
      ...allStrikes.slice(Math.max(0, atmIndex - 12), atmIndex),
      atmStrike,
      ...allStrikes.slice(atmIndex + 1, atmIndex + 13)
    ];

    console.log(`[STRIKE] ✅ Selected ${selectedStrikes.length} strikes around ATM:`, selectedStrikes);

    return selectedStrikes;
  };

  const findOptionData = (strike: number, optionType: 'CE' | 'PE'): OptionData | null => {
    // ✅ FIXED: Match by exact expiry date from CSV SM_EXPIRY_DATE column
    for (const [securityId, data] of Array.from(marketData.entries())) {
      // Check if this is a NIFTY option with matching criteria
      if (data.symbol.includes('NIFTY-') &&  // Exact NIFTY options (not BANKNIFTY, etc.)
          data.symbol.includes(`-${strike}-${optionType}`) &&
          data.expiryDate === selectedExpiry) { // ✅ Direct expiry date match from CSV SM_EXPIRY_DATE

        console.log(`[OPTION] ✅ Found ${optionType} ${strike}: ${data.symbol} (Expiry: ${data.expiryDate})`);

        return {
          securityId,
          symbol: data.symbol,
          exchange: data.exchange,
          strikePrice: strike,
          optionType,
          expiryDate: selectedExpiry,
          ltp: data.ltp || 0,
          change: data.change || 0,
          changePercent: data.changePercent || 0,
          volume: data.volume || 0,
          openInterest: data.openInterest,
          bid: data.marketDepth?.[0]?.bidPrice || data.bid,
          ask: data.marketDepth?.[0]?.askPrice || data.ask,
          bidQty: data.marketDepth?.[0]?.bidQty || data.bidQty,
          askQty: data.marketDepth?.[0]?.askQty || data.askQty,
          high: data.high,
          low: data.low,
          open: data.open,
          close: data.close,
          timestamp: data.timestamp
        };
      }
    }

    // Debug: Log when option not found
    if (process.env.NODE_ENV === 'development') {
      console.log(`[OPTION] ❌ Not found ${optionType} ${strike} for expiry ${selectedExpiry}`);

      // Show available options for debugging
      const availableOptions = Array.from(marketData.values())
        .filter(data => data.symbol.includes('NIFTY-') && data.symbol.includes(`-${optionType}`))
        .slice(0, 5);

      console.log(`[DEBUG] Available ${optionType} options:`, availableOptions.map(opt =>
        `${opt.symbol} (Expiry: ${opt.expiryDate})`
      ));
    }

    return null;
  };

  const formatExpiryForSymbol = (expiry: string): string => {
    // Convert YYYY-MM-DD to format used in symbols (e.g., Jun2025)
    const date = new Date(expiry);
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return `${months[date.getMonth()]}${date.getFullYear()}`;
  };

  // Use centralized formatters for consistency
  const formatPrice = MarketFormatters.price;
  const formatNumber = MarketFormatters.number;

  // Use centralized formatters for consistency

  const getChangeColor = (change: number | undefined): string => {
    if (!change) return 'text-gray-400';
    return change > 0 ? 'text-green-400' : change < 0 ? 'text-red-400' : 'text-gray-400';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading option chain...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <h3 className="text-red-800 font-medium">Error Loading Option Chain</h3>
        <p className="text-red-600 text-sm mt-1">{error}</p>
        <button 
          onClick={fetchExpiryDates}
          className="mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white min-h-screen">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <h1 className="text-2xl font-bold text-gray-900">Options</h1>

            {/* Tab Navigation */}
            <div className="flex space-x-1">
              <button className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md">
                🔍 NIFTY
              </button>
              <button className="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md">
                Strategy builder
              </button>
              <button className="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md">
                Class
              </button>
              <button className="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md">
                Volatility
              </button>
            </div>
          </div>

          {/* Right side controls */}
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-600">
              By expiration | by strike
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">NIFTY Spot:</span>
              <span className="text-lg font-bold text-gray-900">
                {formatPrice(niftySpotPrice)}
              </span>
              {niftySpotPrice === 24850 && (
                <span className="text-xs text-gray-500">(Mock)</span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Expiry Selection - Enhanced Format */}
      {expiryData && (
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center space-x-2 overflow-x-auto pb-2">
            {expiryData.expiries.slice(0, 15).map((expiry, index) => {
              const date = new Date(expiry);
              const isSelected = expiry === selectedExpiry;
              const isToday = date.toDateString() === new Date().toDateString();
              const isCurrentWeek = Math.abs(date.getTime() - new Date().getTime()) < 7 * 24 * 60 * 60 * 1000;

              // Enhanced date formatting
              const currentYear = new Date().getFullYear();
              const expiryYear = date.getFullYear();
              const monthShort = date.toLocaleDateString('en-US', { month: 'short' });
              const day = date.getDate();

              // Show year if different from current year
              const showYear = expiryYear !== currentYear;

              return (
                <button
                  key={expiry}
                  onClick={() => setSelectedExpiry(expiry)}
                  className={`flex-shrink-0 px-3 py-2 text-xs font-medium transition-colors border ${
                    isSelected
                      ? 'bg-black text-white border-black'
                      : isToday
                      ? 'bg-orange-500 text-white border-orange-500'
                      : isCurrentWeek
                      ? 'bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <div className="text-center min-w-[40px]">
                    <div className="text-xs font-normal text-gray-600">
                      {monthShort}{showYear && ` ${expiryYear.toString().slice(-2)}`}
                    </div>
                    <div className="font-bold text-sm">
                      {day}
                    </div>
                  </div>
                </button>
              );
            })}
          </div>

          {/* Enhanced Debug Info */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-3 text-xs text-gray-500 bg-gray-100 p-3 rounded-md">
              <div className="grid grid-cols-2 gap-2">
                <div><strong>Selected Expiry:</strong> {selectedExpiry}</div>
                <div><strong>NIFTY Spot:</strong> ₹{niftySpotPrice} {niftySpotPrice === 24850 ? '(Mock)' : '(Live)'}</div>
                <div><strong>Available Strikes:</strong> {optionChain?.rows.length || 0}</div>
                <div><strong>Market Data Size:</strong> {marketData.size} instruments</div>
                <div><strong>NIFTY Options:</strong> {Array.from(marketData.values()).filter(d => d.symbol.includes('NIFTY-')).length} found</div>
                <div><strong>Options with Expiry:</strong> {Array.from(marketData.values()).filter(d => d.symbol.includes('NIFTY-') && d.expiryDate).length} matched</div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Option Chain Table */}
      {optionChain && (
        <div className="bg-white">
          {/* Table Headers */}
          <div className="flex bg-gray-50 border-b border-gray-200 text-xs font-medium text-gray-700 uppercase tracking-wider">
            {/* Calls Header */}
            <div className="flex-1 text-center py-3 border-r border-gray-200">
              <span className="text-green-600 font-bold">Calls</span>
            </div>
            {/* Strike Header */}
            <div className="w-20 text-center py-3 border-r border-gray-200">
              <span className="font-bold">Strike</span>
            </div>
            {/* Puts Header */}
            <div className="flex-1 text-center py-3">
              <span className="text-red-600 font-bold">Puts</span>
            </div>
          </div>

          {/* Column Headers */}
          <div className="flex bg-gray-100 border-b border-gray-200 text-xs font-medium text-gray-600 uppercase tracking-wider">
            {/* Call columns */}
            <div className="flex-1 text-center py-2 px-1">OI</div>
            <div className="flex-1 text-center py-2 px-1">Volume</div>
            <div className="flex-1 text-center py-2 px-1">Bid</div>
            <div className="flex-1 text-center py-2 px-1">Ask</div>
            <div className="flex-1 text-center py-2 px-1">Change</div>
            <div className="flex-1 text-center py-2 px-1 border-r border-gray-200">LTP</div>

            {/* Strike */}
            <div className="w-20 text-center py-2 px-1 border-r border-gray-200 font-bold">Strike</div>

            {/* Put columns */}
            <div className="flex-1 text-center py-2 px-1">LTP</div>
            <div className="flex-1 text-center py-2 px-1">Change</div>
            <div className="flex-1 text-center py-2 px-1">Ask</div>
            <div className="flex-1 text-center py-2 px-1">Bid</div>
            <div className="flex-1 text-center py-2 px-1">Volume</div>
            <div className="flex-1 text-center py-2 px-1">OI</div>
          </div>

          {/* Table Body */}
          <div className="divide-y divide-gray-100">
            {optionChain.rows.map((row, index) => {
              const isATM = Math.abs(row.strikePrice - niftySpotPrice) <= 50;
              const isITM_Call = row.strikePrice < niftySpotPrice;
              const isITM_Put = row.strikePrice > niftySpotPrice;

              return (
                <div
                  key={row.strikePrice}
                  className={`flex hover:bg-gray-50 transition-colors ${
                    isATM ? 'bg-yellow-50' : index % 2 === 0 ? 'bg-white' : 'bg-gray-25'
                  }`}
                >
                  {/* Call Options Data */}
                  <div className={`flex-1 text-center py-3 px-1 text-sm ${isITM_Call ? 'text-green-700 font-medium' : 'text-gray-700'}`}>
                    {formatNumber(row.call?.openInterest)}
                  </div>
                  <div className={`flex-1 text-center py-3 px-1 text-sm ${isITM_Call ? 'text-green-700 font-medium' : 'text-gray-700'}`}>
                    {formatNumber(row.call?.volume)}
                  </div>
                  <div className="flex-1 text-center py-3 px-1 text-sm text-green-600">
                    {formatPrice(row.call?.bid)}
                  </div>
                  <div className="flex-1 text-center py-3 px-1 text-sm text-red-600">
                    {formatPrice(row.call?.ask)}
                  </div>
                  <div className={`flex-1 text-center py-3 px-1 text-sm font-medium ${getChangeColor(row.call?.change)}`}>
                    {row.call?.change ? (
                      <>
                        {row.call.change > 0 ? '+' : ''}{row.call.change.toFixed(2)}
                        <div className="text-xs">
                          ({row.call.changePercent > 0 ? '+' : ''}{row.call.changePercent.toFixed(1)}%)
                        </div>
                      </>
                    ) : '-'}
                  </div>
                  <div className={`flex-1 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 ${
                    isITM_Call ? 'text-green-600' : 'text-gray-700'
                  }`}>
                    {formatPrice(row.call?.ltp)}
                  </div>

                  {/* Strike Price */}
                  <div className={`w-20 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 ${
                    isATM ? 'bg-yellow-100 text-yellow-800' : 'text-gray-900'
                  }`}>
                    {row.strikePrice}
                  </div>

                  {/* Put Options Data */}
                  <div className={`flex-1 text-center py-3 px-1 text-sm font-bold ${
                    isITM_Put ? 'text-red-600' : 'text-gray-700'
                  }`}>
                    {formatPrice(row.put?.ltp)}
                  </div>
                  <div className={`flex-1 text-center py-3 px-1 text-sm font-medium ${getChangeColor(row.put?.change)}`}>
                    {row.put?.change ? (
                      <>
                        {row.put.change > 0 ? '+' : ''}{row.put.change.toFixed(2)}
                        <div className="text-xs">
                          ({row.put.changePercent > 0 ? '+' : ''}{row.put.changePercent.toFixed(1)}%)
                        </div>
                      </>
                    ) : '-'}
                  </div>
                  <div className="flex-1 text-center py-3 px-1 text-sm text-red-600">
                    {formatPrice(row.put?.ask)}
                  </div>
                  <div className="flex-1 text-center py-3 px-1 text-sm text-green-600">
                    {formatPrice(row.put?.bid)}
                  </div>
                  <div className={`flex-1 text-center py-3 px-1 text-sm ${isITM_Put ? 'text-red-700 font-medium' : 'text-gray-700'}`}>
                    {formatNumber(row.put?.volume)}
                  </div>
                  <div className={`flex-1 text-center py-3 px-1 text-sm ${isITM_Put ? 'text-red-700 font-medium' : 'text-gray-700'}`}>
                    {formatNumber(row.put?.openInterest)}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Footer */}
      {optionChain && (
        <div className="bg-gray-50 border-t border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <span>Show all</span>
              <span>•</span>
              <span>Showing {optionChain.rows.length} strikes around ATM</span>
            </div>
            <div className="text-sm text-gray-500">
              Last updated: {new Date(optionChain.timestamp).toLocaleTimeString()}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
