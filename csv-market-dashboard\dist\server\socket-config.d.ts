/**
 * Optimized Socket.IO Configuration for Stable Connections
 */
import { Server as SocketIOServer, ServerOptions } from 'socket.io';
import { Server as HTTPServer } from 'http';
/**
 * Create optimized Socket.IO configuration
 */
export declare function createSocketConfig(): Partial<ServerOptions>;
/**
 * Setup Socket.IO server with optimized configuration
 */
export declare function setupSocketServer(httpServer: HTTPServer): SocketIOServer;
/**
 * Broadcast market data to all connected clients
 */
export declare function broadcastMarketData(io: SocketIOServer, data: any): void;
/**
 * Broadcast market data batch to all connected clients
 */
export declare function broadcastMarketDataBatch(io: SocketIOServer, dataArray: any[]): void;
/**
 * Get connection statistics
 */
export declare function getConnectionStats(io: SocketIOServer): {
    totalConnections: number;
    connectedClients: string[];
    serverUptime: number;
    memoryUsage: NodeJS.MemoryUsage;
};
//# sourceMappingURL=socket-config.d.ts.map