/**
 * Data Caching System for Market Data Persistence
 * Handles localStorage/sessionStorage with TTL and compression
 */

import { CACHE_CONFIG } from './constants'

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
  version: string
}

interface CacheOptions {
  ttl?: number
  useCompression?: boolean
  storage?: 'localStorage' | 'sessionStorage'
}

class DataCache {
  private static instance: DataCache | null = null
  private readonly version = '1.0.0'
  private compressionSupported = false

  /**
   * Check if we're in a browser environment
   */
  private isBrowser(): boolean {
    return typeof window !== 'undefined' && typeof window.localStorage !== 'undefined'
  }

  static getInstance(): DataCache {
    if (!DataCache.instance) {
      DataCache.instance = new DataCache()
    }
    return DataCache.instance
  }

  /**
   * Initialize cache for client-side usage
   * Call this method when the component mounts on the client side
   */
  initializeClientSide(): void {
    if (this.isBrowser()) {
      console.log('🚀 DataCache: Initializing client-side cache')
      // Perform any client-side specific initialization here
      this.cleanupExpired() // Run initial cleanup
    }
  }

  private constructor() {
    this.checkCompressionSupport()
    this.startCleanupInterval()
  }

  /**
   * Check if compression is supported
   */
  private checkCompressionSupport(): void {
    try {
      // Check if CompressionStream is available (modern browsers)
      this.compressionSupported = typeof CompressionStream !== 'undefined'
    } catch {
      this.compressionSupported = false
    }
  }

  /**
   * Set data in cache with TTL
   */
  async set<T>(
    key: string,
    data: T,
    options: CacheOptions = {}
  ): Promise<boolean> {
    try {
      // Skip caching on server side
      if (!this.isBrowser()) {
        console.log(`⚠️ DataCache: Skipping cache on server side for ${key}`)
        return false
      }

      const {
        ttl = CACHE_CONFIG.DEFAULT_TTL,
        useCompression = false,
        storage = 'localStorage'
      } = options

      const entry: CacheEntry<T> = {
        data,
        timestamp: Date.now(),
        ttl,
        version: this.version
      }

      let serializedData = JSON.stringify(entry)

      // Apply compression if requested and supported
      if (useCompression && this.compressionSupported) {
        serializedData = await this.compress(serializedData)
      }

      const storageObj = storage === 'localStorage' ? localStorage : sessionStorage
      const fullKey = this.getFullKey(key)

      storageObj.setItem(fullKey, serializedData)

      console.log(`💾 DataCache: Cached ${key} (${serializedData.length} bytes)`)
      return true
    } catch (error) {
      console.error(`❌ DataCache: Failed to cache ${key}:`, error)
      return false
    }
  }

  /**
   * Get data from cache
   */
  async get<T>(
    key: string,
    options: CacheOptions = {}
  ): Promise<T | null> {
    try {
      // Return null on server side
      if (!this.isBrowser()) {
        return null
      }

      const {
        storage = 'localStorage'
      } = options

      const storageObj = storage === 'localStorage' ? localStorage : sessionStorage
      const fullKey = this.getFullKey(key)
      const serializedData = storageObj.getItem(fullKey)

      if (!serializedData) {
        return null
      }

      let decompressedData = serializedData

      // Try to decompress if it looks compressed
      if (this.compressionSupported && this.isCompressed(serializedData)) {
        decompressedData = await this.decompress(serializedData)
      }

      const entry: CacheEntry<T> = JSON.parse(decompressedData)

      // Check version compatibility
      if (entry.version !== this.version) {
        console.warn(`⚠️ DataCache: Version mismatch for ${key}, removing`)
        this.remove(key, options)
        return null
      }

      // Check TTL
      if (Date.now() - entry.timestamp > entry.ttl) {
        console.log(`⏰ DataCache: ${key} expired, removing`)
        this.remove(key, options)
        return null
      }

      console.log(`📖 DataCache: Retrieved ${key} from cache`)
      return entry.data
    } catch (error) {
      console.error(`❌ DataCache: Failed to retrieve ${key}:`, error)
      // Remove corrupted entry
      this.remove(key, options)
      return null
    }
  }

  /**
   * Remove data from cache
   */
  remove(key: string, options: CacheOptions = {}): void {
    try {
      // Skip on server side
      if (!this.isBrowser()) {
        return
      }

      const { storage = 'localStorage' } = options
      const storageObj = storage === 'localStorage' ? localStorage : sessionStorage
      const fullKey = this.getFullKey(key)

      storageObj.removeItem(fullKey)
      console.log(`🗑️ DataCache: Removed ${key}`)
    } catch (error) {
      console.error(`❌ DataCache: Failed to remove ${key}:`, error)
    }
  }

  /**
   * Clear all cache entries
   */
  clear(storage: 'localStorage' | 'sessionStorage' = 'localStorage'): void {
    try {
      // Skip on server side
      if (!this.isBrowser()) {
        return
      }

      const storageObj = storage === 'localStorage' ? localStorage : sessionStorage
      const keysToRemove: string[] = []

      // Find all our cache keys
      for (let i = 0; i < storageObj.length; i++) {
        const key = storageObj.key(i)
        if (key && key.startsWith('csv_market_dashboard_cache_')) {
          keysToRemove.push(key)
        }
      }

      // Remove them
      keysToRemove.forEach(key => storageObj.removeItem(key))

      console.log(`🧹 DataCache: Cleared ${keysToRemove.length} entries from ${storage}`)
    } catch (error) {
      console.error(`❌ DataCache: Failed to clear ${storage}:`, error)
    }
  }

  /**
   * Get cache statistics
   */
  getStats(storage: 'localStorage' | 'sessionStorage' = 'localStorage'): {
    totalEntries: number
    totalSize: number
    oldestEntry: Date | null
    newestEntry: Date | null
  } {
    // Return empty stats on server side
    if (!this.isBrowser()) {
      return {
        totalEntries: 0,
        totalSize: 0,
        oldestEntry: null,
        newestEntry: null
      }
    }

    const storageObj = storage === 'localStorage' ? localStorage : sessionStorage
    let totalEntries = 0
    let totalSize = 0
    let oldestTimestamp = Infinity
    let newestTimestamp = 0

    try {
      for (let i = 0; i < storageObj.length; i++) {
        const key = storageObj.key(i)
        if (key && key.startsWith('csv_market_dashboard_cache_')) {
          const value = storageObj.getItem(key)
          if (value) {
            totalEntries++
            totalSize += value.length

            try {
              const entry = JSON.parse(value)
              if (entry.timestamp) {
                oldestTimestamp = Math.min(oldestTimestamp, entry.timestamp)
                newestTimestamp = Math.max(newestTimestamp, entry.timestamp)
              }
            } catch {
              // Ignore parsing errors for stats
            }
          }
        }
      }
    } catch (error) {
      console.error('❌ DataCache: Failed to get stats:', error)
    }

    return {
      totalEntries,
      totalSize,
      oldestEntry: oldestTimestamp === Infinity ? null : new Date(oldestTimestamp),
      newestEntry: newestTimestamp === 0 ? null : new Date(newestTimestamp)
    }
  }

  /**
   * Start cleanup interval to remove expired entries
   */
  private startCleanupInterval(): void {
    // Only start cleanup interval on client side
    if (this.isBrowser()) {
      setInterval(() => {
        this.cleanupExpired()
      }, CACHE_CONFIG.EXPIRY_CHECK_INTERVAL)
    }
  }

  /**
   * Clean up expired entries
   */
  private cleanupExpired(): void {
    try {
      // Skip cleanup on server side
      if (!this.isBrowser()) {
        return
      }

      const storages: ('localStorage' | 'sessionStorage')[] = ['localStorage', 'sessionStorage']

      storages.forEach(storageType => {
        const storageObj = storageType === 'localStorage' ? localStorage : sessionStorage
        const keysToRemove: string[] = []

        for (let i = 0; i < storageObj.length; i++) {
          const key = storageObj.key(i)
          if (key && key.startsWith('csv_market_dashboard_cache_')) {
            try {
              const value = storageObj.getItem(key)
              if (value) {
                const entry = JSON.parse(value)
                if (Date.now() - entry.timestamp > entry.ttl) {
                  keysToRemove.push(key)
                }
              }
            } catch {
              // Remove corrupted entries
              keysToRemove.push(key)
            }
          }
        }

        keysToRemove.forEach(key => storageObj.removeItem(key))
        
        if (keysToRemove.length > 0) {
          console.log(`🧹 DataCache: Cleaned up ${keysToRemove.length} expired entries from ${storageType}`)
        }
      })
    } catch (error) {
      console.error('❌ DataCache: Cleanup failed:', error)
    }
  }

  /**
   * Generate full cache key
   */
  private getFullKey(key: string): string {
    return `csv_market_dashboard_cache_${key}`
  }

  /**
   * Check if data is compressed
   */
  private isCompressed(data: string): boolean {
    // Simple heuristic: compressed data usually starts with specific bytes
    return data.startsWith('H4sI') || data.startsWith('eJy') // Common compression signatures
  }

  /**
   * Compress data (placeholder for future implementation)
   */
  private async compress(data: string): Promise<string> {
    // For now, return as-is. Can implement actual compression later
    return data
  }

  /**
   * Decompress data (placeholder for future implementation)
   */
  private async decompress(data: string): Promise<string> {
    // For now, return as-is. Can implement actual decompression later
    return data
  }
}

// Export singleton instance
export const dataCache = DataCache.getInstance()

// Export cache keys for market data
export const MARKET_DATA_CACHE_KEYS = CACHE_CONFIG.KEYS

// Export helper functions
export const cacheHelpers = {
  /**
   * Cache market data with medium TTL for page refresh persistence
   */
  cacheMarketData: async (key: string, data: any) => {
    return dataCache.set(key, data, {
      ttl: CACHE_CONFIG.MARKET_DATA_TTL, // 10 minutes for market data persistence
      storage: 'localStorage' // Use localStorage for page refresh persistence
    })
  },

  /**
   * Cache static data with long TTL
   */
  cacheStaticData: async (key: string, data: any) => {
    return dataCache.set(key, data, {
      ttl: CACHE_CONFIG.STATIC_DATA_TTL, // 10 minutes for static data
      storage: 'localStorage' // Use local storage for persistent data
    })
  },

  /**
   * Get cached market data
   */
  getCachedMarketData: async (key: string) => {
    return dataCache.get(key, { storage: 'localStorage' })
  },

  /**
   * Get cached static data
   */
  getCachedStaticData: async (key: string) => {
    return dataCache.get(key, { storage: 'localStorage' })
  }
}
