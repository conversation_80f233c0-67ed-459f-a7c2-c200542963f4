require('dotenv').config();
const { Client } = require('pg');

async function testConnection() {
  console.log('🔌 Testing PostgreSQL connection...');
  
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: { rejectUnauthorized: false }
  });
  
  try {
    await client.connect();
    console.log('✅ Connected to PostgreSQL successfully!');
    
    // Check tables
    const tables = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);
    
    console.log('📊 Tables found:', tables.rows.map(r => r.table_name));
    
    // Check if Instruments table exists and get structure
    if (tables.rows.some(r => r.table_name === 'Instruments')) {
      const columns = await client.query(`
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'Instruments' 
        ORDER BY ordinal_position
      `);
      
      console.log('📋 Instruments table columns:');
      columns.rows.forEach(col => {
        console.log(`   ${col.column_name}: ${col.data_type}`);
      });
      
      // Get count
      const count = await client.query('SELECT COUNT(*) as count FROM "Instruments"');
      console.log(`📈 Total records: ${count.rows[0].count}`);
      
      // Check for NIFTY options
      const niftyCheck = await client.query(`
        SELECT COUNT(*) as count 
        FROM "Instruments" 
        WHERE ("UNDERLYING_SYMBOL" = 'NIFTY' OR "SYMBOL_NAME" LIKE '%NIFTY%')
        AND ("OPTION_TYPE" = 'CE' OR "OPTION_TYPE" = 'PE')
      `);
      console.log(`🎯 NIFTY options: ${niftyCheck.rows[0].count}`);
    }
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
  } finally {
    await client.end();
  }
}

testConnection();
