{"version": 3, "file": "output.js", "sourceRoot": "", "sources": ["../../src/utils/output.ts"], "names": [], "mappings": ";;;AAUA,+BAA+B;AAE/B,MAAa,MAAM;IAIjB,YAAoB,OAAO,KAAK,EAAE,SAAS,GAAG,KAAK;QAA/B,SAAI,GAAJ,IAAI,CAAQ;QAFhC,UAAK,GAAG,CAAC,OAAe,EAAE,GAAa,EAAE,EAAE,GAAE,CAAC,CAAC;QAG7C,IAAI,SAAS,EAAE;YAEb,IAAI,CAAC,KAAK,GAAG,CAAC,OAAe,EAAE,GAAa,EAAE,EAAE;gBAC9C,OAAO,CAAC,KAAK,CACX,oBAAoB,OAAO,IACzB,GAAG;oBACD,CAAC,CAAC,IAAA,cAAO,EAAC,GAAG,EAAE;wBACX,UAAU,EAAE,IAAI;wBAChB,KAAK,EAAE,QAAQ;wBACf,MAAM,EAAE,IAAI;qBACb,CAAC;oBACJ,CAAC,CAAC,EACN,EAAE,CACH,CAAC;YACJ,CAAC,CAAC;SACH;IACH,CAAC;IAED,IAAW,OAAO,CAAC,KAAc;QAC/B,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;SACnB;IACH,CAAC;IAED,IAAI,CAAC,OAAe;QAClB,IAAI,CAAC,IAAI,CAAC,IAAI;YAAE,OAAO;QACvB,OAAO,CAAC,GAAG,CAAC,mBAAmB,OAAO,EAAE,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,OAAe,EAAE,WAAW,GAAG,KAAK;QACxC,OAAO,CAAC,KAAK,CAEX,2CAA2C,OAAO,SAAS,CAC5D,CAAC;QAEF,IAAI,WAAW;YAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IAED,KAAK;QACH,OAAO,CAAC,KAAK,EAAE,CAAC;IAClB,CAAC;IAED,MAAM,CAAC,KAAc,EAAE,OAAe;QACpC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACrC,CAAC;CACF;AAlDD,wBAkDC", "sourcesContent": ["/**\n * @file\n *\n * Keeping formatting consistent in large projects is difficult.\n * That's why this output class exists, it is used to standardize\n * logging and assertions.\n */\n\n/** */\nimport { IOutput } from '../interfaces';\nimport { inspect } from 'util';\n\nexport class Output implements IOutput {\n  // Default empty\n  debug = (message: string, obj?: unknown) => {};\n\n  constructor(private verb = false, debugMode = false) {\n    if (debugMode) {\n      // When in debug mode. Add debug function.\n      this.debug = (message: string, obj?: unknown) => {\n        console.debug(\n          `tsc-alias debug: ${message} ${\n            obj\n              ? inspect(obj, {\n                  showHidden: true,\n                  depth: Infinity,\n                  colors: true\n                })\n              : ''\n          }`\n        );\n      };\n    }\n  }\n\n  public set verbose(value: boolean) {\n    if (value) {\n      this.verb = value;\n    }\n  }\n\n  info(message: string) {\n    if (!this.verb) return;\n    console.log(`tsc-alias info: ${message}`);\n  }\n\n  error(message: string, exitProcess = false) {\n    console.error(\n      //[BgRed]tsc-alias error:[Reset] [FgRed_]${message}[Reset]\n      `\\x1b[41mtsc-alias error:\\x1b[0m \\x1b[31m${message}\\x1b[0m`\n    );\n\n    if (exitProcess) process.exit(1);\n  }\n\n  clear() {\n    console.clear();\n  }\n\n  assert(claim: unknown, message: string) {\n    claim || this.error(message, true);\n  }\n}\n"]}