(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{4058:function(e,t,s){Promise.resolve().then(s.bind(s,4552))},257:function(e,t,s){"use strict";var a,n;e.exports=(null==(a=s.g.process)?void 0:a.env)&&"object"==typeof(null==(n=s.g.process)?void 0:n.env)?s.g.process:s(4227)},4227:function(e){!function(){var t={229:function(e){var t,s,a,n=e.exports={};function r(){throw Error("setTimeout has not been defined")}function l(){throw Error("clearTimeout has not been defined")}function c(e){if(t===setTimeout)return setTimeout(e,0);if((t===r||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(s){try{return t.call(null,e,0)}catch(s){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:r}catch(e){t=r}try{s="function"==typeof clearTimeout?clearTimeout:l}catch(e){s=l}}();var i=[],o=!1,d=-1;function u(){o&&a&&(o=!1,a.length?i=a.concat(i):d=-1,i.length&&h())}function h(){if(!o){var e=c(u);o=!0;for(var t=i.length;t;){for(a=i,i=[];++d<t;)a&&a[d].run();d=-1,t=i.length}a=null,o=!1,function(e){if(s===clearTimeout)return clearTimeout(e);if((s===l||!s)&&clearTimeout)return s=clearTimeout,clearTimeout(e);try{s(e)}catch(t){try{return s.call(null,e)}catch(t){return s.call(this,e)}}}(e)}}function m(e,t){this.fun=e,this.array=t}function x(){}n.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var s=1;s<arguments.length;s++)t[s-1]=arguments[s];i.push(new m(e,t)),1!==i.length||o||c(h)},m.prototype.run=function(){this.fun.apply(null,this.array)},n.title="browser",n.browser=!0,n.env={},n.argv=[],n.version="",n.versions={},n.on=x,n.addListener=x,n.once=x,n.off=x,n.removeListener=x,n.removeAllListeners=x,n.emit=x,n.prependListener=x,n.prependOnceListener=x,n.listeners=function(e){return[]},n.binding=function(e){throw Error("process.binding is not supported")},n.cwd=function(){return"/"},n.chdir=function(e){throw Error("process.chdir is not supported")},n.umask=function(){return 0}}},s={};function a(e){var n=s[e];if(void 0!==n)return n.exports;var r=s[e]={exports:{}},l=!0;try{t[e](r,r.exports,a),l=!1}finally{l&&delete s[e]}return r.exports}a.ab="//";var n=a(229);e.exports=n}()},4552:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return u}});var a=s(7437),n=s(2265),r=e=>{let{instruments:t,marketData:s,onInstrumentSelect:r,loading:l=!1}=e,[c,i]=(0,n.useState)("symbol"),[o,d]=(0,n.useState)("asc"),u=(0,n.useMemo)(()=>[...t].sort((e,t)=>{let s=e[c],a=t[c];if(void 0===s&&void 0===a)return 0;if(void 0===s)return 1;if(void 0===a)return -1;if(s===a)return 0;let n=s<a?-1:1;return"asc"===o?n:-n}),[t,c,o]),h=e=>{c===e?d("asc"===o?"desc":"asc"):(i(e),d("asc"))},m=e=>void 0===e||0===e?"-":"₹".concat(e.toFixed(2)),x=(e,t)=>{if(void 0===e||void 0===t)return"-";let s=e>=0?"+":"";return"".concat(s).concat(e.toFixed(2)," (").concat(s).concat(t.toFixed(2),"%)")},g=e=>void 0===e||0===e?"text-gray-600":e>0?"text-green-600":"text-red-600",p=e=>void 0===e||0===e?"-":e>=1e7?"".concat((e/1e7).toFixed(1),"Cr"):e>=1e5?"".concat((e/1e5).toFixed(1),"L"):e>=1e3?"".concat((e/1e3).toFixed(1),"K"):e.toString(),f=e=>{let{field:t}=e;return c!==t?(0,a.jsx)("span",{className:"text-gray-400",children:"↕"}):"asc"===o?(0,a.jsx)("span",{className:"text-blue-600",children:"↑"}):(0,a.jsx)("span",{className:"text-blue-600",children:"↓"})};return l?(0,a.jsx)("div",{className:"glass rounded-2xl shadow-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)("div",{className:"spinner w-8 h-8 mr-3"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Loading instruments..."})]})}):0===t.length?(0,a.jsx)("div",{className:"glass rounded-2xl shadow-lg p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"No instruments found"}),(0,a.jsx)("p",{className:"text-gray-500 text-sm mt-2",children:"Try adjusting your filters"})]})}):(0,a.jsxs)("div",{className:"glass rounded-2xl shadow-lg overflow-hidden",children:[(0,a.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Market Instruments"}),(0,a.jsxs)("p",{className:"text-gray-600 text-sm mt-1",children:["Showing ",u.length," instruments"]})]}),(0,a.jsx)("div",{className:"overflow-x-auto custom-scrollbar",style:{maxHeight:"600px"},children:(0,a.jsxs)("table",{className:"market-table",children:[(0,a.jsx)("thead",{className:"sticky top-0 bg-gray-50 z-10",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>h("securityId"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Security ID",(0,a.jsx)(f,{field:"securityId"})]})}),(0,a.jsx)("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>h("symbol"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Symbol",(0,a.jsx)(f,{field:"symbol"})]})}),(0,a.jsx)("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>h("displayName"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Name",(0,a.jsx)(f,{field:"displayName"})]})}),(0,a.jsx)("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>h("exchange"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Exchange",(0,a.jsx)(f,{field:"exchange"})]})}),(0,a.jsx)("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>h("instrumentType"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Type",(0,a.jsx)(f,{field:"instrumentType"})]})}),(0,a.jsx)("th",{className:"text-right",children:"LTP"}),(0,a.jsx)("th",{className:"text-right",children:"Change"}),(0,a.jsx)("th",{className:"text-right",children:"Volume"}),(0,a.jsx)("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>h("lotSize"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Lot Size",(0,a.jsx)(f,{field:"lotSize"})]})})]})}),(0,a.jsx)("tbody",{children:u.map(e=>{let t=s.get(e.securityId);return(0,a.jsxs)("tr",{className:"hover:bg-gray-50 cursor-pointer transition-colors",onClick:()=>null==r?void 0:r(e),children:[(0,a.jsx)("td",{className:"font-mono text-sm text-gray-700",children:e.securityId}),(0,a.jsx)("td",{className:"font-medium text-blue-600",children:e.symbol}),(0,a.jsx)("td",{className:"max-w-xs truncate",title:e.displayName,children:e.displayName}),(0,a.jsx)("td",{children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e.exchange})}),(0,a.jsx)("td",{children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:e.instrumentType})}),(0,a.jsx)("td",{className:"text-right font-medium",children:m(null==t?void 0:t.ltp)}),(0,a.jsx)("td",{className:"text-right font-medium ".concat(g(null==t?void 0:t.change)),children:x(null==t?void 0:t.change,null==t?void 0:t.changePercent)}),(0,a.jsx)("td",{className:"text-right",children:p(null==t?void 0:t.volume)}),(0,a.jsx)("td",{className:"text-right",children:e.lotSize.toLocaleString()})]},e.securityId)})})]})}),u.length>100&&(0,a.jsx)("div",{className:"p-4 bg-gray-50 border-t border-gray-200",children:(0,a.jsx)("p",{className:"text-sm text-gray-600 text-center",children:"Showing first 100 instruments. Use filters to narrow down results."})})]})},l=e=>{var t,s,r;let{filter:l,onFilterChange:c,exchanges:i,instrumentTypes:o,segments:d}=e,[u,h]=(0,n.useState)(l.search||""),m=(e,t)=>{let s=l.exchange||[],a=t?[...s,e]:s.filter(t=>t!==e);c({...l,exchange:a.length>0?a:void 0})},x=(e,t)=>{let s=l.instrumentType||[],a=t?[...s,e]:s.filter(t=>t!==e);c({...l,instrumentType:a.length>0?a:void 0})},g=(e,t)=>{let s=l.segment||[],a=t?[...s,e]:s.filter(t=>t!==e);c({...l,segment:a.length>0?a:void 0})},p=e=>{h(e),c({...l,search:e||void 0})},f=()=>{var e,t,s;return!!((null===(e=l.exchange)||void 0===e?void 0:e.length)||(null===(t=l.instrumentType)||void 0===t?void 0:t.length)||(null===(s=l.segment)||void 0===s?void 0:s.length)||l.search||void 0!==l.isActive||void 0!==l.hasExpiry)};return(0,a.jsxs)("div",{className:"filter-panel",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Filters"}),f()&&(0,a.jsx)("button",{onClick:()=>{h(""),c({})},className:"text-sm text-blue-600 hover:text-blue-800 font-medium",children:"Clear All"})]}),(0,a.jsxs)("div",{className:"filter-group",children:[(0,a.jsx)("label",{className:"filter-label",children:"Search"}),(0,a.jsx)("input",{type:"text",placeholder:"Search by symbol, name, or ISIN...",value:u,onChange:e=>p(e.target.value),className:"filter-input"})]}),(0,a.jsxs)("div",{className:"filter-group",children:[(0,a.jsx)("label",{className:"filter-label",children:"Exchanges"}),(0,a.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto custom-scrollbar",children:i.map(e=>{var t;return(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:(null===(t=l.exchange)||void 0===t?void 0:t.includes(e))||!1,onChange:t=>m(e,t.target.checked),className:"filter-checkbox"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e})]},e)})})]}),(0,a.jsxs)("div",{className:"filter-group",children:[(0,a.jsx)("label",{className:"filter-label",children:"Instrument Types"}),(0,a.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto custom-scrollbar",children:o.map(e=>{var t;return(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:(null===(t=l.instrumentType)||void 0===t?void 0:t.includes(e))||!1,onChange:t=>x(e,t.target.checked),className:"filter-checkbox"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e})]},e)})})]}),(0,a.jsxs)("div",{className:"filter-group",children:[(0,a.jsx)("label",{className:"filter-label",children:"Segments"}),(0,a.jsx)("div",{className:"space-y-2",children:d.map(e=>{var t;return(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:(null===(t=l.segment)||void 0===t?void 0:t.includes(e))||!1,onChange:t=>g(e,t.target.checked),className:"filter-checkbox"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:"C"===e?"Cash (C)":"F"===e?"Futures (F)":"O"===e?"Options (O)":e})]},e)})})]}),(0,a.jsxs)("div",{className:"filter-group",children:[(0,a.jsx)("label",{className:"filter-label",children:"Additional Filters"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:!0===l.isActive,onChange:e=>c({...l,isActive:!!e.target.checked||void 0}),className:"filter-checkbox"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:"Active Only"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:!0===l.hasExpiry,onChange:e=>c({...l,hasExpiry:!!e.target.checked||void 0}),className:"filter-checkbox"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:"Has Expiry"})]})]})]}),(0,a.jsxs)("div",{className:"filter-group",children:[(0,a.jsx)("label",{className:"filter-label",children:"Lot Size Range"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsx)("input",{type:"number",placeholder:"Min",value:l.minLotSize||"",onChange:e=>c({...l,minLotSize:e.target.value?parseInt(e.target.value):void 0}),className:"filter-input text-sm"}),(0,a.jsx)("input",{type:"number",placeholder:"Max",value:l.maxLotSize||"",onChange:e=>c({...l,maxLotSize:e.target.value?parseInt(e.target.value):void 0}),className:"filter-input text-sm"})]})]}),f()&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"Active Filters:"}),(0,a.jsxs)("div",{className:"space-y-1 text-xs text-blue-700",children:[(null===(t=l.exchange)||void 0===t?void 0:t.length)&&(0,a.jsxs)("div",{children:["Exchanges: ",l.exchange.join(", ")]}),(null===(s=l.instrumentType)||void 0===s?void 0:s.length)&&(0,a.jsxs)("div",{children:["Types: ",l.instrumentType.join(", ")]}),(null===(r=l.segment)||void 0===r?void 0:r.length)&&(0,a.jsxs)("div",{children:["Segments: ",l.segment.join(", ")]}),l.search&&(0,a.jsxs)("div",{children:["Search: “",l.search,"”"]}),l.isActive&&(0,a.jsx)("div",{children:"Active instruments only"}),l.hasExpiry&&(0,a.jsx)("div",{children:"With expiry date"}),(l.minLotSize||l.maxLotSize)&&(0,a.jsxs)("div",{children:["Lot size: ",l.minLotSize||0," - ",l.maxLotSize||"∞"]})]})]})]})},c=e=>{let{connected:t}=e;return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(t?"bg-green-500 animate-pulse":"bg-red-500")}),(0,a.jsx)("span",{className:"text-sm font-medium ".concat(t?"text-green-700":"text-red-700"),children:t?"Connected":"Disconnected"})]}),(0,a.jsx)("div",{className:"px-3 py-1 rounded-full text-xs font-semibold ".concat(t?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:t?"LIVE":"OFFLINE"})]})},i=e=>{var t;let{totalInstruments:s,filteredInstruments:n,marketDataCount:r,connected:l,connectionStats:c}=e,i=[{label:"Total Instruments",value:s.toLocaleString(),icon:"\uD83D\uDCCA",color:"bg-blue-500",description:"Available instruments"},{label:"Filtered Results",value:n.toLocaleString(),icon:"\uD83D\uDD0D",color:"bg-purple-500",description:"Matching filters"},{label:"Live Data",value:r.toLocaleString(),icon:"\uD83D\uDCC8",color:l?"bg-green-500":"bg-gray-500",description:c?"".concat(c.connectedInstruments," active"):"Market data points"},{label:"Connection",value:l?"Active":"Inactive",icon:l?"\uD83D\uDFE2":"\uD83D\uDD34",color:l?"bg-green-500":"bg-red-500",description:c?"".concat(c.messagesReceived," messages"):"WebSocket status"}],o=c?[{label:"Cache Size",value:c.cacheSize.toLocaleString(),icon:"\uD83D\uDCBE",color:"bg-indigo-500",description:c.isAutoSaving?"Auto-saving":"Manual save"},{label:"Last Update",value:(e=>{if(!e)return"Never";let t=Math.floor((new Date().getTime()-e.getTime())/1e3);return t<60?"".concat(t,"s ago"):t<3600?"".concat(Math.floor(t/60),"m ago"):e.toLocaleTimeString()})(c.lastUpdate),icon:"\uD83D\uDD52",color:"bg-orange-500",description:"Data freshness"},{label:"Uptime",value:(t=c.connectionUptime)<60?"".concat(t,"s"):t<3600?"".concat(Math.floor(t/60),"m ").concat(t%60,"s"):"".concat(Math.floor(t/3600),"h ").concat(Math.floor(t%3600/60),"m"),icon:"⏱️",color:"bg-teal-500",description:"Connection stability"},{label:"Reconnects",value:c.reconnectAttempts.toString(),icon:c.reconnectAttempts>0?"\uD83D\uDD04":"✅",color:c.reconnectAttempts>0?"bg-yellow-500":"bg-green-500",description:"Connection reliability"}]:[];return(0,a.jsxs)("div",{className:"space-y-6 mb-6",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:i.map((e,t)=>(0,a.jsxs)("div",{className:"glass rounded-xl shadow-lg p-4 card-hover",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.label}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:e.value}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.description})]}),(0,a.jsx)("div",{className:"text-2xl ml-3",children:e.icon})]}),(0,a.jsx)("div",{className:"mt-3 h-1 rounded-full ".concat(e.color)})]},t))}),o.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 mb-3 flex items-center",children:[(0,a.jsx)("span",{className:"text-xl mr-2",children:"⚡"}),"Real-time Connection Stats"]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:o.map((e,t)=>(0,a.jsxs)("div",{className:"glass rounded-xl shadow-lg p-4 card-hover border-l-4 border-l-blue-400",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.label}),(0,a.jsx)("p",{className:"text-xl font-bold text-gray-900 mt-1",children:e.value}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.description})]}),(0,a.jsx)("div",{className:"text-xl ml-3",children:e.icon})]}),(0,a.jsx)("div",{className:"mt-3 h-1 rounded-full ".concat(e.color)})]},t))})]})]})},o=s(2553),d=s(9191);function u(){let{marketData:e,marketDataMap:t,isConnected:s,connectionError:u,connectionStatus:h,isLoading:m,cacheLoaded:x,refresh:g,stats:p,updateFilters:f,filters:v,getFilteredData:y,getSortedData:b}=(0,o.A)(),[j,N]=(0,n.useState)([]),[C,S]=(0,n.useState)(new Map),[D,E]=(0,n.useState)({}),[T,k]=(0,n.useState)(!0),[w,I]=(0,n.useState)(null),[_,A]=(0,n.useState)([]),[L,R]=(0,n.useState)([]);(0,n.useEffect)(()=>{let t=new Map;e.forEach(e=>{e.securityId&&t.set(e.securityId,e)}),S(t)},[e]);let M=(0,n.useCallback)(async()=>{try{k(!0);let e=await d.qn.getCachedStaticData(d.A_.INSTRUMENTS);if(e&&Array.isArray(e)){console.log("✅ Dashboard: Loaded instruments from cache"),N(e),k(!1),F();return}await F()}catch(e){console.error("❌ Dashboard: Error loading instruments:",e),I("Error loading instruments"),k(!1)}},[j.length]);(0,n.useEffect)(()=>{M()},[M]);let F=async()=>{try{console.log("\uD83C\uDF10 Dashboard: Loading fresh instruments from API...");let e=await fetch("".concat("http://localhost:8080]","/api/instruments"));if(e.ok){let t=await e.json();console.log("✅ Dashboard: Loaded",t.data.instruments.length,"instruments from API"),N(t.data.instruments),await d.qn.cacheStaticData(d.A_.INSTRUMENTS,t.data.instruments),console.log("\uD83D\uDCBE Dashboard: Cached instruments data"),k(!1)}else console.error("❌ Dashboard: Failed to load instruments:",e.statusText),I("Failed to load instruments"),k(!1)}catch(e){console.error("❌ Dashboard: Error fetching fresh instruments:",e),0===j.length&&(I("Error loading instruments"),k(!1))}};(0,n.useEffect)(()=>{(async()=>{try{let e="http://localhost:8080]",t=await fetch("".concat(e,"/api/exchanges"));if(t.ok){let e=await t.json();A(e.data)}let s=await fetch("".concat(e,"/api/instrument-types"));if(s.ok){let e=await s.json();R(e.data)}}catch(e){console.error("❌ Error loading metadata:",e)}})()},[]);let U=j.filter(e=>{if(D.exchange&&D.exchange.length>0&&!D.exchange.includes(e.exchange)||D.instrumentType&&D.instrumentType.length>0&&!D.instrumentType.includes(e.instrumentType))return!1;if(D.search){let t=D.search.toLowerCase();return e.symbol.toLowerCase().includes(t)||e.displayName.toLowerCase().includes(t)||e.isin&&e.isin.toLowerCase().includes(t)}return!0});return T?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading market data..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen p-6",children:[(0,a.jsx)("div",{className:"glass rounded-2xl shadow-lg p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold gradient-text",children:"CSV Market Dashboard"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Real-time market data from CSV instruments"}),(0,a.jsxs)("div",{className:"mt-3 space-x-3",children:[(0,a.jsx)("a",{href:"/subscribed",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors",children:"\uD83D\uDCCA View Subscribed Data Dashboard"}),(0,a.jsx)("a",{href:"/option-chain",className:"inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors",children:"\uD83D\uDD17 NIFTY Option Chain"})]})]}),(0,a.jsx)(c,{connected:s})]})}),(w||u)&&(0,a.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6",children:[(0,a.jsx)("strong",{children:"Error:"})," ",w||u]}),(0,a.jsx)(i,{totalInstruments:j.length,filteredInstruments:U.length,marketDataCount:p.totalInstruments,connected:s,connectionStats:p}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsx)(l,{filter:D,onFilterChange:e=>{E(e)},exchanges:_,instrumentTypes:L,segments:["C","F","O"]})}),(0,a.jsx)("div",{className:"lg:col-span-3",children:(0,a.jsx)(r,{instruments:U.slice(0,100),marketData:C,onInstrumentSelect:e=>{console.log("Selected instrument:",e)},loading:T})})]}),(0,a.jsxs)("div",{className:"mt-8 text-center text-sm text-gray-500",children:[(0,a.jsxs)("p",{children:["CSV Market Dashboard - Real-time data from ",j.length," instruments"]}),(0,a.jsxs)("p",{children:["Last updated: ",new Date().toLocaleTimeString()]})]})]})}},2553:function(e,t,s){"use strict";s.d(t,{A:function(){return n}});var a=s(2265);let n=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{autoConnect:t=!0,autoLoadCache:s=!0,autoSaveInterval:n=3e4,reconnectOnError:r=!0,maxReconnectAttempts:l=5}=e,c={status:"disconnected",isConnected:!1,error:null,connectionStats:{totalMessages:0,connectionUptime:0}},i=new Map,o={isLoaded:!1,totalCacheSize:0,pendingUpdates:0,lastCacheUpdate:null},d={filters:{},sortConfig:{field:"symbol",direction:"asc"},selectedInstruments:new Set,viewMode:"table",autoRefresh:!0},u=(0,a.useCallback)(async()=>{},[]),h=(0,a.useCallback)(async()=>{},[]),m=(0,a.useCallback)(()=>{},[]),x=(0,a.useCallback)(async()=>{},[]),g=(0,a.useCallback)(async()=>{},[]),p=(0,a.useCallback)(async e=>{},[]),f=(0,a.useCallback)(async()=>{},[]),v=(0,a.useCallback)(e=>{},[]),y=(0,a.useCallback)((e,t)=>{},[]),b=(0,a.useCallback)(()=>[],[]),j=(0,a.useCallback)(()=>[],[]),N=(0,a.useCallback)(e=>void 0,[]),C=(0,a.useCallback)(e=>void 0,[]),S=(0,a.useCallback)(e=>{},[]),D=(0,a.useCallback)(e=>{},[]),E=(0,a.useCallback)(()=>{},[]),T=(0,a.useRef)(null),k=(0,a.useRef)(0),w=(0,a.useRef)(!1),I=Array.from(i.values());(0,a.useEffect)(()=>((async()=>{if(!w.current){w.current=!0,console.log("\uD83D\uDE80 Enhanced Hook: Initializing...");try{s&&!o.isLoaded&&await g(),t&&"disconnected"===c.status&&await u()}catch(e){console.error("❌ Enhanced Hook: Initialization failed:",e)}}})(),()=>{T.current&&clearInterval(T.current)}),[t,s,o.isLoaded,c.status,u,g]),(0,a.useEffect)(()=>{if(n>0&&I.length>0)return T.current&&clearInterval(T.current),T.current=setInterval(()=>{p()},n),()=>{T.current&&clearInterval(T.current)}},[n,I.length,p]),(0,a.useEffect)(()=>{if(r&&"error"===c.status&&k.current<l){let e=Math.min(1e3*Math.pow(2,k.current),3e4);console.log("\uD83D\uDD04 Enhanced Hook: Auto-reconnecting in ".concat(e,"ms (attempt ").concat(k.current+1,"/").concat(l,")"));let t=setTimeout(async()=>{try{k.current++,await x()}catch(e){console.error("❌ Enhanced Hook: Auto-reconnect failed:",e)}},e);return()=>clearTimeout(t)}},[c.status,x,r,l]),(0,a.useEffect)(()=>{"connected"===c.status&&(k.current=0)},[c.status]);let _=(0,a.useCallback)(()=>b(),[b]),A=(0,a.useCallback)(()=>j(),[j]),L=(0,a.useCallback)(e=>N(e),[N]),R=(0,a.useCallback)(e=>C(e),[C]),M=(0,a.useCallback)(e=>{v(e)},[v]),F=(0,a.useCallback)((e,t)=>{y(e,t)},[y]),U=(0,a.useCallback)(e=>{S(e)},[S]),O=(0,a.useCallback)(e=>{D(e)},[D]),z=(0,a.useCallback)(async()=>{try{k.current=0,await x()}catch(e){throw console.error("❌ Enhanced Hook: Manual reconnect failed:",e),e}},[x]),P=(0,a.useCallback)(async()=>{try{await g()}catch(e){throw console.error("❌ Enhanced Hook: Force refresh failed:",e),e}},[g]),H=(0,a.useCallback)(async()=>{try{await p(!0)}catch(e){throw console.error("❌ Enhanced Hook: Force save failed:",e),e}},[p]),B={totalInstruments:I.length,connectedInstruments:I.filter(e=>e.ltp&&e.ltp>0).length,lastUpdate:null,cacheSize:o.totalCacheSize,connectionUptime:c.connectionStats.connectionUptime,messagesReceived:c.connectionStats.totalMessages,reconnectAttempts:k.current,isAutoSaving:null!==T.current};return{marketData:I,marketDataMap:i,filteredData:_(),sortedData:A(),isConnected:c.isConnected,connectionStatus:c.status,connectionError:c.error,connectionStats:c.connectionStats,cacheLoaded:o.isLoaded,cacheUpdating:o.pendingUpdates>0,lastCacheUpdate:o.lastCacheUpdate,filters:d.filters,sortConfig:d.sortConfig,selectedInstruments:d.selectedInstruments,viewMode:d.viewMode,autoRefresh:d.autoRefresh,isLoading:!1,isInitializing:!w.current,getDataBySecurityId:L,getDataBySymbol:R,getFilteredData:_,getSortedData:A,updateFilters:M,updateSort:F,subscribe:U,unsubscribe:O,connect:h,disconnect:m,reconnect:z,refresh:P,save:H,clearCache:f,reset:E,stats:B,_store:{setFilters:v,setSortConfig:y,subscribeToInstrument:S,unsubscribeFromInstrument:D}}}},8705:function(e,t,s){"use strict";s.d(t,{Hh:function(){return r},Hz:function(){return n},ej:function(){return l}});var a=s(257);let n={BASE_URL:a.env.NEXT_PUBLIC_API_URL||"http://localhost:8080",TIMEOUT:3e4,RETRY_ATTEMPTS:3,RETRY_DELAY:1e3},r={RECONNECT_INTERVAL:3e3,MAX_RECONNECT_ATTEMPTS:15,PING_INTERVAL:25e3,PONG_TIMEOUT:1e4,CONNECTION_TIMEOUT:2e4,HEARTBEAT_INTERVAL:3e4,MAX_LISTENERS_PER_EVENT:10,CLEANUP_INTERVAL:6e4},l={DEFAULT_TTL:3e5,MARKET_DATA_TTL:6e5,STATIC_DATA_TTL:18e5,EXPIRY_CHECK_INTERVAL:6e4,MAX_CACHE_SIZE:52428800,KEYS:{INSTRUMENTS:"instruments",MARKET_DATA:"market_data",OPTION_CHAIN:"option_chain",EXPIRY_DATES:"expiry_dates",NIFTY_SPOT:"nifty_spot",USER_SETTINGS:"user_settings",USER_PREFERENCES:"user_preferences"}};parseInt(a.env.PORT||"8080"),parseInt(a.env.NEXT_PORT||"3000"),a.env.LOG_LEVEL,a.env.ENABLE_METRICS},9191:function(e,t,s){"use strict";s.d(t,{A_:function(){return l},qn:function(){return c}});var a=s(8705);class n{isBrowser(){return void 0!==window.localStorage}static getInstance(){return n.instance||(n.instance=new n),n.instance}initializeClientSide(){this.isBrowser()&&(console.log("\uD83D\uDE80 DataCache: Initializing client-side cache"),this.cleanupExpired())}checkCompressionSupport(){try{this.compressionSupported="undefined"!=typeof CompressionStream}catch(e){this.compressionSupported=!1}}async set(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};try{if(!this.isBrowser())return console.log("⚠️ DataCache: Skipping cache on server side for ".concat(e)),!1;let{ttl:n=a.ej.DEFAULT_TTL,useCompression:r=!1,storage:l="localStorage"}=s,c={data:t,timestamp:Date.now(),ttl:n,version:this.version},i=JSON.stringify(c);r&&this.compressionSupported&&(i=await this.compress(i));let o="localStorage"===l?localStorage:sessionStorage,d=this.getFullKey(e);return o.setItem(d,i),console.log("\uD83D\uDCBE DataCache: Cached ".concat(e," (").concat(i.length," bytes)")),!0}catch(t){return console.error("❌ DataCache: Failed to cache ".concat(e,":"),t),!1}}async get(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{if(!this.isBrowser())return null;let{storage:s="localStorage"}=t,a="localStorage"===s?localStorage:sessionStorage,n=this.getFullKey(e),r=a.getItem(n);if(!r)return null;let l=r;this.compressionSupported&&this.isCompressed(r)&&(l=await this.decompress(r));let c=JSON.parse(l);if(c.version!==this.version)return console.warn("⚠️ DataCache: Version mismatch for ".concat(e,", removing")),this.remove(e,t),null;if(Date.now()-c.timestamp>c.ttl)return console.log("⏰ DataCache: ".concat(e," expired, removing")),this.remove(e,t),null;return console.log("\uD83D\uDCD6 DataCache: Retrieved ".concat(e," from cache")),c.data}catch(s){return console.error("❌ DataCache: Failed to retrieve ".concat(e,":"),s),this.remove(e,t),null}}remove(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{if(!this.isBrowser())return;let{storage:s="localStorage"}=t,a="localStorage"===s?localStorage:sessionStorage,n=this.getFullKey(e);a.removeItem(n),console.log("\uD83D\uDDD1️ DataCache: Removed ".concat(e))}catch(t){console.error("❌ DataCache: Failed to remove ".concat(e,":"),t)}}clear(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"localStorage";try{if(!this.isBrowser())return;let t="localStorage"===e?localStorage:sessionStorage,s=[];for(let e=0;e<t.length;e++){let a=t.key(e);a&&a.startsWith("csv_market_dashboard_cache_")&&s.push(a)}s.forEach(e=>t.removeItem(e)),console.log("\uD83E\uDDF9 DataCache: Cleared ".concat(s.length," entries from ").concat(e))}catch(t){console.error("❌ DataCache: Failed to clear ".concat(e,":"),t)}}getStats(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"localStorage";if(!this.isBrowser())return{totalEntries:0,totalSize:0,oldestEntry:null,newestEntry:null};let t="localStorage"===e?localStorage:sessionStorage,s=0,a=0,n=1/0,r=0;try{for(let e=0;e<t.length;e++){let l=t.key(e);if(l&&l.startsWith("csv_market_dashboard_cache_")){let e=t.getItem(l);if(e){s++,a+=e.length;try{let t=JSON.parse(e);t.timestamp&&(n=Math.min(n,t.timestamp),r=Math.max(r,t.timestamp))}catch(e){}}}}}catch(e){console.error("❌ DataCache: Failed to get stats:",e)}return{totalEntries:s,totalSize:a,oldestEntry:n===1/0?null:new Date(n),newestEntry:0===r?null:new Date(r)}}startCleanupInterval(){this.isBrowser()&&setInterval(()=>{this.cleanupExpired()},a.ej.EXPIRY_CHECK_INTERVAL)}cleanupExpired(){try{if(!this.isBrowser())return;["localStorage","sessionStorage"].forEach(e=>{let t="localStorage"===e?localStorage:sessionStorage,s=[];for(let e=0;e<t.length;e++){let a=t.key(e);if(a&&a.startsWith("csv_market_dashboard_cache_"))try{let e=t.getItem(a);if(e){let t=JSON.parse(e);Date.now()-t.timestamp>t.ttl&&s.push(a)}}catch(e){s.push(a)}}s.forEach(e=>t.removeItem(e)),s.length>0&&console.log("\uD83E\uDDF9 DataCache: Cleaned up ".concat(s.length," expired entries from ").concat(e))})}catch(e){console.error("❌ DataCache: Cleanup failed:",e)}}getFullKey(e){return"csv_market_dashboard_cache_".concat(e)}isCompressed(e){return e.startsWith("H4sI")||e.startsWith("eJy")}async compress(e){return e}async decompress(e){return e}constructor(){this.version="1.0.0",this.compressionSupported=!1,this.checkCompressionSupport(),this.startCleanupInterval()}}n.instance=null;let r=n.getInstance(),l=a.ej.KEYS,c={cacheMarketData:async(e,t)=>r.set(e,t,{ttl:a.ej.MARKET_DATA_TTL,storage:"localStorage"}),cacheStaticData:async(e,t)=>r.set(e,t,{ttl:a.ej.STATIC_DATA_TTL,storage:"localStorage"}),getCachedMarketData:async e=>r.get(e,{storage:"localStorage"}),getCachedStaticData:async e=>r.get(e,{storage:"localStorage"})}}},function(e){e.O(0,[971,117,744],function(){return e(e.s=4058)}),_N_E=e.O()}]);