{"version": 3, "file": "LoggingService.js", "sourceRoot": "", "sources": ["../../src/services/LoggingService.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,IAAY,QAKX;AALD,WAAY,QAAQ;IAClB,yCAAS,CAAA;IACT,uCAAQ,CAAA;IACR,uCAAQ,CAAA;IACR,yCAAS,CAAA;AACX,CAAC,EALW,QAAQ,wBAAR,QAAQ,QAKnB;AAED,IAAY,WAUX;AAVD,WAAY,WAAW;IACrB,4BAAa,CAAA;IACb,8BAAe,CAAA;IACf,0BAAW,CAAA;IACX,0BAAW,CAAA;IACX,gCAAiB,CAAA;IACjB,gCAAiB,CAAA;IACjB,4CAA6B,CAAA;IAC7B,wCAAyB,CAAA;IACzB,kCAAmB,CAAA;AACrB,CAAC,EAVW,WAAW,2BAAX,WAAW,QAUtB;AAUD,MAAM,cAAc;IACV,IAAI,GAAe,EAAE,CAAC;IACtB,OAAO,GAAG,IAAI,CAAC;IACf,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC;IAC7B,iBAAiB,GAAG,IAAI,GAAG,CAAc,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;IAE7E;;OAEG;IACH,QAAQ,CAAC,KAAe;QACtB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,UAAyB;QACrC,IAAI,CAAC,iBAAiB,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAqB,EAAE,OAAe,EAAE,IAAU;QACtD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,QAAqB,EAAE,OAAe,EAAE,IAAU;QACrD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,QAAqB,EAAE,OAAe,EAAE,IAAU;QACrD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAqB,EAAE,OAAe,EAAE,IAAU;QACtD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACK,GAAG,CAAC,KAAe,EAAE,QAAqB,EAAE,OAAe,EAAE,IAAU;QAC7E,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvE,OAAO;QACT,CAAC;QAED,MAAM,KAAK,GAAa;YACtB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,KAAK;YACL,QAAQ;YACR,OAAO;YACP,IAAI;SACL,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEtB,6BAA6B;QAC7B,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YACpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC;QAED,wCAAwC;QACxC,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;QAClC,MAAM,MAAM,GAAG,IAAI,QAAQ,KAAK,SAAS,GAAG,CAAC;QAC7C,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,CAAC;QAEjE,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,QAAQ,CAAC,KAAK;gBACjB,OAAO,CAAC,KAAK,CAAC,GAAG,SAAS,IAAI,MAAM,IAAI,OAAO,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;gBAC/D,MAAM;YACR,KAAK,QAAQ,CAAC,IAAI;gBAChB,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,IAAI,MAAM,IAAI,OAAO,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;gBAC9D,MAAM;YACR,KAAK,QAAQ,CAAC,IAAI;gBAChB,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,IAAI,MAAM,IAAI,OAAO,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;gBAC9D,MAAM;YACR,KAAK,QAAQ,CAAC,KAAK;gBACjB,OAAO,CAAC,KAAK,CAAC,GAAG,SAAS,IAAI,MAAM,IAAI,OAAO,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;gBAC/D,MAAM;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,KAAK,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,QAAqB,EAAE,KAAK,GAAG,EAAE;QACjD,OAAO,IAAI,CAAC,IAAI;aACb,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC;aACxC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,mBAAmB,CACjB,MAAc,EACd,YAAoB,EACpB,YAAoB,EACpB,UAAe;QAEf,MAAM,WAAW,GAA8B;YAC7C,CAAC,EAAE,QAAQ;YACX,CAAC,EAAE,OAAO;YACV,CAAC,EAAE,IAAI;YACP,CAAC,EAAE,OAAO;YACV,CAAC,EAAE,MAAM;SACV,CAAC;QAEF,MAAM,UAAU,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,WAAW,YAAY,EAAE,CAAC;QAE1E,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,MAAM,IAAI,UAAU,SAAS,EAAE;YAChE,YAAY;YACZ,YAAY;YACZ,GAAG,EAAE,UAAU,CAAC,GAAG;YACnB,MAAM,EAAE,UAAU,CAAC,MAAM;YACzB,YAAY,EAAE,UAAU,CAAC,YAAY;YACrC,GAAG,EAAE,UAAU,CAAC,GAAG;YACnB,GAAG,EAAE,UAAU,CAAC,GAAG;SACpB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAc,EAAE,WAAkB;QAC/C,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7C,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,MAAM,uBAAuB,CAAC,CAAC;YAC/D,OAAO;QACT,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,MAAM,kBAAkB,WAAW,CAAC,MAAM,UAAU,EAAE;YACrF,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,QAAQ;YACjC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,QAAQ;YACjC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM;YAC9B,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM;YAC9B,SAAS,EAAE,WAAW;SACvB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,KAAa,EAAE,MAAc,EAAE,UAAmB;QACnE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,gBAAgB,KAAK,SAAS,MAAM,EAAE,EAAE;YAClE,KAAK;YACL,MAAM;YACN,UAAU;YACV,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,WAAkB,EAAE,SAAiB;QACnD,MAAM,SAAS,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACjD,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC/D,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA+B,CAAC,CAAC;QAEpC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,kBAAkB,WAAW,CAAC,MAAM,cAAc,EAAE;YACtF,KAAK,EAAE,WAAW,CAAC,MAAM;YACzB,SAAS;YACT,SAAS;YACT,MAAM,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACxC,MAAM,EAAE,CAAC,CAAC,MAAM;gBAChB,QAAQ,EAAE,CAAC,CAAC,QAAQ;gBACpB,UAAU,EAAE,CAAC,CAAC,UAAU;aACzB,CAAC,CAAC;SACJ,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,KAAa,EAAE,OAAa;QACxC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,MAAc,EAAE,SAAiB,EAAE,WAAmB;QACxE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,6BAA6B,MAAM,EAAE,EAAE;YACnE,MAAM;YACN,SAAS;YACT,WAAW;YACX,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;CACF;AAED,4BAA4B;AACf,QAAA,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC;AAEnD,4CAA4C;AAC5C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;IAC3C,sBAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC1C,CAAC;KAAM,CAAC;IACN,sBAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACzC,CAAC"}