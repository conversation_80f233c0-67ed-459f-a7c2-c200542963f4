# 🧪 Live Data Testing Guide

## 🚀 **Quick Start Testing**

### 1. **Start Both Servers**
```bash
# Terminal 1: Start Backend Server
npm run dev:server

# Terminal 2: Start Frontend
npx next dev
```

**Expected Output**:
- Backend: `🌐 Server running on http://localhost:8081`
- Frontend: `✓ Ready in 1567ms`
- Backend: `✅ Connected to Dhan live market feed`
- Backend: `✅ Auto-subscribed to 11527 NSE derivatives and index instruments`

### 2. **Verify Live Data API**
```bash
curl http://localhost:8081/api/market-data
```

**Expected Response**:
- Status: `200 OK`
- Content: Large JSON (4.6MB+) with live market data
- `"connected": true`
- Array of instruments with live prices

### 3. **Test Frontend Application**
1. Open browser: http://localhost:3000
2. Check main dashboard for connection status
3. Navigate to "NIFTY Option Chain" 
4. Navigate to "View Subscribed Data Dashboard"

## 🔍 **Detailed Testing Scenarios**

### **Scenario 1: NIFTY Spot Price Detection**

**Test Steps**:
1. Go to http://localhost:3000/option-chain
2. Open browser developer console (F12)
3. Look for console logs starting with `[SPOT]`

**Expected Results**:
- ✅ `[SPOT] 🎯 NIFTY Spot Price Found: [LIVE_PRICE] from NIFTY (ID: 2)`
- ❌ `[SPOT] ⚠️ Using mock NIFTY Spot Price: 24850`

**If Mock Price Appears**:
- Check if market is open (9:15 AM - 3:30 PM IST)
- Verify NIFTY index data in API response
- Check server logs for subscription status

### **Scenario 2: Real-time Data Updates**

**Test Steps**:
1. Open http://localhost:3000/subscribed
2. Watch for price changes in the data table
3. Check connection status indicator (should be green)

**Expected Results**:
- Prices should update every few seconds during market hours
- Connection status: "Connected" (green indicator)
- Instrument count should show 11,527+ instruments

### **Scenario 3: Option Chain Live Data**

**Test Steps**:
1. Go to http://localhost:3000/option-chain
2. Select current week expiry
3. Look for live option prices around ATM strikes

**Expected Results**:
- NIFTY spot price should be live (not 24850)
- Option prices should show real LTP values
- Bid/Ask spreads should be realistic
- Volume and OI data should be present

### **Scenario 4: WebSocket Connection Health**

**Test Steps**:
1. Open browser Network tab (F12 → Network)
2. Filter by "WS" (WebSocket)
3. Refresh the page

**Expected Results**:
- WebSocket connection to localhost:8081
- Connection status: "101 Switching Protocols"
- No connection errors or frequent reconnections

## 🐛 **Troubleshooting Guide**

### **Issue: Mock Data Still Appearing**

**Symptoms**:
- NIFTY spot price shows 24850
- Option prices look unrealistic
- Console shows mock data warnings

**Solutions**:
1. **Check Market Hours**: Ensure testing during 9:15 AM - 3:30 PM IST
2. **Verify Credentials**: Check `.env` file has valid ACCESS_TOKEN and CLIENT_ID
3. **Restart Servers**: Kill and restart both backend and frontend
4. **Check API Response**: Verify `/api/market-data` returns live data

### **Issue: WebSocket Connection Failed**

**Symptoms**:
- Connection status shows "Disconnected" (red)
- No real-time updates
- Console errors about WebSocket

**Solutions**:
1. **Port Conflicts**: Ensure ports 3000 and 8081 are free
2. **Firewall**: Check if firewall is blocking connections
3. **Environment**: Verify `NEXT_PUBLIC_SERVER_URL=http://localhost:8081`
4. **Server Status**: Confirm backend server is running and healthy

### **Issue: No Option Data**

**Symptoms**:
- Option chain shows empty or minimal data
- Missing strike prices
- No bid/ask data

**Solutions**:
1. **Subscription Status**: Check server logs for successful subscriptions
2. **Expiry Selection**: Try different expiry dates
3. **Market Hours**: Ensure testing during active trading hours
4. **Data Filtering**: Check if filters are too restrictive

## 📊 **Performance Monitoring**

### **Server Performance**
```bash
# Monitor server logs
npm run dev:server

# Look for these indicators:
# ✅ Subscribed to instrument batch
# 📊 Market data updates
# 🔗 WebSocket connections
```

### **Memory Usage**
- Backend should use ~200-500MB RAM
- Frontend should use ~100-200MB RAM
- Watch for memory leaks during extended testing

### **Network Traffic**
- Initial subscription: High traffic (11K+ instruments)
- Live updates: Moderate traffic (real-time data)
- WebSocket: Persistent connection

## 🎯 **Success Criteria**

### **✅ Live Data Confirmed When**:
1. NIFTY spot price is NOT 24850 (mock value)
2. Prices change during market hours
3. Option chain shows realistic bid/ask spreads
4. Connection status is "Connected" (green)
5. API returns `"connected": true`
6. Server logs show live data subscriptions

### **❌ Still Using Mock Data When**:
1. NIFTY spot price is exactly 24850
2. Prices don't change over time
3. All option prices look artificial
4. Connection status is "Disconnected" (red)
5. Console shows mock data warnings

## 🕐 **Market Hours Testing**

**NSE Trading Hours** (IST):
- **Pre-market**: 9:00 AM - 9:15 AM
- **Regular Trading**: 9:15 AM - 3:30 PM
- **Post-market**: 3:40 PM - 4:00 PM

**Best Testing Times**:
- **10:00 AM - 12:00 PM**: High volatility, active trading
- **2:00 PM - 3:15 PM**: Afternoon session activity
- **Avoid**: 12:00 PM - 1:00 PM (lunch break, lower activity)

## 📞 **Support Information**

If live data is still not working after following this guide:

1. **Check Dhan API Status**: Verify your Dhan account and API access
2. **Review Server Logs**: Look for authentication errors
3. **Network Connectivity**: Ensure stable internet connection
4. **API Limits**: Check if you've hit any rate limits

The system should now be fully operational with live market data!
