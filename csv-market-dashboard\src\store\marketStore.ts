/**
 * Zustand Store for Market Data Management
 * Handles state persistence, WebSocket connections, and caching
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import WebSocketManager from '@/lib/websocket-manager';

export interface MarketDataItem {
  securityId: string;
  symbol: string;
  exchange: string;
  ltp?: number;
  ltq?: number;
  ltt?: string;
  open?: number;
  high?: number;
  low?: number;
  close?: number;
  volume?: number;
  oi?: number;
  oiChange?: number;
  change?: number;
  changePercent?: number;
  bid?: number;
  ask?: number;
  bidQty?: number;
  askQty?: number;
  [key: string]: any;
}

interface MarketStore {
  // State
  marketData: Record<string, MarketDataItem>;
  isConnected: boolean;
  isLoading: boolean;
  lastUpdate: Date | null;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  error: string | null;
  cacheLoaded: boolean;

  // WebSocket Manager
  wsManager: WebSocketManager | null;

  // Actions
  setMarketData: (data: MarketDataItem[]) => void;
  updateMarketData: (tick: MarketDataItem) => void;
  updateMarketDataBatch: (ticks: MarketDataItem[]) => void;
  hydrateFromRedis: (initial: MarketDataItem[]) => void;
  setConnectionStatus: (status: 'connecting' | 'connected' | 'disconnected' | 'error') => void;
  setError: (error: string | null) => void;
  setLoading: (loading: boolean) => void;
  setCacheLoaded: (loaded: boolean) => void;

  // WebSocket Actions
  initializeWebSocket: () => void;
  connect: () => void;
  disconnect: () => void;

  // Cache Actions
  loadFromCache: () => Promise<void>;
  refreshFromCache: () => Promise<void>;
  clearCache: () => Promise<void>;
  saveToLocalStorage: () => void;
  loadFromLocalStorage: () => boolean;

  // Utility Actions
  reset: () => void;
  getMarketDataBySymbol: (symbol: string) => MarketDataItem | undefined;
  getMarketDataBySecurityId: (securityId: string) => MarketDataItem | undefined;
}

const useMarketStore = create<MarketStore>()(
  persist(
    (set, get) => ({
      // Initial State
      marketData: {},
      isConnected: false,
      isLoading: true,
      lastUpdate: null,
      connectionStatus: 'disconnected',
      error: null,
      cacheLoaded: false,
      wsManager: null,

      // State Setters
      setMarketData: (data) => {
        set({ 
          marketData: data.reduce((acc, item) => {
            acc[item.securityId] = item;
            return acc;
          }, {} as Record<string, MarketDataItem>),
          lastUpdate: new Date(),
          isLoading: false 
        });
        get().saveToLocalStorage();
      },

      updateMarketData: (tick) =>
        set(state => ({
          marketData: {
            ...state.marketData,
            [tick.securityId]: { ...state.marketData[tick.securityId], ...tick }
          }
        })),

      updateMarketDataBatch: (ticks) =>
        set(state => {
          const updated = { ...state.marketData };
          for (const tick of ticks) {
            updated[tick.securityId] = { ...updated[tick.securityId], ...tick };
          }
          return { marketData: updated };
        }),

      hydrateFromRedis: (initial) =>
        set({
          marketData: initial.reduce((acc, tick) => {
            acc[tick.securityId] = tick;
            return acc;
          }, {} as Record<string, MarketDataItem>)
        }),

      setConnectionStatus: (status) => {
        set({ 
          connectionStatus: status,
          isConnected: status === 'connected',
          error: status === 'error' ? get().error : null
        });
      },

      setError: (error) => set({ error }),
      setLoading: (loading) => set({ isLoading: loading }),
      setCacheLoaded: (loaded) => set({ cacheLoaded: loaded }),

      // WebSocket Management
      initializeWebSocket: () => {
        const wsManager = WebSocketManager.getInstance();
        set({ wsManager });

        // Connect with proper options and event handlers
        wsManager.connect({
          onConnect: () => {
            console.log('🔌 MarketStore: WebSocket connected');
            get().setConnectionStatus('connected');
          },
          onDisconnect: (reason: string) => {
            console.log('🔌 MarketStore: WebSocket disconnected:', reason);
            get().setConnectionStatus('disconnected');
          },
          onError: (error: Error) => {
            console.error('❌ MarketStore: WebSocket error:', error);
            get().setConnectionStatus('error');
            get().setError(error.message || 'WebSocket connection error');
          },
          onReconnect: (attemptNumber: number) => {
            console.log('🔄 MarketStore: WebSocket reconnected after', attemptNumber, 'attempts');
            get().setConnectionStatus('connected');
            get().setError(null);
          },
          onMarketData: (data: any) => {
            if (data && typeof data === 'object') {
              get().updateMarketData(data);
            }
          },
          onMarketDataBatch: (data: any[]) => {
            if (Array.isArray(data) && data.length > 0) {
              get().updateMarketDataBatch(data);
            }
          }
        });
      },

      connect: () => {
        const { wsManager } = get();
        if (wsManager && !wsManager.isConnected()) {
          get().setConnectionStatus('connecting');
          // WebSocket manager handles connection automatically when initialized
          console.log('🔌 MarketStore: Connection request - WebSocket manager will handle');
        }
      },

      disconnect: () => {
        const { wsManager } = get();
        if (wsManager) {
          wsManager.disconnect();
          get().setConnectionStatus('disconnected');
        }
      },

      // Cache Management
      loadFromCache: async () => {
        try {
          set({ isLoading: true });
          console.log('📖 MarketStore: Loading cached market data...');

          // First try localStorage for instant display
          const hasLocalData = get().loadFromLocalStorage();
          
          // Then load from Redis
          const response = await fetch('/api/cache/bulk');
          if (response.ok) {
            const result = await response.json();
            if (result.success && result.data && Array.isArray(result.data) && result.data.length > 0) {
              get().setMarketData(result.data);
              console.log(`✅ MarketStore: Loaded ${result.data.length} items from Redis cache`);
            } else if (!hasLocalData) {
              console.log('📭 MarketStore: No cached data found');
            }
          } else if (!hasLocalData) {
            console.log('📭 MarketStore: Failed to load cached data');
          }

          set({ cacheLoaded: true, isLoading: false });
        } catch (error) {
          console.error('❌ MarketStore: Failed to load cached data:', error);
          set({ cacheLoaded: true, isLoading: false });
        }
      },

      refreshFromCache: async () => {
        try {
          const response = await fetch('/api/cache/bulk');
          if (response.ok) {
            const result = await response.json();
            if (result.success && result.data && Array.isArray(result.data) && result.data.length > 0) {
              get().setMarketData(result.data);
              console.log(`✅ MarketStore: Refreshed ${result.data.length} items from cache`);
            }
          }
        } catch (error) {
          console.error('❌ MarketStore: Failed to refresh from cache:', error);
        }
      },

      clearCache: async () => {
        try {
          const response = await fetch('/api/cache/clear', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ pattern: 'market_data:*' })
          });
          
          if (response.ok) {
            set({ marketData: {}, lastUpdate: null });
            localStorage.removeItem('marketData');
            localStorage.removeItem('marketDataTimestamp');
            console.log('🧹 MarketStore: Cache cleared');
          }
        } catch (error) {
          console.error('❌ MarketStore: Failed to clear cache:', error);
        }
      },

      // Local Storage Management
      saveToLocalStorage: () => {
        const { marketData } = get();
        if (Object.keys(marketData).length > 0) {
          try {
            localStorage.setItem('marketData', JSON.stringify(Object.values(marketData)));
            localStorage.setItem('marketDataTimestamp', new Date().toISOString());
          } catch (error) {
            console.warn('⚠️ MarketStore: Failed to save to localStorage:', error);
          }
        }
      },

      loadFromLocalStorage: () => {
        try {
          const localData = localStorage.getItem('marketData');
          const localTimestamp = localStorage.getItem('marketDataTimestamp');
          
          if (localData && localTimestamp) {
            const parsedData = JSON.parse(localData);
            const timestamp = new Date(localTimestamp);
            const now = new Date();
            const ageMinutes = (now.getTime() - timestamp.getTime()) / (1000 * 60);
            
            // Use localStorage data if it's less than 10 minutes old
            if (ageMinutes < 10 && Array.isArray(parsedData) && parsedData.length > 0) {
              set({ 
                marketData: parsedData.reduce((acc, item) => {
                  acc[item.securityId] = item;
                  return acc;
                }, {} as Record<string, MarketDataItem>),
                lastUpdate: timestamp,
                isLoading: false 
              });
              console.log(`⚡ MarketStore: Loaded ${parsedData.length} items from localStorage (${ageMinutes.toFixed(1)}min old)`);
              return true;
            }
          }
        } catch (error) {
          console.warn('⚠️ MarketStore: Failed to load from localStorage:', error);
          localStorage.removeItem('marketData');
          localStorage.removeItem('marketDataTimestamp');
        }
        return false;
      },

      // Utility Functions
      reset: () => {
        set({
          marketData: {},
          isConnected: false,
          isLoading: true,
          lastUpdate: null,
          connectionStatus: 'disconnected',
          error: null,
          cacheLoaded: false
        });
        localStorage.removeItem('marketData');
        localStorage.removeItem('marketDataTimestamp');
      },

      getMarketDataBySymbol: (symbol) => {
        return Object.values(get().marketData).find(item => item.symbol === symbol);
      },

      getMarketDataBySecurityId: (securityId) => {
        return get().marketData[securityId];
      },
    }),
    {
      name: 'market-store',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        marketData: state.marketData
      }),
    }
  )
);

export default useMarketStore;
