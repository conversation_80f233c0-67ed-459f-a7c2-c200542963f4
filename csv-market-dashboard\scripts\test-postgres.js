const { PrismaClient } = require('../generated/prisma');

async function testPostgreSQLConnection() {
  console.log('🔌 Testing PostgreSQL connection...');
  
  const prisma = new PrismaClient();
  
  try {
    // Test connection
    await prisma.$connect();
    console.log('✅ Successfully connected to PostgreSQL database!');
    
    // Test a simple query
    const result = await prisma.$queryRaw`SELECT version()`;
    console.log('📊 Database version:', result);
    
    // Check if instrument table exists
    try {
      const count = await prisma.instrument.count();
      console.log(`📊 Current instrument count: ${count}`);
    } catch (error) {
      console.log('⚠️ Instrument table does not exist yet - this is expected for first setup');
    }
    
    console.log('🎉 PostgreSQL connection test successful!');
    
  } catch (error) {
    console.error('❌ PostgreSQL connection failed:');
    console.error('Error:', error.message);
    console.error('Code:', error.code);
    
    if (error.message.includes('Tenant or user not found')) {
      console.log('\n💡 Troubleshooting tips:');
      console.log('1. Check if the username and password are correct');
      console.log('2. Verify the database URL format');
      console.log('3. Make sure the Supabase project is active');
      console.log('4. Check if the database user has proper permissions');
    }
    
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testPostgreSQLConnection();
