(()=>{var e={};e.id=288,e.ids=[288],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},7871:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>x,routeModule:()=>h,serverHooks:()=>y,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>g});var r={};s.r(r),s.d(r,{GET:()=>p,OPTIONS:()=>d});var n=s(6559),a=s(8088),i=s(7719),o=s(2190);class c{constructor(e,t){this.baseUrl="https://api.dhan.co",this.expiryCache=new Map,this.cacheTimeout=3e5,this.accessToken=e||process.env.ACCESS_TOKEN||"",this.clientId=t||process.env.CLIENT_ID||"",this.accessToken&&this.clientId||console.warn("⚠️ Dhan API credentials not provided")}async getExpiryDates(e,t){try{let s=`${e}_${t}`,r=this.expiryCache.get(s);if(r&&Date.now()-r.timestamp<this.cacheTimeout)return console.log("\uD83D\uDCCB Using cached expiry dates for",s),r.data;if(!this.accessToken||!this.clientId){console.warn("⚠️ No Dhan API credentials - returning mock expiry dates");let e=this.getMockExpiryDates();return this.expiryCache.set(s,{data:e,timestamp:Date.now()}),e}let n={UnderlyingScrip:e,UnderlyingSeg:t};console.log("\uD83D\uDD0D Fetching expiry dates from Dhan API:",n);let a=await fetch(`${this.baseUrl}/v2/optionchain/expirylist`,{method:"POST",headers:{"Content-Type":"application/json","access-token":this.accessToken,"client-id":this.clientId,Accept:"application/json"},body:JSON.stringify(n)});if(!a.ok)throw Error(`Dhan API error: ${a.status} ${a.statusText}`);let i=await a.json();console.log("✅ Received expiry dates:",i);let o=i.data||[];return this.expiryCache.set(s,{data:o,timestamp:Date.now()}),o}catch(n){console.error("❌ Error fetching expiry dates:",n);let s=this.getMockExpiryDates(),r=`${e}_${t}`;return this.expiryCache.set(r,{data:s,timestamp:Date.now()}),s}}getMockExpiryDates(){let e=new Date,t=[];for(let s=0;s<6;s++){let r=new Date(e),n=(4-e.getDay()+7)%7||7;r.setDate(e.getDate()+n+7*s),t.push(r.toISOString().split("T")[0])}for(let s=1;s<=3;s++){let r=new Date(e.getFullYear(),e.getMonth()+s,1);r.setMonth(r.getMonth()+1,0);let n=r.getDate()-(r.getDay()+3)%7;r.setDate(n);let a=r.toISOString().split("T")[0];t.includes(a)||t.push(a)}return t.sort()}hasCredentials(){return!!(this.accessToken&&this.clientId)}getStatus(){return{hasCredentials:this.hasCredentials(),baseUrl:this.baseUrl}}}let l=null;async function p(e){try{console.log("\uD83D\uDCC5 Fetching NIFTY expiry dates...");let e=(l||(l=new c),l),t="IDX_I",s=await e.getExpiryDates(13,t);return console.log(`✅ Found ${s.length} NIFTY expiry dates`),o.NextResponse.json({success:!0,data:{underlying:"NIFTY",securityId:13,segment:t,expiries:s,count:s.length,hasCredentials:e.hasCredentials()},timestamp:new Date().toISOString()})}catch(e){return console.error("❌ Error fetching NIFTY expiry dates:",e),o.NextResponse.json({success:!1,error:"Failed to fetch NIFTY expiry dates",message:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function d(e){return new o.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}let h=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/nifty-expiry/route",pathname:"/api/nifty-expiry",filename:"route",bundlePath:"app/api/nifty-expiry/route"},resolvedPagePath:"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\api\\nifty-expiry\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:u,workUnitAsyncStorage:g,serverHooks:y}=h;function x(){return(0,i.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:g})}},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580],()=>s(7871));module.exports=r})();