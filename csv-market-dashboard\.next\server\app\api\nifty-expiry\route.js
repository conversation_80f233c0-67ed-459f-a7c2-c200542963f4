/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/nifty-expiry/route";
exports.ids = ["app/api/nifty-expiry/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnifty-expiry%2Froute&page=%2Fapi%2Fnifty-expiry%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnifty-expiry%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnifty-expiry%2Froute&page=%2Fapi%2Fnifty-expiry%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnifty-expiry%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_love_dashboard_csv_market_dashboard_src_app_api_nifty_expiry_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/nifty-expiry/route.ts */ \"(rsc)/./src/app/api/nifty-expiry/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/nifty-expiry/route\",\n        pathname: \"/api/nifty-expiry\",\n        filename: \"route\",\n        bundlePath: \"app/api/nifty-expiry/route\"\n    },\n    resolvedPagePath: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\api\\\\nifty-expiry\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_love_dashboard_csv_market_dashboard_src_app_api_nifty_expiry_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnifty-expiry%2Froute&page=%2Fapi%2Fnifty-expiry%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnifty-expiry%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/nifty-expiry/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/nifty-expiry/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _services_DhanAPIService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../services/DhanAPIService */ \"(rsc)/./src/services/DhanAPIService.ts\");\n\n\n/**\r\n * API endpoint to get NIFTY expiry dates\r\n * GET /api/nifty-expiry\r\n */ async function GET(request) {\n    try {\n        console.log('📅 Fetching NIFTY expiry dates...');\n        const dhanAPI = (0,_services_DhanAPIService__WEBPACK_IMPORTED_MODULE_1__.getDhanAPIService)();\n        // NIFTY parameters for Dhan API\n        const NIFTY_SCRIP = 13; // NIFTY security ID\n        const NIFTY_SEGMENT = 'IDX_I'; // Index segment\n        const expiryDates = await dhanAPI.getExpiryDates(NIFTY_SCRIP, NIFTY_SEGMENT);\n        console.log(`✅ Found ${expiryDates.length} NIFTY expiry dates`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                underlying: 'NIFTY',\n                securityId: NIFTY_SCRIP,\n                segment: NIFTY_SEGMENT,\n                expiries: expiryDates,\n                count: expiryDates.length,\n                hasCredentials: dhanAPI.hasCredentials()\n            },\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('❌ Error fetching NIFTY expiry dates:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch NIFTY expiry dates',\n            message: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n/**\r\n * Handle OPTIONS for CORS\r\n */ async function OPTIONS(request) {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            'Access-Control-Allow-Origin': '*',\n            'Access-Control-Allow-Methods': 'GET, OPTIONS',\n            'Access-Control-Allow-Headers': 'Content-Type'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/nifty-expiry/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/DhanAPIService.ts":
/*!****************************************!*\
  !*** ./src/services/DhanAPIService.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DhanAPIService: () => (/* binding */ DhanAPIService),\n/* harmony export */   getDhanAPIService: () => (/* binding */ getDhanAPIService)\n/* harmony export */ });\n/**\r\n * Dhan API Service for REST API calls\r\n * Handles authentication and API requests to Dhan\r\n */ class DhanAPIService {\n    constructor(accessToken, clientId){\n        this.baseUrl = 'https://api.dhan.co';\n        this.expiryCache = new Map();\n        this.cacheTimeout = 5 * 60 * 1000 // 5 minutes cache\n        ;\n        this.accessToken = accessToken || process.env.ACCESS_TOKEN || '';\n        this.clientId = clientId || process.env.CLIENT_ID || '';\n        if (!this.accessToken || !this.clientId) {\n            console.warn('⚠️ Dhan API credentials not provided');\n        }\n    }\n    /**\r\n   * Get expiry dates for an underlying instrument\r\n   */ async getExpiryDates(underlyingScrip, underlyingSeg) {\n        try {\n            const cacheKey = `${underlyingScrip}_${underlyingSeg}`;\n            // Check cache first\n            const cached = this.expiryCache.get(cacheKey);\n            if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {\n                console.log('📋 Using cached expiry dates for', cacheKey);\n                return cached.data;\n            }\n            if (!this.accessToken || !this.clientId) {\n                console.warn('⚠️ No Dhan API credentials - returning mock expiry dates');\n                const mockData = this.getMockExpiryDates();\n                this.expiryCache.set(cacheKey, {\n                    data: mockData,\n                    timestamp: Date.now()\n                });\n                return mockData;\n            }\n            const requestBody = {\n                UnderlyingScrip: underlyingScrip,\n                UnderlyingSeg: underlyingSeg\n            };\n            console.log('🔍 Fetching expiry dates from Dhan API:', requestBody);\n            const response = await fetch(`${this.baseUrl}/v2/optionchain/expirylist`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'access-token': this.accessToken,\n                    'client-id': this.clientId,\n                    'Accept': 'application/json'\n                },\n                body: JSON.stringify(requestBody)\n            });\n            if (!response.ok) {\n                throw new Error(`Dhan API error: ${response.status} ${response.statusText}`);\n            }\n            const data = await response.json();\n            console.log('✅ Received expiry dates:', data);\n            const expiryDates = data.data || [];\n            // Cache the result\n            this.expiryCache.set(cacheKey, {\n                data: expiryDates,\n                timestamp: Date.now()\n            });\n            return expiryDates;\n        } catch (error) {\n            console.error('❌ Error fetching expiry dates:', error);\n            // Return mock data as fallback\n            const mockData = this.getMockExpiryDates();\n            const cacheKey = `${underlyingScrip}_${underlyingSeg}`;\n            this.expiryCache.set(cacheKey, {\n                data: mockData,\n                timestamp: Date.now()\n            });\n            return mockData;\n        }\n    }\n    /**\r\n   * Mock expiry dates for development/fallback\r\n   */ getMockExpiryDates() {\n        const today = new Date();\n        const expiries = [];\n        // Generate next 6 weekly expiries (Thursdays)\n        for(let i = 0; i < 6; i++){\n            const nextThursday = new Date(today);\n            const daysUntilThursday = (4 - today.getDay() + 7) % 7 || 7; // Thursday is day 4\n            nextThursday.setDate(today.getDate() + daysUntilThursday + i * 7);\n            expiries.push(nextThursday.toISOString().split('T')[0]);\n        }\n        // Add monthly expiries (last Thursday of month)\n        for(let i = 1; i <= 3; i++){\n            const monthlyExpiry = new Date(today.getFullYear(), today.getMonth() + i, 1);\n            // Find last Thursday of the month\n            monthlyExpiry.setMonth(monthlyExpiry.getMonth() + 1, 0); // Last day of month\n            const lastDay = monthlyExpiry.getDate();\n            const lastDayOfWeek = monthlyExpiry.getDay();\n            const lastThursday = lastDay - (lastDayOfWeek + 3) % 7;\n            monthlyExpiry.setDate(lastThursday);\n            const expiryStr = monthlyExpiry.toISOString().split('T')[0];\n            if (!expiries.includes(expiryStr)) {\n                expiries.push(expiryStr);\n            }\n        }\n        return expiries.sort();\n    }\n    /**\r\n   * Check if API credentials are available\r\n   */ hasCredentials() {\n        return !!(this.accessToken && this.clientId);\n    }\n    /**\r\n   * Get API status\r\n   */ getStatus() {\n        return {\n            hasCredentials: this.hasCredentials(),\n            baseUrl: this.baseUrl\n        };\n    }\n}\n// Singleton instance\nlet dhanAPIService = null;\nfunction getDhanAPIService() {\n    if (!dhanAPIService) {\n        dhanAPIService = new DhanAPIService();\n    }\n    return dhanAPIService;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/DhanAPIService.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnifty-expiry%2Froute&page=%2Fapi%2Fnifty-expiry%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnifty-expiry%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();