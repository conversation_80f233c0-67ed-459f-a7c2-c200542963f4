"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/nifty-expiry/route";
exports.ids = ["app/api/nifty-expiry/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnifty-expiry%2Froute&page=%2Fapi%2Fnifty-expiry%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnifty-expiry%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnifty-expiry%2Froute&page=%2Fapi%2Fnifty-expiry%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnifty-expiry%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_love_dashboard_csv_market_dashboard_src_app_api_nifty_expiry_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/nifty-expiry/route.ts */ \"(rsc)/./src/app/api/nifty-expiry/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/nifty-expiry/route\",\n        pathname: \"/api/nifty-expiry\",\n        filename: \"route\",\n        bundlePath: \"app/api/nifty-expiry/route\"\n    },\n    resolvedPagePath: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\api\\\\nifty-expiry\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_love_dashboard_csv_market_dashboard_src_app_api_nifty_expiry_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/nifty-expiry/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnifty-expiry%2Froute&page=%2Fapi%2Fnifty-expiry%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnifty-expiry%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/nifty-expiry/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/nifty-expiry/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _services_DhanAPIService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../services/DhanAPIService */ \"(rsc)/./src/services/DhanAPIService.ts\");\n\n\n/**\r\n * API endpoint to get NIFTY expiry dates\r\n * GET /api/nifty-expiry\r\n */ async function GET(request) {\n    try {\n        console.log(\"\\uD83D\\uDCC5 Fetching NIFTY expiry dates...\");\n        const dhanAPI = (0,_services_DhanAPIService__WEBPACK_IMPORTED_MODULE_1__.getDhanAPIService)();\n        // NIFTY parameters for Dhan API\n        const NIFTY_SCRIP = 13; // NIFTY security ID\n        const NIFTY_SEGMENT = \"IDX_I\"; // Index segment\n        const expiryDates = await dhanAPI.getExpiryDates(NIFTY_SCRIP, NIFTY_SEGMENT);\n        console.log(`✅ Found ${expiryDates.length} NIFTY expiry dates`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                underlying: \"NIFTY\",\n                securityId: NIFTY_SCRIP,\n                segment: NIFTY_SEGMENT,\n                expiries: expiryDates,\n                count: expiryDates.length,\n                hasCredentials: dhanAPI.hasCredentials()\n            },\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"❌ Error fetching NIFTY expiry dates:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to fetch NIFTY expiry dates\",\n            message: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n/**\r\n * Handle OPTIONS for CORS\r\n */ async function OPTIONS(request) {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            \"Access-Control-Allow-Origin\": \"*\",\n            \"Access-Control-Allow-Methods\": \"GET, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/nifty-expiry/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/DhanAPIService.ts":
/*!****************************************!*\
  !*** ./src/services/DhanAPIService.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DhanAPIService: () => (/* binding */ DhanAPIService),\n/* harmony export */   getDhanAPIService: () => (/* binding */ getDhanAPIService)\n/* harmony export */ });\n/**\r\n * Dhan API Service for REST API calls\r\n * Handles authentication and API requests to Dhan\r\n */ class DhanAPIService {\n    constructor(accessToken, clientId){\n        this.baseUrl = \"https://api.dhan.co\";\n        this.expiryCache = new Map();\n        this.cacheTimeout = 5 * 60 * 1000 // 5 minutes cache\n        ;\n        this.accessToken = accessToken || process.env.ACCESS_TOKEN || \"\";\n        this.clientId = clientId || process.env.CLIENT_ID || \"\";\n        if (!this.accessToken || !this.clientId) {\n            console.warn(\"⚠️ Dhan API credentials not provided\");\n        }\n    }\n    /**\r\n   * Get expiry dates for an underlying instrument\r\n   */ async getExpiryDates(underlyingScrip, underlyingSeg) {\n        try {\n            const cacheKey = `${underlyingScrip}_${underlyingSeg}`;\n            // Check cache first\n            const cached = this.expiryCache.get(cacheKey);\n            if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {\n                console.log(\"\\uD83D\\uDCCB Using cached expiry dates for\", cacheKey);\n                return cached.data;\n            }\n            if (!this.accessToken || !this.clientId) {\n                console.warn(\"⚠️ No Dhan API credentials - returning mock expiry dates\");\n                const mockData = this.getMockExpiryDates();\n                this.expiryCache.set(cacheKey, {\n                    data: mockData,\n                    timestamp: Date.now()\n                });\n                return mockData;\n            }\n            const requestBody = {\n                UnderlyingScrip: underlyingScrip,\n                UnderlyingSeg: underlyingSeg\n            };\n            console.log(\"\\uD83D\\uDD0D Fetching expiry dates from Dhan API:\", requestBody);\n            const response = await fetch(`${this.baseUrl}/v2/optionchain/expirylist`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"access-token\": this.accessToken,\n                    \"client-id\": this.clientId,\n                    \"Accept\": \"application/json\"\n                },\n                body: JSON.stringify(requestBody)\n            });\n            if (!response.ok) {\n                throw new Error(`Dhan API error: ${response.status} ${response.statusText}`);\n            }\n            const data = await response.json();\n            console.log(\"✅ Received expiry dates:\", data);\n            const expiryDates = data.data || [];\n            // Cache the result\n            this.expiryCache.set(cacheKey, {\n                data: expiryDates,\n                timestamp: Date.now()\n            });\n            return expiryDates;\n        } catch (error) {\n            console.error(\"❌ Error fetching expiry dates:\", error);\n            // Return mock data as fallback\n            const mockData = this.getMockExpiryDates();\n            const cacheKey = `${underlyingScrip}_${underlyingSeg}`;\n            this.expiryCache.set(cacheKey, {\n                data: mockData,\n                timestamp: Date.now()\n            });\n            return mockData;\n        }\n    }\n    /**\r\n   * Mock expiry dates for development/fallback\r\n   */ getMockExpiryDates() {\n        const today = new Date();\n        const expiries = [];\n        // Generate next 6 weekly expiries (Thursdays)\n        for(let i = 0; i < 6; i++){\n            const nextThursday = new Date(today);\n            const daysUntilThursday = (4 - today.getDay() + 7) % 7 || 7; // Thursday is day 4\n            nextThursday.setDate(today.getDate() + daysUntilThursday + i * 7);\n            expiries.push(nextThursday.toISOString().split(\"T\")[0]);\n        }\n        // Add monthly expiries (last Thursday of month)\n        for(let i = 1; i <= 3; i++){\n            const monthlyExpiry = new Date(today.getFullYear(), today.getMonth() + i, 1);\n            // Find last Thursday of the month\n            monthlyExpiry.setMonth(monthlyExpiry.getMonth() + 1, 0); // Last day of month\n            const lastDay = monthlyExpiry.getDate();\n            const lastDayOfWeek = monthlyExpiry.getDay();\n            const lastThursday = lastDay - (lastDayOfWeek + 3) % 7;\n            monthlyExpiry.setDate(lastThursday);\n            const expiryStr = monthlyExpiry.toISOString().split(\"T\")[0];\n            if (!expiries.includes(expiryStr)) {\n                expiries.push(expiryStr);\n            }\n        }\n        return expiries.sort();\n    }\n    /**\r\n   * Check if API credentials are available\r\n   */ hasCredentials() {\n        return !!(this.accessToken && this.clientId);\n    }\n    /**\r\n   * Get API status\r\n   */ getStatus() {\n        return {\n            hasCredentials: this.hasCredentials(),\n            baseUrl: this.baseUrl\n        };\n    }\n}\n// Singleton instance\nlet dhanAPIService = null;\nfunction getDhanAPIService() {\n    if (!dhanAPIService) {\n        dhanAPIService = new DhanAPIService();\n    }\n    return dhanAPIService;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/DhanAPIService.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnifty-expiry%2Froute&page=%2Fapi%2Fnifty-expiry%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnifty-expiry%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();