"use strict";(()=>{var e={};e.id=546,e.ids=[546],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},815:(e,t,s)=>{s.r(t),s.d(t,{originalPathname:()=>x,patchFetch:()=>D,requestAsyncStorage:()=>u,routeModule:()=>d,serverHooks:()=>g,staticGenerationAsyncStorage:()=>y});var r={};s.r(r),s.d(r,{GET:()=>p,OPTIONS:()=>h});var a=s(9303),n=s(8716),i=s(670),o=s(7070);class c{constructor(e,t){this.baseUrl="https://api.dhan.co",this.expiryCache=new Map,this.cacheTimeout=3e5,this.accessToken=e||process.env.ACCESS_TOKEN||"",this.clientId=t||process.env.CLIENT_ID||"",this.accessToken&&this.clientId||console.warn("⚠️ Dhan API credentials not provided")}async getExpiryDates(e,t){try{let s=`${e}_${t}`,r=this.expiryCache.get(s);if(r&&Date.now()-r.timestamp<this.cacheTimeout)return console.log("\uD83D\uDCCB Using cached expiry dates for",s),r.data;if(!this.accessToken||!this.clientId){console.warn("⚠️ No Dhan API credentials - returning mock expiry dates");let e=this.getMockExpiryDates();return this.expiryCache.set(s,{data:e,timestamp:Date.now()}),e}let a={UnderlyingScrip:e,UnderlyingSeg:t};console.log("\uD83D\uDD0D Fetching expiry dates from Dhan API:",a);let n=await fetch(`${this.baseUrl}/v2/optionchain/expirylist`,{method:"POST",headers:{"Content-Type":"application/json","access-token":this.accessToken,"client-id":this.clientId,Accept:"application/json"},body:JSON.stringify(a)});if(!n.ok)throw Error(`Dhan API error: ${n.status} ${n.statusText}`);let i=await n.json();console.log("✅ Received expiry dates:",i);let o=i.data||[];return this.expiryCache.set(s,{data:o,timestamp:Date.now()}),o}catch(a){console.error("❌ Error fetching expiry dates:",a);let s=this.getMockExpiryDates(),r=`${e}_${t}`;return this.expiryCache.set(r,{data:s,timestamp:Date.now()}),s}}getMockExpiryDates(){let e=new Date,t=[];for(let s=0;s<6;s++){let r=new Date(e),a=(4-e.getDay()+7)%7||7;r.setDate(e.getDate()+a+7*s),t.push(r.toISOString().split("T")[0])}for(let s=1;s<=3;s++){let r=new Date(e.getFullYear(),e.getMonth()+s,1);r.setMonth(r.getMonth()+1,0);let a=r.getDate()-(r.getDay()+3)%7;r.setDate(a);let n=r.toISOString().split("T")[0];t.includes(n)||t.push(n)}return t.sort()}hasCredentials(){return!!(this.accessToken&&this.clientId)}getStatus(){return{hasCredentials:this.hasCredentials(),baseUrl:this.baseUrl}}}let l=null;async function p(e){try{console.log("\uD83D\uDCC5 Fetching NIFTY expiry dates...");let e=(l||(l=new c),l),t="IDX_I",s=await e.getExpiryDates(13,t);return console.log(`✅ Found ${s.length} NIFTY expiry dates`),o.NextResponse.json({success:!0,data:{underlying:"NIFTY",securityId:13,segment:t,expiries:s,count:s.length,hasCredentials:e.hasCredentials()},timestamp:new Date().toISOString()})}catch(e){return console.error("❌ Error fetching NIFTY expiry dates:",e),o.NextResponse.json({success:!1,error:"Failed to fetch NIFTY expiry dates",message:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function h(e){return new o.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}let d=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/nifty-expiry/route",pathname:"/api/nifty-expiry",filename:"route",bundlePath:"app/api/nifty-expiry/route"},resolvedPagePath:"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\api\\nifty-expiry\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:u,staticGenerationAsyncStorage:y,serverHooks:g}=d,x="/api/nifty-expiry/route";function D(){return(0,i.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:y})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[276,972],()=>s(815));module.exports=r})();