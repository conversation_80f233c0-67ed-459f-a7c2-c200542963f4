(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[190],{942:(e,t,a)=>{"use strict";a.d(t,{aq:()=>r,ko:()=>c});var n=a(1950);class o{isBrowser(){return void 0!==window.localStorage}static getInstance(){return o.instance||(o.instance=new o),o.instance}initializeClientSide(){this.isBrowser()&&(console.log("\uD83D\uDE80 DataCache: Initializing client-side cache"),this.cleanupExpired())}checkCompressionSupport(){try{this.compressionSupported="undefined"!=typeof CompressionStream}catch(e){this.compressionSupported=!1}}async set(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};try{if(!this.isBrowser())return console.log("⚠️ DataCache: Skipping cache on server side for ".concat(e)),!1;let{ttl:o=n.WS.DEFAULT_TTL,useCompression:s=!1,storage:r="localStorage"}=a,c={data:t,timestamp:Date.now(),ttl:o,version:this.version},i=JSON.stringify(c);s&&this.compressionSupported&&(i=await this.compress(i));let l="localStorage"===r?localStorage:sessionStorage,d=this.getFullKey(e);return l.setItem(d,i),console.log("\uD83D\uDCBE DataCache: Cached ".concat(e," (").concat(i.length," bytes)")),!0}catch(t){return console.error("❌ DataCache: Failed to cache ".concat(e,":"),t),!1}}async get(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{if(!this.isBrowser())return null;let{storage:a="localStorage"}=t,n="localStorage"===a?localStorage:sessionStorage,o=this.getFullKey(e),s=n.getItem(o);if(!s)return null;let r=s;this.compressionSupported&&this.isCompressed(s)&&(r=await this.decompress(s));let c=JSON.parse(r);if(c.version!==this.version)return console.warn("⚠️ DataCache: Version mismatch for ".concat(e,", removing")),this.remove(e,t),null;if(Date.now()-c.timestamp>c.ttl)return console.log("⏰ DataCache: ".concat(e," expired, removing")),this.remove(e,t),null;return console.log("\uD83D\uDCD6 DataCache: Retrieved ".concat(e," from cache")),c.data}catch(a){return console.error("❌ DataCache: Failed to retrieve ".concat(e,":"),a),this.remove(e,t),null}}remove(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{if(!this.isBrowser())return;let{storage:a="localStorage"}=t,n="localStorage"===a?localStorage:sessionStorage,o=this.getFullKey(e);n.removeItem(o),console.log("\uD83D\uDDD1️ DataCache: Removed ".concat(e))}catch(t){console.error("❌ DataCache: Failed to remove ".concat(e,":"),t)}}clear(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"localStorage";try{if(!this.isBrowser())return;let t="localStorage"===e?localStorage:sessionStorage,a=[];for(let e=0;e<t.length;e++){let n=t.key(e);n&&n.startsWith("csv_market_dashboard_cache_")&&a.push(n)}a.forEach(e=>t.removeItem(e)),console.log("\uD83E\uDDF9 DataCache: Cleared ".concat(a.length," entries from ").concat(e))}catch(t){console.error("❌ DataCache: Failed to clear ".concat(e,":"),t)}}getStats(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"localStorage";if(!this.isBrowser())return{totalEntries:0,totalSize:0,oldestEntry:null,newestEntry:null};let t="localStorage"===e?localStorage:sessionStorage,a=0,n=0,o=1/0,s=0;try{for(let e=0;e<t.length;e++){let r=t.key(e);if(r&&r.startsWith("csv_market_dashboard_cache_")){let e=t.getItem(r);if(e){a++,n+=e.length;try{let t=JSON.parse(e);t.timestamp&&(o=Math.min(o,t.timestamp),s=Math.max(s,t.timestamp))}catch(e){}}}}}catch(e){console.error("❌ DataCache: Failed to get stats:",e)}return{totalEntries:a,totalSize:n,oldestEntry:o===1/0?null:new Date(o),newestEntry:0===s?null:new Date(s)}}startCleanupInterval(){this.isBrowser()&&setInterval(()=>{this.cleanupExpired()},n.WS.EXPIRY_CHECK_INTERVAL)}cleanupExpired(){try{if(!this.isBrowser())return;["localStorage","sessionStorage"].forEach(e=>{let t="localStorage"===e?localStorage:sessionStorage,a=[];for(let e=0;e<t.length;e++){let n=t.key(e);if(n&&n.startsWith("csv_market_dashboard_cache_"))try{let e=t.getItem(n);if(e){let t=JSON.parse(e);Date.now()-t.timestamp>t.ttl&&a.push(n)}}catch(e){a.push(n)}}a.forEach(e=>t.removeItem(e)),a.length>0&&console.log("\uD83E\uDDF9 DataCache: Cleaned up ".concat(a.length," expired entries from ").concat(e))})}catch(e){console.error("❌ DataCache: Cleanup failed:",e)}}getFullKey(e){return"csv_market_dashboard_cache_".concat(e)}isCompressed(e){return e.startsWith("H4sI")||e.startsWith("eJy")}async compress(e){return e}async decompress(e){return e}constructor(){this.version="1.0.0",this.compressionSupported=!1,this.checkCompressionSupport(),this.startCleanupInterval()}}o.instance=null;let s=o.getInstance(),r=n.WS.KEYS,c={cacheMarketData:async(e,t)=>s.set(e,t,{ttl:n.WS.MARKET_DATA_TTL,storage:"localStorage"}),cacheStaticData:async(e,t)=>s.set(e,t,{ttl:n.WS.STATIC_DATA_TTL,storage:"localStorage"}),getCachedMarketData:async e=>s.get(e,{storage:"localStorage"}),getCachedStaticData:async e=>s.get(e,{storage:"localStorage"})}},1950:(e,t,a)=>{"use strict";a.d(t,{WS:()=>r,i3:()=>o,ld:()=>s});var n=a(9509);let o={BASE_URL:n.env.NEXT_PUBLIC_API_URL||"http://localhost:8080",TIMEOUT:3e4,RETRY_ATTEMPTS:3,RETRY_DELAY:1e3},s={RECONNECT_INTERVAL:3e3,MAX_RECONNECT_ATTEMPTS:15,PING_INTERVAL:25e3,PONG_TIMEOUT:1e4,CONNECTION_TIMEOUT:2e4,HEARTBEAT_INTERVAL:3e4,MAX_LISTENERS_PER_EVENT:10,CLEANUP_INTERVAL:6e4},r={DEFAULT_TTL:3e5,MARKET_DATA_TTL:6e5,STATIC_DATA_TTL:18e5,EXPIRY_CHECK_INTERVAL:6e4,MAX_CACHE_SIZE:0x3200000,KEYS:{INSTRUMENTS:"instruments",MARKET_DATA:"market_data",OPTION_CHAIN:"option_chain",EXPIRY_DATES:"expiry_dates",NIFTY_SPOT:"nifty_spot",USER_SETTINGS:"user_settings",USER_PREFERENCES:"user_preferences"}};parseInt(n.env.PORT||"8080"),parseInt(n.env.NEXT_PORT||"3000"),n.env.LOG_LEVEL,n.env.ENABLE_METRICS},3095:(e,t,a)=>{Promise.resolve().then(a.bind(a,9524))},9434:(e,t,a)=>{"use strict";a.d(t,{ap:()=>n});let n={price:e=>!e||e<=0?"-":"₹".concat(e.toFixed(2)),number:e=>null==e?"-":e.toLocaleString(),percentage:e=>{if(!e||0===e)return"-";let t=e>=0?"+":"";return"".concat(t).concat(e.toFixed(2),"%")},change:(e,t)=>{if(!e||0===e)return"-";let a=e>0?"+":"",n=t?" (".concat(a).concat(t.toFixed(2),"%)"):"";return"".concat(a).concat(e.toFixed(2)).concat(n)},volume:e=>null==e?"-":function(e){return e>=1e7?"".concat((e/1e7).toFixed(2)," Cr"):e>=1e5?"".concat((e/1e5).toFixed(2)," L"):e>=1e3?"".concat((e/1e3).toFixed(2)," K"):e.toString()}(e),time:e=>e?new Date(e).toLocaleTimeString():"-",date:e=>e?new Date(e).toLocaleDateString():"-",bidAsk:(e,t)=>!e||e<=0?"-":"₹".concat(e.toFixed(2)).concat(t?" (".concat(t,")"):"")}},9524:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>x});var n=a(5155),o=a(2115),s=a(9434),r=a(942);function c(e){let{marketData:t}=e,[a,c]=(0,o.useState)(null),[i,l]=(0,o.useState)(""),[d,h]=(0,o.useState)(null),[m,g]=(0,o.useState)(!1),[u,x]=(0,o.useState)(null),[p,y]=(0,o.useState)(0),[k,f]=(0,o.useState)(!1);(0,o.useEffect)(()=>{(async()=>{try{console.log("\uD83D\uDCD6 OptionChain: Loading cached data...");let e=await r.ko.getCachedStaticData(r.aq.OPTION_CHAIN);e&&"object"==typeof e&&"rows"in e&&(h(e),console.log("✅ OptionChain: Loaded option chain from cache"));let t=await r.ko.getCachedStaticData(r.aq.EXPIRY_DATES);t&&"object"==typeof t&&"expiries"in t&&Array.isArray(t.expiries)&&(c(t),t.expiries.length>0&&l(t.expiries[0]),console.log("✅ OptionChain: Loaded expiry data from cache"));let a=await r.ko.getCachedMarketData(r.aq.NIFTY_SPOT);a&&"object"==typeof a&&"ltp"in a&&"number"==typeof a.ltp&&(y(a.ltp),console.log("✅ OptionChain: Loaded NIFTY spot from cache:",a.ltp)),f(!0)}catch(e){console.error("❌ OptionChain: Failed to load cached data:",e),f(!0)}})()},[]),(0,o.useEffect)(()=>{let e=t.get("13");if(e&&e.ltp>0){y(e.ltp),r.ko.cacheMarketData(r.aq.NIFTY_SPOT,e),console.log("[SPOT] \uD83C\uDFAF NIFTY Spot Price Updated:",e.ltp,"from security ID 13 (IDX_I)");return}for(let[e,a]of Array.from(t.entries()))if("NIFTY"===a.symbol&&a.ltp>0){y(a.ltp),r.ko.cacheMarketData(r.aq.NIFTY_SPOT,a),console.log("[SPOT] \uD83C\uDFAF NIFTY Spot Price Found:",a.ltp,"from security:",e,"exchange:",a.exchange);return}0===p&&k&&(y(24850),console.warn("[SPOT] ⚠️ Using mock NIFTY Spot Price:",24850,"(INDEX data not available - check subscription)"))},[t,k,p]);let S=(0,o.useCallback)(e=>{let t=new Date(e);return"".concat(["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][t.getMonth()]).concat(t.getFullYear())},[]),b=(0,o.useCallback)(e=>{let a=new Set;for(let[e,n]of Array.from(t.entries()))if(n.symbol.includes("NIFTY")&&n.symbol.includes(S(i))){let e=n.symbol.split("-");if(e.length>=4){let t=parseFloat(e[2]);isNaN(t)||a.add(t)}}let n=Array.from(a).sort((e,t)=>e-t);if(0===n.length){console.warn("[STRIKE] ⚠️ No strikes found for expiry:",i);let t=[],a=50*Math.round(e/50);for(let e=-12;e<=12;e++)t.push(a+50*e);return t}let o=n.reduce((t,a)=>Math.abs(a-e)<Math.abs(t-e)?a:t),s=n.indexOf(o),r=[...n.slice(Math.max(0,s-12),s),o,...n.slice(s+1,s+13)];return console.log("[STRIKE] ✅ Selected ".concat(r.length," strikes around ATM:"),r),r},[i,t,S]),D=(0,o.useCallback)((e,a)=>{for(let[m,g]of Array.from(t.entries()))if(g.symbol.includes("NIFTY-")&&g.symbol.includes("-".concat(e,"-").concat(a))&&g.expiryDate===i){var n,o,s,r,c,l,d,h;return console.log("[OPTION] ✅ Found ".concat(a," ").concat(e,": ").concat(g.symbol," (Expiry: ").concat(g.expiryDate,")")),{securityId:m,symbol:g.symbol,exchange:g.exchange,strikePrice:e,optionType:a,expiryDate:i,ltp:g.ltp||0,change:g.change||0,changePercent:g.changePercent||0,volume:g.volume||0,openInterest:g.openInterest,bid:(null==(o=g.marketDepth)||null==(n=o[0])?void 0:n.bidPrice)||g.bid,ask:(null==(r=g.marketDepth)||null==(s=r[0])?void 0:s.askPrice)||g.ask,bidQty:(null==(l=g.marketDepth)||null==(c=l[0])?void 0:c.bidQty)||g.bidQty,askQty:(null==(h=g.marketDepth)||null==(d=h[0])?void 0:d.askQty)||g.askQty,high:g.high,low:g.low,open:g.open,close:g.close,timestamp:g.timestamp}}return null},[i,t]),v=(0,o.useCallback)(()=>{if(!i||p<=0)return;console.log("\uD83D\uDD17 Building option chain for expiry:",i,"spot:",p);let e=b(p).map(e=>{let t=D(e,"CE"),a=D(e,"PE");return{strikePrice:e,call:t,put:a}}),t={underlying:"NIFTY",spotPrice:p,expiry:i,rows:e,timestamp:Date.now()};h(t),r.ko.cacheStaticData(r.aq.OPTION_CHAIN,t),console.log("\uD83D\uDCBE OptionChain: Cached option chain data for",i)},[i,p,b,D]);(0,o.useEffect)(()=>{C()},[]),(0,o.useEffect)(()=>{i&&p>0&&v()},[i,p,v]);let C=async()=>{try{g(!0),x(null);let e=await r.ko.getCachedStaticData(r.aq.EXPIRY_DATES);if(e&&"object"==typeof e&&"expiries"in e&&Array.isArray(e.expiries)){console.log("✅ OptionChain: Using cached expiry data"),c(e),e.expiries.length>0&&l(e.expiries[0]),g(!1);return}console.log("\uD83C\uDF10 OptionChain: Fetching fresh expiry data from API");let t=await fetch("/api/nifty-expiry");if(!t.ok)throw Error("Failed to fetch expiry dates: ".concat(t.statusText));let a=await t.json();if(a.success)c(a.data),await r.ko.cacheStaticData(r.aq.EXPIRY_DATES,a.data),console.log("\uD83D\uDCBE OptionChain: Cached expiry data"),a.data.expiries.length>0&&l(a.data.expiries[0]);else throw Error(a.message||"Failed to fetch expiry dates")}catch(e){console.error("❌ OptionChain: Error fetching expiry dates:",e),x(e instanceof Error?e.message:"Unknown error")}finally{g(!1)}},N=s.ap.price,T=s.ap.number,E=e=>e?e>0?"text-green-400":e<0?"text-red-400":"text-gray-400":"text-gray-400";return m?(0,n.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Loading option chain..."})]})}):u?(0,n.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,n.jsx)("h3",{className:"text-red-800 font-medium",children:"Error Loading Option Chain"}),(0,n.jsx)("p",{className:"text-red-600 text-sm mt-1",children:u}),(0,n.jsx)("button",{onClick:C,className:"mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700",children:"Retry"})]}):(0,n.jsxs)("div",{className:"bg-white min-h-screen",children:[(0,n.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Options"}),(0,n.jsxs)("div",{className:"flex space-x-1",children:[(0,n.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md",children:"\uD83D\uDD0D NIFTY"}),(0,n.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md",children:"Strategy builder"}),(0,n.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md",children:"Class"}),(0,n.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md",children:"Volatility"})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"By expiration | by strike"}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("span",{className:"text-sm text-gray-600",children:"NIFTY Spot:"}),(0,n.jsx)("span",{className:"text-lg font-bold text-gray-900",children:N(p)}),24850===p&&(0,n.jsx)("span",{className:"text-xs text-gray-500",children:"(Mock)"})]})]})]})}),a&&(0,n.jsxs)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:[(0,n.jsx)("div",{className:"flex items-center space-x-2 overflow-x-auto pb-2",children:a.expiries.slice(0,15).map((e,t)=>{let a=new Date(e),o=e===i,s=a.toDateString()===new Date().toDateString(),r=6048e5>Math.abs(a.getTime()-new Date().getTime()),c=new Date().getFullYear(),d=a.getFullYear(),h=a.toLocaleDateString("en-US",{month:"short"}),m=a.getDate(),g=d!==c;return(0,n.jsx)("button",{onClick:()=>l(e),className:"flex-shrink-0 px-3 py-2 text-xs font-medium transition-colors border ".concat(o?"bg-black text-white border-black":s?"bg-orange-500 text-white border-orange-500":r?"bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"),children:(0,n.jsxs)("div",{className:"text-center min-w-[40px]",children:[(0,n.jsxs)("div",{className:"text-xs font-normal text-gray-600",children:[h,g&&" ".concat(d.toString().slice(-2))]}),(0,n.jsx)("div",{className:"font-bold text-sm",children:m})]})},e)})}),!1]}),d&&(0,n.jsxs)("div",{className:"bg-white",children:[(0,n.jsxs)("div",{className:"flex bg-gray-50 border-b border-gray-200 text-xs font-medium text-gray-700 uppercase tracking-wider",children:[(0,n.jsx)("div",{className:"flex-1 text-center py-3 border-r border-gray-200",children:(0,n.jsx)("span",{className:"text-green-600 font-bold",children:"Calls"})}),(0,n.jsx)("div",{className:"w-20 text-center py-3 border-r border-gray-200",children:(0,n.jsx)("span",{className:"font-bold",children:"Strike"})}),(0,n.jsx)("div",{className:"flex-1 text-center py-3",children:(0,n.jsx)("span",{className:"text-red-600 font-bold",children:"Puts"})})]}),(0,n.jsxs)("div",{className:"flex bg-gray-100 border-b border-gray-200 text-xs font-medium text-gray-600 uppercase tracking-wider",children:[(0,n.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"OI"}),(0,n.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Volume"}),(0,n.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Bid"}),(0,n.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Ask"}),(0,n.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Change"}),(0,n.jsx)("div",{className:"flex-1 text-center py-2 px-1 border-r border-gray-200",children:"LTP"}),(0,n.jsx)("div",{className:"w-20 text-center py-2 px-1 border-r border-gray-200 font-bold",children:"Strike"}),(0,n.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"LTP"}),(0,n.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Change"}),(0,n.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Ask"}),(0,n.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Bid"}),(0,n.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Volume"}),(0,n.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"OI"})]}),(0,n.jsx)("div",{className:"divide-y divide-gray-100",children:d.rows.map((e,t)=>{var a,o,s,r,c,i,l,d,h,m,g,u,x,y;let k=50>=Math.abs(e.strikePrice-p),f=e.strikePrice<p,S=e.strikePrice>p;return(0,n.jsxs)("div",{className:"flex hover:bg-gray-50 transition-colors ".concat(k?"bg-yellow-50":t%2==0?"bg-white":"bg-gray-25"),children:[(0,n.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm ".concat(f?"text-green-700 font-medium":"text-gray-700"),children:T(null==(a=e.call)?void 0:a.openInterest)}),(0,n.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm ".concat(f?"text-green-700 font-medium":"text-gray-700"),children:T(null==(o=e.call)?void 0:o.volume)}),(0,n.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm text-green-600",children:N(null==(s=e.call)?void 0:s.bid)}),(0,n.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm text-red-600",children:N(null==(r=e.call)?void 0:r.ask)}),(0,n.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm font-medium ".concat(E(null==(c=e.call)?void 0:c.change)),children:(null==(i=e.call)?void 0:i.change)?(0,n.jsxs)(n.Fragment,{children:[e.call.change>0?"+":"",e.call.change.toFixed(2),(0,n.jsxs)("div",{className:"text-xs",children:["(",e.call.changePercent>0?"+":"",e.call.changePercent.toFixed(1),"%)"]})]}):"-"}),(0,n.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 ".concat(f?"text-green-600":"text-gray-700"),children:N(null==(l=e.call)?void 0:l.ltp)}),(0,n.jsx)("div",{className:"w-20 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 ".concat(k?"bg-yellow-100 text-yellow-800":"text-gray-900"),children:e.strikePrice}),(0,n.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm font-bold ".concat(S?"text-red-600":"text-gray-700"),children:N(null==(d=e.put)?void 0:d.ltp)}),(0,n.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm font-medium ".concat(E(null==(h=e.put)?void 0:h.change)),children:(null==(m=e.put)?void 0:m.change)?(0,n.jsxs)(n.Fragment,{children:[e.put.change>0?"+":"",e.put.change.toFixed(2),(0,n.jsxs)("div",{className:"text-xs",children:["(",e.put.changePercent>0?"+":"",e.put.changePercent.toFixed(1),"%)"]})]}):"-"}),(0,n.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm text-red-600",children:N(null==(g=e.put)?void 0:g.ask)}),(0,n.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm text-green-600",children:N(null==(u=e.put)?void 0:u.bid)}),(0,n.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm ".concat(S?"text-red-700 font-medium":"text-gray-700"),children:T(null==(x=e.put)?void 0:x.volume)}),(0,n.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm ".concat(S?"text-red-700 font-medium":"text-gray-700"),children:T(null==(y=e.put)?void 0:y.openInterest)})]},e.strikePrice)})})]}),d&&(0,n.jsx)("div",{className:"bg-gray-50 border-t border-gray-200 px-6 py-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[(0,n.jsx)("span",{children:"Show all"}),(0,n.jsx)("span",{children:"•"}),(0,n.jsxs)("span",{children:["Showing ",d.rows.length," strikes around ATM"]})]}),(0,n.jsxs)("div",{className:"text-sm text-gray-500",children:["Last updated: ",new Date(d.timestamp).toLocaleTimeString()]})]})})]})}var i=a(5453),l=a(6786),d=a(4298),h=a(1950);class m{static getInstance(){return m.instance||(m.instance=new m,console.log("\uD83D\uDD27 WebSocketManager: New singleton instance created")),m.instance}async connect(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=this.generateClientId();if(console.log("\uD83D\uDD0C WebSocketManager: Client ".concat(t," requesting connection")),this.clientCallbacks.set(t,e),this.socket&&this.socket.connected)return console.log("✅ WebSocketManager: Reusing existing connection for client ".concat(t)),this.addClient(e,t),this.socket;if(this.isConnecting)return console.log("⏳ WebSocketManager: Connection in progress, waiting for client ".concat(t)),new Promise((a,n)=>{let o=()=>{this.socket&&this.socket.connected?(console.log("✅ WebSocketManager: Connection ready for waiting client ".concat(t)),this.addClient(e,t),a(this.socket)):this.isConnecting?setTimeout(o,100):(console.log("❌ WebSocketManager: Connection failed for waiting client ".concat(t)),n(Error("Connection failed")))};o()});console.log("\uD83D\uDE80 WebSocketManager: Creating new connection for client ".concat(t)),this.isConnecting=!0;try{return await this.createConnection(e),this.addClient(e,t),this.socket}catch(e){throw this.isConnecting=!1,this.clientCallbacks.delete(t),console.error("❌ WebSocketManager: Connection failed for client ".concat(t,":"),e),e}}generateClientId(){return"client_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}async createConnection(e){return new Promise((e,t)=>{let a=h.i3.BASE_URL;console.log("\uD83D\uDD0C WebSocketManager: Creating connection to ".concat(a)),this.socket&&(console.log("\uD83E\uDDF9 WebSocketManager: Cleaning up existing connection"),this.socket.removeAllListeners(),this.socket.disconnect()),this.socket=(0,d.io)(a,{transports:["websocket","polling"],upgrade:!0,rememberUpgrade:!1,timeout:h.ld.CONNECTION_TIMEOUT,forceNew:!1,reconnection:!0,reconnectionAttempts:h.ld.MAX_RECONNECT_ATTEMPTS,reconnectionDelay:h.ld.RECONNECT_INTERVAL,reconnectionDelayMax:4*h.ld.RECONNECT_INTERVAL,randomizationFactor:.5,autoConnect:!0}),this.socket.on("connect",()=>{var t;console.log("✅ WebSocketManager: Connected successfully"),this.isConnecting=!1,this.stats.connected=!0,this.stats.lastConnected=new Date,this.stats.reconnectAttempts=0,this.stats.connectionId=(null==(t=this.socket)?void 0:t.id)||null,this.startHeartbeat(),this.notifyAllClients("onConnect"),e()}),this.socket.on("disconnect",e=>{console.log("❌ WebSocketManager: Disconnected - ".concat(e)),this.stats.connected=!1,this.stats.connectionId=null,this.stopHeartbeat(),this.notifyAllClients("onDisconnect",e),"io client disconnect"!==e&&this.scheduleReconnection()}),this.socket.on("connect_error",e=>{console.error("\uD83D\uDD25 WebSocketManager: Connection error:",e.message),this.stats.errors++,this.isConnecting=!1,this.notifyAllClients("onError",e),t(e)}),this.socket.on("reconnect",e=>{var t;console.log("\uD83D\uDD04 WebSocketManager: Reconnected after ".concat(e," attempts")),this.stats.reconnectAttempts=e,this.stats.connected=!0,this.stats.lastConnected=new Date,this.stats.connectionId=(null==(t=this.socket)?void 0:t.id)||null,this.notifyAllClients("onReconnect",e)}),this.socket.on("reconnect_attempt",e=>{console.log("\uD83D\uDD04 WebSocketManager: Reconnection attempt ".concat(e)),this.stats.reconnectAttempts=e}),this.socket.on("reconnect_failed",()=>{console.error("\uD83D\uDCA5 WebSocketManager: Reconnection failed"),this.stats.connected=!1,this.isConnecting=!1,this.stats.connectionId=null}),this.socket.on("marketData",e=>{this.stats.totalMessages++,this.notifyListeners("marketData",e)}),this.socket.on("marketDataBatch",e=>{this.stats.totalMessages+=e.length,this.notifyListeners("marketDataBatch",e)}),this.startCleanupInterval(),setTimeout(()=>{this.isConnecting&&(this.isConnecting=!1,t(Error("Connection timeout")))},h.ld.CONNECTION_TIMEOUT)})}addClient(e,t){this.clientCount++,this.stats.clients=this.clientCount,e.onMarketData&&this.addListener("marketData",e.onMarketData),e.onMarketDataBatch&&this.addListener("marketDataBatch",e.onMarketDataBatch),console.log("\uD83D\uDCCA WebSocketManager: Client ".concat(t," added (Total: ").concat(this.clientCount,")"))}notifyAllClients(e,t){this.clientCallbacks.forEach((a,n)=>{try{var o,s,r,c;switch(e){case"onConnect":null==(o=a.onConnect)||o.call(a);break;case"onDisconnect":null==(s=a.onDisconnect)||s.call(a,t);break;case"onError":null==(r=a.onError)||r.call(a,t);break;case"onReconnect":null==(c=a.onReconnect)||c.call(a,t)}}catch(e){console.error("❌ WebSocketManager: Error notifying client ".concat(n,":"),e)}})}scheduleReconnection(){this.reconnectTimeout&&clearTimeout(this.reconnectTimeout);let e=Math.min(h.ld.RECONNECT_INTERVAL*Math.pow(2,this.stats.reconnectAttempts),3e4);console.log("\uD83D\uDD04 WebSocketManager: Scheduling reconnection in ".concat(e,"ms")),this.reconnectTimeout=setTimeout(()=>{!this.stats.connected&&this.clientCount>0&&(console.log("\uD83D\uDD04 WebSocketManager: Attempting auto-reconnection"),this.connect().catch(e=>{console.error("❌ WebSocketManager: Auto-reconnection failed:",e)}))},e)}removeClient(e){if(this.clientCount>0){this.clientCount--,this.stats.clients=this.clientCount;let t="unknown";for(let[a,n]of this.clientCallbacks.entries())if(n===e){this.clientCallbacks.delete(a),t=a;break}e.onMarketData&&this.removeListener("marketData",e.onMarketData),e.onMarketDataBatch&&this.removeListener("marketDataBatch",e.onMarketDataBatch),console.log("\uD83D\uDCCA WebSocketManager: Client ".concat(t," removed (Total: ").concat(this.clientCount,")")),0===this.clientCount&&(console.log("⏳ WebSocketManager: No clients remaining, scheduling disconnect"),setTimeout(()=>{0===this.clientCount&&(console.log("\uD83D\uDD0C WebSocketManager: Disconnecting due to no clients"),this.disconnect())},5e3))}}addListener(e,t){this.listeners.has(e)||this.listeners.set(e,new Set);let a=this.listeners.get(e);if(a.size>=h.ld.MAX_LISTENERS_PER_EVENT)return void console.warn("⚠️ Maximum listeners reached for event: ".concat(e));a.add(t)}removeListener(e,t){let a=this.listeners.get(e);a&&(a.delete(t),0===a.size&&this.listeners.delete(e))}notifyListeners(e,t){let a=this.listeners.get(e);a&&a.forEach(a=>{try{a(t)}catch(t){console.error("Error in ".concat(e," listener:"),t)}})}startHeartbeat(){this.stopHeartbeat(),this.heartbeatInterval=setInterval(()=>{this.socket&&this.socket.connected&&this.socket.emit("ping")},h.ld.HEARTBEAT_INTERVAL)}stopHeartbeat(){this.heartbeatInterval&&(clearInterval(this.heartbeatInterval),this.heartbeatInterval=null)}startCleanupInterval(){this.cleanupInterval=setInterval(()=>{this.cleanup()},h.ld.CLEANUP_INTERVAL)}cleanup(){let e=[];this.listeners.forEach((t,a)=>{0===t.size&&e.push(a)}),e.forEach(e=>{this.listeners.delete(e)}),console.log("\uD83D\uDCCA WebSocket Stats:",this.getStats())}disconnect(){console.log("\uD83D\uDD0C WebSocketManager: Disconnecting..."),this.stopHeartbeat(),this.cleanupInterval&&(clearInterval(this.cleanupInterval),this.cleanupInterval=null),this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.socket&&(this.socket.removeAllListeners(),this.socket.disconnect(),this.socket=null),this.listeners.clear(),this.clientCallbacks.clear(),this.clientCount=0,this.stats.connected=!1,this.stats.clients=0,this.stats.connectionId=null,this.isConnecting=!1,console.log("✅ WebSocketManager: Disconnected and cleaned up")}getStats(){return{...this.stats}}isConnected(){var e;return(null==(e=this.socket)?void 0:e.connected)||!1}emit(e,t){this.socket&&this.socket.connected?this.socket.emit(e,t):console.warn("Cannot emit ".concat(e,": WebSocket not connected"))}constructor(){this.socket=null,this.isConnecting=!1,this.clientCount=0,this.listeners=new Map,this.clientCallbacks=new Map,this.stats={connected:!1,clients:0,reconnectAttempts:0,lastConnected:null,totalMessages:0,errors:0,connectionId:null},this.heartbeatInterval=null,this.cleanupInterval=null,this.reconnectTimeout=null,console.log("\uD83D\uDD27 WebSocketManager: Constructor called")}}m.instance=null,m.getInstance();let g=(0,i.v)()((0,l.Zr)((e,t)=>({marketData:{},isConnected:!1,isLoading:!0,lastUpdate:null,connectionStatus:"disconnected",error:null,cacheLoaded:!1,wsManager:null,setMarketData:a=>{e({marketData:a.reduce((e,t)=>(e[t.securityId]=t,e),{}),lastUpdate:new Date,isLoading:!1}),t().saveToLocalStorage()},updateMarketData:t=>e(e=>({marketData:{...e.marketData,[t.securityId]:{...e.marketData[t.securityId],...t}}})),updateMarketDataBatch:t=>e(e=>{let a={...e.marketData};for(let e of t)a[e.securityId]={...a[e.securityId],...e};return{marketData:a}}),hydrateFromRedis:t=>e({marketData:t.reduce((e,t)=>(e[t.securityId]=t,e),{})}),setConnectionStatus:a=>{e({connectionStatus:a,isConnected:"connected"===a,error:"error"===a?t().error:null})},setError:t=>e({error:t}),setLoading:t=>e({isLoading:t}),setCacheLoaded:t=>e({cacheLoaded:t}),initializeWebSocket:()=>{let a=m.getInstance();e({wsManager:a}),a.connect({onConnect:()=>{console.log("\uD83D\uDD0C MarketStore: WebSocket connected"),t().setConnectionStatus("connected")},onDisconnect:e=>{console.log("\uD83D\uDD0C MarketStore: WebSocket disconnected:",e),t().setConnectionStatus("disconnected")},onError:e=>{console.error("❌ MarketStore: WebSocket error:",e),t().setConnectionStatus("error"),t().setError(e.message||"WebSocket connection error")},onReconnect:e=>{console.log("\uD83D\uDD04 MarketStore: WebSocket reconnected after",e,"attempts"),t().setConnectionStatus("connected"),t().setError(null)},onMarketData:e=>{e&&"object"==typeof e&&t().updateMarketData(e)},onMarketDataBatch:e=>{Array.isArray(e)&&e.length>0&&t().updateMarketDataBatch(e)}})},connect:()=>{let{wsManager:e}=t();e&&!e.isConnected()&&(t().setConnectionStatus("connecting"),console.log("\uD83D\uDD0C MarketStore: Connection request - WebSocket manager will handle"))},disconnect:()=>{let{wsManager:e}=t();e&&(e.disconnect(),t().setConnectionStatus("disconnected"))},loadFromCache:async()=>{try{e({isLoading:!0}),console.log("\uD83D\uDCD6 MarketStore: Loading cached market data...");let a=t().loadFromLocalStorage(),n=await fetch("/api/cache/bulk");if(n.ok){let e=await n.json();e.success&&e.data&&Array.isArray(e.data)&&e.data.length>0?(t().setMarketData(e.data),console.log("✅ MarketStore: Loaded ".concat(e.data.length," items from Redis cache"))):a||console.log("\uD83D\uDCED MarketStore: No cached data found")}else a||console.log("\uD83D\uDCED MarketStore: Failed to load cached data");e({cacheLoaded:!0,isLoading:!1})}catch(t){console.error("❌ MarketStore: Failed to load cached data:",t),e({cacheLoaded:!0,isLoading:!1})}},refreshFromCache:async()=>{try{let e=await fetch("/api/cache/bulk");if(e.ok){let a=await e.json();a.success&&a.data&&Array.isArray(a.data)&&a.data.length>0&&(t().setMarketData(a.data),console.log("✅ MarketStore: Refreshed ".concat(a.data.length," items from cache")))}}catch(e){console.error("❌ MarketStore: Failed to refresh from cache:",e)}},clearCache:async()=>{try{(await fetch("/api/cache/clear",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({pattern:"market_data:*"})})).ok&&(e({marketData:{},lastUpdate:null}),localStorage.removeItem("marketData"),localStorage.removeItem("marketDataTimestamp"),console.log("\uD83E\uDDF9 MarketStore: Cache cleared"))}catch(e){console.error("❌ MarketStore: Failed to clear cache:",e)}},saveToLocalStorage:()=>{let{marketData:e}=t();if(Object.keys(e).length>0)try{localStorage.setItem("marketData",JSON.stringify(Object.values(e))),localStorage.setItem("marketDataTimestamp",new Date().toISOString())}catch(e){console.warn("⚠️ MarketStore: Failed to save to localStorage:",e)}},loadFromLocalStorage:()=>{try{let t=localStorage.getItem("marketData"),a=localStorage.getItem("marketDataTimestamp");if(t&&a){let n=JSON.parse(t),o=new Date(a),s=(new Date().getTime()-o.getTime())/6e4;if(s<10&&Array.isArray(n)&&n.length>0)return e({marketData:n.reduce((e,t)=>(e[t.securityId]=t,e),{}),lastUpdate:o,isLoading:!1}),console.log("⚡ MarketStore: Loaded ".concat(n.length," items from localStorage (").concat(s.toFixed(1),"min old)")),!0}}catch(e){console.warn("⚠️ MarketStore: Failed to load from localStorage:",e),localStorage.removeItem("marketData"),localStorage.removeItem("marketDataTimestamp")}return!1},reset:()=>{e({marketData:{},isConnected:!1,isLoading:!0,lastUpdate:null,connectionStatus:"disconnected",error:null,cacheLoaded:!1}),localStorage.removeItem("marketData"),localStorage.removeItem("marketDataTimestamp")},getMarketDataBySymbol:e=>Object.values(t().marketData).find(t=>t.symbol===e),getMarketDataBySecurityId:e=>t().marketData[e]}),{name:"market-store",storage:(0,l.KU)(()=>localStorage),partialize:e=>({marketData:e.marketData})})),u=()=>{let e=g();return(0,o.useEffect)(()=>{fetch("/api/cache/all-latest").then(e=>e.json()).then(t=>{let{data:a}=t;Array.isArray(a)&&e.hydrateFromRedis(a)})},[e]),{marketData:Object.values(e.marketData),updateMarketData:e.updateMarketData,updateMarketDataBatch:e.updateMarketDataBatch,hydrateFromRedis:e.hydrateFromRedis}};function x(){let{marketData:e}=u(),t=new Map;return e.forEach(e=>{e.securityId&&t.set(e.securityId,e)}),(0,n.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,n.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-3",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("a",{href:"/",className:"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors",children:"← Main Dashboard"}),(0,n.jsx)("a",{href:"/subscribed",className:"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors",children:"\uD83D\uDCCA Subscribed Data"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsxs)("span",{className:"text-sm text-gray-600",children:[e.length," instruments"]}),(0,n.jsxs)("span",{className:"text-sm text-gray-600",children:[e.filter(e=>{var t;return(null!=(t=e.ltp)?t:0)>0}).length," active"]})]})]})}),(0,n.jsx)(c,{marketData:t})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[249,441,684,358],()=>t(3095)),_N_E=e.O()}]);