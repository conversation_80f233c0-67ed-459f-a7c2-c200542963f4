(()=>{var e={};e.id=862,e.ids=[862],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3549:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>h,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>g});var a={};r.r(a),r.d(a,{GET:()=>d,POST:()=>u});var n=r(6559),s=r(8088),o=r(7719),c=r(2190),i=r(7155),l=r(8842);async function u(e){try{let{key:t,data:r}=await e.json();if(!t||"string"!=typeof t)return c.NextResponse.json({success:!1,error:"Key is required and must be a string"},{status:400});if(!r)return c.NextResponse.json({success:!1,error:"Data is required"},{status:400});let a=await (0,i.bt)(t,r);return a||(a=(0,l.D)().set(t,r)),c.NextResponse.json({success:!0,message:`Cached data for key: ${t}`,cached:a})}catch(e){return console.error("❌ API: Failed to cache item:",e),c.NextResponse.json({success:!1,error:"Failed to cache item"},{status:500})}}async function d(e){try{let{searchParams:t}=new URL(e.url),r=t.get("securityId");if(!r)return c.NextResponse.json({success:!1,error:"securityId is required as a query parameter"},{status:400});let a=await (0,i.T7)(r);if(a||(a=(0,l.D)().get(`market_data:${r}`)),!a)return c.NextResponse.json({success:!1,error:"No data found for securityId"},{status:404});return c.NextResponse.json({success:!0,data:a})}catch(e){return console.error("❌ API: Failed to get item:",e),c.NextResponse.json({success:!1,error:"Failed to get item"},{status:500})}}let h=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/cache/item/route",pathname:"/api/cache/item",filename:"route",bundlePath:"app/api/cache/item/route"},resolvedPagePath:"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\api\\cache\\item\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:g,serverHooks:m}=h;function f(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:g})}},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},7155:(e,t,r)=>{"use strict";r.d(t,{No:()=>u,bt:()=>i,IL:()=>p,vX:()=>g,vI:()=>d,T7:()=>h});let a=require("redis"),n=null,s=null;async function o(){if(n&&n.isOpen)return n;if(s)return s;s=c();try{return n=await s,s=null,n}catch(e){throw s=null,e}}async function c(){try{if("phase-production-build"===process.env.NEXT_PHASE)return console.log("\uD83D\uDD17 Redis: Skipping Redis connection during build phase"),null;if(!process.env.RUNTIME)return console.log("\uD83D\uDD17 Redis: Skipping Redis connection during static generation"),null;let e=process.env.REDIS_URL?process.env.REDIS_URL:(console.error("❌ Redis: REDIS_URL environment variable is required in production"),null);if(!e)return console.log("\uD83D\uDD17 Redis: No Redis URL provided, skipping Redis connection"),null;return console.log("\uD83D\uDD17 Redis: Connecting to Redis..."),console.log("\uD83D\uDD17 Redis: URL:",e.replace(/\/\/.*@/,"//***@")),(n=(0,a.createClient)({url:e,socket:{connectTimeout:5e3,reconnectStrategy:e=>e>3?(console.error("❌ Redis: Max reconnection attempts reached"),!1):Math.min(100*e,3e3)}})).on("error",e=>{console.error("❌ Redis Client Error:",e)}),n.on("connect",()=>{console.log("\uD83D\uDD17 Redis Client Connected")}),n.on("ready",()=>{console.log("✅ Redis Client Ready")}),n.on("end",()=>{console.log("\uD83D\uDD0C Redis Client Disconnected")}),await n.connect(),console.log("\uD83D\uDE80 Redis Cache Manager initialized"),n}catch(e){return console.error("❌ Failed to connect to Redis:",e),n=null,null}}async function i(e,t,r=600){try{let a=await o();if(!a)return!1;let n=JSON.stringify({data:t,timestamp:Date.now(),version:"1.0.0"});return await a.setEx(e,r,n),console.log(`💾 Redis: Cached ${e} (${n.length} bytes, TTL: ${r}s)`),!0}catch(t){return console.error(`❌ Redis: Failed to cache ${e}:`,t),!1}}async function l(e){try{let t=await o();if(!t)return null;let r=await t.get(e);if(!r)return null;let a=JSON.parse(r);return console.log(`📖 Redis: Retrieved ${e} from cache`),a.data}catch(t){return console.error(`❌ Redis: Failed to retrieve ${e}:`,t),null}}async function u(e,t=600){try{let r=await o();if(!r)return!1;let a=await i("market_data:bulk",e,t),n=r.multi(),s=Date.now();return e.forEach(e=>{let r=`market_data:${e.securityId}`,a=JSON.stringify({data:e,timestamp:s,version:"1.0.0"});n.setEx(r,t,a)}),await n.exec(),console.log(`💾 Redis: Cached ${e.length} individual market data entries`),a}catch(e){return console.error("❌ Redis: Failed to cache bulk market data:",e),!1}}async function d(){return await l("market_data:bulk")||[]}async function h(e){return l(`market_data:${e}`)}async function p(e="market_data:*"){try{let t=await o();if(!t)return 0;let r=await t.keys(e);if(0===r.length)return 0;let a=await t.del(r);return console.log(`🧹 Redis: Cleared ${a} entries matching ${e}`),a}catch(e){return console.error(`❌ Redis: Failed to clear cache:`,e),0}}async function g(){let e=await o();if(!e)return[];let t=await e.keys("market_data:*");return t.length?(await e.mGet(t)).map(e=>e&&JSON.parse(e)?.data).filter(Boolean):[]}},8335:()=>{},8842:(e,t,r)=>{"use strict";r.d(t,{D:()=>s});class a{constructor(){this.cache=new Map,this.cleanupInterval=null,this.startCleanup()}startCleanup(){this.cleanupInterval=setInterval(()=>{this.cleanup()},3e4)}cleanup(){let e=Date.now(),t=0;Array.from(this.cache.entries()).forEach(([r,a])=>{e>a.timestamp+1e3*a.ttl&&(this.cache.delete(r),t++)}),t>0&&console.log(`🧹 FallbackCache: Cleaned ${t} expired entries`)}set(e,t,r=600){try{return this.cache.set(e,{data:t,timestamp:Date.now(),ttl:r}),console.log(`💾 FallbackCache: Cached ${e} (TTL: ${r}s)`),!0}catch(t){return console.error(`❌ FallbackCache: Failed to cache ${e}:`,t),!1}}get(e){try{let t=this.cache.get(e);if(!t)return null;if(Date.now()>t.timestamp+1e3*t.ttl)return this.cache.delete(e),null;return console.log(`📖 FallbackCache: Retrieved ${e} from cache`),t.data}catch(t){return console.error(`❌ FallbackCache: Failed to retrieve ${e}:`,t),null}}delete(e){let t=this.cache.delete(e);return t&&console.log(`🗑️ FallbackCache: Removed ${e}`),t}clear(e){if(!e){let e=this.cache.size;return this.cache.clear(),console.log(`🧹 FallbackCache: Cleared all ${e} entries`),e}let t=0,r=new RegExp(e.replace("*",".*"));return Array.from(this.cache.keys()).forEach(e=>{r.test(e)&&(this.cache.delete(e),t++)}),console.log(`🧹 FallbackCache: Cleared ${t} entries matching ${e}`),t}getStats(){return{totalKeys:this.cache.size,memoryUsage:`${Math.round(JSON.stringify(Array.from(this.cache.entries())).length/1024)}KB`,connectedClients:1,uptime:process.uptime()}}destroy(){this.cleanupInterval&&clearInterval(this.cleanupInterval),this.cache.clear(),console.log("\uD83D\uDC4B FallbackCache: Destroyed")}}let n=null;function s(){return n||(n=new a,console.log("\uD83D\uDE80 FallbackCache: Initialized in-memory cache for development")),n}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,580],()=>r(3549));module.exports=a})();