/**
 * Dhan API Service for REST API calls
 * Handles authentication and API requests to Dhan
 */
export interface DhanExpiryResponse {
    data: string[];
    status: string;
}
export interface DhanOptionChainRequest {
    UnderlyingScrip: number;
    UnderlyingSeg: string;
}
export declare class DhanAPIService {
    private accessToken;
    private clientId;
    private baseUrl;
    private expiryCache;
    private cacheTimeout;
    constructor(accessToken?: string, clientId?: string);
    /**
     * Get expiry dates for an underlying instrument
     */
    getExpiryDates(underlyingScrip: number, underlyingSeg: string): Promise<string[]>;
    /**
     * Mock expiry dates for development/fallback
     */
    private getMockExpiryDates;
    /**
     * Check if API credentials are available
     */
    hasCredentials(): boolean;
    /**
     * Get API status
     */
    getStatus(): {
        hasCredentials: boolean;
        baseUrl: string;
    };
}
export declare function getDhanAPIService(): DhanAPIService;
//# sourceMappingURL=DhanAPIService.d.ts.map