const path = require('path');
const fs = require('fs');

console.log('Current working directory:', process.cwd());
console.log('.env file path:', path.join(process.cwd(), '.env'));
console.log('.env file exists:', fs.existsSync('.env'));

if (fs.existsSync('.env')) {
  const envContent = fs.readFileSync('.env', 'utf8');
  console.log('File size:', envContent.length);
  console.log('Lines containing DATABASE_URL:');
  envContent.split('\n').forEach((line, index) => {
    if (line.includes('DATABASE_URL')) {
      console.log(`Line ${index + 1}: "${line}"`);
      console.log('Line length:', line.length);
      console.log('Char codes:', line.split('').map(c => c.charCodeAt(0)).join(','));
    }
  });
}

require('dotenv').config();
console.log('\nAfter dotenv.config():');
console.log('DATABASE_URL found:', !!process.env.DATABASE_URL);
console.log('All env vars starting with DATABASE:', Object.keys(process.env).filter(k => k.includes('DATABASE')));
