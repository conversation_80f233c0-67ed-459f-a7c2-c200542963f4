(()=>{var e={};e.id=547,e.ids=[547],e.modules={231:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(5239),a=s(8088),n=s(8170),o=s.n(n),i=s(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let c={children:["",{children:["subscribed",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,7267)),"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\subscribed\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\subscribed\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/subscribed/page",pathname:"/subscribed",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1055:(e,t,s)=>{Promise.resolve().then(s.bind(s,5041))},1398:(e,t,s)=>{Promise.resolve().then(s.bind(s,6853))},1719:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},1735:(e,t,s)=>{Promise.resolve().then(s.bind(s,4431))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4263:()=>{},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\layout.tsx","default")},4780:(e,t,s)=>{"use strict";s.d(t,{ap:()=>r});let r={price:e=>!e||e<=0?"-":`₹${e.toFixed(2)}`,number:e=>null==e?"-":e.toLocaleString(),percentage:e=>{if(!e||0===e)return"-";let t=e>=0?"+":"";return`${t}${e.toFixed(2)}%`},change:(e,t)=>{if(!e||0===e)return"-";let s=e>0?"+":"",r=t?` (${s}${t.toFixed(2)}%)`:"";return`${s}${e.toFixed(2)}${r}`},volume:e=>null==e?"-":function(e){return e>=1e7?`${(e/1e7).toFixed(2)} Cr`:e>=1e5?`${(e/1e5).toFixed(2)} L`:e>=1e3?`${(e/1e3).toFixed(2)} K`:e.toString()}(e),time:e=>e?new Date(e).toLocaleTimeString():"-",date:e=>e?new Date(e).toLocaleDateString():"-",bidAsk:(e,t)=>{if(!e||e<=0)return"-";let s=t?` (${t})`:"";return`₹${e.toFixed(2)}${s}`}}},5041:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(687),a=s(4369),n=s.n(a),o=s(7590),i=s(2918);function l({error:e,resetErrorBoundary:t}){return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg border border-red-200",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3",children:(0,r.jsx)("span",{className:"text-red-600 text-lg",children:"⚠"})}),(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Application Error"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"An unexpected error occurred. Please try refreshing the page or contact support if the problem persists."}),(0,r.jsxs)("details",{className:"mb-4",children:[(0,r.jsx)("summary",{className:"cursor-pointer text-sm text-gray-500 hover:text-gray-700",children:"Technical Details"}),(0,r.jsx)("pre",{className:"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto max-h-32",children:e.message})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)("button",{onClick:t,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"Try Again"}),(0,r.jsx)("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors",children:"Refresh Page"})]})]})})}function c({children:e}){return(0,r.jsxs)("html",{lang:"en",children:[(0,r.jsxs)("head",{children:[(0,r.jsx)("title",{children:"CSV Market Dashboard"}),(0,r.jsx)("meta",{name:"description",content:"Real-time market data dashboard with enhanced WebSocket management and data caching"}),(0,r.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,r.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,r.jsx)("body",{className:n().className,children:(0,r.jsx)(i.tH,{FallbackComponent:l,onError:(e,t)=>{console.error("Application Error:",e,t)},onReset:()=>{localStorage.removeItem("enhanced-market-data"),localStorage.removeItem("enhanced-market-timestamp"),sessionStorage.clear()},children:(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20",children:[e,(0,r.jsx)(o.l$,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{style:{background:"#10B981"}},error:{style:{background:"#EF4444"}}}})]})})})]})}s(4263)},5631:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},6853:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l,dynamic:()=>i});var r=s(687),a=s(3210),n=s(4780),o=s(9255);let i="force-dynamic";function l(){let{marketData:e,isConnected:t,connectionError:s,stats:i}=(0,o.Q)();i.totalInstruments;let[l,c]=(0,a.useState)(""),[d,u]=(0,a.useState)("symbol"),[m,h]=(0,a.useState)("asc"),[x,p]=(0,a.useState)("all"),b=e.filter(e=>{let t=e.symbol.toLowerCase().includes(l.toLowerCase()),s="all"===x||"options"===x&&(e.symbol.includes("-CE")||e.symbol.includes("-PE"))||"futures"===x&&!e.symbol.includes("-CE")&&!e.symbol.includes("-PE");return t&&s}).sort((e,t)=>{let s,r;switch(d){case"symbol":default:s=e.symbol,r=t.symbol;break;case"ltp":s=e.ltp||0,r=t.ltp||0;break;case"change":s=e.change||0,r=t.change||0;break;case"volume":s=e.volume||0,r=t.volume||0;break;case"openInterest":s=e.openInterest||0,r=t.openInterest||0}return"string"==typeof s?"asc"===m?s.localeCompare(r):r.localeCompare(s):"asc"===m?s-r:r-s}),g=e=>{d===e?h("asc"===m?"desc":"asc"):(u(e),h("asc"))},v=n.ap.number,f=n.ap.price,y=e=>e?e>0?"text-green-600":e<0?"text-red-600":"text-gray-500":"text-gray-500";return(0,r.jsxs)("div",{className:"container mx-auto p-4 max-w-7xl",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"NSE Derivatives - Subscribed Data Dashboard"}),(0,r.jsxs)("div",{className:"space-x-3",children:[(0,r.jsx)("a",{href:"/",className:"inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 transition-colors",children:"\uD83C\uDFE0 Main Dashboard"}),(0,r.jsx)("a",{href:"/option-chain",className:"inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors",children:"\uD83D\uDD17 Option Chain"})]})]}),(0,r.jsx)("div",{className:"mb-6 p-4 bg-gray-100 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:`inline-block px-3 py-1 rounded ${t?"bg-green-500":"bg-red-500"} text-white`,children:t?"\uD83D\uDFE2 Connected":"\uD83D\uDD34 Disconnected"}),s&&(0,r.jsxs)("div",{className:"inline-block px-3 py-1 rounded bg-red-100 text-red-800 text-sm",children:["Error: ",s]}),(0,r.jsxs)("span",{className:"text-gray-700",children:["\uD83D\uDCCA Total Subscribed: ",e.length," instruments"]}),(0,r.jsxs)("span",{className:"text-gray-700",children:["\uD83D\uDCC8 Live Data: ",e.filter(e=>e.ltp>0).length," active"]}),(0,r.jsxs)("span",{className:"text-gray-700",children:["\uD83D\uDD22 With OI: ",e.filter(e=>e.openInterest&&e.openInterest>0).length," contracts"]})]}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Server Auto-Subscription: NSE OPTIDX + FUTIDX Only"})]})}),(0,r.jsx)("div",{className:"mb-6 p-4 bg-white rounded-lg shadow",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Instruments"}),(0,r.jsx)("input",{type:"text",placeholder:"Search by symbol...",value:l,onChange:e=>c(e.target.value),className:"w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Filter by Type"}),(0,r.jsxs)("select",{value:x,onChange:e=>p(e.target.value),className:"w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"All Instruments"}),(0,r.jsx)("option",{value:"options",children:"Options (CE/PE)"}),(0,r.jsx)("option",{value:"futures",children:"Futures"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Showing Results"}),(0,r.jsxs)("div",{className:"p-2 bg-gray-50 rounded-md text-sm",children:[b.length," of ",e.length," instruments"]})]})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsxs)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>g("symbol"),children:["Symbol ","symbol"===d&&("asc"===m?"↑":"↓")]}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Exchange"}),(0,r.jsxs)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>g("ltp"),children:["LTP ","ltp"===d&&("asc"===m?"↑":"↓")]}),(0,r.jsxs)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>g("change"),children:["Change ","change"===d&&("asc"===m?"↑":"↓")]}),(0,r.jsxs)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>g("volume"),children:["Volume ","volume"===d&&("asc"===m?"↑":"↓")]}),(0,r.jsxs)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>g("openInterest"),children:["Open Interest ","openInterest"===d&&("asc"===m?"↑":"↓")]}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"High/Low"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Open/Close"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:b.slice(0,100).map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.symbol}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.exchange}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right font-medium",children:f(e.ltp)}),(0,r.jsx)("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-right font-medium ${y(e.change)}`,children:e.change?(0,r.jsxs)(r.Fragment,{children:[e.change>0?"+":"",e.change.toFixed(2),(0,r.jsx)("br",{}),(0,r.jsxs)("span",{className:"text-xs",children:["(",e.changePercent>0?"+":"",e.changePercent.toFixed(2),"%)"]})]}):"-"}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900",children:v(e.volume)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-blue-600",children:v(e.openInterest)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500",children:e.high?(0,r.jsxs)(r.Fragment,{children:[f(e.high),(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"text-xs",children:f(e.low)})]}):"-"}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500",children:e.open?(0,r.jsxs)(r.Fragment,{children:[f(e.open),(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"text-xs",children:f(e.close)})]}):"-"})]},e.securityId))})]})}),0===b.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("p",{className:"text-gray-500",children:"No subscribed data available"}),(0,r.jsx)("p",{className:"text-sm text-gray-400 mt-2",children:"The server will automatically subscribe to NSE OPTIDX and FUTIDX instruments"})]}),b.length>100&&(0,r.jsxs)("div",{className:"bg-gray-50 px-6 py-3 text-sm text-gray-600",children:["Showing first 100 of ",b.length," results. Use search to narrow down."]})]})]})}},7265:(e,t,s)=>{Promise.resolve().then(s.bind(s,7267))},7267:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n,dynamic:()=>a});var r=s(2907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\subscribed\\page.tsx","dynamic"),n=(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\subscribed\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\subscribed\\page.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9255:(e,t,s)=>{"use strict";s.d(t,{Q:()=>a});var r=s(3210);let a=(e={})=>{let{autoConnect:t=!0,autoLoadCache:s=!0,autoSaveInterval:a=3e4,reconnectOnError:n=!0,maxReconnectAttempts:o=5}=e,i={status:"disconnected",isConnected:!1,error:null,connectionStats:{totalMessages:0,connectionUptime:0}},l=new Map,c={isLoaded:!1,totalCacheSize:0,pendingUpdates:0,lastCacheUpdate:null},d=(0,r.useMemo)(()=>({filters:{},sortConfig:{field:"symbol",direction:"asc"},selectedInstruments:new Set,viewMode:"table",autoRefresh:!0}),[]),u=(0,r.useCallback)(async()=>{},[]),m=(0,r.useCallback)(async()=>{},[]),h=(0,r.useCallback)(()=>{},[]),x=(0,r.useCallback)(async()=>{},[]),p=(0,r.useCallback)(async()=>{},[]),b=(0,r.useCallback)(async e=>{},[]),g=(0,r.useCallback)(async()=>{},[]),v=(0,r.useCallback)(e=>{},[]),f=(0,r.useCallback)((e,t)=>{},[]),y=(0,r.useCallback)(()=>[],[]),j=(0,r.useCallback)(()=>[],[]),k=(0,r.useCallback)(e=>void 0,[]),w=(0,r.useCallback)(e=>void 0,[]),C=(0,r.useCallback)(e=>{},[]),N=(0,r.useCallback)(e=>{},[]),P=(0,r.useCallback)(()=>{},[]),D=(0,r.useRef)(null),E=(0,r.useRef)(0),S=(0,r.useRef)(!1),I=Array.from(l.values());(0,r.useEffect)(()=>((async()=>{if(!S.current){S.current=!0,console.log("\uD83D\uDE80 Enhanced Hook: Initializing...");try{s&&!c.isLoaded&&await p(),t&&"disconnected"===i.status&&await u()}catch(e){console.error("❌ Enhanced Hook: Initialization failed:",e)}}})(),()=>{D.current&&clearInterval(D.current)}),[t,s,c.isLoaded,i.status,u,p]),(0,r.useEffect)(()=>{if(a>0&&I.length>0)return D.current&&clearInterval(D.current),D.current=setInterval(()=>{b()},a),()=>{D.current&&clearInterval(D.current)}},[a,I.length,b]),(0,r.useEffect)(()=>{if(n&&"error"===i.status&&E.current<o){let e=Math.min(1e3*Math.pow(2,E.current),3e4);console.log(`🔄 Enhanced Hook: Auto-reconnecting in ${e}ms (attempt ${E.current+1}/${o})`);let t=setTimeout(async()=>{try{E.current++,await x()}catch(e){console.error("❌ Enhanced Hook: Auto-reconnect failed:",e)}},e);return()=>clearTimeout(t)}},[i.status,x,n,o]),(0,r.useEffect)(()=>{"connected"===i.status&&(E.current=0)},[i.status]);let F=(0,r.useCallback)(()=>y(),[y]),A=(0,r.useCallback)(()=>j(),[j]),$=(0,r.useCallback)(e=>k(e),[k]),R=(0,r.useCallback)(e=>w(e),[w]),_=(0,r.useCallback)(e=>{v(e)},[v,d]),L=(0,r.useCallback)((e,t)=>{f(e,t)},[f]),M=(0,r.useCallback)(e=>{C(e)},[C]),T=(0,r.useCallback)(e=>{N(e)},[N]),U=(0,r.useCallback)(async()=>{try{E.current=0,await x()}catch(e){throw console.error("❌ Enhanced Hook: Manual reconnect failed:",e),e}},[x]),O=(0,r.useCallback)(async()=>{try{await p()}catch(e){throw console.error("❌ Enhanced Hook: Force refresh failed:",e),e}},[p]),H=(0,r.useCallback)(async()=>{try{await b(!0)}catch(e){throw console.error("❌ Enhanced Hook: Force save failed:",e),e}},[b]),q={totalInstruments:I.length,connectedInstruments:I.filter(e=>e.ltp&&e.ltp>0).length,lastUpdate:null,cacheSize:c.totalCacheSize,connectionUptime:i.connectionStats.connectionUptime,messagesReceived:i.connectionStats.totalMessages,reconnectAttempts:E.current,isAutoSaving:null!==D.current};return{marketData:I,marketDataMap:l,filteredData:F(),sortedData:A(),isConnected:i.isConnected,connectionStatus:i.status,connectionError:i.error,connectionStats:i.connectionStats,cacheLoaded:c.isLoaded,cacheUpdating:c.pendingUpdates>0,lastCacheUpdate:c.lastCacheUpdate,filters:d.filters,sortConfig:d.sortConfig,selectedInstruments:d.selectedInstruments,viewMode:d.viewMode,autoRefresh:d.autoRefresh,isLoading:!1,isInitializing:!S.current,getDataBySecurityId:$,getDataBySymbol:R,getFilteredData:F,getSortedData:A,updateFilters:_,updateSort:L,subscribe:M,unsubscribe:T,connect:m,disconnect:h,reconnect:U,refresh:O,save:H,clearCache:g,reset:P,stats:q,_store:{setFilters:v,setSortConfig:f,subscribeToInstrument:C,unsubscribeFromInstrument:N}}}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,151],()=>s(231));module.exports=r})();