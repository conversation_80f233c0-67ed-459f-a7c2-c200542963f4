(()=>{var e={};e.id=200,e.ids=[200],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3185:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>c.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>o}),s(2311),s(2029),s(5866);var a=s(3191),r=s(8716),n=s(7922),c=s.n(n),l=s(5231),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);s.d(t,i);let o=["",{children:["subscribed",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,2311)),"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\subscribed\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,2029)),"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\subscribed\\page.tsx"],u="/subscribed/page",x={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/subscribed/page",pathname:"/subscribed",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},5961:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2994,23)),Promise.resolve().then(s.t.bind(s,6114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,9671,23)),Promise.resolve().then(s.t.bind(s,1868,23)),Promise.resolve().then(s.t.bind(s,4759,23))},7134:(e,t,s)=>{Promise.resolve().then(s.bind(s,7480))},9554:(e,t,s)=>{Promise.resolve().then(s.bind(s,4833))},7480:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var a=s(326),r=s(6465),n=s.n(r),c=s(381),l=s(938);function i({error:e,resetErrorBoundary:t}){return a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg border border-red-200",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx("div",{className:"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3",children:a.jsx("span",{className:"text-red-600 text-lg",children:"⚠"})}),a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Application Error"})]}),a.jsx("p",{className:"text-gray-600 mb-4",children:"An unexpected error occurred. Please try refreshing the page or contact support if the problem persists."}),(0,a.jsxs)("details",{className:"mb-4",children:[a.jsx("summary",{className:"cursor-pointer text-sm text-gray-500 hover:text-gray-700",children:"Technical Details"}),a.jsx("pre",{className:"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto max-h-32",children:e.message})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx("button",{onClick:t,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"Try Again"}),a.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors",children:"Refresh Page"})]})]})})}function o({children:e}){return(0,a.jsxs)("html",{lang:"en",children:[(0,a.jsxs)("head",{children:[a.jsx("title",{children:"CSV Market Dashboard"}),a.jsx("meta",{name:"description",content:"Real-time market data dashboard with enhanced WebSocket management and data caching"}),a.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),a.jsx("link",{rel:"icon",href:"/favicon.ico"})]}),a.jsx("body",{className:n().className,children:a.jsx(l.SV,{FallbackComponent:i,onError:(e,t)=>{console.error("Application Error:",e,t)},onReset:()=>{localStorage.removeItem("enhanced-market-data"),localStorage.removeItem("enhanced-market-timestamp"),sessionStorage.clear()},children:(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20",children:[e,a.jsx(c.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{style:{background:"#10B981"}},error:{style:{background:"#EF4444"}}}})]})})})]})}s(3824)},4833:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,dynamic:()=>l});var a=s(326),r=s(7577),n=s(1223),c=s(8051);let l="force-dynamic";function i(){let{marketData:e,isConnected:t,connectionError:s,stats:l}=(0,c.A)();l.totalInstruments;let[i,o]=(0,r.useState)(""),[d,u]=(0,r.useState)("symbol"),[x,m]=(0,r.useState)("asc"),[h,p]=(0,r.useState)("all"),g=e.filter(e=>{let t=e.symbol.toLowerCase().includes(i.toLowerCase()),s="all"===h||"options"===h&&(e.symbol.includes("-CE")||e.symbol.includes("-PE"))||"futures"===h&&!e.symbol.includes("-CE")&&!e.symbol.includes("-PE");return t&&s}).sort((e,t)=>{let s,a;switch(d){case"symbol":default:s=e.symbol,a=t.symbol;break;case"ltp":s=e.ltp||0,a=t.ltp||0;break;case"change":s=e.change||0,a=t.change||0;break;case"volume":s=e.volume||0,a=t.volume||0;break;case"openInterest":s=e.openInterest||0,a=t.openInterest||0}return"string"==typeof s?"asc"===x?s.localeCompare(a):a.localeCompare(s):"asc"===x?s-a:a-s}),b=e=>{d===e?m("asc"===x?"desc":"asc"):(u(e),m("asc"))},y=n.A1.number,f=n.A1.price,v=e=>e?e>0?"text-green-600":e<0?"text-red-600":"text-gray-500":"text-gray-500";return(0,a.jsxs)("div",{className:"container mx-auto p-4 max-w-7xl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[a.jsx("h1",{className:"text-3xl font-bold",children:"NSE Derivatives - Subscribed Data Dashboard"}),(0,a.jsxs)("div",{className:"space-x-3",children:[a.jsx("a",{href:"/",className:"inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 transition-colors",children:"\uD83C\uDFE0 Main Dashboard"}),a.jsx("a",{href:"/option-chain",className:"inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors",children:"\uD83D\uDD17 Option Chain"})]})]}),a.jsx("div",{className:"mb-6 p-4 bg-gray-100 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("div",{className:`inline-block px-3 py-1 rounded ${t?"bg-green-500":"bg-red-500"} text-white`,children:t?"\uD83D\uDFE2 Connected":"\uD83D\uDD34 Disconnected"}),s&&(0,a.jsxs)("div",{className:"inline-block px-3 py-1 rounded bg-red-100 text-red-800 text-sm",children:["Error: ",s]}),(0,a.jsxs)("span",{className:"text-gray-700",children:["\uD83D\uDCCA Total Subscribed: ",e.length," instruments"]}),(0,a.jsxs)("span",{className:"text-gray-700",children:["\uD83D\uDCC8 Live Data: ",e.filter(e=>e.ltp>0).length," active"]}),(0,a.jsxs)("span",{className:"text-gray-700",children:["\uD83D\uDD22 With OI: ",e.filter(e=>e.openInterest&&e.openInterest>0).length," contracts"]})]}),a.jsx("div",{className:"text-sm text-gray-600",children:"Server Auto-Subscription: NSE OPTIDX + FUTIDX Only"})]})}),a.jsx("div",{className:"mb-6 p-4 bg-white rounded-lg shadow",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Instruments"}),a.jsx("input",{type:"text",placeholder:"Search by symbol...",value:i,onChange:e=>o(e.target.value),className:"w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Filter by Type"}),(0,a.jsxs)("select",{value:h,onChange:e=>p(e.target.value),className:"w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500",children:[a.jsx("option",{value:"all",children:"All Instruments"}),a.jsx("option",{value:"options",children:"Options (CE/PE)"}),a.jsx("option",{value:"futures",children:"Futures"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Showing Results"}),(0,a.jsxs)("div",{className:"p-2 bg-gray-50 rounded-md text-sm",children:[g.length," of ",e.length," instruments"]})]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsxs)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>b("symbol"),children:["Symbol ","symbol"===d&&("asc"===x?"↑":"↓")]}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Exchange"}),(0,a.jsxs)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>b("ltp"),children:["LTP ","ltp"===d&&("asc"===x?"↑":"↓")]}),(0,a.jsxs)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>b("change"),children:["Change ","change"===d&&("asc"===x?"↑":"↓")]}),(0,a.jsxs)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>b("volume"),children:["Volume ","volume"===d&&("asc"===x?"↑":"↓")]}),(0,a.jsxs)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>b("openInterest"),children:["Open Interest ","openInterest"===d&&("asc"===x?"↑":"↓")]}),a.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"High/Low"}),a.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Open/Close"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:g.slice(0,100).map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.symbol}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.exchange}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right font-medium",children:f(e.ltp)}),a.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-right font-medium ${v(e.change)}`,children:e.change?(0,a.jsxs)(a.Fragment,{children:[e.change>0?"+":"",e.change.toFixed(2),a.jsx("br",{}),(0,a.jsxs)("span",{className:"text-xs",children:["(",e.changePercent>0?"+":"",e.changePercent.toFixed(2),"%)"]})]}):"-"}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900",children:y(e.volume)}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-blue-600",children:y(e.openInterest)}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500",children:e.high?(0,a.jsxs)(a.Fragment,{children:[f(e.high),a.jsx("br",{}),a.jsx("span",{className:"text-xs",children:f(e.low)})]}):"-"}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500",children:e.open?(0,a.jsxs)(a.Fragment,{children:[f(e.open),a.jsx("br",{}),a.jsx("span",{className:"text-xs",children:f(e.close)})]}):"-"})]},e.securityId))})]})}),0===g.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("p",{className:"text-gray-500",children:"No subscribed data available"}),a.jsx("p",{className:"text-sm text-gray-400 mt-2",children:"The server will automatically subscribe to NSE OPTIDX and FUTIDX instruments"})]}),g.length>100&&(0,a.jsxs)("div",{className:"bg-gray-50 px-6 py-3 text-sm text-gray-600",children:["Showing first 100 of ",g.length," results. Use search to narrow down."]})]})]})}},8051:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(7577);let r=(e={})=>{let{autoConnect:t=!0,autoLoadCache:s=!0,autoSaveInterval:r=3e4,reconnectOnError:n=!0,maxReconnectAttempts:c=5}=e,l={status:"disconnected",isConnected:!1,error:null,connectionStats:{totalMessages:0,connectionUptime:0}},i=new Map,o={isLoaded:!1,totalCacheSize:0,pendingUpdates:0,lastCacheUpdate:null},d={filters:{},sortConfig:{field:"symbol",direction:"asc"},selectedInstruments:new Set,viewMode:"table",autoRefresh:!0},u=(0,a.useCallback)(async()=>{},[]),x=(0,a.useCallback)(async()=>{},[]),m=(0,a.useCallback)(()=>{},[]),h=(0,a.useCallback)(async()=>{},[]),p=(0,a.useCallback)(async()=>{},[]),g=(0,a.useCallback)(async e=>{},[]),b=(0,a.useCallback)(async()=>{},[]),y=(0,a.useCallback)(e=>{},[]),f=(0,a.useCallback)((e,t)=>{},[]),v=(0,a.useCallback)(()=>[],[]),j=(0,a.useCallback)(()=>[],[]),w=(0,a.useCallback)(e=>void 0,[]),k=(0,a.useCallback)(e=>void 0,[]),N=(0,a.useCallback)(e=>{},[]),C=(0,a.useCallback)(e=>{},[]),D=(0,a.useCallback)(()=>{},[]),S=(0,a.useRef)(null),E=(0,a.useRef)(0),P=(0,a.useRef)(!1),I=Array.from(i.values());(0,a.useEffect)(()=>((async()=>{if(!P.current){P.current=!0,console.log("\uD83D\uDE80 Enhanced Hook: Initializing...");try{s&&!o.isLoaded&&await p(),t&&"disconnected"===l.status&&await u()}catch(e){console.error("❌ Enhanced Hook: Initialization failed:",e)}}})(),()=>{S.current&&clearInterval(S.current)}),[t,s,o.isLoaded,l.status,u,p]),(0,a.useEffect)(()=>{if(r>0&&I.length>0)return S.current&&clearInterval(S.current),S.current=setInterval(()=>{g()},r),()=>{S.current&&clearInterval(S.current)}},[r,I.length,g]),(0,a.useEffect)(()=>{if(n&&"error"===l.status&&E.current<c){let e=Math.min(1e3*Math.pow(2,E.current),3e4);console.log(`🔄 Enhanced Hook: Auto-reconnecting in ${e}ms (attempt ${E.current+1}/${c})`);let t=setTimeout(async()=>{try{E.current++,await h()}catch(e){console.error("❌ Enhanced Hook: Auto-reconnect failed:",e)}},e);return()=>clearTimeout(t)}},[l.status,h,n,c]),(0,a.useEffect)(()=>{"connected"===l.status&&(E.current=0)},[l.status]);let F=(0,a.useCallback)(()=>v(),[v]),A=(0,a.useCallback)(()=>j(),[j]),$=(0,a.useCallback)(e=>w(e),[w]),_=(0,a.useCallback)(e=>k(e),[k]),L=(0,a.useCallback)(e=>{y(e)},[y]),M=(0,a.useCallback)((e,t)=>{f(e,t)},[f]),T=(0,a.useCallback)(e=>{N(e)},[N]),U=(0,a.useCallback)(e=>{C(e)},[C]),R=(0,a.useCallback)(async()=>{try{E.current=0,await h()}catch(e){throw console.error("❌ Enhanced Hook: Manual reconnect failed:",e),e}},[h]),O=(0,a.useCallback)(async()=>{try{await p()}catch(e){throw console.error("❌ Enhanced Hook: Force refresh failed:",e),e}},[p]),H=(0,a.useCallback)(async()=>{try{await g(!0)}catch(e){throw console.error("❌ Enhanced Hook: Force save failed:",e),e}},[g]),q={totalInstruments:I.length,connectedInstruments:I.filter(e=>e.ltp&&e.ltp>0).length,lastUpdate:null,cacheSize:o.totalCacheSize,connectionUptime:l.connectionStats.connectionUptime,messagesReceived:l.connectionStats.totalMessages,reconnectAttempts:E.current,isAutoSaving:null!==S.current};return{marketData:I,marketDataMap:i,filteredData:F(),sortedData:A(),isConnected:l.isConnected,connectionStatus:l.status,connectionError:l.error,connectionStats:l.connectionStats,cacheLoaded:o.isLoaded,cacheUpdating:o.pendingUpdates>0,lastCacheUpdate:o.lastCacheUpdate,filters:d.filters,sortConfig:d.sortConfig,selectedInstruments:d.selectedInstruments,viewMode:d.viewMode,autoRefresh:d.autoRefresh,isLoading:!1,isInitializing:!P.current,getDataBySecurityId:$,getDataBySymbol:_,getFilteredData:F,getSortedData:A,updateFilters:L,updateSort:M,subscribe:T,unsubscribe:U,connect:x,disconnect:m,reconnect:R,refresh:O,save:H,clearCache:b,reset:D,stats:q,_store:{setFilters:y,setSortConfig:f,subscribeToInstrument:N,unsubscribeFromInstrument:C}}}},1223:(e,t,s)=>{"use strict";s.d(t,{A1:()=>a});let a={price:e=>!e||e<=0?"-":`₹${e.toFixed(2)}`,number:e=>null==e?"-":e.toLocaleString(),percentage:e=>e&&0!==e?`${e>=0?"+":""}${e.toFixed(2)}%`:"-",change:(e,t)=>{if(!e||0===e)return"-";let s=e>0?"+":"",a=t?` (${s}${t.toFixed(2)}%)`:"";return`${s}${e.toFixed(2)}${a}`},volume:e=>null==e?"-":function(e){return e>=1e7?`${(e/1e7).toFixed(2)} Cr`:e>=1e5?`${(e/1e5).toFixed(2)} L`:e>=1e3?`${(e/1e3).toFixed(2)} K`:e.toString()}(e),time:e=>e?new Date(e).toLocaleTimeString():"-",date:e=>e?new Date(e).toLocaleDateString():"-",bidAsk:(e,t)=>{if(!e||e<=0)return"-";let s=t?` (${t})`:"";return`₹${e.toFixed(2)}${s}`}}},2029:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(8570).createProxy)(String.raw`D:\love\dashboard\csv-market-dashboard\src\app\layout.tsx#default`)},2311:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n,dynamic:()=>r});var a=s(8570);let r=(0,a.createProxy)(String.raw`D:\love\dashboard\csv-market-dashboard\src\app\subscribed\page.tsx#dynamic`),n=(0,a.createProxy)(String.raw`D:\love\dashboard\csv-market-dashboard\src\app\subscribed\page.tsx#default`)},3824:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[276,314],()=>s(3185));module.exports=a})();