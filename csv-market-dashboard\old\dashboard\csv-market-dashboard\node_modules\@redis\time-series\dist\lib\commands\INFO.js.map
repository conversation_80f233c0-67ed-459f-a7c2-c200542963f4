{"version": 3, "file": "INFO.js", "sourceRoot": "", "sources": ["../../../lib/commands/INFO.ts"], "names": [], "mappings": ";;AAIA,+FAA4F;AAqE5F,kBAAe;IACX,YAAY,EAAE,IAAI;IAClB;;;;OAIG;IACH,YAAY,CAAC,MAAqB,EAAE,GAAW;QAC7C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACvB,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACtB,CAAC;IACD,cAAc,EAAE;QACd,CAAC,EAAE,CAAC,KAAmB,EAAE,CAAC,EAAE,WAAyB,EAAa,EAAE;YAClE,MAAM,GAAG,GAAG,EAAS,CAAC;YAEtB,KAAK,IAAI,CAAC,GAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvC,MAAM,GAAG,GAAI,KAAK,CAAC,CAAC,CAAS,CAAC,QAAQ,EAAE,CAAC;gBAEzC,QAAQ,GAAG,EAAE,CAAC;oBACZ,KAAK,cAAc,CAAC;oBACpB,KAAK,aAAa,CAAC;oBACnB,KAAK,gBAAgB,CAAC;oBACtB,KAAK,eAAe,CAAC;oBACrB,KAAK,eAAe,CAAC;oBACrB,KAAK,YAAY,CAAC;oBAClB,KAAK,WAAW,CAAC;oBACjB,KAAK,WAAW,CAAC;oBACjB,KAAK,iBAAiB,CAAC;oBACvB,KAAK,WAAW,CAAC;oBACjB,KAAK,mBAAmB;wBACtB,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,GAAC,CAAC,CAAC,CAAC;wBACtB,MAAM;oBACR,KAAK,QAAQ;wBACX,GAAG,CAAC,GAAG,CAAC,GAAI,KAAK,CAAC,CAAC,GAAC,CAAC,CAA4D,CAAC,GAAG,CACnF,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;4BAClB,IAAI;4BACJ,KAAK;yBACN,CAAC,CACH,CAAC;wBACF,MAAM;oBACR,KAAK,OAAO;wBACV,GAAG,CAAC,GAAG,CAAC,GAAI,KAAK,CAAC,CAAC,GAAC,CAAC,CAAwG,CAAC,GAAG,CAC/H,CAAC,CAAC,GAAG,EAAE,UAAU,EAAE,eAAe,CAAC,EAAE,EAAE,CAAC,CAAC;4BACvC,GAAG;4BACH,UAAU;4BACV,eAAe;yBAChB,CAAC,CACH,CAAC;wBACF,MAAM;oBACR,KAAK,kBAAkB;wBACrB,GAAG,CAAC,GAAG,CAAC,GAAG,2CAAoB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAA+B,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;wBACpG,MAAM;gBACV,CAAC;YACH,CAAC;YAED,OAAO,GAAG,CAAC;QACb,CAAC;QACD,CAAC,EAAE,SAAwC;KAC5C;IACD,aAAa,EAAE,IAAI;CACO,CAAC"}