require('dotenv').config({ path: '.env.database' });
const { PrismaClient } = require('./generated/prisma');

async function testPrisma() {
  const prisma = new PrismaClient();

  try {
    console.log('🔌 Connecting to database with Prisma...');
    await prisma.$connect();
    console.log('✅ Prisma connected successfully');

    // Use raw queries since Prisma schema doesn't match yet
    const countResult = await prisma.$queryRaw`SELECT COUNT(*) FROM "Instruments"`;
    console.log('📊 Total instruments:', countResult[0].count);

    // Check NIFTY instruments
    const niftyInstruments = await prisma.$queryRaw`
      SELECT * FROM "Instruments"
      WHERE "SYMBOL_NAME" = 'NIFTY'
      LIMIT 5
    `;
    console.log('🎯 NIFTY instruments:', niftyInstruments.length);
    niftyInstruments.forEach(inst => {
      console.log('  -', inst.SYMBOL_NAME, inst.INSTRUMENT_TYPE, inst.SM_EXPIRY_DATE, inst.STRIKE_PRICE, inst.OPTION_TYPE);
    });

    // Check expiry dates
    const expiries = await prisma.$queryRaw`
      SELECT DISTINCT "SM_EXPIRY_DATE"
      FROM "Instruments"
      WHERE "SYMBOL_NAME" = 'NIFTY'
      AND "INSTRUMENT_TYPE" = 'OP'
      AND "SM_EXPIRY_DATE" IS NOT NULL
      ORDER BY "SM_EXPIRY_DATE"
      LIMIT 10
    `;
    console.log('📅 NIFTY expiry dates:', expiries.length);
    expiries.forEach(exp => {
      console.log('  -', exp.SM_EXPIRY_DATE);
    });

  } catch (error) {
    console.error('❌ Prisma error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testPrisma();
