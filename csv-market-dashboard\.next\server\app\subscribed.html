<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/2d1ca8c180da41e6.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-074987c53fb04d31.js"/><script src="/_next/static/chunks/fd9d1056-008c9d96c13e1bc8.js" async=""></script><script src="/_next/static/chunks/117-9e8b0277af870132.js" async=""></script><script src="/_next/static/chunks/main-app-a710ca6cf3c3f5fa.js" async=""></script><script src="/_next/static/chunks/app/subscribed/page-156c88a9d5d20252.js" async=""></script><script src="/_next/static/chunks/app/layout-e4b09416c05c8e59.js" async=""></script><title>CSV Market Dashboard</title><meta name="description" content="Real-time market data dashboard with enhanced WebSocket management and data caching"/><link rel="icon" href="/favicon.ico"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c"><div class="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20"><div class="container mx-auto p-4 max-w-7xl"><div class="flex items-center justify-between mb-6"><h1 class="text-3xl font-bold">NSE Derivatives - Subscribed Data Dashboard</h1><div class="space-x-3"><a href="/" class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 transition-colors">🏠 Main Dashboard</a><a href="/option-chain" class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors">🔗 Option Chain</a></div></div><div class="mb-6 p-4 bg-gray-100 rounded-lg"><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><div class="inline-block px-3 py-1 rounded bg-red-500 text-white">🔴 Disconnected</div><span class="text-gray-700">📊 Total Subscribed: <!-- -->0<!-- --> instruments</span><span class="text-gray-700">📈 Live Data: <!-- -->0<!-- --> active</span><span class="text-gray-700">🔢 With OI: <!-- -->0<!-- --> contracts</span></div><div class="text-sm text-gray-600">Server Auto-Subscription: NSE OPTIDX + FUTIDX Only</div></div></div><div class="mb-6 p-4 bg-white rounded-lg shadow"><div class="grid grid-cols-1 md:grid-cols-3 gap-4"><div><label class="block text-sm font-medium text-gray-700 mb-2">Search Instruments</label><input type="text" placeholder="Search by symbol..." class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500" value=""/></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Filter by Type</label><select class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Instruments</option><option value="options">Options (CE/PE)</option><option value="futures">Futures</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Showing Results</label><div class="p-2 bg-gray-50 rounded-md text-sm">0<!-- --> of <!-- -->0<!-- --> instruments</div></div></div></div><div class="bg-white rounded-lg shadow overflow-hidden"><div class="overflow-x-auto"><table class="min-w-full divide-y divide-gray-200"><thead class="bg-gray-50"><tr><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">Symbol <!-- -->↑</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exchange</th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">LTP </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">Change </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">Volume </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">Open Interest </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">High/Low</th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Open/Close</th></tr></thead><tbody class="bg-white divide-y divide-gray-200"></tbody></table></div><div class="text-center py-12"><p class="text-gray-500">No subscribed data available</p><p class="text-sm text-gray-400 mt-2">The server will automatically subscribe to NSE OPTIDX and FUTIDX instruments</p></div></div></div><div id="_rht_toaster" style="position:fixed;z-index:9999;top:16px;left:16px;right:16px;bottom:16px;pointer-events:none"></div></div><script src="/_next/static/chunks/webpack-074987c53fb04d31.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/css/2d1ca8c180da41e6.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"2:I[2846,[],\"\"]\n4:I[9107,[],\"ClientPageRoot\"]\n5:I[7272,[\"200\",\"static/chunks/app/subscribed/page-156c88a9d5d20252.js\"],\"default\",1]\n6:I[4707,[],\"\"]\n7:I[6423,[],\"\"]\n8:I[1707,[\"185\",\"static/chunks/app/layout-e4b09416c05c8e59.js\"],\"default\",1]\nb:I[1060,[],\"\"]\n9:{}\nc:[]\n"])</script><script>self.__next_f.push([1,"0:[\"$\",\"$L2\",null,{\"buildId\":\"rdxwNFjkBiacoXC8qCEWy\",\"assetPrefix\":\"\",\"urlParts\":[\"\",\"subscribed\"],\"initialTree\":[\"\",{\"children\":[\"subscribed\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"subscribed\",{\"children\":[\"__PAGE__\",{},[[\"$L3\",[\"$\",\"$L4\",null,{\"props\":{\"params\":{},\"searchParams\":{}},\"Component\":\"$5\"}],null],null],null]},[null,[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"subscribed\",\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\"}]],null]},[[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/2d1ca8c180da41e6.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}]],[\"$\",\"$L8\",null,{\"children\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[]}],\"params\":\"$9\"}]],null],null],\"couldBeIntercepted\":false,\"initialHead\":[null,\"$La\"],\"globalErrorComponent\":\"$b\",\"missingSlots\":\"$Wc\"}]\n"])</script><script>self.__next_f.push([1,"a:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}]]\n3:null\n"])</script></body></html>