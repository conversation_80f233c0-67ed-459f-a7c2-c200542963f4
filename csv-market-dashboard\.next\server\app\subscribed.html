<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/1d6fe5e72aababc2.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-75e90ec69d4ac2a5.js"/><script src="/_next/static/chunks/4bd1b696-c0a34b31fc175432.js" async=""></script><script src="/_next/static/chunks/684-f4bdabe674c8c62c.js" async=""></script><script src="/_next/static/chunks/main-app-62d6470820f726d1.js" async=""></script><script src="/_next/static/chunks/app/layout-6ce8438b701039af.js" async=""></script><script src="/_next/static/chunks/app/subscribed/page-61ac8cf3e9441736.js" async=""></script><title>CSV Market Dashboard</title><meta name="description" content="Real-time market data dashboard with enhanced WebSocket management and data caching"/><link rel="icon" href="/favicon.ico"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c"><div hidden=""><!--$--><!--/$--></div><div class="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20"><div class="container mx-auto p-4 max-w-7xl"><div class="flex items-center justify-between mb-6"><h1 class="text-3xl font-bold">NSE Derivatives - Subscribed Data Dashboard</h1><div class="space-x-3"><a href="/" class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 transition-colors">🏠 Main Dashboard</a><a href="/option-chain" class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors">🔗 Option Chain</a></div></div><div class="mb-6 p-4 bg-gray-100 rounded-lg"><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><div class="inline-block px-3 py-1 rounded bg-red-500 text-white">🔴 Disconnected</div><span class="text-gray-700">📊 Total Subscribed: <!-- -->0<!-- --> instruments</span><span class="text-gray-700">📈 Live Data: <!-- -->0<!-- --> active</span><span class="text-gray-700">🔢 With OI: <!-- -->0<!-- --> contracts</span></div><div class="text-sm text-gray-600">Server Auto-Subscription: NSE OPTIDX + FUTIDX Only</div></div></div><div class="mb-6 p-4 bg-white rounded-lg shadow"><div class="grid grid-cols-1 md:grid-cols-3 gap-4"><div><label class="block text-sm font-medium text-gray-700 mb-2">Search Instruments</label><input type="text" placeholder="Search by symbol..." class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500" value=""/></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Filter by Type</label><select class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Instruments</option><option value="options">Options (CE/PE)</option><option value="futures">Futures</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Showing Results</label><div class="p-2 bg-gray-50 rounded-md text-sm">0<!-- --> of <!-- -->0<!-- --> instruments</div></div></div></div><div class="bg-white rounded-lg shadow overflow-hidden"><div class="overflow-x-auto"><table class="min-w-full divide-y divide-gray-200"><thead class="bg-gray-50"><tr><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">Symbol <!-- -->↑</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exchange</th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">LTP </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">Change </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">Volume </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">Open Interest </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">High/Low</th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Open/Close</th></tr></thead><tbody class="bg-white divide-y divide-gray-200"></tbody></table></div><div class="text-center py-12"><p class="text-gray-500">No subscribed data available</p><p class="text-sm text-gray-400 mt-2">The server will automatically subscribe to NSE OPTIDX and FUTIDX instruments</p></div></div></div><!--$--><!--/$--><div id="_rht_toaster" style="position:fixed;z-index:9999;top:16px;left:16px;right:16px;bottom:16px;pointer-events:none"></div></div><script src="/_next/static/chunks/webpack-75e90ec69d4ac2a5.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[4970,[],\"ClientSegmentRoot\"]\n3:I[3480,[\"177\",\"static/chunks/app/layout-6ce8438b701039af.js\"],\"default\"]\n4:I[7555,[],\"\"]\n5:I[1295,[],\"\"]\n7:I[894,[],\"ClientPageRoot\"]\n8:I[9625,[\"547\",\"static/chunks/app/subscribed/page-61ac8cf3e9441736.js\"],\"default\"]\nb:I[9665,[],\"OutletBoundary\"]\ne:I[4911,[],\"AsyncMetadataOutlet\"]\n10:I[9665,[],\"ViewportBoundary\"]\n12:I[9665,[],\"MetadataBoundary\"]\n14:I[6614,[],\"\"]\n:HL[\"/_next/static/css/1d6fe5e72aababc2.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"NS3xxMcj5bxy3gmGHKkwd\",\"p\":\"\",\"c\":[\"\",\"subscribed\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"subscribed\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/1d6fe5e72aababc2.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"$L2\",null,{\"Component\":\"$3\",\"slots\":{\"children\":[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]},\"params\":{},\"promise\":\"$@6\"}]]}],{\"children\":[\"subscribed\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L7\",null,{\"Component\":\"$8\",\"searchParams\":{},\"params\":\"$0:f:0:1:1:props:children:1:props:params\",\"promises\":[\"$@9\",\"$@a\"]}],null,[\"$\",\"$Lb\",null,{\"children\":[\"$Lc\",\"$Ld\",[\"$\",\"$Le\",null,{\"promise\":\"$@f\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"FirdSagtb1GzDV_V2hvqAv\",{\"children\":[[\"$\",\"$L10\",null,{\"children\":\"$L11\"}],null]}],[\"$\",\"$L12\",null,{\"children\":\"$L13\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$14\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"15:\"$Sreact.suspense\"\n16:I[4911,[],\"AsyncMetadata\"]\n6:{}\n9:{}\na:{}\n13:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$15\",null,{\"fallback\":null,\"children\":[\"$\",\"$L16\",null,{\"promise\":\"$@17\"}]}]}]\n"])</script><script>self.__next_f.push([1,"d:null\n"])</script><script>self.__next_f.push([1,"11:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nc:null\n"])</script><script>self.__next_f.push([1,"f:{\"metadata\":[],\"error\":null,\"digest\":\"$undefined\"}\n17:{\"metadata\":\"$f:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>