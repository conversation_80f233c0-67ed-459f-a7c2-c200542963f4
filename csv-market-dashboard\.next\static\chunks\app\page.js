/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(app-pages-browser)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q2xvdmUlNUMlNUNkYXNoYm9hcmQlNUMlNUNjc3YtbWFya2V0LWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQWtHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxsb3ZlXFxcXGRhc2hib2FyZFxcXFxjc3YtbWFya2V0LWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxsb3ZlXFxkYXNoYm9hcmRcXGNzdi1tYXJrZXQtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_InstrumentTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/InstrumentTable */ \"(app-pages-browser)/./src/components/InstrumentTable.tsx\");\n/* harmony import */ var _components_FilterPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/FilterPanel */ \"(app-pages-browser)/./src/components/FilterPanel.tsx\");\n/* harmony import */ var _components_ConnectionStatus__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ConnectionStatus */ \"(app-pages-browser)/./src/components/ConnectionStatus.tsx\");\n/* harmony import */ var _components_Stats__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Stats */ \"(app-pages-browser)/./src/components/Stats.tsx\");\n/* harmony import */ var _hooks_useEnhancedMarketData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useEnhancedMarketData */ \"(app-pages-browser)/./src/hooks/useEnhancedMarketData.ts\");\n/* harmony import */ var _lib_data_cache__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/data-cache */ \"(app-pages-browser)/./src/lib/data-cache.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const { marketData: marketDataArray, marketDataMap, isConnected, connectionError, connectionStatus, isLoading: wsLoading, cacheLoaded, refresh: refreshFromCache, stats, updateFilters, filters, getFilteredData, getSortedData } = (0,_hooks_useEnhancedMarketData__WEBPACK_IMPORTED_MODULE_6__.useEnhancedMarketData)();\n    const [instruments, setInstruments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [exchanges, setExchanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [instrumentTypes, setInstrumentTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Convert market data array to Map for compatibility\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            const dataMap = new Map();\n            marketDataArray.forEach({\n                \"Dashboard.useEffect\": (item)=>{\n                    if (item.securityId) {\n                        dataMap.set(item.securityId, item);\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            setMarketData(dataMap);\n        }\n    }[\"Dashboard.useEffect\"], [\n        marketDataArray\n    ]);\n    const fetchFreshInstruments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Dashboard.useCallback[fetchFreshInstruments]\": async ()=>{\n            try {\n                const serverUrl = \"http://localhost:8081\" || 0;\n                console.log('🌐 Dashboard: Loading fresh instruments from API...');\n                const response = await fetch(\"\".concat(serverUrl, \"/api/instruments\"));\n                if (response.ok) {\n                    const data = await response.json();\n                    console.log('✅ Dashboard: Loaded', data.data.instruments.length, 'instruments from API');\n                    setInstruments(data.data.instruments);\n                    // Cache the instruments\n                    await _lib_data_cache__WEBPACK_IMPORTED_MODULE_7__.cacheHelpers.cacheStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_7__.MARKET_DATA_CACHE_KEYS.INSTRUMENTS, data.data.instruments);\n                    console.log('💾 Dashboard: Cached instruments data');\n                    setLoading(false);\n                } else {\n                    console.error('❌ Dashboard: Failed to load instruments:', response.statusText);\n                    setError('Failed to load instruments');\n                    setLoading(false);\n                }\n            } catch (error) {\n                console.error('❌ Dashboard: Error fetching fresh instruments:', error);\n                setError('Error fetching instruments');\n                setLoading(false);\n            }\n        }\n    }[\"Dashboard.useCallback[fetchFreshInstruments]\"], []);\n    // Load all instruments from API with caching\n    const loadAllInstruments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Dashboard.useCallback[loadAllInstruments]\": async ()=>{\n            try {\n                setLoading(true);\n                // Check cache first\n                const cachedInstruments = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_7__.cacheHelpers.getCachedStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_7__.MARKET_DATA_CACHE_KEYS.INSTRUMENTS);\n                if (cachedInstruments && Array.isArray(cachedInstruments)) {\n                    console.log('✅ Dashboard: Loaded instruments from cache');\n                    setInstruments(cachedInstruments);\n                    setLoading(false);\n                    // Still fetch fresh data in background\n                    fetchFreshInstruments();\n                    return;\n                }\n                // Fetch fresh data\n                await fetchFreshInstruments();\n            } catch (error) {\n                console.error('❌ Dashboard: Error loading instruments:', error);\n                setError('Error loading instruments');\n                setLoading(false);\n            }\n        }\n    }[\"Dashboard.useCallback[loadAllInstruments]\"], [\n        fetchFreshInstruments\n    ]);\n    // Load all instruments on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            loadAllInstruments();\n        }\n    }[\"Dashboard.useEffect\"], [\n        loadAllInstruments\n    ]);\n    // Load additional data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            const loadMetadata = {\n                \"Dashboard.useEffect.loadMetadata\": async ()=>{\n                    try {\n                        const serverUrl = \"http://localhost:8081\" || 0;\n                        // Load exchanges\n                        const exchangesResponse = await fetch(\"\".concat(serverUrl, \"/api/exchanges\"));\n                        if (exchangesResponse.ok) {\n                            const exchangesData = await exchangesResponse.json();\n                            setExchanges(exchangesData.data);\n                        }\n                        // Load instrument types\n                        const typesResponse = await fetch(\"\".concat(serverUrl, \"/api/instrument-types\"));\n                        if (typesResponse.ok) {\n                            const typesData = await typesResponse.json();\n                            setInstrumentTypes(typesData.data);\n                        }\n                    } catch (error) {\n                        console.error('❌ Error loading metadata:', error);\n                    }\n                }\n            }[\"Dashboard.useEffect.loadMetadata\"];\n            loadMetadata();\n        }\n    }[\"Dashboard.useEffect\"], []);\n    // Filter instruments based on current filter\n    const filteredInstruments = instruments.filter((instrument)=>{\n        if (filter.exchange && filter.exchange.length > 0 && !filter.exchange.includes(instrument.exchange)) {\n            return false;\n        }\n        if (filter.instrumentType && filter.instrumentType.length > 0 && !filter.instrumentType.includes(instrument.instrumentType)) {\n            return false;\n        }\n        if (filter.search) {\n            const searchTerm = filter.search.toLowerCase();\n            return instrument.symbol.toLowerCase().includes(searchTerm) || instrument.displayName.toLowerCase().includes(searchTerm) || instrument.isin && instrument.isin.toLowerCase().includes(searchTerm);\n        }\n        return true;\n    });\n    // Note: Subscriptions are now handled server-side automatically\n    const handleFilterChange = (newFilter)=>{\n        setFilter(newFilter);\n    };\n    const handleInstrumentSelect = (instrument)=>{\n        console.log('Selected instrument:', instrument);\n    // You can add more functionality here, like showing detailed view\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner w-12 h-12 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading market data...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 169,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass rounded-2xl shadow-lg p-6 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold gradient-text\",\n                                    children: \"CSV Market Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-1\",\n                                    children: \"Real-time market data from CSV instruments\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3 space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/subscribed\",\n                                            className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors\",\n                                            children: \"\\uD83D\\uDCCA View Subscribed Data Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/option-chain\",\n                                            className: \"inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors\",\n                                            children: \"\\uD83D\\uDD17 NIFTY Option Chain\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConnectionStatus__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            connected: isConnected\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            (error || connectionError) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Error:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this),\n                    \" \",\n                    error || connectionError\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Stats__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                totalInstruments: instruments.length,\n                filteredInstruments: filteredInstruments.length,\n                marketDataCount: stats.totalInstruments,\n                connected: isConnected,\n                connectionStats: stats\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FilterPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            filter: filter,\n                            onFilterChange: handleFilterChange,\n                            exchanges: exchanges,\n                            instrumentTypes: instrumentTypes,\n                            segments: [\n                                'C',\n                                'F',\n                                'O'\n                            ]\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InstrumentTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            instruments: filteredInstruments.slice(0, 100),\n                            marketData: marketData,\n                            onInstrumentSelect: handleInstrumentSelect,\n                            loading: loading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 text-center text-sm text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"CSV Market Dashboard - Real-time data from \",\n                            instruments.length,\n                            \" instruments\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Last updated: \",\n                            new Date().toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"wOUSZNBu5Zzu0TU5leUfa8hlk1s=\", false, function() {\n    return [\n        _hooks_useEnhancedMarketData__WEBPACK_IMPORTED_MODULE_6__.useEnhancedMarketData\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ConnectionStatus.tsx":
/*!*********************************************!*\
  !*** ./src/components/ConnectionStatus.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst ConnectionStatus = (param)=>{\n    let { connected } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-3 h-3 rounded-full \".concat(connected ? 'bg-green-500 animate-pulse' : 'bg-red-500')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium \".concat(connected ? 'text-green-700' : 'text-red-700'),\n                        children: connected ? 'Connected' : 'Disconnected'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-3 py-1 rounded-full text-xs font-semibold \".concat(connected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                children: connected ? 'LIVE' : 'OFFLINE'\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ConnectionStatus;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ConnectionStatus);\nvar _c;\n$RefreshReg$(_c, \"ConnectionStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ConnectionStatus.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/FilterPanel.tsx":
/*!****************************************!*\
  !*** ./src/components/FilterPanel.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst FilterPanel = (param)=>{\n    let { filter, onFilterChange, exchanges, instrumentTypes, segments } = param;\n    var _filter_exchange, _filter_instrumentType, _filter_segment;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(filter.search || '');\n    const handleExchangeChange = (exchange, checked)=>{\n        const currentExchanges = filter.exchange || [];\n        const newExchanges = checked ? [\n            ...currentExchanges,\n            exchange\n        ] : currentExchanges.filter((e)=>e !== exchange);\n        onFilterChange({\n            ...filter,\n            exchange: newExchanges.length > 0 ? newExchanges : undefined\n        });\n    };\n    const handleInstrumentTypeChange = (type, checked)=>{\n        const currentTypes = filter.instrumentType || [];\n        const newTypes = checked ? [\n            ...currentTypes,\n            type\n        ] : currentTypes.filter((t)=>t !== type);\n        onFilterChange({\n            ...filter,\n            instrumentType: newTypes.length > 0 ? newTypes : undefined\n        });\n    };\n    const handleSegmentChange = (segment, checked)=>{\n        const currentSegments = filter.segment || [];\n        const newSegments = checked ? [\n            ...currentSegments,\n            segment\n        ] : currentSegments.filter((s)=>s !== segment);\n        onFilterChange({\n            ...filter,\n            segment: newSegments.length > 0 ? newSegments : undefined\n        });\n    };\n    const handleSearchChange = (value)=>{\n        setSearchTerm(value);\n        onFilterChange({\n            ...filter,\n            search: value || undefined\n        });\n    };\n    const handleClearFilters = ()=>{\n        setSearchTerm('');\n        onFilterChange({});\n    };\n    const isFilterActive = ()=>{\n        var _filter_exchange, _filter_instrumentType, _filter_segment;\n        return !!(((_filter_exchange = filter.exchange) === null || _filter_exchange === void 0 ? void 0 : _filter_exchange.length) || ((_filter_instrumentType = filter.instrumentType) === null || _filter_instrumentType === void 0 ? void 0 : _filter_instrumentType.length) || ((_filter_segment = filter.segment) === null || _filter_segment === void 0 ? void 0 : _filter_segment.length) || filter.search || filter.isActive !== undefined || filter.hasExpiry !== undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"filter-panel\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Filters\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    isFilterActive() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleClearFilters,\n                        className: \"text-sm text-blue-600 hover:text-blue-800 font-medium\",\n                        children: \"Clear All\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Search\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        placeholder: \"Search by symbol, name, or ISIN...\",\n                        value: searchTerm,\n                        onChange: (e)=>handleSearchChange(e.target.value),\n                        className: \"filter-input\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Exchanges\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 max-h-32 overflow-y-auto custom-scrollbar\",\n                        children: exchanges.map((exchange)=>{\n                            var _filter_exchange;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: ((_filter_exchange = filter.exchange) === null || _filter_exchange === void 0 ? void 0 : _filter_exchange.includes(exchange)) || false,\n                                        onChange: (e)=>handleExchangeChange(exchange, e.target.checked),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: exchange\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, exchange, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Instrument Types\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 max-h-32 overflow-y-auto custom-scrollbar\",\n                        children: instrumentTypes.map((type)=>{\n                            var _filter_instrumentType;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: ((_filter_instrumentType = filter.instrumentType) === null || _filter_instrumentType === void 0 ? void 0 : _filter_instrumentType.includes(type)) || false,\n                                        onChange: (e)=>handleInstrumentTypeChange(type, e.target.checked),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: type\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, type, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Segments\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: segments.map((segment)=>{\n                            var _filter_segment;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: ((_filter_segment = filter.segment) === null || _filter_segment === void 0 ? void 0 : _filter_segment.includes(segment)) || false,\n                                        onChange: (e)=>handleSegmentChange(segment, e.target.checked),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: segment === 'C' ? 'Cash (C)' : segment === 'F' ? 'Futures (F)' : segment === 'O' ? 'Options (O)' : segment\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, segment, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Additional Filters\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filter.isActive === true,\n                                        onChange: (e)=>onFilterChange({\n                                                ...filter,\n                                                isActive: e.target.checked ? true : undefined\n                                            }),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: \"Active Only\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filter.hasExpiry === true,\n                                        onChange: (e)=>onFilterChange({\n                                                ...filter,\n                                                hasExpiry: e.target.checked ? true : undefined\n                                            }),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: \"Has Expiry\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Lot Size Range\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"number\",\n                                placeholder: \"Min\",\n                                value: filter.minLotSize || '',\n                                onChange: (e)=>onFilterChange({\n                                        ...filter,\n                                        minLotSize: e.target.value ? parseInt(e.target.value) : undefined\n                                    }),\n                                className: \"filter-input text-sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"number\",\n                                placeholder: \"Max\",\n                                value: filter.maxLotSize || '',\n                                onChange: (e)=>onFilterChange({\n                                        ...filter,\n                                        maxLotSize: e.target.value ? parseInt(e.target.value) : undefined\n                                    }),\n                                className: \"filter-input text-sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, undefined),\n            isFilterActive() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-blue-900 mb-2\",\n                        children: \"Active Filters:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1 text-xs text-blue-700\",\n                        children: [\n                            ((_filter_exchange = filter.exchange) === null || _filter_exchange === void 0 ? void 0 : _filter_exchange.length) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Exchanges: \",\n                                    filter.exchange.join(', ')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, undefined),\n                            ((_filter_instrumentType = filter.instrumentType) === null || _filter_instrumentType === void 0 ? void 0 : _filter_instrumentType.length) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Types: \",\n                                    filter.instrumentType.join(', ')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, undefined),\n                            ((_filter_segment = filter.segment) === null || _filter_segment === void 0 ? void 0 : _filter_segment.length) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Segments: \",\n                                    filter.segment.join(', ')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.search && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Search: “\",\n                                    filter.search,\n                                    \"”\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Active instruments only\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.hasExpiry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"With expiry date\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, undefined),\n                            (filter.minLotSize || filter.maxLotSize) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Lot size: \",\n                                    filter.minLotSize || 0,\n                                    \" - \",\n                                    filter.maxLotSize || '∞'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 220,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FilterPanel, \"ViotEHOY83tXzCBEuSV/ICV6Rsw=\");\n_c = FilterPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FilterPanel);\nvar _c;\n$RefreshReg$(_c, \"FilterPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FilterPanel.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/InstrumentTable.tsx":
/*!********************************************!*\
  !*** ./src/components/InstrumentTable.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst InstrumentTable = (param)=>{\n    let { instruments, marketData, onInstrumentSelect, loading = false } = param;\n    _s();\n    const [sortField, setSortField] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('symbol');\n    const [sortDirection, setSortDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('asc');\n    // Sort instruments\n    const sortedInstruments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"InstrumentTable.useMemo[sortedInstruments]\": ()=>{\n            return [\n                ...instruments\n            ].sort({\n                \"InstrumentTable.useMemo[sortedInstruments]\": (a, b)=>{\n                    const aValue = a[sortField];\n                    const bValue = b[sortField];\n                    // Handle undefined values\n                    if (aValue === undefined && bValue === undefined) return 0;\n                    if (aValue === undefined) return 1;\n                    if (bValue === undefined) return -1;\n                    if (aValue === bValue) return 0;\n                    const comparison = aValue < bValue ? -1 : 1;\n                    return sortDirection === 'asc' ? comparison : -comparison;\n                }\n            }[\"InstrumentTable.useMemo[sortedInstruments]\"]);\n        }\n    }[\"InstrumentTable.useMemo[sortedInstruments]\"], [\n        instruments,\n        sortField,\n        sortDirection\n    ]);\n    const handleSort = (field)=>{\n        if (sortField === field) {\n            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n        } else {\n            setSortField(field);\n            setSortDirection('asc');\n        }\n    };\n    const formatPrice = (price)=>{\n        if (price === undefined || price === 0) return '-';\n        return \"₹\".concat(price.toFixed(2));\n    };\n    const formatChange = (change, changePercent)=>{\n        if (change === undefined || changePercent === undefined) return '-';\n        const sign = change >= 0 ? '+' : '';\n        return \"\".concat(sign).concat(change.toFixed(2), \" (\").concat(sign).concat(changePercent.toFixed(2), \"%)\");\n    };\n    const getChangeColor = (change)=>{\n        if (change === undefined || change === 0) return 'text-gray-600';\n        return change > 0 ? 'text-green-600' : 'text-red-600';\n    };\n    const formatVolume = (volume)=>{\n        if (volume === undefined || volume === 0) return '-';\n        if (volume >= 10000000) return \"\".concat((volume / 10000000).toFixed(1), \"Cr\");\n        if (volume >= 100000) return \"\".concat((volume / 100000).toFixed(1), \"L\");\n        if (volume >= 1000) return \"\".concat((volume / 1000).toFixed(1), \"K\");\n        return volume.toString();\n    };\n    const SortIcon = (param)=>{\n        let { field } = param;\n        if (sortField !== field) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-gray-400\",\n                children: \"↕\"\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 68,\n                columnNumber: 14\n            }, undefined);\n        }\n        return sortDirection === 'asc' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-blue-600\",\n            children: \"↑\"\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 70,\n            columnNumber: 38\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-blue-600\",\n            children: \"↓\"\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 70,\n            columnNumber: 81\n        }, undefined);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"glass rounded-2xl shadow-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner w-8 h-8 mr-3\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading instruments...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (instruments.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"glass rounded-2xl shadow-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-lg\",\n                        children: \"No instruments found\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 text-sm mt-2\",\n                        children: \"Try adjusting your filters\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"glass rounded-2xl shadow-lg overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900\",\n                        children: \"Market Instruments\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-sm mt-1\",\n                        children: [\n                            \"Showing \",\n                            sortedInstruments.length,\n                            \" instruments\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto custom-scrollbar\",\n                style: {\n                    maxHeight: '600px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"market-table\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"sticky top-0 bg-gray-50 z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('securityId'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Security ID\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"securityId\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('symbol'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Symbol\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"symbol\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('displayName'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Name\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"displayName\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('exchange'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Exchange\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"exchange\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('instrumentType'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Type\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"instrumentType\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right\",\n                                        children: \"LTP\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right\",\n                                        children: \"Change\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right\",\n                                        children: \"Volume\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort('lotSize'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Lot Size\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"lotSize\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            children: sortedInstruments.map((instrument)=>{\n                                const data = marketData.get(instrument.securityId);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-gray-50 cursor-pointer transition-colors\",\n                                    onClick: ()=>onInstrumentSelect === null || onInstrumentSelect === void 0 ? void 0 : onInstrumentSelect(instrument),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"font-mono text-sm text-gray-700\",\n                                            children: instrument.securityId\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"font-medium text-blue-600\",\n                                            children: instrument.symbol\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"max-w-xs truncate\",\n                                            title: instrument.displayName,\n                                            children: instrument.displayName\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                children: instrument.exchange\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                children: instrument.instrumentType\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"text-right font-medium\",\n                                            children: formatPrice(data === null || data === void 0 ? void 0 : data.ltp)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"text-right font-medium \".concat(getChangeColor(data === null || data === void 0 ? void 0 : data.change)),\n                                            children: formatChange(data === null || data === void 0 ? void 0 : data.change, data === null || data === void 0 ? void 0 : data.changePercent)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"text-right\",\n                                            children: formatVolume(data === null || data === void 0 ? void 0 : data.volume)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"text-right\",\n                                            children: instrument.lotSize.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, instrument.securityId, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, undefined),\n            sortedInstruments.length > 100 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-gray-50 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600 text-center\",\n                    children: \"Showing first 100 instruments. Use filters to narrow down results.\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, undefined);\n};\n_s(InstrumentTable, \"AUdWssv40DkD69fxRdafzsbHvFc=\");\n_c = InstrumentTable;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InstrumentTable);\nvar _c;\n$RefreshReg$(_c, \"InstrumentTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0luc3RydW1lbnRUYWJsZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRWlEO0FBR2pELE1BQU1HLGtCQUFrRDtRQUFDLEVBQ3ZEQyxXQUFXLEVBQ1hDLFVBQVUsRUFDVkMsa0JBQWtCLEVBQ2xCQyxVQUFVLEtBQUssRUFDaEI7O0lBQ0MsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUdSLCtDQUFRQSxDQUFtQjtJQUM3RCxNQUFNLENBQUNTLGVBQWVDLGlCQUFpQixHQUFHViwrQ0FBUUEsQ0FBaUI7SUFFbkUsbUJBQW1CO0lBQ25CLE1BQU1XLG9CQUFvQlYsOENBQU9BO3NEQUFDO1lBQ2hDLE9BQU87bUJBQUlFO2FBQVksQ0FBQ1MsSUFBSTs4REFBQyxDQUFDQyxHQUFHQztvQkFDL0IsTUFBTUMsU0FBU0YsQ0FBQyxDQUFDTixVQUFVO29CQUMzQixNQUFNUyxTQUFTRixDQUFDLENBQUNQLFVBQVU7b0JBRTNCLDBCQUEwQjtvQkFDMUIsSUFBSVEsV0FBV0UsYUFBYUQsV0FBV0MsV0FBVyxPQUFPO29CQUN6RCxJQUFJRixXQUFXRSxXQUFXLE9BQU87b0JBQ2pDLElBQUlELFdBQVdDLFdBQVcsT0FBTyxDQUFDO29CQUVsQyxJQUFJRixXQUFXQyxRQUFRLE9BQU87b0JBRTlCLE1BQU1FLGFBQWFILFNBQVNDLFNBQVMsQ0FBQyxJQUFJO29CQUMxQyxPQUFPUCxrQkFBa0IsUUFBUVMsYUFBYSxDQUFDQTtnQkFDakQ7O1FBQ0Y7cURBQUc7UUFBQ2Y7UUFBYUk7UUFBV0U7S0FBYztJQUUxQyxNQUFNVSxhQUFhLENBQUNDO1FBQ2xCLElBQUliLGNBQWNhLE9BQU87WUFDdkJWLGlCQUFpQkQsa0JBQWtCLFFBQVEsU0FBUztRQUN0RCxPQUFPO1lBQ0xELGFBQWFZO1lBQ2JWLGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsTUFBTVcsY0FBYyxDQUFDQztRQUNuQixJQUFJQSxVQUFVTCxhQUFhSyxVQUFVLEdBQUcsT0FBTztRQUMvQyxPQUFPLElBQXFCLE9BQWpCQSxNQUFNQyxPQUFPLENBQUM7SUFDM0I7SUFFQSxNQUFNQyxlQUFlLENBQUNDLFFBQTRCQztRQUNoRCxJQUFJRCxXQUFXUixhQUFhUyxrQkFBa0JULFdBQVcsT0FBTztRQUNoRSxNQUFNVSxPQUFPRixVQUFVLElBQUksTUFBTTtRQUNqQyxPQUFPLEdBQVVBLE9BQVBFLE1BQTZCQSxPQUF0QkYsT0FBT0YsT0FBTyxDQUFDLElBQUcsTUFBV0csT0FBUEMsTUFBZ0MsT0FBekJELGNBQWNILE9BQU8sQ0FBQyxJQUFHO0lBQ3pFO0lBRUEsTUFBTUssaUJBQWlCLENBQUNIO1FBQ3RCLElBQUlBLFdBQVdSLGFBQWFRLFdBQVcsR0FBRyxPQUFPO1FBQ2pELE9BQU9BLFNBQVMsSUFBSSxtQkFBbUI7SUFDekM7SUFFQSxNQUFNSSxlQUFlLENBQUNDO1FBQ3BCLElBQUlBLFdBQVdiLGFBQWFhLFdBQVcsR0FBRyxPQUFPO1FBQ2pELElBQUlBLFVBQVUsVUFBVSxPQUFPLEdBQWtDLE9BQS9CLENBQUNBLFNBQVMsUUFBTyxFQUFHUCxPQUFPLENBQUMsSUFBRztRQUNqRSxJQUFJTyxVQUFVLFFBQVEsT0FBTyxHQUFnQyxPQUE3QixDQUFDQSxTQUFTLE1BQUssRUFBR1AsT0FBTyxDQUFDLElBQUc7UUFDN0QsSUFBSU8sVUFBVSxNQUFNLE9BQU8sR0FBOEIsT0FBM0IsQ0FBQ0EsU0FBUyxJQUFHLEVBQUdQLE9BQU8sQ0FBQyxJQUFHO1FBQ3pELE9BQU9PLE9BQU9DLFFBQVE7SUFDeEI7SUFFQSxNQUFNQyxXQUFXO1lBQUMsRUFBRVosS0FBSyxFQUErQjtRQUN0RCxJQUFJYixjQUFjYSxPQUFPO1lBQ3ZCLHFCQUFPLDhEQUFDYTtnQkFBS0MsV0FBVTswQkFBZ0I7Ozs7OztRQUN6QztRQUNBLE9BQU96QixrQkFBa0Isc0JBQVEsOERBQUN3QjtZQUFLQyxXQUFVO3NCQUFnQjs7Ozs7c0NBQVcsOERBQUNEO1lBQUtDLFdBQVU7c0JBQWdCOzs7Ozs7SUFDOUc7SUFFQSxJQUFJNUIsU0FBUztRQUNYLHFCQUNFLDhEQUFDNkI7WUFBSUQsV0FBVTtzQkFDYiw0RUFBQ0M7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDQzt3QkFBSUQsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDRDt3QkFBS0MsV0FBVTtrQ0FBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSXhDO0lBRUEsSUFBSS9CLFlBQVlpQyxNQUFNLEtBQUssR0FBRztRQUM1QixxQkFDRSw4REFBQ0Q7WUFBSUQsV0FBVTtzQkFDYiw0RUFBQ0M7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDRzt3QkFBRUgsV0FBVTtrQ0FBd0I7Ozs7OztrQ0FDckMsOERBQUNHO3dCQUFFSCxXQUFVO2tDQUE2Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJbEQ7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUQsV0FBVTs7MEJBQ2IsOERBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0k7d0JBQUdKLFdBQVU7a0NBQWtDOzs7Ozs7a0NBQ2hELDhEQUFDRzt3QkFBRUgsV0FBVTs7NEJBQTZCOzRCQUMvQnZCLGtCQUFrQnlCLE1BQU07NEJBQUM7Ozs7Ozs7Ozs7Ozs7MEJBSXRDLDhEQUFDRDtnQkFBSUQsV0FBVTtnQkFBbUNLLE9BQU87b0JBQUVDLFdBQVc7Z0JBQVE7MEJBQzVFLDRFQUFDQztvQkFBTVAsV0FBVTs7c0NBQ2YsOERBQUNROzRCQUFNUixXQUFVO3NDQUNmLDRFQUFDUzs7a0RBQ0MsOERBQUNDO3dDQUNDVixXQUFVO3dDQUNWVyxTQUFTLElBQU0xQixXQUFXO2tEQUUxQiw0RUFBQ2dCOzRDQUFJRCxXQUFVOztnREFBb0M7OERBRWpELDhEQUFDRjtvREFBU1osT0FBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBR3BCLDhEQUFDd0I7d0NBQ0NWLFdBQVU7d0NBQ1ZXLFNBQVMsSUFBTTFCLFdBQVc7a0RBRTFCLDRFQUFDZ0I7NENBQUlELFdBQVU7O2dEQUFvQzs4REFFakQsOERBQUNGO29EQUFTWixPQUFNOzs7Ozs7Ozs7Ozs7Ozs7OztrREFHcEIsOERBQUN3Qjt3Q0FDQ1YsV0FBVTt3Q0FDVlcsU0FBUyxJQUFNMUIsV0FBVztrREFFMUIsNEVBQUNnQjs0Q0FBSUQsV0FBVTs7Z0RBQW9DOzhEQUVqRCw4REFBQ0Y7b0RBQVNaLE9BQU07Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUdwQiw4REFBQ3dCO3dDQUNDVixXQUFVO3dDQUNWVyxTQUFTLElBQU0xQixXQUFXO2tEQUUxQiw0RUFBQ2dCOzRDQUFJRCxXQUFVOztnREFBb0M7OERBRWpELDhEQUFDRjtvREFBU1osT0FBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBR3BCLDhEQUFDd0I7d0NBQ0NWLFdBQVU7d0NBQ1ZXLFNBQVMsSUFBTTFCLFdBQVc7a0RBRTFCLDRFQUFDZ0I7NENBQUlELFdBQVU7O2dEQUFvQzs4REFFakQsOERBQUNGO29EQUFTWixPQUFNOzs7Ozs7Ozs7Ozs7Ozs7OztrREFHcEIsOERBQUN3Qjt3Q0FBR1YsV0FBVTtrREFBYTs7Ozs7O2tEQUMzQiw4REFBQ1U7d0NBQUdWLFdBQVU7a0RBQWE7Ozs7OztrREFDM0IsOERBQUNVO3dDQUFHVixXQUFVO2tEQUFhOzs7Ozs7a0RBQzNCLDhEQUFDVTt3Q0FDQ1YsV0FBVTt3Q0FDVlcsU0FBUyxJQUFNMUIsV0FBVztrREFFMUIsNEVBQUNnQjs0Q0FBSUQsV0FBVTs7Z0RBQW9DOzhEQUVqRCw4REFBQ0Y7b0RBQVNaLE9BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBS3hCLDhEQUFDMEI7c0NBQ0VuQyxrQkFBa0JvQyxHQUFHLENBQUMsQ0FBQ0M7Z0NBQ3RCLE1BQU1DLE9BQU83QyxXQUFXOEMsR0FBRyxDQUFDRixXQUFXRyxVQUFVO2dDQUNqRCxxQkFDRSw4REFBQ1I7b0NBRUNULFdBQVU7b0NBQ1ZXLFNBQVMsSUFBTXhDLCtCQUFBQSx5Q0FBQUEsbUJBQXFCMkM7O3NEQUVwQyw4REFBQ0k7NENBQUdsQixXQUFVO3NEQUNYYyxXQUFXRyxVQUFVOzs7Ozs7c0RBRXhCLDhEQUFDQzs0Q0FBR2xCLFdBQVU7c0RBQ1hjLFdBQVdLLE1BQU07Ozs7OztzREFFcEIsOERBQUNEOzRDQUFHbEIsV0FBVTs0Q0FBb0JvQixPQUFPTixXQUFXTyxXQUFXO3NEQUM1RFAsV0FBV08sV0FBVzs7Ozs7O3NEQUV6Qiw4REFBQ0g7c0RBQ0MsNEVBQUNuQjtnREFBS0MsV0FBVTswREFDYmMsV0FBV1EsUUFBUTs7Ozs7Ozs7Ozs7c0RBR3hCLDhEQUFDSjtzREFDQyw0RUFBQ25CO2dEQUFLQyxXQUFVOzBEQUNiYyxXQUFXUyxjQUFjOzs7Ozs7Ozs7OztzREFHOUIsOERBQUNMOzRDQUFHbEIsV0FBVTtzREFDWGIsWUFBWTRCLGlCQUFBQSwyQkFBQUEsS0FBTVMsR0FBRzs7Ozs7O3NEQUV4Qiw4REFBQ047NENBQUdsQixXQUFXLDBCQUF1RCxPQUE3Qk4sZUFBZXFCLGlCQUFBQSwyQkFBQUEsS0FBTXhCLE1BQU07c0RBQ2pFRCxhQUFheUIsaUJBQUFBLDJCQUFBQSxLQUFNeEIsTUFBTSxFQUFFd0IsaUJBQUFBLDJCQUFBQSxLQUFNdkIsYUFBYTs7Ozs7O3NEQUVqRCw4REFBQzBCOzRDQUFHbEIsV0FBVTtzREFDWEwsYUFBYW9CLGlCQUFBQSwyQkFBQUEsS0FBTW5CLE1BQU07Ozs7OztzREFFNUIsOERBQUNzQjs0Q0FBR2xCLFdBQVU7c0RBQ1hjLFdBQVdXLE9BQU8sQ0FBQ0MsY0FBYzs7Ozs7OzttQ0FqQy9CWixXQUFXRyxVQUFVOzs7Ozs0QkFxQ2hDOzs7Ozs7Ozs7Ozs7Ozs7OztZQUtMeEMsa0JBQWtCeUIsTUFBTSxHQUFHLHFCQUMxQiw4REFBQ0Q7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNHO29CQUFFSCxXQUFVOzhCQUFvQzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPM0Q7R0F6Tk1oQztLQUFBQTtBQTJOTixpRUFBZUEsZUFBZUEsRUFBQyIsInNvdXJjZXMiOlsiRDpcXGxvdmVcXGRhc2hib2FyZFxcY3N2LW1hcmtldC1kYXNoYm9hcmRcXHNyY1xcY29tcG9uZW50c1xcSW5zdHJ1bWVudFRhYmxlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgSW5zdHJ1bWVudCwgTWFya2V0RGF0YSwgSW5zdHJ1bWVudFRhYmxlUHJvcHMgfSBmcm9tICdAL3R5cGVzJztcclxuXHJcbmNvbnN0IEluc3RydW1lbnRUYWJsZTogUmVhY3QuRkM8SW5zdHJ1bWVudFRhYmxlUHJvcHM+ID0gKHtcclxuICBpbnN0cnVtZW50cyxcclxuICBtYXJrZXREYXRhLFxyXG4gIG9uSW5zdHJ1bWVudFNlbGVjdCxcclxuICBsb2FkaW5nID0gZmFsc2UsXHJcbn0pID0+IHtcclxuICBjb25zdCBbc29ydEZpZWxkLCBzZXRTb3J0RmllbGRdID0gdXNlU3RhdGU8a2V5b2YgSW5zdHJ1bWVudD4oJ3N5bWJvbCcpO1xyXG4gIGNvbnN0IFtzb3J0RGlyZWN0aW9uLCBzZXRTb3J0RGlyZWN0aW9uXSA9IHVzZVN0YXRlPCdhc2MnIHwgJ2Rlc2MnPignYXNjJyk7XHJcblxyXG4gIC8vIFNvcnQgaW5zdHJ1bWVudHNcclxuICBjb25zdCBzb3J0ZWRJbnN0cnVtZW50cyA9IHVzZU1lbW8oKCkgPT4ge1xyXG4gICAgcmV0dXJuIFsuLi5pbnN0cnVtZW50c10uc29ydCgoYSwgYikgPT4ge1xyXG4gICAgICBjb25zdCBhVmFsdWUgPSBhW3NvcnRGaWVsZF07XHJcbiAgICAgIGNvbnN0IGJWYWx1ZSA9IGJbc29ydEZpZWxkXTtcclxuXHJcbiAgICAgIC8vIEhhbmRsZSB1bmRlZmluZWQgdmFsdWVzXHJcbiAgICAgIGlmIChhVmFsdWUgPT09IHVuZGVmaW5lZCAmJiBiVmFsdWUgPT09IHVuZGVmaW5lZCkgcmV0dXJuIDA7XHJcbiAgICAgIGlmIChhVmFsdWUgPT09IHVuZGVmaW5lZCkgcmV0dXJuIDE7XHJcbiAgICAgIGlmIChiVmFsdWUgPT09IHVuZGVmaW5lZCkgcmV0dXJuIC0xO1xyXG5cclxuICAgICAgaWYgKGFWYWx1ZSA9PT0gYlZhbHVlKSByZXR1cm4gMDtcclxuXHJcbiAgICAgIGNvbnN0IGNvbXBhcmlzb24gPSBhVmFsdWUgPCBiVmFsdWUgPyAtMSA6IDE7XHJcbiAgICAgIHJldHVybiBzb3J0RGlyZWN0aW9uID09PSAnYXNjJyA/IGNvbXBhcmlzb24gOiAtY29tcGFyaXNvbjtcclxuICAgIH0pO1xyXG4gIH0sIFtpbnN0cnVtZW50cywgc29ydEZpZWxkLCBzb3J0RGlyZWN0aW9uXSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZVNvcnQgPSAoZmllbGQ6IGtleW9mIEluc3RydW1lbnQpID0+IHtcclxuICAgIGlmIChzb3J0RmllbGQgPT09IGZpZWxkKSB7XHJcbiAgICAgIHNldFNvcnREaXJlY3Rpb24oc29ydERpcmVjdGlvbiA9PT0gJ2FzYycgPyAnZGVzYycgOiAnYXNjJyk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBzZXRTb3J0RmllbGQoZmllbGQpO1xyXG4gICAgICBzZXRTb3J0RGlyZWN0aW9uKCdhc2MnKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBmb3JtYXRQcmljZSA9IChwcmljZTogbnVtYmVyIHwgdW5kZWZpbmVkKTogc3RyaW5nID0+IHtcclxuICAgIGlmIChwcmljZSA9PT0gdW5kZWZpbmVkIHx8IHByaWNlID09PSAwKSByZXR1cm4gJy0nO1xyXG4gICAgcmV0dXJuIGDigrkke3ByaWNlLnRvRml4ZWQoMil9YDtcclxuICB9O1xyXG5cclxuICBjb25zdCBmb3JtYXRDaGFuZ2UgPSAoY2hhbmdlOiBudW1iZXIgfCB1bmRlZmluZWQsIGNoYW5nZVBlcmNlbnQ6IG51bWJlciB8IHVuZGVmaW5lZCk6IHN0cmluZyA9PiB7XHJcbiAgICBpZiAoY2hhbmdlID09PSB1bmRlZmluZWQgfHwgY2hhbmdlUGVyY2VudCA9PT0gdW5kZWZpbmVkKSByZXR1cm4gJy0nO1xyXG4gICAgY29uc3Qgc2lnbiA9IGNoYW5nZSA+PSAwID8gJysnIDogJyc7XHJcbiAgICByZXR1cm4gYCR7c2lnbn0ke2NoYW5nZS50b0ZpeGVkKDIpfSAoJHtzaWdufSR7Y2hhbmdlUGVyY2VudC50b0ZpeGVkKDIpfSUpYDtcclxuICB9O1xyXG5cclxuICBjb25zdCBnZXRDaGFuZ2VDb2xvciA9IChjaGFuZ2U6IG51bWJlciB8IHVuZGVmaW5lZCk6IHN0cmluZyA9PiB7XHJcbiAgICBpZiAoY2hhbmdlID09PSB1bmRlZmluZWQgfHwgY2hhbmdlID09PSAwKSByZXR1cm4gJ3RleHQtZ3JheS02MDAnO1xyXG4gICAgcmV0dXJuIGNoYW5nZSA+IDAgPyAndGV4dC1ncmVlbi02MDAnIDogJ3RleHQtcmVkLTYwMCc7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZm9ybWF0Vm9sdW1lID0gKHZvbHVtZTogbnVtYmVyIHwgdW5kZWZpbmVkKTogc3RyaW5nID0+IHtcclxuICAgIGlmICh2b2x1bWUgPT09IHVuZGVmaW5lZCB8fCB2b2x1bWUgPT09IDApIHJldHVybiAnLSc7XHJcbiAgICBpZiAodm9sdW1lID49IDEwMDAwMDAwKSByZXR1cm4gYCR7KHZvbHVtZSAvIDEwMDAwMDAwKS50b0ZpeGVkKDEpfUNyYDtcclxuICAgIGlmICh2b2x1bWUgPj0gMTAwMDAwKSByZXR1cm4gYCR7KHZvbHVtZSAvIDEwMDAwMCkudG9GaXhlZCgxKX1MYDtcclxuICAgIGlmICh2b2x1bWUgPj0gMTAwMCkgcmV0dXJuIGAkeyh2b2x1bWUgLyAxMDAwKS50b0ZpeGVkKDEpfUtgO1xyXG4gICAgcmV0dXJuIHZvbHVtZS50b1N0cmluZygpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IFNvcnRJY29uID0gKHsgZmllbGQgfTogeyBmaWVsZDoga2V5b2YgSW5zdHJ1bWVudCB9KSA9PiB7XHJcbiAgICBpZiAoc29ydEZpZWxkICE9PSBmaWVsZCkge1xyXG4gICAgICByZXR1cm4gPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPuKGlTwvc3Bhbj47XHJcbiAgICB9XHJcbiAgICByZXR1cm4gc29ydERpcmVjdGlvbiA9PT0gJ2FzYycgPyA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwXCI+4oaRPC9zcGFuPiA6IDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDBcIj7ihpM8L3NwYW4+O1xyXG4gIH07XHJcblxyXG4gIGlmIChsb2FkaW5nKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdsYXNzIHJvdW5kZWQtMnhsIHNoYWRvdy1sZyBwLTZcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtNjRcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3Bpbm5lciB3LTggaC04IG1yLTNcIj48L2Rpdj5cclxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5Mb2FkaW5nIGluc3RydW1lbnRzLi4uPC9zcGFuPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICBpZiAoaW5zdHJ1bWVudHMubGVuZ3RoID09PSAwKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdsYXNzIHJvdW5kZWQtMnhsIHNoYWRvdy1sZyBwLTZcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHRleHQtbGdcIj5ObyBpbnN0cnVtZW50cyBmb3VuZDwvcD5cclxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgdGV4dC1zbSBtdC0yXCI+VHJ5IGFkanVzdGluZyB5b3VyIGZpbHRlcnM8L3A+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImdsYXNzIHJvdW5kZWQtMnhsIHNoYWRvdy1sZyBvdmVyZmxvdy1oaWRkZW5cIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XHJcbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5NYXJrZXQgSW5zdHJ1bWVudHM8L2gyPlxyXG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgdGV4dC1zbSBtdC0xXCI+XHJcbiAgICAgICAgICBTaG93aW5nIHtzb3J0ZWRJbnN0cnVtZW50cy5sZW5ndGh9IGluc3RydW1lbnRzXHJcbiAgICAgICAgPC9wPlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3ZlcmZsb3cteC1hdXRvIGN1c3RvbS1zY3JvbGxiYXJcIiBzdHlsZT17eyBtYXhIZWlnaHQ6ICc2MDBweCcgfX0+XHJcbiAgICAgICAgPHRhYmxlIGNsYXNzTmFtZT1cIm1hcmtldC10YWJsZVwiPlxyXG4gICAgICAgICAgPHRoZWFkIGNsYXNzTmFtZT1cInN0aWNreSB0b3AtMCBiZy1ncmF5LTUwIHotMTBcIj5cclxuICAgICAgICAgICAgPHRyPlxyXG4gICAgICAgICAgICAgIDx0aFxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY3Vyc29yLXBvaW50ZXIgaG92ZXI6YmctZ3JheS0xMDBcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlU29ydCgnc2VjdXJpdHlJZCcpfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICAgIFNlY3VyaXR5IElEXHJcbiAgICAgICAgICAgICAgICAgIDxTb3J0SWNvbiBmaWVsZD1cInNlY3VyaXR5SWRcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC90aD5cclxuICAgICAgICAgICAgICA8dGhcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImN1cnNvci1wb2ludGVyIGhvdmVyOmJnLWdyYXktMTAwXCJcclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVNvcnQoJ3N5bWJvbCcpfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICAgIFN5bWJvbFxyXG4gICAgICAgICAgICAgICAgICA8U29ydEljb24gZmllbGQ9XCJzeW1ib2xcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC90aD5cclxuICAgICAgICAgICAgICA8dGhcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImN1cnNvci1wb2ludGVyIGhvdmVyOmJnLWdyYXktMTAwXCJcclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVNvcnQoJ2Rpc3BsYXlOYW1lJyl9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgICAgTmFtZVxyXG4gICAgICAgICAgICAgICAgICA8U29ydEljb24gZmllbGQ9XCJkaXNwbGF5TmFtZVwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L3RoPlxyXG4gICAgICAgICAgICAgIDx0aCBcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImN1cnNvci1wb2ludGVyIGhvdmVyOmJnLWdyYXktMTAwXCJcclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVNvcnQoJ2V4Y2hhbmdlJyl9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgICAgRXhjaGFuZ2VcclxuICAgICAgICAgICAgICAgICAgPFNvcnRJY29uIGZpZWxkPVwiZXhjaGFuZ2VcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC90aD5cclxuICAgICAgICAgICAgICA8dGggXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1ncmF5LTEwMFwiXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTb3J0KCdpbnN0cnVtZW50VHlwZScpfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICAgIFR5cGVcclxuICAgICAgICAgICAgICAgICAgPFNvcnRJY29uIGZpZWxkPVwiaW5zdHJ1bWVudFR5cGVcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC90aD5cclxuICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPkxUUDwvdGg+XHJcbiAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5DaGFuZ2U8L3RoPlxyXG4gICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+Vm9sdW1lPC90aD5cclxuICAgICAgICAgICAgICA8dGggXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1ncmF5LTEwMFwiXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTb3J0KCdsb3RTaXplJyl9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgICAgTG90IFNpemVcclxuICAgICAgICAgICAgICAgICAgPFNvcnRJY29uIGZpZWxkPVwibG90U2l6ZVwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L3RoPlxyXG4gICAgICAgICAgICA8L3RyPlxyXG4gICAgICAgICAgPC90aGVhZD5cclxuICAgICAgICAgIDx0Ym9keT5cclxuICAgICAgICAgICAge3NvcnRlZEluc3RydW1lbnRzLm1hcCgoaW5zdHJ1bWVudCkgPT4ge1xyXG4gICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSBtYXJrZXREYXRhLmdldChpbnN0cnVtZW50LnNlY3VyaXR5SWQpO1xyXG4gICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICA8dHJcclxuICAgICAgICAgICAgICAgICAga2V5PXtpbnN0cnVtZW50LnNlY3VyaXR5SWR9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImhvdmVyOmJnLWdyYXktNTAgY3Vyc29yLXBvaW50ZXIgdHJhbnNpdGlvbi1jb2xvcnNcIlxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvbkluc3RydW1lbnRTZWxlY3Q/LihpbnN0cnVtZW50KX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cImZvbnQtbW9ubyB0ZXh0LXNtIHRleHQtZ3JheS03MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICB7aW5zdHJ1bWVudC5zZWN1cml0eUlkfVxyXG4gICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ibHVlLTYwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHtpbnN0cnVtZW50LnN5bWJvbH1cclxuICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cIm1heC13LXhzIHRydW5jYXRlXCIgdGl0bGU9e2luc3RydW1lbnQuZGlzcGxheU5hbWV9PlxyXG4gICAgICAgICAgICAgICAgICAgIHtpbnN0cnVtZW50LmRpc3BsYXlOYW1lfVxyXG4gICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICA8dGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIuNSBweS0wLjUgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAge2luc3RydW1lbnQuZXhjaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICA8dGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIuNSBweS0wLjUgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAge2luc3RydW1lbnQuaW5zdHJ1bWVudFR5cGV9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwidGV4dC1yaWdodCBmb250LW1lZGl1bVwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXRQcmljZShkYXRhPy5sdHApfVxyXG4gICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPXtgdGV4dC1yaWdodCBmb250LW1lZGl1bSAke2dldENoYW5nZUNvbG9yKGRhdGE/LmNoYW5nZSl9YH0+XHJcbiAgICAgICAgICAgICAgICAgICAge2Zvcm1hdENoYW5nZShkYXRhPy5jaGFuZ2UsIGRhdGE/LmNoYW5nZVBlcmNlbnQpfVxyXG4gICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXRWb2x1bWUoZGF0YT8udm9sdW1lKX1cclxuICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cclxuICAgICAgICAgICAgICAgICAgICB7aW5zdHJ1bWVudC5sb3RTaXplLnRvTG9jYWxlU3RyaW5nKCl9XHJcbiAgICAgICAgICAgICAgICAgIDwvdGQ+XHJcbiAgICAgICAgICAgICAgICA8L3RyPlxyXG4gICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgIH0pfVxyXG4gICAgICAgICAgPC90Ym9keT5cclxuICAgICAgICA8L3RhYmxlPlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHtzb3J0ZWRJbnN0cnVtZW50cy5sZW5ndGggPiAxMDAgJiYgKFxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJnLWdyYXktNTAgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgdGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgU2hvd2luZyBmaXJzdCAxMDAgaW5zdHJ1bWVudHMuIFVzZSBmaWx0ZXJzIHRvIG5hcnJvdyBkb3duIHJlc3VsdHMuXHJcbiAgICAgICAgICA8L3A+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICl9XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgSW5zdHJ1bWVudFRhYmxlO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZU1lbW8iLCJJbnN0cnVtZW50VGFibGUiLCJpbnN0cnVtZW50cyIsIm1hcmtldERhdGEiLCJvbkluc3RydW1lbnRTZWxlY3QiLCJsb2FkaW5nIiwic29ydEZpZWxkIiwic2V0U29ydEZpZWxkIiwic29ydERpcmVjdGlvbiIsInNldFNvcnREaXJlY3Rpb24iLCJzb3J0ZWRJbnN0cnVtZW50cyIsInNvcnQiLCJhIiwiYiIsImFWYWx1ZSIsImJWYWx1ZSIsInVuZGVmaW5lZCIsImNvbXBhcmlzb24iLCJoYW5kbGVTb3J0IiwiZmllbGQiLCJmb3JtYXRQcmljZSIsInByaWNlIiwidG9GaXhlZCIsImZvcm1hdENoYW5nZSIsImNoYW5nZSIsImNoYW5nZVBlcmNlbnQiLCJzaWduIiwiZ2V0Q2hhbmdlQ29sb3IiLCJmb3JtYXRWb2x1bWUiLCJ2b2x1bWUiLCJ0b1N0cmluZyIsIlNvcnRJY29uIiwic3BhbiIsImNsYXNzTmFtZSIsImRpdiIsImxlbmd0aCIsInAiLCJoMiIsInN0eWxlIiwibWF4SGVpZ2h0IiwidGFibGUiLCJ0aGVhZCIsInRyIiwidGgiLCJvbkNsaWNrIiwidGJvZHkiLCJtYXAiLCJpbnN0cnVtZW50IiwiZGF0YSIsImdldCIsInNlY3VyaXR5SWQiLCJ0ZCIsInN5bWJvbCIsInRpdGxlIiwiZGlzcGxheU5hbWUiLCJleGNoYW5nZSIsImluc3RydW1lbnRUeXBlIiwibHRwIiwibG90U2l6ZSIsInRvTG9jYWxlU3RyaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/InstrumentTable.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Stats.tsx":
/*!**********************************!*\
  !*** ./src/components/Stats.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Stats = (param)=>{\n    let { totalInstruments, filteredInstruments, marketDataCount, connected, connectionStats } = param;\n    const formatUptime = (seconds)=>{\n        if (seconds < 60) return \"\".concat(seconds, \"s\");\n        if (seconds < 3600) return \"\".concat(Math.floor(seconds / 60), \"m \").concat(seconds % 60, \"s\");\n        const hours = Math.floor(seconds / 3600);\n        const minutes = Math.floor(seconds % 3600 / 60);\n        return \"\".concat(hours, \"h \").concat(minutes, \"m\");\n    };\n    const formatLastUpdate = (date)=>{\n        if (!date) return 'Never';\n        const now = new Date();\n        const diffMs = now.getTime() - date.getTime();\n        const diffSeconds = Math.floor(diffMs / 1000);\n        if (diffSeconds < 60) return \"\".concat(diffSeconds, \"s ago\");\n        if (diffSeconds < 3600) return \"\".concat(Math.floor(diffSeconds / 60), \"m ago\");\n        return date.toLocaleTimeString();\n    };\n    const basicStats = [\n        {\n            label: 'Total Instruments',\n            value: totalInstruments.toLocaleString(),\n            icon: '📊',\n            color: 'bg-blue-500',\n            description: 'Available instruments'\n        },\n        {\n            label: 'Filtered Results',\n            value: filteredInstruments.toLocaleString(),\n            icon: '🔍',\n            color: 'bg-purple-500',\n            description: 'Matching filters'\n        },\n        {\n            label: 'Live Data',\n            value: marketDataCount.toLocaleString(),\n            icon: '📈',\n            color: connected ? 'bg-green-500' : 'bg-gray-500',\n            description: connectionStats ? \"\".concat(connectionStats.connectedInstruments, \" active\") : 'Market data points'\n        },\n        {\n            label: 'Connection',\n            value: connected ? 'Active' : 'Inactive',\n            icon: connected ? '🟢' : '🔴',\n            color: connected ? 'bg-green-500' : 'bg-red-500',\n            description: connectionStats ? \"\".concat(connectionStats.messagesReceived, \" messages\") : 'WebSocket status'\n        }\n    ];\n    const enhancedStats = connectionStats ? [\n        {\n            label: 'Cache Size',\n            value: connectionStats.cacheSize.toLocaleString(),\n            icon: '💾',\n            color: 'bg-indigo-500',\n            description: connectionStats.isAutoSaving ? 'Auto-saving' : 'Manual save'\n        },\n        {\n            label: 'Last Update',\n            value: formatLastUpdate(connectionStats.lastUpdate),\n            icon: '🕒',\n            color: 'bg-orange-500',\n            description: 'Data freshness'\n        },\n        {\n            label: 'Uptime',\n            value: formatUptime(connectionStats.connectionUptime),\n            icon: '⏱️',\n            color: 'bg-teal-500',\n            description: 'Connection stability'\n        },\n        {\n            label: 'Reconnects',\n            value: connectionStats.reconnectAttempts.toString(),\n            icon: connectionStats.reconnectAttempts > 0 ? '🔄' : '✅',\n            color: connectionStats.reconnectAttempts > 0 ? 'bg-yellow-500' : 'bg-green-500',\n            description: 'Connection reliability'\n        }\n    ] : [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: basicStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glass rounded-xl shadow-lg p-4 card-hover\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: stat.label\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900 mt-1\",\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: stat.description\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl ml-3\",\n                                        children: stat.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 h-1 rounded-full \".concat(stat.color)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined),\n            enhancedStats.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800 mb-3 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl mr-2\",\n                                children: \"⚡\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Real-time Connection Stats\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: enhancedStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"glass rounded-xl shadow-lg p-4 card-hover border-l-4 border-l-blue-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: stat.label\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-gray-900 mt-1\",\n                                                        children: stat.value\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: stat.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl ml-3\",\n                                                children: stat.icon\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3 h-1 rounded-full \".concat(stat.color)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Stats;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Stats);\nvar _c;\n$RefreshReg$(_c, \"Stats\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Stats.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useEnhancedMarketData.ts":
/*!********************************************!*\
  !*** ./src/hooks/useEnhancedMarketData.ts ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useEnhancedMarketData: () => (/* binding */ useEnhancedMarketData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * Enhanced Market Data Hook\r\n * Provides optimized access to market data with automatic initialization\r\n * Features:\r\n * - Automatic WebSocket connection management\r\n * - Optimized re-rendering with selective subscriptions\r\n * - Auto-save functionality\r\n * - Error recovery and reconnection\r\n */ \nconst useEnhancedMarketData = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { autoConnect = true, autoLoadCache = true, autoSaveInterval = 30000, reconnectOnError = true, maxReconnectAttempts = 5 } = options;\n    // Temporary fallback implementation\n    const connection = {\n        status: 'disconnected',\n        isConnected: false,\n        error: null,\n        connectionStats: {\n            totalMessages: 0,\n            connectionUptime: 0\n        }\n    };\n    const marketDataMap = new Map();\n    const cache = {\n        isLoaded: false,\n        totalCacheSize: 0,\n        pendingUpdates: 0,\n        lastCacheUpdate: null\n    };\n    const ui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useEnhancedMarketData.useMemo[ui]\": ()=>({\n                filters: {},\n                sortConfig: {\n                    field: 'symbol',\n                    direction: 'asc'\n                },\n                selectedInstruments: new Set(),\n                viewMode: 'table',\n                autoRefresh: true\n            })\n    }[\"useEnhancedMarketData.useMemo[ui]\"], []);\n    const isLoading = false;\n    // Store actions - temporary mock functions with useCallback to fix dependency warnings\n    const initializeConnection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[initializeConnection]\": async ()=>{}\n    }[\"useEnhancedMarketData.useCallback[initializeConnection]\"], []);\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[connect]\": async ()=>{}\n    }[\"useEnhancedMarketData.useCallback[connect]\"], []);\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[disconnect]\": ()=>{}\n    }[\"useEnhancedMarketData.useCallback[disconnect]\"], []);\n    const reconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[reconnect]\": async ()=>{}\n    }[\"useEnhancedMarketData.useCallback[reconnect]\"], []);\n    const loadFromCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[loadFromCache]\": async ()=>{}\n    }[\"useEnhancedMarketData.useCallback[loadFromCache]\"], []);\n    const saveToCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[saveToCache]\": async (force)=>{}\n    }[\"useEnhancedMarketData.useCallback[saveToCache]\"], []);\n    const clearCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[clearCache]\": async ()=>{}\n    }[\"useEnhancedMarketData.useCallback[clearCache]\"], []);\n    const setFilters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[setFilters]\": (filters)=>{}\n    }[\"useEnhancedMarketData.useCallback[setFilters]\"], []);\n    const setSortConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[setSortConfig]\": (field, direction)=>{}\n    }[\"useEnhancedMarketData.useCallback[setSortConfig]\"], []);\n    const getFilteredMarketData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[getFilteredMarketData]\": ()=>[]\n    }[\"useEnhancedMarketData.useCallback[getFilteredMarketData]\"], []);\n    const getSortedMarketData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[getSortedMarketData]\": ()=>[]\n    }[\"useEnhancedMarketData.useCallback[getSortedMarketData]\"], []);\n    const getMarketDataBySecurityId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[getMarketDataBySecurityId]\": (securityId)=>undefined\n    }[\"useEnhancedMarketData.useCallback[getMarketDataBySecurityId]\"], []);\n    const getMarketDataBySymbol = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[getMarketDataBySymbol]\": (symbol)=>undefined\n    }[\"useEnhancedMarketData.useCallback[getMarketDataBySymbol]\"], []);\n    const subscribeToInstrument = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[subscribeToInstrument]\": (securityId)=>{}\n    }[\"useEnhancedMarketData.useCallback[subscribeToInstrument]\"], []);\n    const unsubscribeFromInstrument = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[unsubscribeFromInstrument]\": (securityId)=>{}\n    }[\"useEnhancedMarketData.useCallback[unsubscribeFromInstrument]\"], []);\n    const reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[reset]\": ()=>{}\n    }[\"useEnhancedMarketData.useCallback[reset]\"], []);\n    // Refs for intervals and tracking\n    const autoSaveIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const initializationRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Convert Map to Array for components that expect arrays\n    const marketDataArray = Array.from(marketDataMap.values());\n    // Initialize connection and cache on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEnhancedMarketData.useEffect\": ()=>{\n            const initialize = {\n                \"useEnhancedMarketData.useEffect.initialize\": async ()=>{\n                    if (initializationRef.current) return;\n                    initializationRef.current = true;\n                    console.log('🚀 Enhanced Hook: Initializing...');\n                    try {\n                        // Load cache first for instant data display\n                        if (autoLoadCache && !cache.isLoaded) {\n                            await loadFromCache();\n                        }\n                        // Initialize WebSocket connection\n                        if (autoConnect && connection.status === 'disconnected') {\n                            await initializeConnection();\n                        }\n                    } catch (error) {\n                        console.error('❌ Enhanced Hook: Initialization failed:', error);\n                    }\n                }\n            }[\"useEnhancedMarketData.useEffect.initialize\"];\n            initialize();\n            // Cleanup on unmount\n            return ({\n                \"useEnhancedMarketData.useEffect\": ()=>{\n                    if (autoSaveIntervalRef.current) {\n                        clearInterval(autoSaveIntervalRef.current);\n                    }\n                }\n            })[\"useEnhancedMarketData.useEffect\"];\n        }\n    }[\"useEnhancedMarketData.useEffect\"], [\n        autoConnect,\n        autoLoadCache,\n        cache.isLoaded,\n        connection.status,\n        initializeConnection,\n        loadFromCache\n    ]);\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEnhancedMarketData.useEffect\": ()=>{\n            if (autoSaveInterval > 0 && marketDataArray.length > 0) {\n                if (autoSaveIntervalRef.current) {\n                    clearInterval(autoSaveIntervalRef.current);\n                }\n                autoSaveIntervalRef.current = setInterval({\n                    \"useEnhancedMarketData.useEffect\": ()=>{\n                        saveToCache();\n                    }\n                }[\"useEnhancedMarketData.useEffect\"], autoSaveInterval);\n                return ({\n                    \"useEnhancedMarketData.useEffect\": ()=>{\n                        if (autoSaveIntervalRef.current) {\n                            clearInterval(autoSaveIntervalRef.current);\n                        }\n                    }\n                })[\"useEnhancedMarketData.useEffect\"];\n            }\n        }\n    }[\"useEnhancedMarketData.useEffect\"], [\n        autoSaveInterval,\n        marketDataArray.length,\n        saveToCache\n    ]);\n    // Auto-reconnect on error\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEnhancedMarketData.useEffect\": ()=>{\n            if (reconnectOnError && connection.status === 'error' && reconnectAttemptsRef.current < maxReconnectAttempts) {\n                const retryDelay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);\n                console.log(\"\\uD83D\\uDD04 Enhanced Hook: Auto-reconnecting in \".concat(retryDelay, \"ms (attempt \").concat(reconnectAttemptsRef.current + 1, \"/\").concat(maxReconnectAttempts, \")\"));\n                const timeoutId = setTimeout({\n                    \"useEnhancedMarketData.useEffect.timeoutId\": async ()=>{\n                        try {\n                            reconnectAttemptsRef.current++;\n                            await reconnect();\n                        } catch (error) {\n                            console.error('❌ Enhanced Hook: Auto-reconnect failed:', error);\n                        }\n                    }\n                }[\"useEnhancedMarketData.useEffect.timeoutId\"], retryDelay);\n                return ({\n                    \"useEnhancedMarketData.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"useEnhancedMarketData.useEffect\"];\n            }\n        }\n    }[\"useEnhancedMarketData.useEffect\"], [\n        connection.status,\n        reconnect,\n        reconnectOnError,\n        maxReconnectAttempts\n    ]);\n    // Reset reconnect attempts on successful connection\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEnhancedMarketData.useEffect\": ()=>{\n            if (connection.status === 'connected') {\n                reconnectAttemptsRef.current = 0;\n            }\n        }\n    }[\"useEnhancedMarketData.useEffect\"], [\n        connection.status\n    ]);\n    // Optimized data access functions\n    const getFilteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[getFilteredData]\": ()=>{\n            return getFilteredMarketData();\n        }\n    }[\"useEnhancedMarketData.useCallback[getFilteredData]\"], [\n        getFilteredMarketData\n    ]);\n    const getSortedData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[getSortedData]\": ()=>{\n            return getSortedMarketData();\n        }\n    }[\"useEnhancedMarketData.useCallback[getSortedData]\"], [\n        getSortedMarketData\n    ]);\n    const getDataBySecurityId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[getDataBySecurityId]\": (securityId)=>{\n            return getMarketDataBySecurityId(securityId);\n        }\n    }[\"useEnhancedMarketData.useCallback[getDataBySecurityId]\"], [\n        getMarketDataBySecurityId\n    ]);\n    const getDataBySymbol = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[getDataBySymbol]\": (symbol)=>{\n            return getMarketDataBySymbol(symbol);\n        }\n    }[\"useEnhancedMarketData.useCallback[getDataBySymbol]\"], [\n        getMarketDataBySymbol\n    ]);\n    // Enhanced filter functions\n    const updateFilters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[updateFilters]\": (filters)=>{\n            setFilters(filters);\n        }\n    }[\"useEnhancedMarketData.useCallback[updateFilters]\"], [\n        setFilters,\n        ui\n    ]);\n    const updateSort = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[updateSort]\": (field, direction)=>{\n            setSortConfig(field, direction);\n        }\n    }[\"useEnhancedMarketData.useCallback[updateSort]\"], [\n        setSortConfig\n    ]);\n    // Subscription management\n    const subscribe = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[subscribe]\": (securityId)=>{\n            subscribeToInstrument(securityId);\n        }\n    }[\"useEnhancedMarketData.useCallback[subscribe]\"], [\n        subscribeToInstrument\n    ]);\n    const unsubscribe = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[unsubscribe]\": (securityId)=>{\n            unsubscribeFromInstrument(securityId);\n        }\n    }[\"useEnhancedMarketData.useCallback[unsubscribe]\"], [\n        unsubscribeFromInstrument\n    ]);\n    // Connection management\n    const forceReconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[forceReconnect]\": async ()=>{\n            try {\n                reconnectAttemptsRef.current = 0; // Reset attempts for manual reconnect\n                await reconnect();\n            } catch (error) {\n                console.error('❌ Enhanced Hook: Manual reconnect failed:', error);\n                throw error;\n            }\n        }\n    }[\"useEnhancedMarketData.useCallback[forceReconnect]\"], [\n        reconnect\n    ]);\n    const forceRefresh = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[forceRefresh]\": async ()=>{\n            try {\n                await loadFromCache();\n            } catch (error) {\n                console.error('❌ Enhanced Hook: Force refresh failed:', error);\n                throw error;\n            }\n        }\n    }[\"useEnhancedMarketData.useCallback[forceRefresh]\"], [\n        loadFromCache\n    ]);\n    const forceSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedMarketData.useCallback[forceSave]\": async ()=>{\n            try {\n                await saveToCache(true);\n            } catch (error) {\n                console.error('❌ Enhanced Hook: Force save failed:', error);\n                throw error;\n            }\n        }\n    }[\"useEnhancedMarketData.useCallback[forceSave]\"], [\n        saveToCache\n    ]);\n    // Stats and computed values\n    const stats = {\n        totalInstruments: marketDataArray.length,\n        connectedInstruments: marketDataArray.filter((item)=>item.ltp && item.ltp > 0).length,\n        lastUpdate: null,\n        cacheSize: cache.totalCacheSize,\n        connectionUptime: connection.connectionStats.connectionUptime,\n        messagesReceived: connection.connectionStats.totalMessages,\n        reconnectAttempts: reconnectAttemptsRef.current,\n        isAutoSaving: autoSaveIntervalRef.current !== null\n    };\n    return {\n        // Data\n        marketData: marketDataArray,\n        marketDataMap,\n        filteredData: getFilteredData(),\n        sortedData: getSortedData(),\n        // Connection state\n        isConnected: connection.isConnected,\n        connectionStatus: connection.status,\n        connectionError: connection.error,\n        connectionStats: connection.connectionStats,\n        // Cache state\n        cacheLoaded: cache.isLoaded,\n        cacheUpdating: cache.pendingUpdates > 0,\n        lastCacheUpdate: cache.lastCacheUpdate,\n        // UI state\n        filters: ui.filters,\n        sortConfig: ui.sortConfig,\n        selectedInstruments: ui.selectedInstruments,\n        viewMode: ui.viewMode,\n        autoRefresh: ui.autoRefresh,\n        // Loading states\n        isLoading,\n        isInitializing: !initializationRef.current,\n        // Data access functions\n        getDataBySecurityId,\n        getDataBySymbol,\n        getFilteredData,\n        getSortedData,\n        // Actions\n        updateFilters,\n        updateSort,\n        subscribe,\n        unsubscribe,\n        // Connection management\n        connect,\n        disconnect,\n        reconnect: forceReconnect,\n        // Cache management\n        refresh: forceRefresh,\n        save: forceSave,\n        clearCache,\n        // Utility\n        reset,\n        stats,\n        // Advanced actions (expose if needed)\n        _store: {\n            setFilters,\n            setSortConfig,\n            subscribeToInstrument,\n            unsubscribeFromInstrument\n        }\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useEnhancedMarketData);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VFbmhhbmNlZE1hcmtldERhdGEudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBOzs7Ozs7OztDQVFDLEdBRStEO0FBWXpELE1BQU1JLHdCQUF3QjtRQUFDQywyRUFBd0MsQ0FBQztJQUM3RSxNQUFNLEVBQ0pDLGNBQWMsSUFBSSxFQUNsQkMsZ0JBQWdCLElBQUksRUFDcEJDLG1CQUFtQixLQUFLLEVBQ3hCQyxtQkFBbUIsSUFBSSxFQUN2QkMsdUJBQXVCLENBQUMsRUFDekIsR0FBR0w7SUFFSixvQ0FBb0M7SUFDcEMsTUFBTU0sYUFBYTtRQUNqQkMsUUFBUTtRQUNSQyxhQUFhO1FBQ2JDLE9BQU87UUFDUEMsaUJBQWlCO1lBQ2ZDLGVBQWU7WUFDZkMsa0JBQWtCO1FBQ3BCO0lBQ0Y7SUFDQSxNQUFNQyxnQkFBZ0IsSUFBSUM7SUFDMUIsTUFBTUMsUUFBUTtRQUNaQyxVQUFVO1FBQ1ZDLGdCQUFnQjtRQUNoQkMsZ0JBQWdCO1FBQ2hCQyxpQkFBaUI7SUFDbkI7SUFDQSxNQUFNQyxLQUFLdEIsOENBQU9BOzZDQUFDLElBQU87Z0JBQ3hCdUIsU0FBUyxDQUFDO2dCQUNWQyxZQUFZO29CQUFFQyxPQUFPO29CQUFVQyxXQUFXO2dCQUFhO2dCQUN2REMscUJBQXFCLElBQUlDO2dCQUN6QkMsVUFBVTtnQkFDVkMsYUFBYTtZQUNmOzRDQUFJLEVBQUU7SUFDTixNQUFNQyxZQUFZO0lBRWxCLHVGQUF1RjtJQUN2RixNQUFNQyx1QkFBdUJsQyxrREFBV0E7bUVBQUMsV0FBYTtrRUFBRyxFQUFFO0lBQzNELE1BQU1tQyxVQUFVbkMsa0RBQVdBO3NEQUFDLFdBQWE7cURBQUcsRUFBRTtJQUM5QyxNQUFNb0MsYUFBYXBDLGtEQUFXQTt5REFBQyxLQUFPO3dEQUFHLEVBQUU7SUFDM0MsTUFBTXFDLFlBQVlyQyxrREFBV0E7d0RBQUMsV0FBYTt1REFBRyxFQUFFO0lBQ2hELE1BQU1zQyxnQkFBZ0J0QyxrREFBV0E7NERBQUMsV0FBYTsyREFBRyxFQUFFO0lBQ3BELE1BQU11QyxjQUFjdkMsa0RBQVdBOzBEQUFDLE9BQU93QyxTQUFxQjt5REFBRyxFQUFFO0lBQ2pFLE1BQU1DLGFBQWF6QyxrREFBV0E7eURBQUMsV0FBYTt3REFBRyxFQUFFO0lBQ2pELE1BQU0wQyxhQUFhMUMsa0RBQVdBO3lEQUFDLENBQUN5QixXQUFrQjt3REFBRyxFQUFFO0lBQ3ZELE1BQU1rQixnQkFBZ0IzQyxrREFBV0E7NERBQUMsQ0FBQzJCLE9BQWVDLGFBQWdDOzJEQUFHLEVBQUU7SUFDdkYsTUFBTWdCLHdCQUF3QjVDLGtEQUFXQTtvRUFBQyxJQUFNLEVBQUU7bUVBQUUsRUFBRTtJQUN0RCxNQUFNNkMsc0JBQXNCN0Msa0RBQVdBO2tFQUFDLElBQU0sRUFBRTtpRUFBRSxFQUFFO0lBQ3BELE1BQU04Qyw0QkFBNEI5QyxrREFBV0E7d0VBQUMsQ0FBQytDLGFBQXVCQzt1RUFBVyxFQUFFO0lBQ25GLE1BQU1DLHdCQUF3QmpELGtEQUFXQTtvRUFBQyxDQUFDa0QsU0FBbUJGO21FQUFXLEVBQUU7SUFDM0UsTUFBTUcsd0JBQXdCbkQsa0RBQVdBO29FQUFDLENBQUMrQyxjQUF3QjttRUFBRyxFQUFFO0lBQ3hFLE1BQU1LLDRCQUE0QnBELGtEQUFXQTt3RUFBQyxDQUFDK0MsY0FBd0I7dUVBQUcsRUFBRTtJQUM1RSxNQUFNTSxRQUFRckQsa0RBQVdBO29EQUFDLEtBQU87bURBQUcsRUFBRTtJQUV0QyxrQ0FBa0M7SUFDbEMsTUFBTXNELHNCQUFzQnJELDZDQUFNQSxDQUF3QjtJQUMxRCxNQUFNc0QsdUJBQXVCdEQsNkNBQU1BLENBQUM7SUFDcEMsTUFBTXVELG9CQUFvQnZELDZDQUFNQSxDQUFDO0lBRWpDLHlEQUF5RDtJQUN6RCxNQUFNd0Qsa0JBQWtCQyxNQUFNQyxJQUFJLENBQUMxQyxjQUFjMkMsTUFBTTtJQUV2RCwyQ0FBMkM7SUFDM0M3RCxnREFBU0E7MkNBQUM7WUFDUixNQUFNOEQ7OERBQWE7b0JBQ2pCLElBQUlMLGtCQUFrQk0sT0FBTyxFQUFFO29CQUMvQk4sa0JBQWtCTSxPQUFPLEdBQUc7b0JBRTVCQyxRQUFRQyxHQUFHLENBQUM7b0JBRVosSUFBSTt3QkFDRiw0Q0FBNEM7d0JBQzVDLElBQUkxRCxpQkFBaUIsQ0FBQ2EsTUFBTUMsUUFBUSxFQUFFOzRCQUNwQyxNQUFNa0I7d0JBQ1I7d0JBRUEsa0NBQWtDO3dCQUNsQyxJQUFJakMsZUFBZUssV0FBV0MsTUFBTSxLQUFLLGdCQUFnQjs0QkFDdkQsTUFBTXVCO3dCQUNSO29CQUNGLEVBQUUsT0FBT3JCLE9BQU87d0JBQ2RrRCxRQUFRbEQsS0FBSyxDQUFDLDJDQUEyQ0E7b0JBQzNEO2dCQUNGOztZQUVBZ0Q7WUFFQSxxQkFBcUI7WUFDckI7bURBQU87b0JBQ0wsSUFBSVAsb0JBQW9CUSxPQUFPLEVBQUU7d0JBQy9CRyxjQUFjWCxvQkFBb0JRLE9BQU87b0JBQzNDO2dCQUNGOztRQUNGOzBDQUFHO1FBQUN6RDtRQUFhQztRQUFlYSxNQUFNQyxRQUFRO1FBQUVWLFdBQVdDLE1BQU07UUFBRXVCO1FBQXNCSTtLQUFjO0lBRXZHLDBCQUEwQjtJQUMxQnZDLGdEQUFTQTsyQ0FBQztZQUNSLElBQUlRLG1CQUFtQixLQUFLa0QsZ0JBQWdCUyxNQUFNLEdBQUcsR0FBRztnQkFDdEQsSUFBSVosb0JBQW9CUSxPQUFPLEVBQUU7b0JBQy9CRyxjQUFjWCxvQkFBb0JRLE9BQU87Z0JBQzNDO2dCQUVBUixvQkFBb0JRLE9BQU8sR0FBR0s7dURBQVk7d0JBQ3hDNUI7b0JBQ0Y7c0RBQUdoQztnQkFFSDt1REFBTzt3QkFDTCxJQUFJK0Msb0JBQW9CUSxPQUFPLEVBQUU7NEJBQy9CRyxjQUFjWCxvQkFBb0JRLE9BQU87d0JBQzNDO29CQUNGOztZQUNGO1FBQ0Y7MENBQUc7UUFBQ3ZEO1FBQWtCa0QsZ0JBQWdCUyxNQUFNO1FBQUUzQjtLQUFZO0lBRTFELDBCQUEwQjtJQUMxQnhDLGdEQUFTQTsyQ0FBQztZQUNSLElBQ0VTLG9CQUNBRSxXQUFXQyxNQUFNLEtBQUssV0FDdEI0QyxxQkFBcUJPLE9BQU8sR0FBR3JELHNCQUMvQjtnQkFDQSxNQUFNMkQsYUFBYUMsS0FBS0MsR0FBRyxDQUFDLE9BQU9ELEtBQUtFLEdBQUcsQ0FBQyxHQUFHaEIscUJBQXFCTyxPQUFPLEdBQUc7Z0JBRTlFQyxRQUFRQyxHQUFHLENBQ1Qsb0RBQW1FVCxPQUF6QmEsWUFBVyxnQkFBa0QzRCxPQUFwQzhDLHFCQUFxQk8sT0FBTyxHQUFHLEdBQUUsS0FBd0IsT0FBckJyRCxzQkFBcUI7Z0JBRzlILE1BQU0rRCxZQUFZQztpRUFBVzt3QkFDM0IsSUFBSTs0QkFDRmxCLHFCQUFxQk8sT0FBTzs0QkFDNUIsTUFBTXpCO3dCQUNSLEVBQUUsT0FBT3hCLE9BQU87NEJBQ2RrRCxRQUFRbEQsS0FBSyxDQUFDLDJDQUEyQ0E7d0JBQzNEO29CQUNGO2dFQUFHdUQ7Z0JBRUg7dURBQU8sSUFBTU0sYUFBYUY7O1lBQzVCO1FBQ0Y7MENBQUc7UUFBQzlELFdBQVdDLE1BQU07UUFBRTBCO1FBQVc3QjtRQUFrQkM7S0FBcUI7SUFFekUsb0RBQW9EO0lBQ3BEVixnREFBU0E7MkNBQUM7WUFDUixJQUFJVyxXQUFXQyxNQUFNLEtBQUssYUFBYTtnQkFDckM0QyxxQkFBcUJPLE9BQU8sR0FBRztZQUNqQztRQUNGOzBDQUFHO1FBQUNwRCxXQUFXQyxNQUFNO0tBQUM7SUFFdEIsa0NBQWtDO0lBQ2xDLE1BQU1nRSxrQkFBa0IzRSxrREFBV0E7OERBQUM7WUFDbEMsT0FBTzRDO1FBQ1Q7NkRBQUc7UUFBQ0E7S0FBc0I7SUFFMUIsTUFBTWdDLGdCQUFnQjVFLGtEQUFXQTs0REFBQztZQUNoQyxPQUFPNkM7UUFDVDsyREFBRztRQUFDQTtLQUFvQjtJQUV4QixNQUFNZ0Msc0JBQXNCN0Usa0RBQVdBO2tFQUFDLENBQUMrQztZQUN2QyxPQUFPRCwwQkFBMEJDO1FBQ25DO2lFQUFHO1FBQUNEO0tBQTBCO0lBRTlCLE1BQU1nQyxrQkFBa0I5RSxrREFBV0E7OERBQUMsQ0FBQ2tEO1lBQ25DLE9BQU9ELHNCQUFzQkM7UUFDL0I7NkRBQUc7UUFBQ0Q7S0FBc0I7SUFFMUIsNEJBQTRCO0lBQzVCLE1BQU04QixnQkFBZ0IvRSxrREFBV0E7NERBQUMsQ0FBQ3lCO1lBQ2pDaUIsV0FBV2pCO1FBQ2I7MkRBQUc7UUFBQ2lCO1FBQVlsQjtLQUFHO0lBRW5CLE1BQU13RCxhQUFhaEYsa0RBQVdBO3lEQUFDLENBQUMyQixPQUFlQztZQUM3Q2UsY0FBY2hCLE9BQU9DO1FBQ3ZCO3dEQUFHO1FBQUNlO0tBQWM7SUFFbEIsMEJBQTBCO0lBQzFCLE1BQU1zQyxZQUFZakYsa0RBQVdBO3dEQUFDLENBQUMrQztZQUM3Qkksc0JBQXNCSjtRQUN4Qjt1REFBRztRQUFDSTtLQUFzQjtJQUUxQixNQUFNK0IsY0FBY2xGLGtEQUFXQTswREFBQyxDQUFDK0M7WUFDL0JLLDBCQUEwQkw7UUFDNUI7eURBQUc7UUFBQ0s7S0FBMEI7SUFFOUIsd0JBQXdCO0lBQ3hCLE1BQU0rQixpQkFBaUJuRixrREFBV0E7NkRBQUM7WUFDakMsSUFBSTtnQkFDRnVELHFCQUFxQk8sT0FBTyxHQUFHLEdBQUcsc0NBQXNDO2dCQUN4RSxNQUFNekI7WUFDUixFQUFFLE9BQU94QixPQUFPO2dCQUNka0QsUUFBUWxELEtBQUssQ0FBQyw2Q0FBNkNBO2dCQUMzRCxNQUFNQTtZQUNSO1FBQ0Y7NERBQUc7UUFBQ3dCO0tBQVU7SUFFZCxNQUFNK0MsZUFBZXBGLGtEQUFXQTsyREFBQztZQUMvQixJQUFJO2dCQUNGLE1BQU1zQztZQUNSLEVBQUUsT0FBT3pCLE9BQU87Z0JBQ2RrRCxRQUFRbEQsS0FBSyxDQUFDLDBDQUEwQ0E7Z0JBQ3hELE1BQU1BO1lBQ1I7UUFDRjswREFBRztRQUFDeUI7S0FBYztJQUVsQixNQUFNK0MsWUFBWXJGLGtEQUFXQTt3REFBQztZQUM1QixJQUFJO2dCQUNGLE1BQU11QyxZQUFZO1lBQ3BCLEVBQUUsT0FBTzFCLE9BQU87Z0JBQ2RrRCxRQUFRbEQsS0FBSyxDQUFDLHVDQUF1Q0E7Z0JBQ3JELE1BQU1BO1lBQ1I7UUFDRjt1REFBRztRQUFDMEI7S0FBWTtJQUVoQiw0QkFBNEI7SUFDNUIsTUFBTStDLFFBQVE7UUFDWkMsa0JBQWtCOUIsZ0JBQWdCUyxNQUFNO1FBQ3hDc0Isc0JBQXNCL0IsZ0JBQWdCZ0MsTUFBTSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLQyxHQUFHLElBQUlELEtBQUtDLEdBQUcsR0FBRyxHQUFHekIsTUFBTTtRQUNyRjBCLFlBQVk7UUFDWkMsV0FBVzFFLE1BQU1FLGNBQWM7UUFDL0JMLGtCQUFrQk4sV0FBV0ksZUFBZSxDQUFDRSxnQkFBZ0I7UUFDN0Q4RSxrQkFBa0JwRixXQUFXSSxlQUFlLENBQUNDLGFBQWE7UUFDMURnRixtQkFBbUJ4QyxxQkFBcUJPLE9BQU87UUFDL0NrQyxjQUFjMUMsb0JBQW9CUSxPQUFPLEtBQUs7SUFDaEQ7SUFFQSxPQUFPO1FBQ0wsT0FBTztRQUNQbUMsWUFBWXhDO1FBQ1p4QztRQUNBaUYsY0FBY3ZCO1FBQ2R3QixZQUFZdkI7UUFFWixtQkFBbUI7UUFDbkJoRSxhQUFhRixXQUFXRSxXQUFXO1FBQ25Dd0Ysa0JBQWtCMUYsV0FBV0MsTUFBTTtRQUNuQzBGLGlCQUFpQjNGLFdBQVdHLEtBQUs7UUFDakNDLGlCQUFpQkosV0FBV0ksZUFBZTtRQUUzQyxjQUFjO1FBQ2R3RixhQUFhbkYsTUFBTUMsUUFBUTtRQUMzQm1GLGVBQWVwRixNQUFNRyxjQUFjLEdBQUc7UUFDdENDLGlCQUFpQkosTUFBTUksZUFBZTtRQUV0QyxXQUFXO1FBQ1hFLFNBQVNELEdBQUdDLE9BQU87UUFDbkJDLFlBQVlGLEdBQUdFLFVBQVU7UUFDekJHLHFCQUFxQkwsR0FBR0ssbUJBQW1CO1FBQzNDRSxVQUFVUCxHQUFHTyxRQUFRO1FBQ3JCQyxhQUFhUixHQUFHUSxXQUFXO1FBRTNCLGlCQUFpQjtRQUNqQkM7UUFDQXVFLGdCQUFnQixDQUFDaEQsa0JBQWtCTSxPQUFPO1FBRTFDLHdCQUF3QjtRQUN4QmU7UUFDQUM7UUFDQUg7UUFDQUM7UUFFQSxVQUFVO1FBQ1ZHO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBRUEsd0JBQXdCO1FBQ3hCL0M7UUFDQUM7UUFDQUMsV0FBVzhDO1FBRVgsbUJBQW1CO1FBQ25Cc0IsU0FBU3JCO1FBQ1RzQixNQUFNckI7UUFDTjVDO1FBRUEsVUFBVTtRQUNWWTtRQUNBaUM7UUFFQSxzQ0FBc0M7UUFDdENxQixRQUFRO1lBQ05qRTtZQUNBQztZQUNBUTtZQUNBQztRQUVGO0lBQ0Y7QUFDRixFQUFFO0FBRUYsaUVBQWVqRCxxQkFBcUJBLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxsb3ZlXFxkYXNoYm9hcmRcXGNzdi1tYXJrZXQtZGFzaGJvYXJkXFxzcmNcXGhvb2tzXFx1c2VFbmhhbmNlZE1hcmtldERhdGEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXHJcbiAqIEVuaGFuY2VkIE1hcmtldCBEYXRhIEhvb2tcclxuICogUHJvdmlkZXMgb3B0aW1pemVkIGFjY2VzcyB0byBtYXJrZXQgZGF0YSB3aXRoIGF1dG9tYXRpYyBpbml0aWFsaXphdGlvblxyXG4gKiBGZWF0dXJlczpcclxuICogLSBBdXRvbWF0aWMgV2ViU29ja2V0IGNvbm5lY3Rpb24gbWFuYWdlbWVudFxyXG4gKiAtIE9wdGltaXplZCByZS1yZW5kZXJpbmcgd2l0aCBzZWxlY3RpdmUgc3Vic2NyaXB0aW9uc1xyXG4gKiAtIEF1dG8tc2F2ZSBmdW5jdGlvbmFsaXR5XHJcbiAqIC0gRXJyb3IgcmVjb3ZlcnkgYW5kIHJlY29ubmVjdGlvblxyXG4gKi9cclxuXHJcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2ssIHVzZVJlZiwgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcclxuLy8gVGVtcG9yYXJpbHkgZGlzYWJsZWQgZW5oYW5jZWQgc3RvcmUgaW1wb3J0XHJcbi8vIGltcG9ydCB7IHVzZUVuaGFuY2VkTWFya2V0U3RvcmUsIHNlbGVjdENvbm5lY3Rpb24sIHNlbGVjdE1hcmtldERhdGEsIHNlbGVjdENhY2hlLCBzZWxlY3RVSSwgc2VsZWN0SXNMb2FkaW5nIH0gZnJvbSAnQC9zdG9yZS9lbmhhbmNlZE1hcmtldFN0b3JlJztcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgVXNlRW5oYW5jZWRNYXJrZXREYXRhT3B0aW9ucyB7XHJcbiAgYXV0b0Nvbm5lY3Q/OiBib29sZWFuO1xyXG4gIGF1dG9Mb2FkQ2FjaGU/OiBib29sZWFuO1xyXG4gIGF1dG9TYXZlSW50ZXJ2YWw/OiBudW1iZXI7XHJcbiAgcmVjb25uZWN0T25FcnJvcj86IGJvb2xlYW47XHJcbiAgbWF4UmVjb25uZWN0QXR0ZW1wdHM/OiBudW1iZXI7XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCB1c2VFbmhhbmNlZE1hcmtldERhdGEgPSAob3B0aW9uczogVXNlRW5oYW5jZWRNYXJrZXREYXRhT3B0aW9ucyA9IHt9KSA9PiB7XHJcbiAgY29uc3Qge1xyXG4gICAgYXV0b0Nvbm5lY3QgPSB0cnVlLFxyXG4gICAgYXV0b0xvYWRDYWNoZSA9IHRydWUsXHJcbiAgICBhdXRvU2F2ZUludGVydmFsID0gMzAwMDAsIC8vIDMwIHNlY29uZHNcclxuICAgIHJlY29ubmVjdE9uRXJyb3IgPSB0cnVlLFxyXG4gICAgbWF4UmVjb25uZWN0QXR0ZW1wdHMgPSA1LFxyXG4gIH0gPSBvcHRpb25zO1xyXG5cclxuICAvLyBUZW1wb3JhcnkgZmFsbGJhY2sgaW1wbGVtZW50YXRpb25cclxuICBjb25zdCBjb25uZWN0aW9uID0ge1xyXG4gICAgc3RhdHVzOiAnZGlzY29ubmVjdGVkJyBhcyBhbnksXHJcbiAgICBpc0Nvbm5lY3RlZDogZmFsc2UsXHJcbiAgICBlcnJvcjogbnVsbCxcclxuICAgIGNvbm5lY3Rpb25TdGF0czoge1xyXG4gICAgICB0b3RhbE1lc3NhZ2VzOiAwLFxyXG4gICAgICBjb25uZWN0aW9uVXB0aW1lOiAwLFxyXG4gICAgfSxcclxuICB9O1xyXG4gIGNvbnN0IG1hcmtldERhdGFNYXAgPSBuZXcgTWFwKCk7XHJcbiAgY29uc3QgY2FjaGUgPSB7XHJcbiAgICBpc0xvYWRlZDogZmFsc2UsXHJcbiAgICB0b3RhbENhY2hlU2l6ZTogMCxcclxuICAgIHBlbmRpbmdVcGRhdGVzOiAwLFxyXG4gICAgbGFzdENhY2hlVXBkYXRlOiBudWxsLFxyXG4gIH07XHJcbiAgY29uc3QgdWkgPSB1c2VNZW1vKCgpID0+ICh7XHJcbiAgICBmaWx0ZXJzOiB7fSxcclxuICAgIHNvcnRDb25maWc6IHsgZmllbGQ6ICdzeW1ib2wnLCBkaXJlY3Rpb246ICdhc2MnIGFzIGFueSB9LFxyXG4gICAgc2VsZWN0ZWRJbnN0cnVtZW50czogbmV3IFNldCgpLFxyXG4gICAgdmlld01vZGU6ICd0YWJsZScgYXMgYW55LFxyXG4gICAgYXV0b1JlZnJlc2g6IHRydWUsXHJcbiAgfSksIFtdKTtcclxuICBjb25zdCBpc0xvYWRpbmcgPSBmYWxzZTtcclxuXHJcbiAgLy8gU3RvcmUgYWN0aW9ucyAtIHRlbXBvcmFyeSBtb2NrIGZ1bmN0aW9ucyB3aXRoIHVzZUNhbGxiYWNrIHRvIGZpeCBkZXBlbmRlbmN5IHdhcm5pbmdzXHJcbiAgY29uc3QgaW5pdGlhbGl6ZUNvbm5lY3Rpb24gPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7fSwgW10pO1xyXG4gIGNvbnN0IGNvbm5lY3QgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7fSwgW10pO1xyXG4gIGNvbnN0IGRpc2Nvbm5lY3QgPSB1c2VDYWxsYmFjaygoKSA9PiB7fSwgW10pO1xyXG4gIGNvbnN0IHJlY29ubmVjdCA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHt9LCBbXSk7XHJcbiAgY29uc3QgbG9hZEZyb21DYWNoZSA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHt9LCBbXSk7XHJcbiAgY29uc3Qgc2F2ZVRvQ2FjaGUgPSB1c2VDYWxsYmFjayhhc3luYyAoZm9yY2U/OiBib29sZWFuKSA9PiB7fSwgW10pO1xyXG4gIGNvbnN0IGNsZWFyQ2FjaGUgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7fSwgW10pO1xyXG4gIGNvbnN0IHNldEZpbHRlcnMgPSB1c2VDYWxsYmFjaygoZmlsdGVyczogYW55KSA9PiB7fSwgW10pO1xyXG4gIGNvbnN0IHNldFNvcnRDb25maWcgPSB1c2VDYWxsYmFjaygoZmllbGQ6IHN0cmluZywgZGlyZWN0aW9uPzogJ2FzYycgfCAnZGVzYycpID0+IHt9LCBbXSk7XHJcbiAgY29uc3QgZ2V0RmlsdGVyZWRNYXJrZXREYXRhID0gdXNlQ2FsbGJhY2soKCkgPT4gW10sIFtdKTtcclxuICBjb25zdCBnZXRTb3J0ZWRNYXJrZXREYXRhID0gdXNlQ2FsbGJhY2soKCkgPT4gW10sIFtdKTtcclxuICBjb25zdCBnZXRNYXJrZXREYXRhQnlTZWN1cml0eUlkID0gdXNlQ2FsbGJhY2soKHNlY3VyaXR5SWQ6IHN0cmluZykgPT4gdW5kZWZpbmVkLCBbXSk7XHJcbiAgY29uc3QgZ2V0TWFya2V0RGF0YUJ5U3ltYm9sID0gdXNlQ2FsbGJhY2soKHN5bWJvbDogc3RyaW5nKSA9PiB1bmRlZmluZWQsIFtdKTtcclxuICBjb25zdCBzdWJzY3JpYmVUb0luc3RydW1lbnQgPSB1c2VDYWxsYmFjaygoc2VjdXJpdHlJZDogc3RyaW5nKSA9PiB7fSwgW10pO1xyXG4gIGNvbnN0IHVuc3Vic2NyaWJlRnJvbUluc3RydW1lbnQgPSB1c2VDYWxsYmFjaygoc2VjdXJpdHlJZDogc3RyaW5nKSA9PiB7fSwgW10pO1xyXG4gIGNvbnN0IHJlc2V0ID0gdXNlQ2FsbGJhY2soKCkgPT4ge30sIFtdKTtcclxuXHJcbiAgLy8gUmVmcyBmb3IgaW50ZXJ2YWxzIGFuZCB0cmFja2luZ1xyXG4gIGNvbnN0IGF1dG9TYXZlSW50ZXJ2YWxSZWYgPSB1c2VSZWY8Tm9kZUpTLlRpbWVvdXQgfCBudWxsPihudWxsKTtcclxuICBjb25zdCByZWNvbm5lY3RBdHRlbXB0c1JlZiA9IHVzZVJlZigwKTtcclxuICBjb25zdCBpbml0aWFsaXphdGlvblJlZiA9IHVzZVJlZihmYWxzZSk7XHJcblxyXG4gIC8vIENvbnZlcnQgTWFwIHRvIEFycmF5IGZvciBjb21wb25lbnRzIHRoYXQgZXhwZWN0IGFycmF5c1xyXG4gIGNvbnN0IG1hcmtldERhdGFBcnJheSA9IEFycmF5LmZyb20obWFya2V0RGF0YU1hcC52YWx1ZXMoKSk7XHJcblxyXG4gIC8vIEluaXRpYWxpemUgY29ubmVjdGlvbiBhbmQgY2FjaGUgb24gbW91bnRcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgaW5pdGlhbGl6ZSA9IGFzeW5jICgpID0+IHtcclxuICAgICAgaWYgKGluaXRpYWxpemF0aW9uUmVmLmN1cnJlbnQpIHJldHVybjtcclxuICAgICAgaW5pdGlhbGl6YXRpb25SZWYuY3VycmVudCA9IHRydWU7XHJcblxyXG4gICAgICBjb25zb2xlLmxvZygn8J+agCBFbmhhbmNlZCBIb29rOiBJbml0aWFsaXppbmcuLi4nKTtcclxuXHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgLy8gTG9hZCBjYWNoZSBmaXJzdCBmb3IgaW5zdGFudCBkYXRhIGRpc3BsYXlcclxuICAgICAgICBpZiAoYXV0b0xvYWRDYWNoZSAmJiAhY2FjaGUuaXNMb2FkZWQpIHtcclxuICAgICAgICAgIGF3YWl0IGxvYWRGcm9tQ2FjaGUoKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIEluaXRpYWxpemUgV2ViU29ja2V0IGNvbm5lY3Rpb25cclxuICAgICAgICBpZiAoYXV0b0Nvbm5lY3QgJiYgY29ubmVjdGlvbi5zdGF0dXMgPT09ICdkaXNjb25uZWN0ZWQnKSB7XHJcbiAgICAgICAgICBhd2FpdCBpbml0aWFsaXplQ29ubmVjdGlvbigpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgRW5oYW5jZWQgSG9vazogSW5pdGlhbGl6YXRpb24gZmFpbGVkOicsIGVycm9yKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBpbml0aWFsaXplKCk7XHJcblxyXG4gICAgLy8gQ2xlYW51cCBvbiB1bm1vdW50XHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBpZiAoYXV0b1NhdmVJbnRlcnZhbFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgY2xlYXJJbnRlcnZhbChhdXRvU2F2ZUludGVydmFsUmVmLmN1cnJlbnQpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gIH0sIFthdXRvQ29ubmVjdCwgYXV0b0xvYWRDYWNoZSwgY2FjaGUuaXNMb2FkZWQsIGNvbm5lY3Rpb24uc3RhdHVzLCBpbml0aWFsaXplQ29ubmVjdGlvbiwgbG9hZEZyb21DYWNoZV0pO1xyXG5cclxuICAvLyBBdXRvLXNhdmUgZnVuY3Rpb25hbGl0eVxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoYXV0b1NhdmVJbnRlcnZhbCA+IDAgJiYgbWFya2V0RGF0YUFycmF5Lmxlbmd0aCA+IDApIHtcclxuICAgICAgaWYgKGF1dG9TYXZlSW50ZXJ2YWxSZWYuY3VycmVudCkge1xyXG4gICAgICAgIGNsZWFySW50ZXJ2YWwoYXV0b1NhdmVJbnRlcnZhbFJlZi5jdXJyZW50KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgYXV0b1NhdmVJbnRlcnZhbFJlZi5jdXJyZW50ID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xyXG4gICAgICAgIHNhdmVUb0NhY2hlKCk7XHJcbiAgICAgIH0sIGF1dG9TYXZlSW50ZXJ2YWwpO1xyXG5cclxuICAgICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgICBpZiAoYXV0b1NhdmVJbnRlcnZhbFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgICBjbGVhckludGVydmFsKGF1dG9TYXZlSW50ZXJ2YWxSZWYuY3VycmVudCk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9O1xyXG4gICAgfVxyXG4gIH0sIFthdXRvU2F2ZUludGVydmFsLCBtYXJrZXREYXRhQXJyYXkubGVuZ3RoLCBzYXZlVG9DYWNoZV0pO1xyXG5cclxuICAvLyBBdXRvLXJlY29ubmVjdCBvbiBlcnJvclxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoXHJcbiAgICAgIHJlY29ubmVjdE9uRXJyb3IgJiZcclxuICAgICAgY29ubmVjdGlvbi5zdGF0dXMgPT09ICdlcnJvcicgJiZcclxuICAgICAgcmVjb25uZWN0QXR0ZW1wdHNSZWYuY3VycmVudCA8IG1heFJlY29ubmVjdEF0dGVtcHRzXHJcbiAgICApIHtcclxuICAgICAgY29uc3QgcmV0cnlEZWxheSA9IE1hdGgubWluKDEwMDAgKiBNYXRoLnBvdygyLCByZWNvbm5lY3RBdHRlbXB0c1JlZi5jdXJyZW50KSwgMzAwMDApO1xyXG4gICAgICBcclxuICAgICAgY29uc29sZS5sb2coXHJcbiAgICAgICAgYPCflIQgRW5oYW5jZWQgSG9vazogQXV0by1yZWNvbm5lY3RpbmcgaW4gJHtyZXRyeURlbGF5fW1zIChhdHRlbXB0ICR7cmVjb25uZWN0QXR0ZW1wdHNSZWYuY3VycmVudCArIDF9LyR7bWF4UmVjb25uZWN0QXR0ZW1wdHN9KWBcclxuICAgICAgKTtcclxuXHJcbiAgICAgIGNvbnN0IHRpbWVvdXRJZCA9IHNldFRpbWVvdXQoYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICByZWNvbm5lY3RBdHRlbXB0c1JlZi5jdXJyZW50Kys7XHJcbiAgICAgICAgICBhd2FpdCByZWNvbm5lY3QoKTtcclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEVuaGFuY2VkIEhvb2s6IEF1dG8tcmVjb25uZWN0IGZhaWxlZDonLCBlcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgICB9LCByZXRyeURlbGF5KTtcclxuXHJcbiAgICAgIHJldHVybiAoKSA9PiBjbGVhclRpbWVvdXQodGltZW91dElkKTtcclxuICAgIH1cclxuICB9LCBbY29ubmVjdGlvbi5zdGF0dXMsIHJlY29ubmVjdCwgcmVjb25uZWN0T25FcnJvciwgbWF4UmVjb25uZWN0QXR0ZW1wdHNdKTtcclxuXHJcbiAgLy8gUmVzZXQgcmVjb25uZWN0IGF0dGVtcHRzIG9uIHN1Y2Nlc3NmdWwgY29ubmVjdGlvblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoY29ubmVjdGlvbi5zdGF0dXMgPT09ICdjb25uZWN0ZWQnKSB7XHJcbiAgICAgIHJlY29ubmVjdEF0dGVtcHRzUmVmLmN1cnJlbnQgPSAwO1xyXG4gICAgfVxyXG4gIH0sIFtjb25uZWN0aW9uLnN0YXR1c10pO1xyXG5cclxuICAvLyBPcHRpbWl6ZWQgZGF0YSBhY2Nlc3MgZnVuY3Rpb25zXHJcbiAgY29uc3QgZ2V0RmlsdGVyZWREYXRhID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgcmV0dXJuIGdldEZpbHRlcmVkTWFya2V0RGF0YSgpO1xyXG4gIH0sIFtnZXRGaWx0ZXJlZE1hcmtldERhdGFdKTtcclxuXHJcbiAgY29uc3QgZ2V0U29ydGVkRGF0YSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgIHJldHVybiBnZXRTb3J0ZWRNYXJrZXREYXRhKCk7XHJcbiAgfSwgW2dldFNvcnRlZE1hcmtldERhdGFdKTtcclxuXHJcbiAgY29uc3QgZ2V0RGF0YUJ5U2VjdXJpdHlJZCA9IHVzZUNhbGxiYWNrKChzZWN1cml0eUlkOiBzdHJpbmcpID0+IHtcclxuICAgIHJldHVybiBnZXRNYXJrZXREYXRhQnlTZWN1cml0eUlkKHNlY3VyaXR5SWQpO1xyXG4gIH0sIFtnZXRNYXJrZXREYXRhQnlTZWN1cml0eUlkXSk7XHJcblxyXG4gIGNvbnN0IGdldERhdGFCeVN5bWJvbCA9IHVzZUNhbGxiYWNrKChzeW1ib2w6IHN0cmluZykgPT4ge1xyXG4gICAgcmV0dXJuIGdldE1hcmtldERhdGFCeVN5bWJvbChzeW1ib2wpO1xyXG4gIH0sIFtnZXRNYXJrZXREYXRhQnlTeW1ib2xdKTtcclxuXHJcbiAgLy8gRW5oYW5jZWQgZmlsdGVyIGZ1bmN0aW9uc1xyXG4gIGNvbnN0IHVwZGF0ZUZpbHRlcnMgPSB1c2VDYWxsYmFjaygoZmlsdGVyczogUGFydGlhbDx0eXBlb2YgdWkuZmlsdGVycz4pID0+IHtcclxuICAgIHNldEZpbHRlcnMoZmlsdGVycyk7XHJcbiAgfSwgW3NldEZpbHRlcnMsIHVpXSk7XHJcblxyXG4gIGNvbnN0IHVwZGF0ZVNvcnQgPSB1c2VDYWxsYmFjaygoZmllbGQ6IHN0cmluZywgZGlyZWN0aW9uPzogJ2FzYycgfCAnZGVzYycpID0+IHtcclxuICAgIHNldFNvcnRDb25maWcoZmllbGQsIGRpcmVjdGlvbik7XHJcbiAgfSwgW3NldFNvcnRDb25maWddKTtcclxuXHJcbiAgLy8gU3Vic2NyaXB0aW9uIG1hbmFnZW1lbnRcclxuICBjb25zdCBzdWJzY3JpYmUgPSB1c2VDYWxsYmFjaygoc2VjdXJpdHlJZDogc3RyaW5nKSA9PiB7XHJcbiAgICBzdWJzY3JpYmVUb0luc3RydW1lbnQoc2VjdXJpdHlJZCk7XHJcbiAgfSwgW3N1YnNjcmliZVRvSW5zdHJ1bWVudF0pO1xyXG5cclxuICBjb25zdCB1bnN1YnNjcmliZSA9IHVzZUNhbGxiYWNrKChzZWN1cml0eUlkOiBzdHJpbmcpID0+IHtcclxuICAgIHVuc3Vic2NyaWJlRnJvbUluc3RydW1lbnQoc2VjdXJpdHlJZCk7XHJcbiAgfSwgW3Vuc3Vic2NyaWJlRnJvbUluc3RydW1lbnRdKTtcclxuXHJcbiAgLy8gQ29ubmVjdGlvbiBtYW5hZ2VtZW50XHJcbiAgY29uc3QgZm9yY2VSZWNvbm5lY3QgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICByZWNvbm5lY3RBdHRlbXB0c1JlZi5jdXJyZW50ID0gMDsgLy8gUmVzZXQgYXR0ZW1wdHMgZm9yIG1hbnVhbCByZWNvbm5lY3RcclxuICAgICAgYXdhaXQgcmVjb25uZWN0KCk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRW5oYW5jZWQgSG9vazogTWFudWFsIHJlY29ubmVjdCBmYWlsZWQ6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9LCBbcmVjb25uZWN0XSk7XHJcblxyXG4gIGNvbnN0IGZvcmNlUmVmcmVzaCA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGF3YWl0IGxvYWRGcm9tQ2FjaGUoKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFbmhhbmNlZCBIb29rOiBGb3JjZSByZWZyZXNoIGZhaWxlZDonLCBlcnJvcik7XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG4gIH0sIFtsb2FkRnJvbUNhY2hlXSk7XHJcblxyXG4gIGNvbnN0IGZvcmNlU2F2ZSA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGF3YWl0IHNhdmVUb0NhY2hlKHRydWUpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign4p2MIEVuaGFuY2VkIEhvb2s6IEZvcmNlIHNhdmUgZmFpbGVkOicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfSwgW3NhdmVUb0NhY2hlXSk7XHJcblxyXG4gIC8vIFN0YXRzIGFuZCBjb21wdXRlZCB2YWx1ZXNcclxuICBjb25zdCBzdGF0cyA9IHtcclxuICAgIHRvdGFsSW5zdHJ1bWVudHM6IG1hcmtldERhdGFBcnJheS5sZW5ndGgsXHJcbiAgICBjb25uZWN0ZWRJbnN0cnVtZW50czogbWFya2V0RGF0YUFycmF5LmZpbHRlcihpdGVtID0+IGl0ZW0ubHRwICYmIGl0ZW0ubHRwID4gMCkubGVuZ3RoLFxyXG4gICAgbGFzdFVwZGF0ZTogbnVsbCwgLy8gVGVtcG9yYXJ5IG1vY2tcclxuICAgIGNhY2hlU2l6ZTogY2FjaGUudG90YWxDYWNoZVNpemUsXHJcbiAgICBjb25uZWN0aW9uVXB0aW1lOiBjb25uZWN0aW9uLmNvbm5lY3Rpb25TdGF0cy5jb25uZWN0aW9uVXB0aW1lLFxyXG4gICAgbWVzc2FnZXNSZWNlaXZlZDogY29ubmVjdGlvbi5jb25uZWN0aW9uU3RhdHMudG90YWxNZXNzYWdlcyxcclxuICAgIHJlY29ubmVjdEF0dGVtcHRzOiByZWNvbm5lY3RBdHRlbXB0c1JlZi5jdXJyZW50LFxyXG4gICAgaXNBdXRvU2F2aW5nOiBhdXRvU2F2ZUludGVydmFsUmVmLmN1cnJlbnQgIT09IG51bGwsXHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIHtcclxuICAgIC8vIERhdGFcclxuICAgIG1hcmtldERhdGE6IG1hcmtldERhdGFBcnJheSxcclxuICAgIG1hcmtldERhdGFNYXAsXHJcbiAgICBmaWx0ZXJlZERhdGE6IGdldEZpbHRlcmVkRGF0YSgpLFxyXG4gICAgc29ydGVkRGF0YTogZ2V0U29ydGVkRGF0YSgpLFxyXG5cclxuICAgIC8vIENvbm5lY3Rpb24gc3RhdGVcclxuICAgIGlzQ29ubmVjdGVkOiBjb25uZWN0aW9uLmlzQ29ubmVjdGVkLFxyXG4gICAgY29ubmVjdGlvblN0YXR1czogY29ubmVjdGlvbi5zdGF0dXMsXHJcbiAgICBjb25uZWN0aW9uRXJyb3I6IGNvbm5lY3Rpb24uZXJyb3IsXHJcbiAgICBjb25uZWN0aW9uU3RhdHM6IGNvbm5lY3Rpb24uY29ubmVjdGlvblN0YXRzLFxyXG5cclxuICAgIC8vIENhY2hlIHN0YXRlXHJcbiAgICBjYWNoZUxvYWRlZDogY2FjaGUuaXNMb2FkZWQsXHJcbiAgICBjYWNoZVVwZGF0aW5nOiBjYWNoZS5wZW5kaW5nVXBkYXRlcyA+IDAsXHJcbiAgICBsYXN0Q2FjaGVVcGRhdGU6IGNhY2hlLmxhc3RDYWNoZVVwZGF0ZSxcclxuXHJcbiAgICAvLyBVSSBzdGF0ZVxyXG4gICAgZmlsdGVyczogdWkuZmlsdGVycyxcclxuICAgIHNvcnRDb25maWc6IHVpLnNvcnRDb25maWcsXHJcbiAgICBzZWxlY3RlZEluc3RydW1lbnRzOiB1aS5zZWxlY3RlZEluc3RydW1lbnRzLFxyXG4gICAgdmlld01vZGU6IHVpLnZpZXdNb2RlLFxyXG4gICAgYXV0b1JlZnJlc2g6IHVpLmF1dG9SZWZyZXNoLFxyXG5cclxuICAgIC8vIExvYWRpbmcgc3RhdGVzXHJcbiAgICBpc0xvYWRpbmcsXHJcbiAgICBpc0luaXRpYWxpemluZzogIWluaXRpYWxpemF0aW9uUmVmLmN1cnJlbnQsXHJcblxyXG4gICAgLy8gRGF0YSBhY2Nlc3MgZnVuY3Rpb25zXHJcbiAgICBnZXREYXRhQnlTZWN1cml0eUlkLFxyXG4gICAgZ2V0RGF0YUJ5U3ltYm9sLFxyXG4gICAgZ2V0RmlsdGVyZWREYXRhLFxyXG4gICAgZ2V0U29ydGVkRGF0YSxcclxuXHJcbiAgICAvLyBBY3Rpb25zXHJcbiAgICB1cGRhdGVGaWx0ZXJzLFxyXG4gICAgdXBkYXRlU29ydCxcclxuICAgIHN1YnNjcmliZSxcclxuICAgIHVuc3Vic2NyaWJlLFxyXG5cclxuICAgIC8vIENvbm5lY3Rpb24gbWFuYWdlbWVudFxyXG4gICAgY29ubmVjdCxcclxuICAgIGRpc2Nvbm5lY3QsXHJcbiAgICByZWNvbm5lY3Q6IGZvcmNlUmVjb25uZWN0LFxyXG5cclxuICAgIC8vIENhY2hlIG1hbmFnZW1lbnRcclxuICAgIHJlZnJlc2g6IGZvcmNlUmVmcmVzaCxcclxuICAgIHNhdmU6IGZvcmNlU2F2ZSxcclxuICAgIGNsZWFyQ2FjaGUsXHJcblxyXG4gICAgLy8gVXRpbGl0eVxyXG4gICAgcmVzZXQsXHJcbiAgICBzdGF0cyxcclxuXHJcbiAgICAvLyBBZHZhbmNlZCBhY3Rpb25zIChleHBvc2UgaWYgbmVlZGVkKVxyXG4gICAgX3N0b3JlOiB7XHJcbiAgICAgIHNldEZpbHRlcnMsXHJcbiAgICAgIHNldFNvcnRDb25maWcsXHJcbiAgICAgIHN1YnNjcmliZVRvSW5zdHJ1bWVudCxcclxuICAgICAgdW5zdWJzY3JpYmVGcm9tSW5zdHJ1bWVudCxcclxuICAgICAgLy8gQWRkIG1vcmUgaWYgbmVlZGVkXHJcbiAgICB9LFxyXG4gIH07XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCB1c2VFbmhhbmNlZE1hcmtldERhdGE7XHJcbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsInVzZVJlZiIsInVzZU1lbW8iLCJ1c2VFbmhhbmNlZE1hcmtldERhdGEiLCJvcHRpb25zIiwiYXV0b0Nvbm5lY3QiLCJhdXRvTG9hZENhY2hlIiwiYXV0b1NhdmVJbnRlcnZhbCIsInJlY29ubmVjdE9uRXJyb3IiLCJtYXhSZWNvbm5lY3RBdHRlbXB0cyIsImNvbm5lY3Rpb24iLCJzdGF0dXMiLCJpc0Nvbm5lY3RlZCIsImVycm9yIiwiY29ubmVjdGlvblN0YXRzIiwidG90YWxNZXNzYWdlcyIsImNvbm5lY3Rpb25VcHRpbWUiLCJtYXJrZXREYXRhTWFwIiwiTWFwIiwiY2FjaGUiLCJpc0xvYWRlZCIsInRvdGFsQ2FjaGVTaXplIiwicGVuZGluZ1VwZGF0ZXMiLCJsYXN0Q2FjaGVVcGRhdGUiLCJ1aSIsImZpbHRlcnMiLCJzb3J0Q29uZmlnIiwiZmllbGQiLCJkaXJlY3Rpb24iLCJzZWxlY3RlZEluc3RydW1lbnRzIiwiU2V0Iiwidmlld01vZGUiLCJhdXRvUmVmcmVzaCIsImlzTG9hZGluZyIsImluaXRpYWxpemVDb25uZWN0aW9uIiwiY29ubmVjdCIsImRpc2Nvbm5lY3QiLCJyZWNvbm5lY3QiLCJsb2FkRnJvbUNhY2hlIiwic2F2ZVRvQ2FjaGUiLCJmb3JjZSIsImNsZWFyQ2FjaGUiLCJzZXRGaWx0ZXJzIiwic2V0U29ydENvbmZpZyIsImdldEZpbHRlcmVkTWFya2V0RGF0YSIsImdldFNvcnRlZE1hcmtldERhdGEiLCJnZXRNYXJrZXREYXRhQnlTZWN1cml0eUlkIiwic2VjdXJpdHlJZCIsInVuZGVmaW5lZCIsImdldE1hcmtldERhdGFCeVN5bWJvbCIsInN5bWJvbCIsInN1YnNjcmliZVRvSW5zdHJ1bWVudCIsInVuc3Vic2NyaWJlRnJvbUluc3RydW1lbnQiLCJyZXNldCIsImF1dG9TYXZlSW50ZXJ2YWxSZWYiLCJyZWNvbm5lY3RBdHRlbXB0c1JlZiIsImluaXRpYWxpemF0aW9uUmVmIiwibWFya2V0RGF0YUFycmF5IiwiQXJyYXkiLCJmcm9tIiwidmFsdWVzIiwiaW5pdGlhbGl6ZSIsImN1cnJlbnQiLCJjb25zb2xlIiwibG9nIiwiY2xlYXJJbnRlcnZhbCIsImxlbmd0aCIsInNldEludGVydmFsIiwicmV0cnlEZWxheSIsIk1hdGgiLCJtaW4iLCJwb3ciLCJ0aW1lb3V0SWQiLCJzZXRUaW1lb3V0IiwiY2xlYXJUaW1lb3V0IiwiZ2V0RmlsdGVyZWREYXRhIiwiZ2V0U29ydGVkRGF0YSIsImdldERhdGFCeVNlY3VyaXR5SWQiLCJnZXREYXRhQnlTeW1ib2wiLCJ1cGRhdGVGaWx0ZXJzIiwidXBkYXRlU29ydCIsInN1YnNjcmliZSIsInVuc3Vic2NyaWJlIiwiZm9yY2VSZWNvbm5lY3QiLCJmb3JjZVJlZnJlc2giLCJmb3JjZVNhdmUiLCJzdGF0cyIsInRvdGFsSW5zdHJ1bWVudHMiLCJjb25uZWN0ZWRJbnN0cnVtZW50cyIsImZpbHRlciIsIml0ZW0iLCJsdHAiLCJsYXN0VXBkYXRlIiwiY2FjaGVTaXplIiwibWVzc2FnZXNSZWNlaXZlZCIsInJlY29ubmVjdEF0dGVtcHRzIiwiaXNBdXRvU2F2aW5nIiwibWFya2V0RGF0YSIsImZpbHRlcmVkRGF0YSIsInNvcnRlZERhdGEiLCJjb25uZWN0aW9uU3RhdHVzIiwiY29ubmVjdGlvbkVycm9yIiwiY2FjaGVMb2FkZWQiLCJjYWNoZVVwZGF0aW5nIiwiaXNJbml0aWFsaXppbmciLCJyZWZyZXNoIiwic2F2ZSIsIl9zdG9yZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useEnhancedMarketData.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/constants.ts":
/*!******************************!*\
  !*** ./src/lib/constants.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: () => (/* binding */ API_CONFIG),\n/* harmony export */   CACHE_CONFIG: () => (/* binding */ CACHE_CONFIG),\n/* harmony export */   CHART_CONFIG: () => (/* binding */ CHART_CONFIG),\n/* harmony export */   COLORS: () => (/* binding */ COLORS),\n/* harmony export */   DEFAULTS: () => (/* binding */ DEFAULTS),\n/* harmony export */   DHAN_CONFIG: () => (/* binding */ DHAN_CONFIG),\n/* harmony export */   ENV_CONFIG: () => (/* binding */ ENV_CONFIG),\n/* harmony export */   ERROR_MESSAGES: () => (/* binding */ ERROR_MESSAGES),\n/* harmony export */   EXCHANGE_SEGMENTS: () => (/* binding */ EXCHANGE_SEGMENTS),\n/* harmony export */   FEATURE_FLAGS: () => (/* binding */ FEATURE_FLAGS),\n/* harmony export */   INSTRUMENT_TYPES: () => (/* binding */ INSTRUMENT_TYPES),\n/* harmony export */   LOGGING_CONFIG: () => (/* binding */ LOGGING_CONFIG),\n/* harmony export */   MARKET_CONFIG: () => (/* binding */ MARKET_CONFIG),\n/* harmony export */   OPTION_TYPES: () => (/* binding */ OPTION_TYPES),\n/* harmony export */   PACKET_TYPES: () => (/* binding */ PACKET_TYPES),\n/* harmony export */   PERFORMANCE_CONFIG: () => (/* binding */ PERFORMANCE_CONFIG),\n/* harmony export */   REQUEST_CODES: () => (/* binding */ REQUEST_CODES),\n/* harmony export */   SECURITY_CONFIG: () => (/* binding */ SECURITY_CONFIG),\n/* harmony export */   STORAGE_KEYS: () => (/* binding */ STORAGE_KEYS),\n/* harmony export */   SUCCESS_MESSAGES: () => (/* binding */ SUCCESS_MESSAGES),\n/* harmony export */   UI_CONFIG: () => (/* binding */ UI_CONFIG),\n/* harmony export */   VALIDATION: () => (/* binding */ VALIDATION),\n/* harmony export */   WEBSOCKET_CONFIG: () => (/* binding */ WEBSOCKET_CONFIG)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/**\r\n * Application Constants\r\n */ // API Configuration\nconst API_CONFIG = {\n    BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080',\n    TIMEOUT: 30000,\n    RETRY_ATTEMPTS: 3,\n    RETRY_DELAY: 1000\n};\n// WebSocket Configuration\nconst WEBSOCKET_CONFIG = {\n    RECONNECT_INTERVAL: 3000,\n    MAX_RECONNECT_ATTEMPTS: 15,\n    PING_INTERVAL: 25000,\n    PONG_TIMEOUT: 10000,\n    CONNECTION_TIMEOUT: 20000,\n    HEARTBEAT_INTERVAL: 30000,\n    MAX_LISTENERS_PER_EVENT: 10,\n    CLEANUP_INTERVAL: 60000\n};\n// Dhan API Configuration\nconst DHAN_CONFIG = {\n    BASE_URL: 'https://api.dhan.co',\n    WEBSOCKET_URL: 'wss://api.dhan.co/v2/wsapi',\n    MAX_INSTRUMENTS_PER_CONNECTION: 5000,\n    MAX_INSTRUMENTS_PER_MESSAGE: 100,\n    RATE_LIMIT: {\n        REQUESTS_PER_SECOND: 10,\n        REQUESTS_PER_MINUTE: 600\n    }\n};\n// Market Configuration\nconst MARKET_CONFIG = {\n    TRADING_HOURS: {\n        START: {\n            hour: 9,\n            minute: 15\n        },\n        END: {\n            hour: 15,\n            minute: 30\n        }\n    },\n    TRADING_DAYS: [\n        1,\n        2,\n        3,\n        4,\n        5\n    ],\n    REFRESH_INTERVAL: 1000,\n    BATCH_SIZE: 100\n};\n// Instrument Types\nconst INSTRUMENT_TYPES = {\n    EQUITY: 'EQUITY',\n    INDEX: 'INDEX',\n    FUTIDX: 'FUTIDX',\n    OPTIDX: 'OPTIDX',\n    FUTSTK: 'FUTSTK',\n    OPTSTK: 'OPTSTK',\n    FUTCUR: 'FUTCUR',\n    OPTCUR: 'OPTCUR',\n    FUTCOM: 'FUTCOM',\n    OPTFUT: 'OPTFUT'\n};\n// Exchange Segments\nconst EXCHANGE_SEGMENTS = {\n    NSE_EQ: 'NSE_EQ',\n    NSE_FNO: 'NSE_FNO',\n    BSE_EQ: 'BSE_EQ',\n    MCX_COMM: 'MCX_COMM',\n    IDX_I: 'IDX_I'\n};\n// Option Types\nconst OPTION_TYPES = {\n    CALL: 'CE',\n    PUT: 'PE'\n};\n// Market Data Packet Types\nconst PACKET_TYPES = {\n    TOUCHLINE: 1,\n    QUOTE: 2,\n    SNAP_QUOTE: 3,\n    FULL_PACKET: 4,\n    OI: 5\n};\n// WebSocket Request Codes\nconst REQUEST_CODES = {\n    SUBSCRIBE: 21,\n    UNSUBSCRIBE: 22,\n    SNAP_QUOTE: 23\n};\n// UI Configuration\nconst UI_CONFIG = {\n    DEBOUNCE_DELAY: 300,\n    THROTTLE_DELAY: 100,\n    ANIMATION_DURATION: 200,\n    TOAST_DURATION: 3000,\n    PAGINATION: {\n        DEFAULT_PAGE_SIZE: 50,\n        PAGE_SIZE_OPTIONS: [\n            25,\n            50,\n            100,\n            200\n        ]\n    }\n};\n// Color Schemes\nconst COLORS = {\n    SUCCESS: '#10B981',\n    ERROR: '#EF4444',\n    WARNING: '#F59E0B',\n    INFO: '#3B82F6',\n    NEUTRAL: '#6B7280',\n    BID: '#10B981',\n    ASK: '#EF4444',\n    SPOT: '#3B82F6'\n};\n// Chart Configuration\nconst CHART_CONFIG = {\n    DEFAULT_HEIGHT: 400,\n    COLORS: {\n        PRIMARY: '#3B82F6',\n        SECONDARY: '#10B981',\n        ACCENT: '#F59E0B',\n        GRID: '#E5E7EB',\n        TEXT: '#374151'\n    },\n    ANIMATION: {\n        DURATION: 300,\n        EASING: 'ease-in-out'\n    }\n};\n// Data Validation\nconst VALIDATION = {\n    MIN_STRIKE_PRICE: 1,\n    MAX_STRIKE_PRICE: 100000,\n    MIN_OPTION_PRICE: 0.01,\n    MAX_OPTION_PRICE: 50000,\n    MIN_VOLUME: 0,\n    MAX_VOLUME: 10000000\n};\n// Error Messages\nconst ERROR_MESSAGES = {\n    NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',\n    API_ERROR: 'API request failed. Please try again later.',\n    WEBSOCKET_ERROR: 'WebSocket connection failed. Attempting to reconnect...',\n    DATA_PARSING_ERROR: 'Failed to parse market data. Please refresh the page.',\n    SUBSCRIPTION_ERROR: 'Failed to subscribe to market data. Please try again.',\n    INVALID_INSTRUMENT: 'Invalid instrument selected.',\n    MARKET_CLOSED: 'Market is currently closed.',\n    RATE_LIMIT_EXCEEDED: 'Too many requests. Please wait before trying again.'\n};\n// Success Messages\nconst SUCCESS_MESSAGES = {\n    CONNECTION_ESTABLISHED: 'Successfully connected to market data feed.',\n    SUBSCRIPTION_SUCCESS: 'Successfully subscribed to market data.',\n    DATA_UPDATED: 'Market data updated successfully.',\n    SETTINGS_SAVED: 'Settings saved successfully.'\n};\n// Local Storage Keys\nconst STORAGE_KEYS = {\n    USER_PREFERENCES: 'csv_market_dashboard_preferences',\n    SELECTED_INSTRUMENTS: 'csv_market_dashboard_selected_instruments',\n    THEME: 'csv_market_dashboard_theme',\n    LAYOUT: 'csv_market_dashboard_layout'\n};\n// Feature Flags\nconst FEATURE_FLAGS = {\n    ENABLE_CHARTS: true,\n    ENABLE_ALERTS: true,\n    ENABLE_EXPORT: true,\n    ENABLE_DARK_MODE: true,\n    ENABLE_REAL_TIME_UPDATES: true,\n    ENABLE_OPTION_GREEKS: false\n};\n// Performance Monitoring\nconst PERFORMANCE_CONFIG = {\n    MEMORY_WARNING_THRESHOLD: 100 * 1024 * 1024,\n    CPU_WARNING_THRESHOLD: 80,\n    NETWORK_TIMEOUT_WARNING: 5000,\n    MAX_CONCURRENT_REQUESTS: 10\n};\n// Logging Configuration\nconst LOGGING_CONFIG = {\n    LEVELS: {\n        ERROR: 0,\n        WARN: 1,\n        INFO: 2,\n        DEBUG: 3\n    },\n    MAX_LOG_SIZE: 10 * 1024 * 1024,\n    MAX_LOG_FILES: 5,\n    LOG_ROTATION_INTERVAL: 24 * 60 * 60 * 1000\n};\n// Cache Configuration\nconst CACHE_CONFIG = {\n    DEFAULT_TTL: 5 * 60 * 1000,\n    MARKET_DATA_TTL: 10 * 60 * 1000,\n    STATIC_DATA_TTL: 30 * 60 * 1000,\n    EXPIRY_CHECK_INTERVAL: 60 * 1000,\n    MAX_CACHE_SIZE: 50 * 1024 * 1024,\n    KEYS: {\n        INSTRUMENTS: 'instruments',\n        MARKET_DATA: 'market_data',\n        OPTION_CHAIN: 'option_chain',\n        EXPIRY_DATES: 'expiry_dates',\n        NIFTY_SPOT: 'nifty_spot',\n        USER_SETTINGS: 'user_settings',\n        USER_PREFERENCES: 'user_preferences'\n    }\n};\n// Security Configuration\nconst SECURITY_CONFIG = {\n    RATE_LIMITING: {\n        WINDOW_MS: 15 * 60 * 1000,\n        MAX_REQUESTS: 1000\n    },\n    CORS: {\n        ORIGIN:  false ? 0 : [\n            'http://localhost:3000',\n            'http://localhost:3001'\n        ],\n        CREDENTIALS: true\n    },\n    HEADERS: {\n        CONTENT_SECURITY_POLICY: \"default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';\",\n        X_FRAME_OPTIONS: 'DENY',\n        X_CONTENT_TYPE_OPTIONS: 'nosniff'\n    }\n};\n// Environment Configuration\nconst ENV_CONFIG = {\n    NODE_ENV: \"development\" || 0,\n    PORT: parseInt(process.env.PORT || '8080'),\n    NEXT_PORT: parseInt(process.env.NEXT_PORT || '3000'),\n    LOG_LEVEL: process.env.LOG_LEVEL || 'info',\n    ENABLE_METRICS: process.env.ENABLE_METRICS === 'true'\n};\n// Default Values\nconst DEFAULTS = {\n    NIFTY_SPOT_PRICE: 24850,\n    SELECTED_EXPIRY: '2025-06-19',\n    STRIKES_TO_SHOW: 20,\n    REFRESH_INTERVAL: 1000,\n    CHART_TIMEFRAME: '1D',\n    TABLE_PAGE_SIZE: 50\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/constants.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/data-cache.ts":
/*!*******************************!*\
  !*** ./src/lib/data-cache.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MARKET_DATA_CACHE_KEYS: () => (/* binding */ MARKET_DATA_CACHE_KEYS),\n/* harmony export */   cacheHelpers: () => (/* binding */ cacheHelpers),\n/* harmony export */   dataCache: () => (/* binding */ dataCache)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(app-pages-browser)/./src/lib/constants.ts\");\n/**\r\n * Data Caching System for Market Data Persistence\r\n * Handles localStorage/sessionStorage with TTL and compression\r\n */ \nclass DataCache {\n    /**\r\n   * Check if we're in a browser environment\r\n   */ isBrowser() {\n        return  true && typeof window.localStorage !== 'undefined';\n    }\n    static getInstance() {\n        if (!DataCache.instance) {\n            DataCache.instance = new DataCache();\n        }\n        return DataCache.instance;\n    }\n    /**\r\n   * Initialize cache for client-side usage\r\n   * Call this method when the component mounts on the client side\r\n   */ initializeClientSide() {\n        if (this.isBrowser()) {\n            console.log('🚀 DataCache: Initializing client-side cache');\n            // Perform any client-side specific initialization here\n            this.cleanupExpired() // Run initial cleanup\n            ;\n        }\n    }\n    /**\r\n   * Check if compression is supported\r\n   */ checkCompressionSupport() {\n        try {\n            // Check if CompressionStream is available (modern browsers)\n            this.compressionSupported = typeof CompressionStream !== 'undefined';\n        } catch (e) {\n            this.compressionSupported = false;\n        }\n    }\n    /**\r\n   * Set data in cache with TTL\r\n   */ async set(key, data) {\n        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        try {\n            // Skip caching on server side\n            if (!this.isBrowser()) {\n                console.log(\"⚠️ DataCache: Skipping cache on server side for \".concat(key));\n                return false;\n            }\n            const { ttl = _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.DEFAULT_TTL, useCompression = false, storage = 'localStorage' } = options;\n            const entry = {\n                data,\n                timestamp: Date.now(),\n                ttl,\n                version: this.version\n            };\n            let serializedData = JSON.stringify(entry);\n            // Apply compression if requested and supported\n            if (useCompression && this.compressionSupported) {\n                serializedData = await this.compress(serializedData);\n            }\n            const storageObj = storage === 'localStorage' ? localStorage : sessionStorage;\n            const fullKey = this.getFullKey(key);\n            storageObj.setItem(fullKey, serializedData);\n            console.log(\"\\uD83D\\uDCBE DataCache: Cached \".concat(key, \" (\").concat(serializedData.length, \" bytes)\"));\n            return true;\n        } catch (error) {\n            console.error(\"❌ DataCache: Failed to cache \".concat(key, \":\"), error);\n            return false;\n        }\n    }\n    /**\r\n   * Get data from cache\r\n   */ async get(key) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        try {\n            // Return null on server side\n            if (!this.isBrowser()) {\n                return null;\n            }\n            const { storage = 'localStorage' } = options;\n            const storageObj = storage === 'localStorage' ? localStorage : sessionStorage;\n            const fullKey = this.getFullKey(key);\n            const serializedData = storageObj.getItem(fullKey);\n            if (!serializedData) {\n                return null;\n            }\n            let decompressedData = serializedData;\n            // Try to decompress if it looks compressed\n            if (this.compressionSupported && this.isCompressed(serializedData)) {\n                decompressedData = await this.decompress(serializedData);\n            }\n            const entry = JSON.parse(decompressedData);\n            // Check version compatibility\n            if (entry.version !== this.version) {\n                console.warn(\"⚠️ DataCache: Version mismatch for \".concat(key, \", removing\"));\n                this.remove(key, options);\n                return null;\n            }\n            // Check TTL\n            if (Date.now() - entry.timestamp > entry.ttl) {\n                console.log(\"⏰ DataCache: \".concat(key, \" expired, removing\"));\n                this.remove(key, options);\n                return null;\n            }\n            console.log(\"\\uD83D\\uDCD6 DataCache: Retrieved \".concat(key, \" from cache\"));\n            return entry.data;\n        } catch (error) {\n            console.error(\"❌ DataCache: Failed to retrieve \".concat(key, \":\"), error);\n            // Remove corrupted entry\n            this.remove(key, options);\n            return null;\n        }\n    }\n    /**\r\n   * Remove data from cache\r\n   */ remove(key) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        try {\n            // Skip on server side\n            if (!this.isBrowser()) {\n                return;\n            }\n            const { storage = 'localStorage' } = options;\n            const storageObj = storage === 'localStorage' ? localStorage : sessionStorage;\n            const fullKey = this.getFullKey(key);\n            storageObj.removeItem(fullKey);\n            console.log(\"\\uD83D\\uDDD1️ DataCache: Removed \".concat(key));\n        } catch (error) {\n            console.error(\"❌ DataCache: Failed to remove \".concat(key, \":\"), error);\n        }\n    }\n    /**\r\n   * Clear all cache entries\r\n   */ clear() {\n        let storage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'localStorage';\n        try {\n            // Skip on server side\n            if (!this.isBrowser()) {\n                return;\n            }\n            const storageObj = storage === 'localStorage' ? localStorage : sessionStorage;\n            const keysToRemove = [];\n            // Find all our cache keys\n            for(let i = 0; i < storageObj.length; i++){\n                const key = storageObj.key(i);\n                if (key && key.startsWith('csv_market_dashboard_cache_')) {\n                    keysToRemove.push(key);\n                }\n            }\n            // Remove them\n            keysToRemove.forEach((key)=>storageObj.removeItem(key));\n            console.log(\"\\uD83E\\uDDF9 DataCache: Cleared \".concat(keysToRemove.length, \" entries from \").concat(storage));\n        } catch (error) {\n            console.error(\"❌ DataCache: Failed to clear \".concat(storage, \":\"), error);\n        }\n    }\n    /**\r\n   * Get cache statistics\r\n   */ getStats() {\n        let storage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'localStorage';\n        // Return empty stats on server side\n        if (!this.isBrowser()) {\n            return {\n                totalEntries: 0,\n                totalSize: 0,\n                oldestEntry: null,\n                newestEntry: null\n            };\n        }\n        const storageObj = storage === 'localStorage' ? localStorage : sessionStorage;\n        let totalEntries = 0;\n        let totalSize = 0;\n        let oldestTimestamp = Infinity;\n        let newestTimestamp = 0;\n        try {\n            for(let i = 0; i < storageObj.length; i++){\n                const key = storageObj.key(i);\n                if (key && key.startsWith('csv_market_dashboard_cache_')) {\n                    const value = storageObj.getItem(key);\n                    if (value) {\n                        totalEntries++;\n                        totalSize += value.length;\n                        try {\n                            const entry = JSON.parse(value);\n                            if (entry.timestamp) {\n                                oldestTimestamp = Math.min(oldestTimestamp, entry.timestamp);\n                                newestTimestamp = Math.max(newestTimestamp, entry.timestamp);\n                            }\n                        } catch (e) {\n                        // Ignore parsing errors for stats\n                        }\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('❌ DataCache: Failed to get stats:', error);\n        }\n        return {\n            totalEntries,\n            totalSize,\n            oldestEntry: oldestTimestamp === Infinity ? null : new Date(oldestTimestamp),\n            newestEntry: newestTimestamp === 0 ? null : new Date(newestTimestamp)\n        };\n    }\n    /**\r\n   * Start cleanup interval to remove expired entries\r\n   */ startCleanupInterval() {\n        // Only start cleanup interval on client side\n        if (this.isBrowser()) {\n            setInterval(()=>{\n                this.cleanupExpired();\n            }, _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.EXPIRY_CHECK_INTERVAL);\n        }\n    }\n    /**\r\n   * Clean up expired entries\r\n   */ cleanupExpired() {\n        try {\n            // Skip cleanup on server side\n            if (!this.isBrowser()) {\n                return;\n            }\n            const storages = [\n                'localStorage',\n                'sessionStorage'\n            ];\n            storages.forEach((storageType)=>{\n                const storageObj = storageType === 'localStorage' ? localStorage : sessionStorage;\n                const keysToRemove = [];\n                for(let i = 0; i < storageObj.length; i++){\n                    const key = storageObj.key(i);\n                    if (key && key.startsWith('csv_market_dashboard_cache_')) {\n                        try {\n                            const value = storageObj.getItem(key);\n                            if (value) {\n                                const entry = JSON.parse(value);\n                                if (Date.now() - entry.timestamp > entry.ttl) {\n                                    keysToRemove.push(key);\n                                }\n                            }\n                        } catch (e) {\n                            // Remove corrupted entries\n                            keysToRemove.push(key);\n                        }\n                    }\n                }\n                keysToRemove.forEach((key)=>storageObj.removeItem(key));\n                if (keysToRemove.length > 0) {\n                    console.log(\"\\uD83E\\uDDF9 DataCache: Cleaned up \".concat(keysToRemove.length, \" expired entries from \").concat(storageType));\n                }\n            });\n        } catch (error) {\n            console.error('❌ DataCache: Cleanup failed:', error);\n        }\n    }\n    /**\r\n   * Generate full cache key\r\n   */ getFullKey(key) {\n        return \"csv_market_dashboard_cache_\".concat(key);\n    }\n    /**\r\n   * Check if data is compressed\r\n   */ isCompressed(data) {\n        // Simple heuristic: compressed data usually starts with specific bytes\n        return data.startsWith('H4sI') || data.startsWith('eJy') // Common compression signatures\n        ;\n    }\n    /**\r\n   * Compress data (placeholder for future implementation)\r\n   */ async compress(data) {\n        // For now, return as-is. Can implement actual compression later\n        return data;\n    }\n    /**\r\n   * Decompress data (placeholder for future implementation)\r\n   */ async decompress(data) {\n        // For now, return as-is. Can implement actual decompression later\n        return data;\n    }\n    constructor(){\n        this.version = '1.0.0';\n        this.compressionSupported = false;\n        this.checkCompressionSupport();\n        this.startCleanupInterval();\n    }\n}\nDataCache.instance = null;\n// Export singleton instance\nconst dataCache = DataCache.getInstance();\n// Export cache keys for market data\nconst MARKET_DATA_CACHE_KEYS = _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.KEYS;\n// Export helper functions\nconst cacheHelpers = {\n    /**\r\n   * Cache market data with medium TTL for page refresh persistence\r\n   */ cacheMarketData: async (key, data)=>{\n        return dataCache.set(key, data, {\n            ttl: _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.MARKET_DATA_TTL,\n            storage: 'localStorage' // Use localStorage for page refresh persistence\n        });\n    },\n    /**\r\n   * Cache static data with long TTL\r\n   */ cacheStaticData: async (key, data)=>{\n        return dataCache.set(key, data, {\n            ttl: _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.STATIC_DATA_TTL,\n            storage: 'localStorage' // Use local storage for persistent data\n        });\n    },\n    /**\r\n   * Get cached market data\r\n   */ getCachedMarketData: async (key)=>{\n        return dataCache.get(key, {\n            storage: 'localStorage'\n        });\n    },\n    /**\r\n   * Get cached static data\r\n   */ getCachedStaticData: async (key)=>{\n        return dataCache.get(key, {\n            storage: 'localStorage'\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/data-cache.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);