{"version": 3, "file": "config-loader.js", "sourceRoot": "", "sources": ["../src/config-loader.ts"], "names": [], "mappings": ";;;AAAA,mDAAqD;AACrD,2BAA6B;AAsC7B,SAAgB,UAAU,CAAC,GAA2B;IAA3B,oBAAA,EAAA,MAAc,OAAO,CAAC,GAAG,EAAE;IACpD,OAAO,YAAY,CAAC,EAAE,GAAG,KAAA,EAAE,CAAC,CAAC;AAC/B,CAAC;AAFD,gCAEC;AAED,SAAgB,YAAY,CAAC,EAIR;QAHnB,GAAG,SAAA,EACH,cAAc,oBAAA,EACd,sBAA+C,EAA/C,cAAc,mBAAG,eAAe,CAAC,cAAc,KAAA;IAE/C,IAAI,cAAc,EAAE;QAClB,IAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC;YAC7D,CAAC,CAAC,cAAc,CAAC,OAAO;YACxB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC;QAE3C,OAAO;YACL,UAAU,EAAE,SAAS;YACrB,sBAAsB,EAAE,EAAE;YAC1B,OAAO,EAAE,cAAc,CAAC,OAAO;YAC/B,eAAe,iBAAA;YACf,KAAK,EAAE,cAAc,CAAC,KAAK;YAC3B,UAAU,EAAE,cAAc,CAAC,UAAU;YACrC,WAAW,EAAE,cAAc,CAAC,WAAW;SACxC,CAAC;KACH;IAED,kDAAkD;IAClD,IAAM,UAAU,GAAG,cAAc,CAAC;QAChC,GAAG,KAAA;QACH,MAAM,EAAE,UAAC,GAAW,IAAK,OAAA,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAhB,CAAgB;KAC1C,CAAC,CAAC;IAEH,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE;QAC5B,OAAO;YACL,UAAU,EAAE,QAAQ;YACpB,OAAO,EAAE,6BAA6B;SACvC,CAAC;KACH;IAED,OAAO;QACL,UAAU,EAAE,SAAS;QACrB,sBAAsB,EAAE,UAAU,CAAC,YAAY;QAC/C,OAAO,EAAE,UAAU,CAAC,OAAO;QAC3B,eAAe,EAAE,IAAI,CAAC,OAAO,CAC3B,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,EACrC,UAAU,CAAC,OAAO,IAAI,EAAE,CACzB;QACD,KAAK,EAAE,UAAU,CAAC,KAAK,IAAI,EAAE;QAC7B,WAAW,EAAE,UAAU,CAAC,OAAO,KAAK,SAAS;KAC9C,CAAC;AACJ,CAAC;AA7CD,oCA6CC"}