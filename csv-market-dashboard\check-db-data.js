require('dotenv').config({ path: '.env.database' });
const { PrismaClient } = require('./generated/prisma');

async function checkDbData() {
  const prisma = new PrismaClient();

  try {
    console.log('🔌 Connecting to database...');
    await prisma.$connect();
    console.log('✅ Connected successfully');

    // Check total count
    const totalCount = await prisma.$queryRaw`SELECT COUNT(*) FROM "Instruments"`;
    console.log('📊 Total instruments:', totalCount[0].count);

    // Check instrument types
    const types = await prisma.$queryRaw`
      SELECT "INSTRUMENT_TYPE", COUNT(*) as count
      FROM "Instruments" 
      GROUP BY "INSTRUMENT_TYPE"
      ORDER BY count DESC
      LIMIT 10
    `;
    
    console.log('\n🔧 Instrument types:');
    types.forEach(type => {
      console.log(`  - ${type.INSTRUMENT_TYPE}: ${type.count} instruments`);
    });

    // Check exchanges
    const exchanges = await prisma.$queryRaw`
      SELECT "EXCH_ID", COUNT(*) as count
      FROM "Instruments" 
      GROUP BY "EXCH_ID"
      ORDER BY count DESC
      LIMIT 10
    `;
    
    console.log('\n🏢 Exchanges:');
    exchanges.forEach(exch => {
      console.log(`  - ${exch.EXCH_ID}: ${exch.count} instruments`);
    });

    // Check sample data
    const sample = await prisma.$queryRaw`
      SELECT "SYMBOL_NAME", "UNDERLYING_SYMBOL", "INSTRUMENT_TYPE", "EXCH_ID"
      FROM "Instruments" 
      LIMIT 10
    `;
    
    console.log('\n📋 Sample data:');
    sample.forEach(row => {
      console.log(`  - ${row.SYMBOL_NAME} | ${row.UNDERLYING_SYMBOL} | ${row.INSTRUMENT_TYPE} | ${row.EXCH_ID}`);
    });

    // Check for NSE options specifically
    const nseOptions = await prisma.$queryRaw`
      SELECT "SYMBOL_NAME", "UNDERLYING_SYMBOL", "INSTRUMENT_TYPE", "SM_EXPIRY_DATE", "STRIKE_PRICE", "OPTION_TYPE"
      FROM "Instruments" 
      WHERE "EXCH_ID" = 'NSE'
      AND "INSTRUMENT_TYPE" LIKE '%OPT%'
      LIMIT 10
    `;
    
    console.log('\n📈 NSE Options:');
    nseOptions.forEach(row => {
      console.log(`  - ${row.SYMBOL_NAME} | ${row.UNDERLYING_SYMBOL} | ${row.INSTRUMENT_TYPE} | ${row.SM_EXPIRY_DATE} | ${row.STRIKE_PRICE} | ${row.OPTION_TYPE}`);
    });

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkDbData();
