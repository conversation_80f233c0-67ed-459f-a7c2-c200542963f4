require('dotenv').config({ path: '.env.database' });
const { PrismaClient } = require('../generated/prisma');

const prisma = new PrismaClient();

async function checkDatabase() {
  try {
    console.log('🔌 Connecting to PostgreSQL database...');
    await prisma.$connect();
    console.log('✅ Connected to PostgreSQL successfully!');
    
    // Check what tables exist
    console.log('\n📊 Checking database tables...');
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `;
    
    console.log(`Found ${tables.length} tables:`);
    tables.forEach(table => {
      console.log(`   - ${table.table_name}`);
    });
    
    // Check if Instrument table exists and get count
    try {
      const instrumentCount = await prisma.instrument.count();
      console.log(`\n📈 Instrument table: ${instrumentCount} records`);
      
      // Check NIFTY options specifically
      const niftyOptions = await prisma.instrument.count({
        where: {
          symbol: 'NIFTY',
          instrumentType: 'OP'
        }
      });
      console.log(`🎯 NIFTY options: ${niftyOptions} records`);
      
      // Get sample NIFTY options
      const sampleOptions = await prisma.instrument.findMany({
        where: {
          symbol: 'NIFTY',
          instrumentType: 'OP'
        },
        take: 5,
        orderBy: { expiryDate: 'asc' }
      });
      
      console.log('\n📋 Sample NIFTY options:');
      sampleOptions.forEach(opt => {
        console.log(`   ${opt.symbol} ${opt.strikePrice} ${opt.optionType} (${opt.expiryDate}) - ID: ${opt.securityId}`);
      });
      
      // Get expiry dates
      const expiries = await prisma.instrument.findMany({
        where: {
          symbol: 'NIFTY',
          instrumentType: 'OP',
          expiryDate: { not: null }
        },
        select: { expiryDate: true },
        distinct: ['expiryDate'],
        orderBy: { expiryDate: 'asc' }
      });
      
      console.log(`\n📅 Available expiry dates: ${expiries.length}`);
      expiries.slice(0, 10).forEach(exp => {
        console.log(`   - ${exp.expiryDate}`);
      });
      
    } catch (error) {
      console.log('⚠️ Instrument table does not exist or is empty');
    }
    
    console.log('\n🎉 Database check completed!');
    
  } catch (error) {
    console.error('❌ Database check failed:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabase();
