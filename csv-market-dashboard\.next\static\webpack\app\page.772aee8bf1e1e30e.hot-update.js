"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useEnhancedMarketData.ts":
/*!********************************************!*\
  !*** ./src/hooks/useEnhancedMarketData.ts ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEnhancedMarketData: function() { return /* binding */ useEnhancedMarketData; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * Enhanced Market Data Hook\r\n * Provides optimized access to market data with automatic initialization\r\n * Features:\r\n * - Automatic WebSocket connection management\r\n * - Optimized re-rendering with selective subscriptions\r\n * - Auto-save functionality\r\n * - Error recovery and reconnection\r\n */ \nconst useEnhancedMarketData = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { autoConnect = true, autoLoadCache = true, autoSaveInterval = 30000, reconnectOnError = true, maxReconnectAttempts = 5 } = options;\n    // Temporary fallback implementation\n    const connection = {\n        status: \"disconnected\",\n        isConnected: false,\n        error: null,\n        connectionStats: {\n            totalMessages: 0,\n            connectionUptime: 0\n        }\n    };\n    const marketDataMap = new Map();\n    const cache = {\n        isLoaded: false,\n        totalCacheSize: 0,\n        pendingUpdates: 0,\n        lastCacheUpdate: null\n    };\n    const ui = {\n        filters: {},\n        sortConfig: {\n            field: \"symbol\",\n            direction: \"asc\"\n        },\n        selectedInstruments: new Set(),\n        viewMode: \"table\",\n        autoRefresh: true\n    };\n    const isLoading = false;\n    // Store actions - temporary mock functions with useCallback to fix dependency warnings\n    const initializeConnection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{}, []);\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{}, []);\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{}, []);\n    const reconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{}, []);\n    const loadFromCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{}, []);\n    const saveToCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (force)=>{}, []);\n    const clearCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{}, []);\n    const setFilters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((filters)=>{}, []);\n    const setSortConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((field, direction)=>{}, []);\n    const getFilteredMarketData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>[], []);\n    const getSortedMarketData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>[], []);\n    const getMarketDataBySecurityId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((securityId)=>undefined, []);\n    const getMarketDataBySymbol = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((symbol)=>undefined, []);\n    const subscribeToInstrument = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((securityId)=>{}, []);\n    const unsubscribeFromInstrument = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((securityId)=>{}, []);\n    const reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{}, []);\n    // Refs for intervals and tracking\n    const autoSaveIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const initializationRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Convert Map to Array for components that expect arrays\n    const marketDataArray = Array.from(marketDataMap.values());\n    // Initialize connection and cache on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const initialize = async ()=>{\n            if (initializationRef.current) return;\n            initializationRef.current = true;\n            console.log(\"\\uD83D\\uDE80 Enhanced Hook: Initializing...\");\n            try {\n                // Load cache first for instant data display\n                if (autoLoadCache && !cache.isLoaded) {\n                    await loadFromCache();\n                }\n                // Initialize WebSocket connection\n                if (autoConnect && connection.status === \"disconnected\") {\n                    await initializeConnection();\n                }\n            } catch (error) {\n                console.error(\"❌ Enhanced Hook: Initialization failed:\", error);\n            }\n        };\n        initialize();\n        // Cleanup on unmount\n        return ()=>{\n            if (autoSaveIntervalRef.current) {\n                clearInterval(autoSaveIntervalRef.current);\n            }\n        };\n    }, [\n        autoConnect,\n        autoLoadCache,\n        cache.isLoaded,\n        connection.status,\n        initializeConnection,\n        loadFromCache\n    ]);\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (autoSaveInterval > 0 && marketDataArray.length > 0) {\n            if (autoSaveIntervalRef.current) {\n                clearInterval(autoSaveIntervalRef.current);\n            }\n            autoSaveIntervalRef.current = setInterval(()=>{\n                saveToCache();\n            }, autoSaveInterval);\n            return ()=>{\n                if (autoSaveIntervalRef.current) {\n                    clearInterval(autoSaveIntervalRef.current);\n                }\n            };\n        }\n    }, [\n        autoSaveInterval,\n        marketDataArray.length,\n        saveToCache\n    ]);\n    // Auto-reconnect on error\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (reconnectOnError && connection.status === \"error\" && reconnectAttemptsRef.current < maxReconnectAttempts) {\n            const retryDelay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);\n            console.log(\"\\uD83D\\uDD04 Enhanced Hook: Auto-reconnecting in \".concat(retryDelay, \"ms (attempt \").concat(reconnectAttemptsRef.current + 1, \"/\").concat(maxReconnectAttempts, \")\"));\n            const timeoutId = setTimeout(async ()=>{\n                try {\n                    reconnectAttemptsRef.current++;\n                    await reconnect();\n                } catch (error) {\n                    console.error(\"❌ Enhanced Hook: Auto-reconnect failed:\", error);\n                }\n            }, retryDelay);\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        connection.status,\n        reconnect,\n        reconnectOnError,\n        maxReconnectAttempts\n    ]);\n    // Reset reconnect attempts on successful connection\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (connection.status === \"connected\") {\n            reconnectAttemptsRef.current = 0;\n        }\n    }, [\n        connection.status\n    ]);\n    // Optimized data access functions\n    const getFilteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        return getFilteredMarketData();\n    }, [\n        getFilteredMarketData\n    ]);\n    const getSortedData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        return getSortedMarketData();\n    }, [\n        getSortedMarketData\n    ]);\n    const getDataBySecurityId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((securityId)=>{\n        return getMarketDataBySecurityId(securityId);\n    }, [\n        getMarketDataBySecurityId\n    ]);\n    const getDataBySymbol = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((symbol)=>{\n        return getMarketDataBySymbol(symbol);\n    }, [\n        getMarketDataBySymbol\n    ]);\n    // Enhanced filter functions\n    const updateFilters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((filters)=>{\n        setFilters(filters);\n    }, [\n        setFilters,\n        ui\n    ]);\n    const updateSort = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((field, direction)=>{\n        setSortConfig(field, direction);\n    }, [\n        setSortConfig\n    ]);\n    // Subscription management\n    const subscribe = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((securityId)=>{\n        subscribeToInstrument(securityId);\n    }, [\n        subscribeToInstrument\n    ]);\n    const unsubscribe = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((securityId)=>{\n        unsubscribeFromInstrument(securityId);\n    }, [\n        unsubscribeFromInstrument\n    ]);\n    // Connection management\n    const forceReconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        try {\n            reconnectAttemptsRef.current = 0; // Reset attempts for manual reconnect\n            await reconnect();\n        } catch (error) {\n            console.error(\"❌ Enhanced Hook: Manual reconnect failed:\", error);\n            throw error;\n        }\n    }, [\n        reconnect\n    ]);\n    const forceRefresh = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        try {\n            await loadFromCache();\n        } catch (error) {\n            console.error(\"❌ Enhanced Hook: Force refresh failed:\", error);\n            throw error;\n        }\n    }, [\n        loadFromCache\n    ]);\n    const forceSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        try {\n            await saveToCache(true);\n        } catch (error) {\n            console.error(\"❌ Enhanced Hook: Force save failed:\", error);\n            throw error;\n        }\n    }, [\n        saveToCache\n    ]);\n    // Stats and computed values\n    const stats = {\n        totalInstruments: marketDataArray.length,\n        connectedInstruments: marketDataArray.filter((item)=>item.ltp && item.ltp > 0).length,\n        lastUpdate: null,\n        cacheSize: cache.totalCacheSize,\n        connectionUptime: connection.connectionStats.connectionUptime,\n        messagesReceived: connection.connectionStats.totalMessages,\n        reconnectAttempts: reconnectAttemptsRef.current,\n        isAutoSaving: autoSaveIntervalRef.current !== null\n    };\n    return {\n        // Data\n        marketData: marketDataArray,\n        marketDataMap,\n        filteredData: getFilteredData(),\n        sortedData: getSortedData(),\n        // Connection state\n        isConnected: connection.isConnected,\n        connectionStatus: connection.status,\n        connectionError: connection.error,\n        connectionStats: connection.connectionStats,\n        // Cache state\n        cacheLoaded: cache.isLoaded,\n        cacheUpdating: cache.pendingUpdates > 0,\n        lastCacheUpdate: cache.lastCacheUpdate,\n        // UI state\n        filters: ui.filters,\n        sortConfig: ui.sortConfig,\n        selectedInstruments: ui.selectedInstruments,\n        viewMode: ui.viewMode,\n        autoRefresh: ui.autoRefresh,\n        // Loading states\n        isLoading,\n        isInitializing: !initializationRef.current,\n        // Data access functions\n        getDataBySecurityId,\n        getDataBySymbol,\n        getFilteredData,\n        getSortedData,\n        // Actions\n        updateFilters,\n        updateSort,\n        subscribe,\n        unsubscribe,\n        // Connection management\n        connect,\n        disconnect,\n        reconnect: forceReconnect,\n        // Cache management\n        refresh: forceRefresh,\n        save: forceSave,\n        clearCache,\n        // Utility\n        reset,\n        stats,\n        // Advanced actions (expose if needed)\n        _store: {\n            setFilters,\n            setSortConfig,\n            subscribeToInstrument,\n            unsubscribeFromInstrument\n        }\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useEnhancedMarketData);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VFbmhhbmNlZE1hcmtldERhdGEudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7Ozs7Ozs7O0NBUUMsR0FFc0Q7QUFZaEQsTUFBTUcsd0JBQXdCO1FBQUNDLDJFQUF3QyxDQUFDO0lBQzdFLE1BQU0sRUFDSkMsY0FBYyxJQUFJLEVBQ2xCQyxnQkFBZ0IsSUFBSSxFQUNwQkMsbUJBQW1CLEtBQUssRUFDeEJDLG1CQUFtQixJQUFJLEVBQ3ZCQyx1QkFBdUIsQ0FBQyxFQUN6QixHQUFHTDtJQUVKLG9DQUFvQztJQUNwQyxNQUFNTSxhQUFhO1FBQ2pCQyxRQUFRO1FBQ1JDLGFBQWE7UUFDYkMsT0FBTztRQUNQQyxpQkFBaUI7WUFDZkMsZUFBZTtZQUNmQyxrQkFBa0I7UUFDcEI7SUFDRjtJQUNBLE1BQU1DLGdCQUFnQixJQUFJQztJQUMxQixNQUFNQyxRQUFRO1FBQ1pDLFVBQVU7UUFDVkMsZ0JBQWdCO1FBQ2hCQyxnQkFBZ0I7UUFDaEJDLGlCQUFpQjtJQUNuQjtJQUNBLE1BQU1DLEtBQUs7UUFDVEMsU0FBUyxDQUFDO1FBQ1ZDLFlBQVk7WUFBRUMsT0FBTztZQUFVQyxXQUFXO1FBQWE7UUFDdkRDLHFCQUFxQixJQUFJQztRQUN6QkMsVUFBVTtRQUNWQyxhQUFhO0lBQ2Y7SUFDQSxNQUFNQyxZQUFZO0lBRWxCLHVGQUF1RjtJQUN2RixNQUFNQyx1QkFBdUJqQyxrREFBV0EsQ0FBQyxXQUFhLEdBQUcsRUFBRTtJQUMzRCxNQUFNa0MsVUFBVWxDLGtEQUFXQSxDQUFDLFdBQWEsR0FBRyxFQUFFO0lBQzlDLE1BQU1tQyxhQUFhbkMsa0RBQVdBLENBQUMsS0FBTyxHQUFHLEVBQUU7SUFDM0MsTUFBTW9DLFlBQVlwQyxrREFBV0EsQ0FBQyxXQUFhLEdBQUcsRUFBRTtJQUNoRCxNQUFNcUMsZ0JBQWdCckMsa0RBQVdBLENBQUMsV0FBYSxHQUFHLEVBQUU7SUFDcEQsTUFBTXNDLGNBQWN0QyxrREFBV0EsQ0FBQyxPQUFPdUMsU0FBcUIsR0FBRyxFQUFFO0lBQ2pFLE1BQU1DLGFBQWF4QyxrREFBV0EsQ0FBQyxXQUFhLEdBQUcsRUFBRTtJQUNqRCxNQUFNeUMsYUFBYXpDLGtEQUFXQSxDQUFDLENBQUN3QixXQUFrQixHQUFHLEVBQUU7SUFDdkQsTUFBTWtCLGdCQUFnQjFDLGtEQUFXQSxDQUFDLENBQUMwQixPQUFlQyxhQUFnQyxHQUFHLEVBQUU7SUFDdkYsTUFBTWdCLHdCQUF3QjNDLGtEQUFXQSxDQUFDLElBQU0sRUFBRSxFQUFFLEVBQUU7SUFDdEQsTUFBTTRDLHNCQUFzQjVDLGtEQUFXQSxDQUFDLElBQU0sRUFBRSxFQUFFLEVBQUU7SUFDcEQsTUFBTTZDLDRCQUE0QjdDLGtEQUFXQSxDQUFDLENBQUM4QyxhQUF1QkMsV0FBVyxFQUFFO0lBQ25GLE1BQU1DLHdCQUF3QmhELGtEQUFXQSxDQUFDLENBQUNpRCxTQUFtQkYsV0FBVyxFQUFFO0lBQzNFLE1BQU1HLHdCQUF3QmxELGtEQUFXQSxDQUFDLENBQUM4QyxjQUF3QixHQUFHLEVBQUU7SUFDeEUsTUFBTUssNEJBQTRCbkQsa0RBQVdBLENBQUMsQ0FBQzhDLGNBQXdCLEdBQUcsRUFBRTtJQUM1RSxNQUFNTSxRQUFRcEQsa0RBQVdBLENBQUMsS0FBTyxHQUFHLEVBQUU7SUFFdEMsa0NBQWtDO0lBQ2xDLE1BQU1xRCxzQkFBc0JwRCw2Q0FBTUEsQ0FBd0I7SUFDMUQsTUFBTXFELHVCQUF1QnJELDZDQUFNQSxDQUFDO0lBQ3BDLE1BQU1zRCxvQkFBb0J0RCw2Q0FBTUEsQ0FBQztJQUVqQyx5REFBeUQ7SUFDekQsTUFBTXVELGtCQUFrQkMsTUFBTUMsSUFBSSxDQUFDMUMsY0FBYzJDLE1BQU07SUFFdkQsMkNBQTJDO0lBQzNDNUQsZ0RBQVNBLENBQUM7UUFDUixNQUFNNkQsYUFBYTtZQUNqQixJQUFJTCxrQkFBa0JNLE9BQU8sRUFBRTtZQUMvQk4sa0JBQWtCTSxPQUFPLEdBQUc7WUFFNUJDLFFBQVFDLEdBQUcsQ0FBQztZQUVaLElBQUk7Z0JBQ0YsNENBQTRDO2dCQUM1QyxJQUFJMUQsaUJBQWlCLENBQUNhLE1BQU1DLFFBQVEsRUFBRTtvQkFDcEMsTUFBTWtCO2dCQUNSO2dCQUVBLGtDQUFrQztnQkFDbEMsSUFBSWpDLGVBQWVLLFdBQVdDLE1BQU0sS0FBSyxnQkFBZ0I7b0JBQ3ZELE1BQU11QjtnQkFDUjtZQUNGLEVBQUUsT0FBT3JCLE9BQU87Z0JBQ2RrRCxRQUFRbEQsS0FBSyxDQUFDLDJDQUEyQ0E7WUFDM0Q7UUFDRjtRQUVBZ0Q7UUFFQSxxQkFBcUI7UUFDckIsT0FBTztZQUNMLElBQUlQLG9CQUFvQlEsT0FBTyxFQUFFO2dCQUMvQkcsY0FBY1gsb0JBQW9CUSxPQUFPO1lBQzNDO1FBQ0Y7SUFDRixHQUFHO1FBQUN6RDtRQUFhQztRQUFlYSxNQUFNQyxRQUFRO1FBQUVWLFdBQVdDLE1BQU07UUFBRXVCO1FBQXNCSTtLQUFjO0lBRXZHLDBCQUEwQjtJQUMxQnRDLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSU8sbUJBQW1CLEtBQUtrRCxnQkFBZ0JTLE1BQU0sR0FBRyxHQUFHO1lBQ3RELElBQUlaLG9CQUFvQlEsT0FBTyxFQUFFO2dCQUMvQkcsY0FBY1gsb0JBQW9CUSxPQUFPO1lBQzNDO1lBRUFSLG9CQUFvQlEsT0FBTyxHQUFHSyxZQUFZO2dCQUN4QzVCO1lBQ0YsR0FBR2hDO1lBRUgsT0FBTztnQkFDTCxJQUFJK0Msb0JBQW9CUSxPQUFPLEVBQUU7b0JBQy9CRyxjQUFjWCxvQkFBb0JRLE9BQU87Z0JBQzNDO1lBQ0Y7UUFDRjtJQUNGLEdBQUc7UUFBQ3ZEO1FBQWtCa0QsZ0JBQWdCUyxNQUFNO1FBQUUzQjtLQUFZO0lBRTFELDBCQUEwQjtJQUMxQnZDLGdEQUFTQSxDQUFDO1FBQ1IsSUFDRVEsb0JBQ0FFLFdBQVdDLE1BQU0sS0FBSyxXQUN0QjRDLHFCQUFxQk8sT0FBTyxHQUFHckQsc0JBQy9CO1lBQ0EsTUFBTTJELGFBQWFDLEtBQUtDLEdBQUcsQ0FBQyxPQUFPRCxLQUFLRSxHQUFHLENBQUMsR0FBR2hCLHFCQUFxQk8sT0FBTyxHQUFHO1lBRTlFQyxRQUFRQyxHQUFHLENBQ1Qsb0RBQW1FVCxPQUF6QmEsWUFBVyxnQkFBa0QzRCxPQUFwQzhDLHFCQUFxQk8sT0FBTyxHQUFHLEdBQUUsS0FBd0IsT0FBckJyRCxzQkFBcUI7WUFHOUgsTUFBTStELFlBQVlDLFdBQVc7Z0JBQzNCLElBQUk7b0JBQ0ZsQixxQkFBcUJPLE9BQU87b0JBQzVCLE1BQU16QjtnQkFDUixFQUFFLE9BQU94QixPQUFPO29CQUNka0QsUUFBUWxELEtBQUssQ0FBQywyQ0FBMkNBO2dCQUMzRDtZQUNGLEdBQUd1RDtZQUVILE9BQU8sSUFBTU0sYUFBYUY7UUFDNUI7SUFDRixHQUFHO1FBQUM5RCxXQUFXQyxNQUFNO1FBQUUwQjtRQUFXN0I7UUFBa0JDO0tBQXFCO0lBRXpFLG9EQUFvRDtJQUNwRFQsZ0RBQVNBLENBQUM7UUFDUixJQUFJVSxXQUFXQyxNQUFNLEtBQUssYUFBYTtZQUNyQzRDLHFCQUFxQk8sT0FBTyxHQUFHO1FBQ2pDO0lBQ0YsR0FBRztRQUFDcEQsV0FBV0MsTUFBTTtLQUFDO0lBRXRCLGtDQUFrQztJQUNsQyxNQUFNZ0Usa0JBQWtCMUUsa0RBQVdBLENBQUM7UUFDbEMsT0FBTzJDO0lBQ1QsR0FBRztRQUFDQTtLQUFzQjtJQUUxQixNQUFNZ0MsZ0JBQWdCM0Usa0RBQVdBLENBQUM7UUFDaEMsT0FBTzRDO0lBQ1QsR0FBRztRQUFDQTtLQUFvQjtJQUV4QixNQUFNZ0Msc0JBQXNCNUUsa0RBQVdBLENBQUMsQ0FBQzhDO1FBQ3ZDLE9BQU9ELDBCQUEwQkM7SUFDbkMsR0FBRztRQUFDRDtLQUEwQjtJQUU5QixNQUFNZ0Msa0JBQWtCN0Usa0RBQVdBLENBQUMsQ0FBQ2lEO1FBQ25DLE9BQU9ELHNCQUFzQkM7SUFDL0IsR0FBRztRQUFDRDtLQUFzQjtJQUUxQiw0QkFBNEI7SUFDNUIsTUFBTThCLGdCQUFnQjlFLGtEQUFXQSxDQUFDLENBQUN3QjtRQUNqQ2lCLFdBQVdqQjtJQUNiLEdBQUc7UUFBQ2lCO1FBQVlsQjtLQUFHO0lBRW5CLE1BQU13RCxhQUFhL0Usa0RBQVdBLENBQUMsQ0FBQzBCLE9BQWVDO1FBQzdDZSxjQUFjaEIsT0FBT0M7SUFDdkIsR0FBRztRQUFDZTtLQUFjO0lBRWxCLDBCQUEwQjtJQUMxQixNQUFNc0MsWUFBWWhGLGtEQUFXQSxDQUFDLENBQUM4QztRQUM3Qkksc0JBQXNCSjtJQUN4QixHQUFHO1FBQUNJO0tBQXNCO0lBRTFCLE1BQU0rQixjQUFjakYsa0RBQVdBLENBQUMsQ0FBQzhDO1FBQy9CSywwQkFBMEJMO0lBQzVCLEdBQUc7UUFBQ0s7S0FBMEI7SUFFOUIsd0JBQXdCO0lBQ3hCLE1BQU0rQixpQkFBaUJsRixrREFBV0EsQ0FBQztRQUNqQyxJQUFJO1lBQ0ZzRCxxQkFBcUJPLE9BQU8sR0FBRyxHQUFHLHNDQUFzQztZQUN4RSxNQUFNekI7UUFDUixFQUFFLE9BQU94QixPQUFPO1lBQ2RrRCxRQUFRbEQsS0FBSyxDQUFDLDZDQUE2Q0E7WUFDM0QsTUFBTUE7UUFDUjtJQUNGLEdBQUc7UUFBQ3dCO0tBQVU7SUFFZCxNQUFNK0MsZUFBZW5GLGtEQUFXQSxDQUFDO1FBQy9CLElBQUk7WUFDRixNQUFNcUM7UUFDUixFQUFFLE9BQU96QixPQUFPO1lBQ2RrRCxRQUFRbEQsS0FBSyxDQUFDLDBDQUEwQ0E7WUFDeEQsTUFBTUE7UUFDUjtJQUNGLEdBQUc7UUFBQ3lCO0tBQWM7SUFFbEIsTUFBTStDLFlBQVlwRixrREFBV0EsQ0FBQztRQUM1QixJQUFJO1lBQ0YsTUFBTXNDLFlBQVk7UUFDcEIsRUFBRSxPQUFPMUIsT0FBTztZQUNka0QsUUFBUWxELEtBQUssQ0FBQyx1Q0FBdUNBO1lBQ3JELE1BQU1BO1FBQ1I7SUFDRixHQUFHO1FBQUMwQjtLQUFZO0lBRWhCLDRCQUE0QjtJQUM1QixNQUFNK0MsUUFBUTtRQUNaQyxrQkFBa0I5QixnQkFBZ0JTLE1BQU07UUFDeENzQixzQkFBc0IvQixnQkFBZ0JnQyxNQUFNLENBQUNDLENBQUFBLE9BQVFBLEtBQUtDLEdBQUcsSUFBSUQsS0FBS0MsR0FBRyxHQUFHLEdBQUd6QixNQUFNO1FBQ3JGMEIsWUFBWTtRQUNaQyxXQUFXMUUsTUFBTUUsY0FBYztRQUMvQkwsa0JBQWtCTixXQUFXSSxlQUFlLENBQUNFLGdCQUFnQjtRQUM3RDhFLGtCQUFrQnBGLFdBQVdJLGVBQWUsQ0FBQ0MsYUFBYTtRQUMxRGdGLG1CQUFtQnhDLHFCQUFxQk8sT0FBTztRQUMvQ2tDLGNBQWMxQyxvQkFBb0JRLE9BQU8sS0FBSztJQUNoRDtJQUVBLE9BQU87UUFDTCxPQUFPO1FBQ1BtQyxZQUFZeEM7UUFDWnhDO1FBQ0FpRixjQUFjdkI7UUFDZHdCLFlBQVl2QjtRQUVaLG1CQUFtQjtRQUNuQmhFLGFBQWFGLFdBQVdFLFdBQVc7UUFDbkN3RixrQkFBa0IxRixXQUFXQyxNQUFNO1FBQ25DMEYsaUJBQWlCM0YsV0FBV0csS0FBSztRQUNqQ0MsaUJBQWlCSixXQUFXSSxlQUFlO1FBRTNDLGNBQWM7UUFDZHdGLGFBQWFuRixNQUFNQyxRQUFRO1FBQzNCbUYsZUFBZXBGLE1BQU1HLGNBQWMsR0FBRztRQUN0Q0MsaUJBQWlCSixNQUFNSSxlQUFlO1FBRXRDLFdBQVc7UUFDWEUsU0FBU0QsR0FBR0MsT0FBTztRQUNuQkMsWUFBWUYsR0FBR0UsVUFBVTtRQUN6QkcscUJBQXFCTCxHQUFHSyxtQkFBbUI7UUFDM0NFLFVBQVVQLEdBQUdPLFFBQVE7UUFDckJDLGFBQWFSLEdBQUdRLFdBQVc7UUFFM0IsaUJBQWlCO1FBQ2pCQztRQUNBdUUsZ0JBQWdCLENBQUNoRCxrQkFBa0JNLE9BQU87UUFFMUMsd0JBQXdCO1FBQ3hCZTtRQUNBQztRQUNBSDtRQUNBQztRQUVBLFVBQVU7UUFDVkc7UUFDQUM7UUFDQUM7UUFDQUM7UUFFQSx3QkFBd0I7UUFDeEIvQztRQUNBQztRQUNBQyxXQUFXOEM7UUFFWCxtQkFBbUI7UUFDbkJzQixTQUFTckI7UUFDVHNCLE1BQU1yQjtRQUNONUM7UUFFQSxVQUFVO1FBQ1ZZO1FBQ0FpQztRQUVBLHNDQUFzQztRQUN0Q3FCLFFBQVE7WUFDTmpFO1lBQ0FDO1lBQ0FRO1lBQ0FDO1FBRUY7SUFDRjtBQUNGLEVBQUU7QUFFRiwrREFBZWpELHFCQUFxQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvaG9va3MvdXNlRW5oYW5jZWRNYXJrZXREYXRhLnRzPzVjYTgiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXHJcbiAqIEVuaGFuY2VkIE1hcmtldCBEYXRhIEhvb2tcclxuICogUHJvdmlkZXMgb3B0aW1pemVkIGFjY2VzcyB0byBtYXJrZXQgZGF0YSB3aXRoIGF1dG9tYXRpYyBpbml0aWFsaXphdGlvblxyXG4gKiBGZWF0dXJlczpcclxuICogLSBBdXRvbWF0aWMgV2ViU29ja2V0IGNvbm5lY3Rpb24gbWFuYWdlbWVudFxyXG4gKiAtIE9wdGltaXplZCByZS1yZW5kZXJpbmcgd2l0aCBzZWxlY3RpdmUgc3Vic2NyaXB0aW9uc1xyXG4gKiAtIEF1dG8tc2F2ZSBmdW5jdGlvbmFsaXR5XHJcbiAqIC0gRXJyb3IgcmVjb3ZlcnkgYW5kIHJlY29ubmVjdGlvblxyXG4gKi9cclxuXHJcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2ssIHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcclxuLy8gVGVtcG9yYXJpbHkgZGlzYWJsZWQgZW5oYW5jZWQgc3RvcmUgaW1wb3J0XHJcbi8vIGltcG9ydCB7IHVzZUVuaGFuY2VkTWFya2V0U3RvcmUsIHNlbGVjdENvbm5lY3Rpb24sIHNlbGVjdE1hcmtldERhdGEsIHNlbGVjdENhY2hlLCBzZWxlY3RVSSwgc2VsZWN0SXNMb2FkaW5nIH0gZnJvbSAnQC9zdG9yZS9lbmhhbmNlZE1hcmtldFN0b3JlJztcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgVXNlRW5oYW5jZWRNYXJrZXREYXRhT3B0aW9ucyB7XHJcbiAgYXV0b0Nvbm5lY3Q/OiBib29sZWFuO1xyXG4gIGF1dG9Mb2FkQ2FjaGU/OiBib29sZWFuO1xyXG4gIGF1dG9TYXZlSW50ZXJ2YWw/OiBudW1iZXI7XHJcbiAgcmVjb25uZWN0T25FcnJvcj86IGJvb2xlYW47XHJcbiAgbWF4UmVjb25uZWN0QXR0ZW1wdHM/OiBudW1iZXI7XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCB1c2VFbmhhbmNlZE1hcmtldERhdGEgPSAob3B0aW9uczogVXNlRW5oYW5jZWRNYXJrZXREYXRhT3B0aW9ucyA9IHt9KSA9PiB7XHJcbiAgY29uc3Qge1xyXG4gICAgYXV0b0Nvbm5lY3QgPSB0cnVlLFxyXG4gICAgYXV0b0xvYWRDYWNoZSA9IHRydWUsXHJcbiAgICBhdXRvU2F2ZUludGVydmFsID0gMzAwMDAsIC8vIDMwIHNlY29uZHNcclxuICAgIHJlY29ubmVjdE9uRXJyb3IgPSB0cnVlLFxyXG4gICAgbWF4UmVjb25uZWN0QXR0ZW1wdHMgPSA1LFxyXG4gIH0gPSBvcHRpb25zO1xyXG5cclxuICAvLyBUZW1wb3JhcnkgZmFsbGJhY2sgaW1wbGVtZW50YXRpb25cclxuICBjb25zdCBjb25uZWN0aW9uID0ge1xyXG4gICAgc3RhdHVzOiAnZGlzY29ubmVjdGVkJyBhcyBhbnksXHJcbiAgICBpc0Nvbm5lY3RlZDogZmFsc2UsXHJcbiAgICBlcnJvcjogbnVsbCxcclxuICAgIGNvbm5lY3Rpb25TdGF0czoge1xyXG4gICAgICB0b3RhbE1lc3NhZ2VzOiAwLFxyXG4gICAgICBjb25uZWN0aW9uVXB0aW1lOiAwLFxyXG4gICAgfSxcclxuICB9O1xyXG4gIGNvbnN0IG1hcmtldERhdGFNYXAgPSBuZXcgTWFwKCk7XHJcbiAgY29uc3QgY2FjaGUgPSB7XHJcbiAgICBpc0xvYWRlZDogZmFsc2UsXHJcbiAgICB0b3RhbENhY2hlU2l6ZTogMCxcclxuICAgIHBlbmRpbmdVcGRhdGVzOiAwLFxyXG4gICAgbGFzdENhY2hlVXBkYXRlOiBudWxsLFxyXG4gIH07XHJcbiAgY29uc3QgdWkgPSB7XHJcbiAgICBmaWx0ZXJzOiB7fSxcclxuICAgIHNvcnRDb25maWc6IHsgZmllbGQ6ICdzeW1ib2wnLCBkaXJlY3Rpb246ICdhc2MnIGFzIGFueSB9LFxyXG4gICAgc2VsZWN0ZWRJbnN0cnVtZW50czogbmV3IFNldCgpLFxyXG4gICAgdmlld01vZGU6ICd0YWJsZScgYXMgYW55LFxyXG4gICAgYXV0b1JlZnJlc2g6IHRydWUsXHJcbiAgfTtcclxuICBjb25zdCBpc0xvYWRpbmcgPSBmYWxzZTtcclxuXHJcbiAgLy8gU3RvcmUgYWN0aW9ucyAtIHRlbXBvcmFyeSBtb2NrIGZ1bmN0aW9ucyB3aXRoIHVzZUNhbGxiYWNrIHRvIGZpeCBkZXBlbmRlbmN5IHdhcm5pbmdzXHJcbiAgY29uc3QgaW5pdGlhbGl6ZUNvbm5lY3Rpb24gPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7fSwgW10pO1xyXG4gIGNvbnN0IGNvbm5lY3QgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7fSwgW10pO1xyXG4gIGNvbnN0IGRpc2Nvbm5lY3QgPSB1c2VDYWxsYmFjaygoKSA9PiB7fSwgW10pO1xyXG4gIGNvbnN0IHJlY29ubmVjdCA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHt9LCBbXSk7XHJcbiAgY29uc3QgbG9hZEZyb21DYWNoZSA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHt9LCBbXSk7XHJcbiAgY29uc3Qgc2F2ZVRvQ2FjaGUgPSB1c2VDYWxsYmFjayhhc3luYyAoZm9yY2U/OiBib29sZWFuKSA9PiB7fSwgW10pO1xyXG4gIGNvbnN0IGNsZWFyQ2FjaGUgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7fSwgW10pO1xyXG4gIGNvbnN0IHNldEZpbHRlcnMgPSB1c2VDYWxsYmFjaygoZmlsdGVyczogYW55KSA9PiB7fSwgW10pO1xyXG4gIGNvbnN0IHNldFNvcnRDb25maWcgPSB1c2VDYWxsYmFjaygoZmllbGQ6IHN0cmluZywgZGlyZWN0aW9uPzogJ2FzYycgfCAnZGVzYycpID0+IHt9LCBbXSk7XHJcbiAgY29uc3QgZ2V0RmlsdGVyZWRNYXJrZXREYXRhID0gdXNlQ2FsbGJhY2soKCkgPT4gW10sIFtdKTtcclxuICBjb25zdCBnZXRTb3J0ZWRNYXJrZXREYXRhID0gdXNlQ2FsbGJhY2soKCkgPT4gW10sIFtdKTtcclxuICBjb25zdCBnZXRNYXJrZXREYXRhQnlTZWN1cml0eUlkID0gdXNlQ2FsbGJhY2soKHNlY3VyaXR5SWQ6IHN0cmluZykgPT4gdW5kZWZpbmVkLCBbXSk7XHJcbiAgY29uc3QgZ2V0TWFya2V0RGF0YUJ5U3ltYm9sID0gdXNlQ2FsbGJhY2soKHN5bWJvbDogc3RyaW5nKSA9PiB1bmRlZmluZWQsIFtdKTtcclxuICBjb25zdCBzdWJzY3JpYmVUb0luc3RydW1lbnQgPSB1c2VDYWxsYmFjaygoc2VjdXJpdHlJZDogc3RyaW5nKSA9PiB7fSwgW10pO1xyXG4gIGNvbnN0IHVuc3Vic2NyaWJlRnJvbUluc3RydW1lbnQgPSB1c2VDYWxsYmFjaygoc2VjdXJpdHlJZDogc3RyaW5nKSA9PiB7fSwgW10pO1xyXG4gIGNvbnN0IHJlc2V0ID0gdXNlQ2FsbGJhY2soKCkgPT4ge30sIFtdKTtcclxuXHJcbiAgLy8gUmVmcyBmb3IgaW50ZXJ2YWxzIGFuZCB0cmFja2luZ1xyXG4gIGNvbnN0IGF1dG9TYXZlSW50ZXJ2YWxSZWYgPSB1c2VSZWY8Tm9kZUpTLlRpbWVvdXQgfCBudWxsPihudWxsKTtcclxuICBjb25zdCByZWNvbm5lY3RBdHRlbXB0c1JlZiA9IHVzZVJlZigwKTtcclxuICBjb25zdCBpbml0aWFsaXphdGlvblJlZiA9IHVzZVJlZihmYWxzZSk7XHJcblxyXG4gIC8vIENvbnZlcnQgTWFwIHRvIEFycmF5IGZvciBjb21wb25lbnRzIHRoYXQgZXhwZWN0IGFycmF5c1xyXG4gIGNvbnN0IG1hcmtldERhdGFBcnJheSA9IEFycmF5LmZyb20obWFya2V0RGF0YU1hcC52YWx1ZXMoKSk7XHJcblxyXG4gIC8vIEluaXRpYWxpemUgY29ubmVjdGlvbiBhbmQgY2FjaGUgb24gbW91bnRcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgaW5pdGlhbGl6ZSA9IGFzeW5jICgpID0+IHtcclxuICAgICAgaWYgKGluaXRpYWxpemF0aW9uUmVmLmN1cnJlbnQpIHJldHVybjtcclxuICAgICAgaW5pdGlhbGl6YXRpb25SZWYuY3VycmVudCA9IHRydWU7XHJcblxyXG4gICAgICBjb25zb2xlLmxvZygn8J+agCBFbmhhbmNlZCBIb29rOiBJbml0aWFsaXppbmcuLi4nKTtcclxuXHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgLy8gTG9hZCBjYWNoZSBmaXJzdCBmb3IgaW5zdGFudCBkYXRhIGRpc3BsYXlcclxuICAgICAgICBpZiAoYXV0b0xvYWRDYWNoZSAmJiAhY2FjaGUuaXNMb2FkZWQpIHtcclxuICAgICAgICAgIGF3YWl0IGxvYWRGcm9tQ2FjaGUoKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIEluaXRpYWxpemUgV2ViU29ja2V0IGNvbm5lY3Rpb25cclxuICAgICAgICBpZiAoYXV0b0Nvbm5lY3QgJiYgY29ubmVjdGlvbi5zdGF0dXMgPT09ICdkaXNjb25uZWN0ZWQnKSB7XHJcbiAgICAgICAgICBhd2FpdCBpbml0aWFsaXplQ29ubmVjdGlvbigpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgRW5oYW5jZWQgSG9vazogSW5pdGlhbGl6YXRpb24gZmFpbGVkOicsIGVycm9yKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBpbml0aWFsaXplKCk7XHJcblxyXG4gICAgLy8gQ2xlYW51cCBvbiB1bm1vdW50XHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBpZiAoYXV0b1NhdmVJbnRlcnZhbFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgY2xlYXJJbnRlcnZhbChhdXRvU2F2ZUludGVydmFsUmVmLmN1cnJlbnQpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gIH0sIFthdXRvQ29ubmVjdCwgYXV0b0xvYWRDYWNoZSwgY2FjaGUuaXNMb2FkZWQsIGNvbm5lY3Rpb24uc3RhdHVzLCBpbml0aWFsaXplQ29ubmVjdGlvbiwgbG9hZEZyb21DYWNoZV0pO1xyXG5cclxuICAvLyBBdXRvLXNhdmUgZnVuY3Rpb25hbGl0eVxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoYXV0b1NhdmVJbnRlcnZhbCA+IDAgJiYgbWFya2V0RGF0YUFycmF5Lmxlbmd0aCA+IDApIHtcclxuICAgICAgaWYgKGF1dG9TYXZlSW50ZXJ2YWxSZWYuY3VycmVudCkge1xyXG4gICAgICAgIGNsZWFySW50ZXJ2YWwoYXV0b1NhdmVJbnRlcnZhbFJlZi5jdXJyZW50KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgYXV0b1NhdmVJbnRlcnZhbFJlZi5jdXJyZW50ID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xyXG4gICAgICAgIHNhdmVUb0NhY2hlKCk7XHJcbiAgICAgIH0sIGF1dG9TYXZlSW50ZXJ2YWwpO1xyXG5cclxuICAgICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgICBpZiAoYXV0b1NhdmVJbnRlcnZhbFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgICBjbGVhckludGVydmFsKGF1dG9TYXZlSW50ZXJ2YWxSZWYuY3VycmVudCk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9O1xyXG4gICAgfVxyXG4gIH0sIFthdXRvU2F2ZUludGVydmFsLCBtYXJrZXREYXRhQXJyYXkubGVuZ3RoLCBzYXZlVG9DYWNoZV0pO1xyXG5cclxuICAvLyBBdXRvLXJlY29ubmVjdCBvbiBlcnJvclxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoXHJcbiAgICAgIHJlY29ubmVjdE9uRXJyb3IgJiZcclxuICAgICAgY29ubmVjdGlvbi5zdGF0dXMgPT09ICdlcnJvcicgJiZcclxuICAgICAgcmVjb25uZWN0QXR0ZW1wdHNSZWYuY3VycmVudCA8IG1heFJlY29ubmVjdEF0dGVtcHRzXHJcbiAgICApIHtcclxuICAgICAgY29uc3QgcmV0cnlEZWxheSA9IE1hdGgubWluKDEwMDAgKiBNYXRoLnBvdygyLCByZWNvbm5lY3RBdHRlbXB0c1JlZi5jdXJyZW50KSwgMzAwMDApO1xyXG4gICAgICBcclxuICAgICAgY29uc29sZS5sb2coXHJcbiAgICAgICAgYPCflIQgRW5oYW5jZWQgSG9vazogQXV0by1yZWNvbm5lY3RpbmcgaW4gJHtyZXRyeURlbGF5fW1zIChhdHRlbXB0ICR7cmVjb25uZWN0QXR0ZW1wdHNSZWYuY3VycmVudCArIDF9LyR7bWF4UmVjb25uZWN0QXR0ZW1wdHN9KWBcclxuICAgICAgKTtcclxuXHJcbiAgICAgIGNvbnN0IHRpbWVvdXRJZCA9IHNldFRpbWVvdXQoYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICByZWNvbm5lY3RBdHRlbXB0c1JlZi5jdXJyZW50Kys7XHJcbiAgICAgICAgICBhd2FpdCByZWNvbm5lY3QoKTtcclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEVuaGFuY2VkIEhvb2s6IEF1dG8tcmVjb25uZWN0IGZhaWxlZDonLCBlcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgICB9LCByZXRyeURlbGF5KTtcclxuXHJcbiAgICAgIHJldHVybiAoKSA9PiBjbGVhclRpbWVvdXQodGltZW91dElkKTtcclxuICAgIH1cclxuICB9LCBbY29ubmVjdGlvbi5zdGF0dXMsIHJlY29ubmVjdCwgcmVjb25uZWN0T25FcnJvciwgbWF4UmVjb25uZWN0QXR0ZW1wdHNdKTtcclxuXHJcbiAgLy8gUmVzZXQgcmVjb25uZWN0IGF0dGVtcHRzIG9uIHN1Y2Nlc3NmdWwgY29ubmVjdGlvblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoY29ubmVjdGlvbi5zdGF0dXMgPT09ICdjb25uZWN0ZWQnKSB7XHJcbiAgICAgIHJlY29ubmVjdEF0dGVtcHRzUmVmLmN1cnJlbnQgPSAwO1xyXG4gICAgfVxyXG4gIH0sIFtjb25uZWN0aW9uLnN0YXR1c10pO1xyXG5cclxuICAvLyBPcHRpbWl6ZWQgZGF0YSBhY2Nlc3MgZnVuY3Rpb25zXHJcbiAgY29uc3QgZ2V0RmlsdGVyZWREYXRhID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgcmV0dXJuIGdldEZpbHRlcmVkTWFya2V0RGF0YSgpO1xyXG4gIH0sIFtnZXRGaWx0ZXJlZE1hcmtldERhdGFdKTtcclxuXHJcbiAgY29uc3QgZ2V0U29ydGVkRGF0YSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgIHJldHVybiBnZXRTb3J0ZWRNYXJrZXREYXRhKCk7XHJcbiAgfSwgW2dldFNvcnRlZE1hcmtldERhdGFdKTtcclxuXHJcbiAgY29uc3QgZ2V0RGF0YUJ5U2VjdXJpdHlJZCA9IHVzZUNhbGxiYWNrKChzZWN1cml0eUlkOiBzdHJpbmcpID0+IHtcclxuICAgIHJldHVybiBnZXRNYXJrZXREYXRhQnlTZWN1cml0eUlkKHNlY3VyaXR5SWQpO1xyXG4gIH0sIFtnZXRNYXJrZXREYXRhQnlTZWN1cml0eUlkXSk7XHJcblxyXG4gIGNvbnN0IGdldERhdGFCeVN5bWJvbCA9IHVzZUNhbGxiYWNrKChzeW1ib2w6IHN0cmluZykgPT4ge1xyXG4gICAgcmV0dXJuIGdldE1hcmtldERhdGFCeVN5bWJvbChzeW1ib2wpO1xyXG4gIH0sIFtnZXRNYXJrZXREYXRhQnlTeW1ib2xdKTtcclxuXHJcbiAgLy8gRW5oYW5jZWQgZmlsdGVyIGZ1bmN0aW9uc1xyXG4gIGNvbnN0IHVwZGF0ZUZpbHRlcnMgPSB1c2VDYWxsYmFjaygoZmlsdGVyczogUGFydGlhbDx0eXBlb2YgdWkuZmlsdGVycz4pID0+IHtcclxuICAgIHNldEZpbHRlcnMoZmlsdGVycyk7XHJcbiAgfSwgW3NldEZpbHRlcnMsIHVpXSk7XHJcblxyXG4gIGNvbnN0IHVwZGF0ZVNvcnQgPSB1c2VDYWxsYmFjaygoZmllbGQ6IHN0cmluZywgZGlyZWN0aW9uPzogJ2FzYycgfCAnZGVzYycpID0+IHtcclxuICAgIHNldFNvcnRDb25maWcoZmllbGQsIGRpcmVjdGlvbik7XHJcbiAgfSwgW3NldFNvcnRDb25maWddKTtcclxuXHJcbiAgLy8gU3Vic2NyaXB0aW9uIG1hbmFnZW1lbnRcclxuICBjb25zdCBzdWJzY3JpYmUgPSB1c2VDYWxsYmFjaygoc2VjdXJpdHlJZDogc3RyaW5nKSA9PiB7XHJcbiAgICBzdWJzY3JpYmVUb0luc3RydW1lbnQoc2VjdXJpdHlJZCk7XHJcbiAgfSwgW3N1YnNjcmliZVRvSW5zdHJ1bWVudF0pO1xyXG5cclxuICBjb25zdCB1bnN1YnNjcmliZSA9IHVzZUNhbGxiYWNrKChzZWN1cml0eUlkOiBzdHJpbmcpID0+IHtcclxuICAgIHVuc3Vic2NyaWJlRnJvbUluc3RydW1lbnQoc2VjdXJpdHlJZCk7XHJcbiAgfSwgW3Vuc3Vic2NyaWJlRnJvbUluc3RydW1lbnRdKTtcclxuXHJcbiAgLy8gQ29ubmVjdGlvbiBtYW5hZ2VtZW50XHJcbiAgY29uc3QgZm9yY2VSZWNvbm5lY3QgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICByZWNvbm5lY3RBdHRlbXB0c1JlZi5jdXJyZW50ID0gMDsgLy8gUmVzZXQgYXR0ZW1wdHMgZm9yIG1hbnVhbCByZWNvbm5lY3RcclxuICAgICAgYXdhaXQgcmVjb25uZWN0KCk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRW5oYW5jZWQgSG9vazogTWFudWFsIHJlY29ubmVjdCBmYWlsZWQ6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9LCBbcmVjb25uZWN0XSk7XHJcblxyXG4gIGNvbnN0IGZvcmNlUmVmcmVzaCA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGF3YWl0IGxvYWRGcm9tQ2FjaGUoKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFbmhhbmNlZCBIb29rOiBGb3JjZSByZWZyZXNoIGZhaWxlZDonLCBlcnJvcik7XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG4gIH0sIFtsb2FkRnJvbUNhY2hlXSk7XHJcblxyXG4gIGNvbnN0IGZvcmNlU2F2ZSA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGF3YWl0IHNhdmVUb0NhY2hlKHRydWUpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign4p2MIEVuaGFuY2VkIEhvb2s6IEZvcmNlIHNhdmUgZmFpbGVkOicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfSwgW3NhdmVUb0NhY2hlXSk7XHJcblxyXG4gIC8vIFN0YXRzIGFuZCBjb21wdXRlZCB2YWx1ZXNcclxuICBjb25zdCBzdGF0cyA9IHtcclxuICAgIHRvdGFsSW5zdHJ1bWVudHM6IG1hcmtldERhdGFBcnJheS5sZW5ndGgsXHJcbiAgICBjb25uZWN0ZWRJbnN0cnVtZW50czogbWFya2V0RGF0YUFycmF5LmZpbHRlcihpdGVtID0+IGl0ZW0ubHRwICYmIGl0ZW0ubHRwID4gMCkubGVuZ3RoLFxyXG4gICAgbGFzdFVwZGF0ZTogbnVsbCwgLy8gVGVtcG9yYXJ5IG1vY2tcclxuICAgIGNhY2hlU2l6ZTogY2FjaGUudG90YWxDYWNoZVNpemUsXHJcbiAgICBjb25uZWN0aW9uVXB0aW1lOiBjb25uZWN0aW9uLmNvbm5lY3Rpb25TdGF0cy5jb25uZWN0aW9uVXB0aW1lLFxyXG4gICAgbWVzc2FnZXNSZWNlaXZlZDogY29ubmVjdGlvbi5jb25uZWN0aW9uU3RhdHMudG90YWxNZXNzYWdlcyxcclxuICAgIHJlY29ubmVjdEF0dGVtcHRzOiByZWNvbm5lY3RBdHRlbXB0c1JlZi5jdXJyZW50LFxyXG4gICAgaXNBdXRvU2F2aW5nOiBhdXRvU2F2ZUludGVydmFsUmVmLmN1cnJlbnQgIT09IG51bGwsXHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIHtcclxuICAgIC8vIERhdGFcclxuICAgIG1hcmtldERhdGE6IG1hcmtldERhdGFBcnJheSxcclxuICAgIG1hcmtldERhdGFNYXAsXHJcbiAgICBmaWx0ZXJlZERhdGE6IGdldEZpbHRlcmVkRGF0YSgpLFxyXG4gICAgc29ydGVkRGF0YTogZ2V0U29ydGVkRGF0YSgpLFxyXG5cclxuICAgIC8vIENvbm5lY3Rpb24gc3RhdGVcclxuICAgIGlzQ29ubmVjdGVkOiBjb25uZWN0aW9uLmlzQ29ubmVjdGVkLFxyXG4gICAgY29ubmVjdGlvblN0YXR1czogY29ubmVjdGlvbi5zdGF0dXMsXHJcbiAgICBjb25uZWN0aW9uRXJyb3I6IGNvbm5lY3Rpb24uZXJyb3IsXHJcbiAgICBjb25uZWN0aW9uU3RhdHM6IGNvbm5lY3Rpb24uY29ubmVjdGlvblN0YXRzLFxyXG5cclxuICAgIC8vIENhY2hlIHN0YXRlXHJcbiAgICBjYWNoZUxvYWRlZDogY2FjaGUuaXNMb2FkZWQsXHJcbiAgICBjYWNoZVVwZGF0aW5nOiBjYWNoZS5wZW5kaW5nVXBkYXRlcyA+IDAsXHJcbiAgICBsYXN0Q2FjaGVVcGRhdGU6IGNhY2hlLmxhc3RDYWNoZVVwZGF0ZSxcclxuXHJcbiAgICAvLyBVSSBzdGF0ZVxyXG4gICAgZmlsdGVyczogdWkuZmlsdGVycyxcclxuICAgIHNvcnRDb25maWc6IHVpLnNvcnRDb25maWcsXHJcbiAgICBzZWxlY3RlZEluc3RydW1lbnRzOiB1aS5zZWxlY3RlZEluc3RydW1lbnRzLFxyXG4gICAgdmlld01vZGU6IHVpLnZpZXdNb2RlLFxyXG4gICAgYXV0b1JlZnJlc2g6IHVpLmF1dG9SZWZyZXNoLFxyXG5cclxuICAgIC8vIExvYWRpbmcgc3RhdGVzXHJcbiAgICBpc0xvYWRpbmcsXHJcbiAgICBpc0luaXRpYWxpemluZzogIWluaXRpYWxpemF0aW9uUmVmLmN1cnJlbnQsXHJcblxyXG4gICAgLy8gRGF0YSBhY2Nlc3MgZnVuY3Rpb25zXHJcbiAgICBnZXREYXRhQnlTZWN1cml0eUlkLFxyXG4gICAgZ2V0RGF0YUJ5U3ltYm9sLFxyXG4gICAgZ2V0RmlsdGVyZWREYXRhLFxyXG4gICAgZ2V0U29ydGVkRGF0YSxcclxuXHJcbiAgICAvLyBBY3Rpb25zXHJcbiAgICB1cGRhdGVGaWx0ZXJzLFxyXG4gICAgdXBkYXRlU29ydCxcclxuICAgIHN1YnNjcmliZSxcclxuICAgIHVuc3Vic2NyaWJlLFxyXG5cclxuICAgIC8vIENvbm5lY3Rpb24gbWFuYWdlbWVudFxyXG4gICAgY29ubmVjdCxcclxuICAgIGRpc2Nvbm5lY3QsXHJcbiAgICByZWNvbm5lY3Q6IGZvcmNlUmVjb25uZWN0LFxyXG5cclxuICAgIC8vIENhY2hlIG1hbmFnZW1lbnRcclxuICAgIHJlZnJlc2g6IGZvcmNlUmVmcmVzaCxcclxuICAgIHNhdmU6IGZvcmNlU2F2ZSxcclxuICAgIGNsZWFyQ2FjaGUsXHJcblxyXG4gICAgLy8gVXRpbGl0eVxyXG4gICAgcmVzZXQsXHJcbiAgICBzdGF0cyxcclxuXHJcbiAgICAvLyBBZHZhbmNlZCBhY3Rpb25zIChleHBvc2UgaWYgbmVlZGVkKVxyXG4gICAgX3N0b3JlOiB7XHJcbiAgICAgIHNldEZpbHRlcnMsXHJcbiAgICAgIHNldFNvcnRDb25maWcsXHJcbiAgICAgIHN1YnNjcmliZVRvSW5zdHJ1bWVudCxcclxuICAgICAgdW5zdWJzY3JpYmVGcm9tSW5zdHJ1bWVudCxcclxuICAgICAgLy8gQWRkIG1vcmUgaWYgbmVlZGVkXHJcbiAgICB9LFxyXG4gIH07XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCB1c2VFbmhhbmNlZE1hcmtldERhdGE7XHJcbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsInVzZVJlZiIsInVzZUVuaGFuY2VkTWFya2V0RGF0YSIsIm9wdGlvbnMiLCJhdXRvQ29ubmVjdCIsImF1dG9Mb2FkQ2FjaGUiLCJhdXRvU2F2ZUludGVydmFsIiwicmVjb25uZWN0T25FcnJvciIsIm1heFJlY29ubmVjdEF0dGVtcHRzIiwiY29ubmVjdGlvbiIsInN0YXR1cyIsImlzQ29ubmVjdGVkIiwiZXJyb3IiLCJjb25uZWN0aW9uU3RhdHMiLCJ0b3RhbE1lc3NhZ2VzIiwiY29ubmVjdGlvblVwdGltZSIsIm1hcmtldERhdGFNYXAiLCJNYXAiLCJjYWNoZSIsImlzTG9hZGVkIiwidG90YWxDYWNoZVNpemUiLCJwZW5kaW5nVXBkYXRlcyIsImxhc3RDYWNoZVVwZGF0ZSIsInVpIiwiZmlsdGVycyIsInNvcnRDb25maWciLCJmaWVsZCIsImRpcmVjdGlvbiIsInNlbGVjdGVkSW5zdHJ1bWVudHMiLCJTZXQiLCJ2aWV3TW9kZSIsImF1dG9SZWZyZXNoIiwiaXNMb2FkaW5nIiwiaW5pdGlhbGl6ZUNvbm5lY3Rpb24iLCJjb25uZWN0IiwiZGlzY29ubmVjdCIsInJlY29ubmVjdCIsImxvYWRGcm9tQ2FjaGUiLCJzYXZlVG9DYWNoZSIsImZvcmNlIiwiY2xlYXJDYWNoZSIsInNldEZpbHRlcnMiLCJzZXRTb3J0Q29uZmlnIiwiZ2V0RmlsdGVyZWRNYXJrZXREYXRhIiwiZ2V0U29ydGVkTWFya2V0RGF0YSIsImdldE1hcmtldERhdGFCeVNlY3VyaXR5SWQiLCJzZWN1cml0eUlkIiwidW5kZWZpbmVkIiwiZ2V0TWFya2V0RGF0YUJ5U3ltYm9sIiwic3ltYm9sIiwic3Vic2NyaWJlVG9JbnN0cnVtZW50IiwidW5zdWJzY3JpYmVGcm9tSW5zdHJ1bWVudCIsInJlc2V0IiwiYXV0b1NhdmVJbnRlcnZhbFJlZiIsInJlY29ubmVjdEF0dGVtcHRzUmVmIiwiaW5pdGlhbGl6YXRpb25SZWYiLCJtYXJrZXREYXRhQXJyYXkiLCJBcnJheSIsImZyb20iLCJ2YWx1ZXMiLCJpbml0aWFsaXplIiwiY3VycmVudCIsImNvbnNvbGUiLCJsb2ciLCJjbGVhckludGVydmFsIiwibGVuZ3RoIiwic2V0SW50ZXJ2YWwiLCJyZXRyeURlbGF5IiwiTWF0aCIsIm1pbiIsInBvdyIsInRpbWVvdXRJZCIsInNldFRpbWVvdXQiLCJjbGVhclRpbWVvdXQiLCJnZXRGaWx0ZXJlZERhdGEiLCJnZXRTb3J0ZWREYXRhIiwiZ2V0RGF0YUJ5U2VjdXJpdHlJZCIsImdldERhdGFCeVN5bWJvbCIsInVwZGF0ZUZpbHRlcnMiLCJ1cGRhdGVTb3J0Iiwic3Vic2NyaWJlIiwidW5zdWJzY3JpYmUiLCJmb3JjZVJlY29ubmVjdCIsImZvcmNlUmVmcmVzaCIsImZvcmNlU2F2ZSIsInN0YXRzIiwidG90YWxJbnN0cnVtZW50cyIsImNvbm5lY3RlZEluc3RydW1lbnRzIiwiZmlsdGVyIiwiaXRlbSIsImx0cCIsImxhc3RVcGRhdGUiLCJjYWNoZVNpemUiLCJtZXNzYWdlc1JlY2VpdmVkIiwicmVjb25uZWN0QXR0ZW1wdHMiLCJpc0F1dG9TYXZpbmciLCJtYXJrZXREYXRhIiwiZmlsdGVyZWREYXRhIiwic29ydGVkRGF0YSIsImNvbm5lY3Rpb25TdGF0dXMiLCJjb25uZWN0aW9uRXJyb3IiLCJjYWNoZUxvYWRlZCIsImNhY2hlVXBkYXRpbmciLCJpc0luaXRpYWxpemluZyIsInJlZnJlc2giLCJzYXZlIiwiX3N0b3JlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useEnhancedMarketData.ts\n"));

/***/ })

});