"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-error-boundary";
exports.ids = ["vendor-chunks/react-error-boundary"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-error-boundary/dist/react-error-boundary.development.esm.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/react-error-boundary/dist/react-error-boundary.development.esm.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   ErrorBoundaryContext: () => (/* binding */ ErrorBoundaryContext),\n/* harmony export */   useErrorBoundary: () => (/* binding */ useErrorBoundary),\n/* harmony export */   withErrorBoundary: () => (/* binding */ withErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,ErrorBoundaryContext,useErrorBoundary,withErrorBoundary auto */ \nconst ErrorBoundaryContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nconst initialState = {\n    didCatch: false,\n    error: null\n};\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n    constructor(props){\n        super(props);\n        this.resetErrorBoundary = this.resetErrorBoundary.bind(this);\n        this.state = initialState;\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            didCatch: true,\n            error\n        };\n    }\n    resetErrorBoundary() {\n        const { error } = this.state;\n        if (error !== null) {\n            var _this$props$onReset, _this$props;\n            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                args[_key] = arguments[_key];\n            }\n            (_this$props$onReset = (_this$props = this.props).onReset) === null || _this$props$onReset === void 0 ? void 0 : _this$props$onReset.call(_this$props, {\n                args,\n                reason: \"imperative-api\"\n            });\n            this.setState(initialState);\n        }\n    }\n    componentDidCatch(error, info) {\n        var _this$props$onError, _this$props2;\n        (_this$props$onError = (_this$props2 = this.props).onError) === null || _this$props$onError === void 0 ? void 0 : _this$props$onError.call(_this$props2, error, info);\n    }\n    componentDidUpdate(prevProps, prevState) {\n        const { didCatch } = this.state;\n        const { resetKeys } = this.props;\n        // There's an edge case where if the thing that triggered the error happens to *also* be in the resetKeys array,\n        // we'd end up resetting the error boundary immediately.\n        // This would likely trigger a second error to be thrown.\n        // So we make sure that we don't check the resetKeys on the first call of cDU after the error is set.\n        if (didCatch && prevState.error !== null && hasArrayChanged(prevProps.resetKeys, resetKeys)) {\n            var _this$props$onReset2, _this$props3;\n            (_this$props$onReset2 = (_this$props3 = this.props).onReset) === null || _this$props$onReset2 === void 0 ? void 0 : _this$props$onReset2.call(_this$props3, {\n                next: resetKeys,\n                prev: prevProps.resetKeys,\n                reason: \"keys\"\n            });\n            this.setState(initialState);\n        }\n    }\n    render() {\n        const { children, fallbackRender, FallbackComponent, fallback } = this.props;\n        const { didCatch, error } = this.state;\n        let childToRender = children;\n        if (didCatch) {\n            const props = {\n                error,\n                resetErrorBoundary: this.resetErrorBoundary\n            };\n            if (typeof fallbackRender === \"function\") {\n                childToRender = fallbackRender(props);\n            } else if (FallbackComponent) {\n                childToRender = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(FallbackComponent, props);\n            } else if (fallback !== undefined) {\n                childToRender = fallback;\n            } else {\n                {\n                    console.error(\"react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop\");\n                }\n                throw error;\n            }\n        }\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(ErrorBoundaryContext.Provider, {\n            value: {\n                didCatch,\n                error,\n                resetErrorBoundary: this.resetErrorBoundary\n            }\n        }, childToRender);\n    }\n}\nfunction hasArrayChanged() {\n    let a = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    let b = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n    return a.length !== b.length || a.some((item, index)=>!Object.is(item, b[index]));\n}\nfunction assertErrorBoundaryContext(value) {\n    if (value == null || typeof value.didCatch !== \"boolean\" || typeof value.resetErrorBoundary !== \"function\") {\n        throw new Error(\"ErrorBoundaryContext not found\");\n    }\n}\nfunction useErrorBoundary() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ErrorBoundaryContext);\n    assertErrorBoundaryContext(context);\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        error: null,\n        hasError: false\n    });\n    const memoized = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({\n            resetBoundary: ()=>{\n                context.resetErrorBoundary();\n                setState({\n                    error: null,\n                    hasError: false\n                });\n            },\n            showBoundary: (error)=>setState({\n                    error,\n                    hasError: true\n                })\n        }), [\n        context.resetErrorBoundary\n    ]);\n    if (state.hasError) {\n        throw state.error;\n    }\n    return memoized;\n}\nfunction withErrorBoundary(component, errorBoundaryProps) {\n    const Wrapped = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref)=>/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(ErrorBoundary, errorBoundaryProps, /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(component, {\n            ...props,\n            ref\n        })));\n    // Format for display in DevTools\n    const name = component.displayName || component.name || \"Unknown\";\n    Wrapped.displayName = \"withErrorBoundary(\".concat(name, \")\");\n    return Wrapped;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZXJyb3ItYm91bmRhcnkvZGlzdC9yZWFjdC1lcnJvci1ib3VuZGFyeS5kZXZlbG9wbWVudC5lc20uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OzJIQUMyRztBQUUzRyxNQUFNTyxxQ0FBdUJQLG9EQUFhQSxDQUFDO0FBRTNDLE1BQU1RLGVBQWU7SUFDbkJDLFVBQVU7SUFDVkMsT0FBTztBQUNUO0FBQ0EsTUFBTUMsc0JBQXNCViw0Q0FBU0E7SUFDbkNXLFlBQVlDLEtBQUssQ0FBRTtRQUNqQixLQUFLLENBQUNBO1FBQ04sSUFBSSxDQUFDQyxrQkFBa0IsR0FBRyxJQUFJLENBQUNBLGtCQUFrQixDQUFDQyxJQUFJLENBQUMsSUFBSTtRQUMzRCxJQUFJLENBQUNDLEtBQUssR0FBR1I7SUFDZjtJQUNBLE9BQU9TLHlCQUF5QlAsS0FBSyxFQUFFO1FBQ3JDLE9BQU87WUFDTEQsVUFBVTtZQUNWQztRQUNGO0lBQ0Y7SUFDQUkscUJBQXFCO1FBQ25CLE1BQU0sRUFDSkosS0FBSyxFQUNOLEdBQUcsSUFBSSxDQUFDTSxLQUFLO1FBQ2QsSUFBSU4sVUFBVSxNQUFNO1lBQ2xCLElBQUlRLHFCQUFxQkM7WUFDekIsSUFBSyxJQUFJQyxPQUFPQyxVQUFVQyxNQUFNLEVBQUVDLE9BQU8sSUFBSUMsTUFBTUosT0FBT0ssT0FBTyxHQUFHQSxPQUFPTCxNQUFNSyxPQUFRO2dCQUN2RkYsSUFBSSxDQUFDRSxLQUFLLEdBQUdKLFNBQVMsQ0FBQ0ksS0FBSztZQUM5QjtZQUNDUCxDQUFBQSxzQkFBc0IsQ0FBQ0MsY0FBYyxJQUFJLENBQUNOLEtBQUssRUFBRWEsT0FBTyxNQUFNLFFBQVFSLHdCQUF3QixLQUFLLElBQUksS0FBSyxJQUFJQSxvQkFBb0JTLElBQUksQ0FBQ1IsYUFBYTtnQkFDckpJO2dCQUNBSyxRQUFRO1lBQ1Y7WUFDQSxJQUFJLENBQUNDLFFBQVEsQ0FBQ3JCO1FBQ2hCO0lBQ0Y7SUFDQXNCLGtCQUFrQnBCLEtBQUssRUFBRXFCLElBQUksRUFBRTtRQUM3QixJQUFJQyxxQkFBcUJDO1FBQ3hCRCxDQUFBQSxzQkFBc0IsQ0FBQ0MsZUFBZSxJQUFJLENBQUNwQixLQUFLLEVBQUVxQixPQUFPLE1BQU0sUUFBUUYsd0JBQXdCLEtBQUssSUFBSSxLQUFLLElBQUlBLG9CQUFvQkwsSUFBSSxDQUFDTSxjQUFjdkIsT0FBT3FCO0lBQ2xLO0lBQ0FJLG1CQUFtQkMsU0FBUyxFQUFFQyxTQUFTLEVBQUU7UUFDdkMsTUFBTSxFQUNKNUIsUUFBUSxFQUNULEdBQUcsSUFBSSxDQUFDTyxLQUFLO1FBQ2QsTUFBTSxFQUNKc0IsU0FBUyxFQUNWLEdBQUcsSUFBSSxDQUFDekIsS0FBSztRQUVkLGdIQUFnSDtRQUNoSCx3REFBd0Q7UUFDeEQseURBQXlEO1FBQ3pELHFHQUFxRztRQUVyRyxJQUFJSixZQUFZNEIsVUFBVTNCLEtBQUssS0FBSyxRQUFRNkIsZ0JBQWdCSCxVQUFVRSxTQUFTLEVBQUVBLFlBQVk7WUFDM0YsSUFBSUUsc0JBQXNCQztZQUN6QkQsQ0FBQUEsdUJBQXVCLENBQUNDLGVBQWUsSUFBSSxDQUFDNUIsS0FBSyxFQUFFYSxPQUFPLE1BQU0sUUFBUWMseUJBQXlCLEtBQUssSUFBSSxLQUFLLElBQUlBLHFCQUFxQmIsSUFBSSxDQUFDYyxjQUFjO2dCQUMxSkMsTUFBTUo7Z0JBQ05LLE1BQU1QLFVBQVVFLFNBQVM7Z0JBQ3pCVixRQUFRO1lBQ1Y7WUFDQSxJQUFJLENBQUNDLFFBQVEsQ0FBQ3JCO1FBQ2hCO0lBQ0Y7SUFDQW9DLFNBQVM7UUFDUCxNQUFNLEVBQ0pDLFFBQVEsRUFDUkMsY0FBYyxFQUNkQyxpQkFBaUIsRUFDakJDLFFBQVEsRUFDVCxHQUFHLElBQUksQ0FBQ25DLEtBQUs7UUFDZCxNQUFNLEVBQ0pKLFFBQVEsRUFDUkMsS0FBSyxFQUNOLEdBQUcsSUFBSSxDQUFDTSxLQUFLO1FBQ2QsSUFBSWlDLGdCQUFnQko7UUFDcEIsSUFBSXBDLFVBQVU7WUFDWixNQUFNSSxRQUFRO2dCQUNaSDtnQkFDQUksb0JBQW9CLElBQUksQ0FBQ0Esa0JBQWtCO1lBQzdDO1lBQ0EsSUFBSSxPQUFPZ0MsbUJBQW1CLFlBQVk7Z0JBQ3hDRyxnQkFBZ0JILGVBQWVqQztZQUNqQyxPQUFPLElBQUlrQyxtQkFBbUI7Z0JBQzVCRSw4QkFBZ0IvQyxvREFBYUEsQ0FBQzZDLG1CQUFtQmxDO1lBQ25ELE9BQU8sSUFBSW1DLGFBQWFFLFdBQVc7Z0JBQ2pDRCxnQkFBZ0JEO1lBQ2xCLE9BQU87Z0JBQ0w7b0JBQ0VHLFFBQVF6QyxLQUFLLENBQUM7Z0JBQ2hCO2dCQUNBLE1BQU1BO1lBQ1I7UUFDRjtRQUNBLHFCQUFPUixvREFBYUEsQ0FBQ0sscUJBQXFCNkMsUUFBUSxFQUFFO1lBQ2xEQyxPQUFPO2dCQUNMNUM7Z0JBQ0FDO2dCQUNBSSxvQkFBb0IsSUFBSSxDQUFDQSxrQkFBa0I7WUFDN0M7UUFDRixHQUFHbUM7SUFDTDtBQUNGO0FBQ0EsU0FBU1Y7SUFDUCxJQUFJZSxJQUFJakMsVUFBVUMsTUFBTSxHQUFHLEtBQUtELFNBQVMsQ0FBQyxFQUFFLEtBQUs2QixZQUFZN0IsU0FBUyxDQUFDLEVBQUUsR0FBRyxFQUFFO0lBQzlFLElBQUlrQyxJQUFJbEMsVUFBVUMsTUFBTSxHQUFHLEtBQUtELFNBQVMsQ0FBQyxFQUFFLEtBQUs2QixZQUFZN0IsU0FBUyxDQUFDLEVBQUUsR0FBRyxFQUFFO0lBQzlFLE9BQU9pQyxFQUFFaEMsTUFBTSxLQUFLaUMsRUFBRWpDLE1BQU0sSUFBSWdDLEVBQUVFLElBQUksQ0FBQyxDQUFDQyxNQUFNQyxRQUFVLENBQUNDLE9BQU9DLEVBQUUsQ0FBQ0gsTUFBTUYsQ0FBQyxDQUFDRyxNQUFNO0FBQ25GO0FBRUEsU0FBU0csMkJBQTJCUixLQUFLO0lBQ3ZDLElBQUlBLFNBQVMsUUFBUSxPQUFPQSxNQUFNNUMsUUFBUSxLQUFLLGFBQWEsT0FBTzRDLE1BQU12QyxrQkFBa0IsS0FBSyxZQUFZO1FBQzFHLE1BQU0sSUFBSWdELE1BQU07SUFDbEI7QUFDRjtBQUVBLFNBQVNDO0lBQ1AsTUFBTUMsVUFBVTdELGlEQUFVQSxDQUFDSTtJQUMzQnNELDJCQUEyQkc7SUFDM0IsTUFBTSxDQUFDaEQsT0FBT2EsU0FBUyxHQUFHekIsK0NBQVFBLENBQUM7UUFDakNNLE9BQU87UUFDUHVELFVBQVU7SUFDWjtJQUNBLE1BQU1DLFdBQVc3RCw4Q0FBT0EsQ0FBQyxJQUFPO1lBQzlCOEQsZUFBZTtnQkFDYkgsUUFBUWxELGtCQUFrQjtnQkFDMUJlLFNBQVM7b0JBQ1BuQixPQUFPO29CQUNQdUQsVUFBVTtnQkFDWjtZQUNGO1lBQ0FHLGNBQWMxRCxDQUFBQSxRQUFTbUIsU0FBUztvQkFDOUJuQjtvQkFDQXVELFVBQVU7Z0JBQ1o7UUFDRixJQUFJO1FBQUNELFFBQVFsRCxrQkFBa0I7S0FBQztJQUNoQyxJQUFJRSxNQUFNaUQsUUFBUSxFQUFFO1FBQ2xCLE1BQU1qRCxNQUFNTixLQUFLO0lBQ25CO0lBQ0EsT0FBT3dEO0FBQ1Q7QUFFQSxTQUFTRyxrQkFBa0JDLFNBQVMsRUFBRUMsa0JBQWtCO0lBQ3RELE1BQU1DLHdCQUFVbEUsaURBQVVBLENBQUMsQ0FBQ08sT0FBTzRELG9CQUFRdkUsb0RBQWFBLENBQUNTLGVBQWU0RCxrQ0FBb0JyRSxvREFBYUEsQ0FBQ29FLFdBQVc7WUFDbkgsR0FBR3pELEtBQUs7WUFDUjREO1FBQ0Y7SUFFQSxpQ0FBaUM7SUFDakMsTUFBTUMsT0FBT0osVUFBVUssV0FBVyxJQUFJTCxVQUFVSSxJQUFJLElBQUk7SUFDeERGLFFBQVFHLFdBQVcsR0FBRyxxQkFBcUJDLE1BQU0sQ0FBQ0YsTUFBTTtJQUN4RCxPQUFPRjtBQUNUO0FBRW9GIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3N2LW1hcmtldC1kYXNoYm9hcmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtZXJyb3ItYm91bmRhcnkvZGlzdC9yZWFjdC1lcnJvci1ib3VuZGFyeS5kZXZlbG9wbWVudC5lc20uanM/NmQ1MSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCBDb21wb25lbnQsIGNyZWF0ZUVsZW1lbnQsIHVzZUNvbnRleHQsIHVzZVN0YXRlLCB1c2VNZW1vLCBmb3J3YXJkUmVmIH0gZnJvbSAncmVhY3QnO1xuXG5jb25zdCBFcnJvckJvdW5kYXJ5Q29udGV4dCA9IGNyZWF0ZUNvbnRleHQobnVsbCk7XG5cbmNvbnN0IGluaXRpYWxTdGF0ZSA9IHtcbiAgZGlkQ2F0Y2g6IGZhbHNlLFxuICBlcnJvcjogbnVsbFxufTtcbmNsYXNzIEVycm9yQm91bmRhcnkgZXh0ZW5kcyBDb21wb25lbnQge1xuICBjb25zdHJ1Y3Rvcihwcm9wcykge1xuICAgIHN1cGVyKHByb3BzKTtcbiAgICB0aGlzLnJlc2V0RXJyb3JCb3VuZGFyeSA9IHRoaXMucmVzZXRFcnJvckJvdW5kYXJ5LmJpbmQodGhpcyk7XG4gICAgdGhpcy5zdGF0ZSA9IGluaXRpYWxTdGF0ZTtcbiAgfVxuICBzdGF0aWMgZ2V0RGVyaXZlZFN0YXRlRnJvbUVycm9yKGVycm9yKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIGRpZENhdGNoOiB0cnVlLFxuICAgICAgZXJyb3JcbiAgICB9O1xuICB9XG4gIHJlc2V0RXJyb3JCb3VuZGFyeSgpIHtcbiAgICBjb25zdCB7XG4gICAgICBlcnJvclxuICAgIH0gPSB0aGlzLnN0YXRlO1xuICAgIGlmIChlcnJvciAhPT0gbnVsbCkge1xuICAgICAgdmFyIF90aGlzJHByb3BzJG9uUmVzZXQsIF90aGlzJHByb3BzO1xuICAgICAgZm9yICh2YXIgX2xlbiA9IGFyZ3VtZW50cy5sZW5ndGgsIGFyZ3MgPSBuZXcgQXJyYXkoX2xlbiksIF9rZXkgPSAwOyBfa2V5IDwgX2xlbjsgX2tleSsrKSB7XG4gICAgICAgIGFyZ3NbX2tleV0gPSBhcmd1bWVudHNbX2tleV07XG4gICAgICB9XG4gICAgICAoX3RoaXMkcHJvcHMkb25SZXNldCA9IChfdGhpcyRwcm9wcyA9IHRoaXMucHJvcHMpLm9uUmVzZXQpID09PSBudWxsIHx8IF90aGlzJHByb3BzJG9uUmVzZXQgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF90aGlzJHByb3BzJG9uUmVzZXQuY2FsbChfdGhpcyRwcm9wcywge1xuICAgICAgICBhcmdzLFxuICAgICAgICByZWFzb246IFwiaW1wZXJhdGl2ZS1hcGlcIlxuICAgICAgfSk7XG4gICAgICB0aGlzLnNldFN0YXRlKGluaXRpYWxTdGF0ZSk7XG4gICAgfVxuICB9XG4gIGNvbXBvbmVudERpZENhdGNoKGVycm9yLCBpbmZvKSB7XG4gICAgdmFyIF90aGlzJHByb3BzJG9uRXJyb3IsIF90aGlzJHByb3BzMjtcbiAgICAoX3RoaXMkcHJvcHMkb25FcnJvciA9IChfdGhpcyRwcm9wczIgPSB0aGlzLnByb3BzKS5vbkVycm9yKSA9PT0gbnVsbCB8fCBfdGhpcyRwcm9wcyRvbkVycm9yID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfdGhpcyRwcm9wcyRvbkVycm9yLmNhbGwoX3RoaXMkcHJvcHMyLCBlcnJvciwgaW5mbyk7XG4gIH1cbiAgY29tcG9uZW50RGlkVXBkYXRlKHByZXZQcm9wcywgcHJldlN0YXRlKSB7XG4gICAgY29uc3Qge1xuICAgICAgZGlkQ2F0Y2hcbiAgICB9ID0gdGhpcy5zdGF0ZTtcbiAgICBjb25zdCB7XG4gICAgICByZXNldEtleXNcbiAgICB9ID0gdGhpcy5wcm9wcztcblxuICAgIC8vIFRoZXJlJ3MgYW4gZWRnZSBjYXNlIHdoZXJlIGlmIHRoZSB0aGluZyB0aGF0IHRyaWdnZXJlZCB0aGUgZXJyb3IgaGFwcGVucyB0byAqYWxzbyogYmUgaW4gdGhlIHJlc2V0S2V5cyBhcnJheSxcbiAgICAvLyB3ZSdkIGVuZCB1cCByZXNldHRpbmcgdGhlIGVycm9yIGJvdW5kYXJ5IGltbWVkaWF0ZWx5LlxuICAgIC8vIFRoaXMgd291bGQgbGlrZWx5IHRyaWdnZXIgYSBzZWNvbmQgZXJyb3IgdG8gYmUgdGhyb3duLlxuICAgIC8vIFNvIHdlIG1ha2Ugc3VyZSB0aGF0IHdlIGRvbid0IGNoZWNrIHRoZSByZXNldEtleXMgb24gdGhlIGZpcnN0IGNhbGwgb2YgY0RVIGFmdGVyIHRoZSBlcnJvciBpcyBzZXQuXG5cbiAgICBpZiAoZGlkQ2F0Y2ggJiYgcHJldlN0YXRlLmVycm9yICE9PSBudWxsICYmIGhhc0FycmF5Q2hhbmdlZChwcmV2UHJvcHMucmVzZXRLZXlzLCByZXNldEtleXMpKSB7XG4gICAgICB2YXIgX3RoaXMkcHJvcHMkb25SZXNldDIsIF90aGlzJHByb3BzMztcbiAgICAgIChfdGhpcyRwcm9wcyRvblJlc2V0MiA9IChfdGhpcyRwcm9wczMgPSB0aGlzLnByb3BzKS5vblJlc2V0KSA9PT0gbnVsbCB8fCBfdGhpcyRwcm9wcyRvblJlc2V0MiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3RoaXMkcHJvcHMkb25SZXNldDIuY2FsbChfdGhpcyRwcm9wczMsIHtcbiAgICAgICAgbmV4dDogcmVzZXRLZXlzLFxuICAgICAgICBwcmV2OiBwcmV2UHJvcHMucmVzZXRLZXlzLFxuICAgICAgICByZWFzb246IFwia2V5c1wiXG4gICAgICB9KTtcbiAgICAgIHRoaXMuc2V0U3RhdGUoaW5pdGlhbFN0YXRlKTtcbiAgICB9XG4gIH1cbiAgcmVuZGVyKCkge1xuICAgIGNvbnN0IHtcbiAgICAgIGNoaWxkcmVuLFxuICAgICAgZmFsbGJhY2tSZW5kZXIsXG4gICAgICBGYWxsYmFja0NvbXBvbmVudCxcbiAgICAgIGZhbGxiYWNrXG4gICAgfSA9IHRoaXMucHJvcHM7XG4gICAgY29uc3Qge1xuICAgICAgZGlkQ2F0Y2gsXG4gICAgICBlcnJvclxuICAgIH0gPSB0aGlzLnN0YXRlO1xuICAgIGxldCBjaGlsZFRvUmVuZGVyID0gY2hpbGRyZW47XG4gICAgaWYgKGRpZENhdGNoKSB7XG4gICAgICBjb25zdCBwcm9wcyA9IHtcbiAgICAgICAgZXJyb3IsXG4gICAgICAgIHJlc2V0RXJyb3JCb3VuZGFyeTogdGhpcy5yZXNldEVycm9yQm91bmRhcnlcbiAgICAgIH07XG4gICAgICBpZiAodHlwZW9mIGZhbGxiYWNrUmVuZGVyID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgY2hpbGRUb1JlbmRlciA9IGZhbGxiYWNrUmVuZGVyKHByb3BzKTtcbiAgICAgIH0gZWxzZSBpZiAoRmFsbGJhY2tDb21wb25lbnQpIHtcbiAgICAgICAgY2hpbGRUb1JlbmRlciA9IGNyZWF0ZUVsZW1lbnQoRmFsbGJhY2tDb21wb25lbnQsIHByb3BzKTtcbiAgICAgIH0gZWxzZSBpZiAoZmFsbGJhY2sgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICBjaGlsZFRvUmVuZGVyID0gZmFsbGJhY2s7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcihcInJlYWN0LWVycm9yLWJvdW5kYXJ5IHJlcXVpcmVzIGVpdGhlciBhIGZhbGxiYWNrLCBmYWxsYmFja1JlbmRlciwgb3IgRmFsbGJhY2tDb21wb25lbnQgcHJvcFwiKTtcbiAgICAgICAgfVxuICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGNyZWF0ZUVsZW1lbnQoRXJyb3JCb3VuZGFyeUNvbnRleHQuUHJvdmlkZXIsIHtcbiAgICAgIHZhbHVlOiB7XG4gICAgICAgIGRpZENhdGNoLFxuICAgICAgICBlcnJvcixcbiAgICAgICAgcmVzZXRFcnJvckJvdW5kYXJ5OiB0aGlzLnJlc2V0RXJyb3JCb3VuZGFyeVxuICAgICAgfVxuICAgIH0sIGNoaWxkVG9SZW5kZXIpO1xuICB9XG59XG5mdW5jdGlvbiBoYXNBcnJheUNoYW5nZWQoKSB7XG4gIGxldCBhID0gYXJndW1lbnRzLmxlbmd0aCA+IDAgJiYgYXJndW1lbnRzWzBdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMF0gOiBbXTtcbiAgbGV0IGIgPSBhcmd1bWVudHMubGVuZ3RoID4gMSAmJiBhcmd1bWVudHNbMV0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1sxXSA6IFtdO1xuICByZXR1cm4gYS5sZW5ndGggIT09IGIubGVuZ3RoIHx8IGEuc29tZSgoaXRlbSwgaW5kZXgpID0+ICFPYmplY3QuaXMoaXRlbSwgYltpbmRleF0pKTtcbn1cblxuZnVuY3Rpb24gYXNzZXJ0RXJyb3JCb3VuZGFyeUNvbnRleHQodmFsdWUpIHtcbiAgaWYgKHZhbHVlID09IG51bGwgfHwgdHlwZW9mIHZhbHVlLmRpZENhdGNoICE9PSBcImJvb2xlYW5cIiB8fCB0eXBlb2YgdmFsdWUucmVzZXRFcnJvckJvdW5kYXJ5ICE9PSBcImZ1bmN0aW9uXCIpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJFcnJvckJvdW5kYXJ5Q29udGV4dCBub3QgZm91bmRcIik7XG4gIH1cbn1cblxuZnVuY3Rpb24gdXNlRXJyb3JCb3VuZGFyeSgpIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoRXJyb3JCb3VuZGFyeUNvbnRleHQpO1xuICBhc3NlcnRFcnJvckJvdW5kYXJ5Q29udGV4dChjb250ZXh0KTtcbiAgY29uc3QgW3N0YXRlLCBzZXRTdGF0ZV0gPSB1c2VTdGF0ZSh7XG4gICAgZXJyb3I6IG51bGwsXG4gICAgaGFzRXJyb3I6IGZhbHNlXG4gIH0pO1xuICBjb25zdCBtZW1vaXplZCA9IHVzZU1lbW8oKCkgPT4gKHtcbiAgICByZXNldEJvdW5kYXJ5OiAoKSA9PiB7XG4gICAgICBjb250ZXh0LnJlc2V0RXJyb3JCb3VuZGFyeSgpO1xuICAgICAgc2V0U3RhdGUoe1xuICAgICAgICBlcnJvcjogbnVsbCxcbiAgICAgICAgaGFzRXJyb3I6IGZhbHNlXG4gICAgICB9KTtcbiAgICB9LFxuICAgIHNob3dCb3VuZGFyeTogZXJyb3IgPT4gc2V0U3RhdGUoe1xuICAgICAgZXJyb3IsXG4gICAgICBoYXNFcnJvcjogdHJ1ZVxuICAgIH0pXG4gIH0pLCBbY29udGV4dC5yZXNldEVycm9yQm91bmRhcnldKTtcbiAgaWYgKHN0YXRlLmhhc0Vycm9yKSB7XG4gICAgdGhyb3cgc3RhdGUuZXJyb3I7XG4gIH1cbiAgcmV0dXJuIG1lbW9pemVkO1xufVxuXG5mdW5jdGlvbiB3aXRoRXJyb3JCb3VuZGFyeShjb21wb25lbnQsIGVycm9yQm91bmRhcnlQcm9wcykge1xuICBjb25zdCBXcmFwcGVkID0gZm9yd2FyZFJlZigocHJvcHMsIHJlZikgPT4gY3JlYXRlRWxlbWVudChFcnJvckJvdW5kYXJ5LCBlcnJvckJvdW5kYXJ5UHJvcHMsIGNyZWF0ZUVsZW1lbnQoY29tcG9uZW50LCB7XG4gICAgLi4ucHJvcHMsXG4gICAgcmVmXG4gIH0pKSk7XG5cbiAgLy8gRm9ybWF0IGZvciBkaXNwbGF5IGluIERldlRvb2xzXG4gIGNvbnN0IG5hbWUgPSBjb21wb25lbnQuZGlzcGxheU5hbWUgfHwgY29tcG9uZW50Lm5hbWUgfHwgXCJVbmtub3duXCI7XG4gIFdyYXBwZWQuZGlzcGxheU5hbWUgPSBcIndpdGhFcnJvckJvdW5kYXJ5KFwiLmNvbmNhdChuYW1lLCBcIilcIik7XG4gIHJldHVybiBXcmFwcGVkO1xufVxuXG5leHBvcnQgeyBFcnJvckJvdW5kYXJ5LCBFcnJvckJvdW5kYXJ5Q29udGV4dCwgdXNlRXJyb3JCb3VuZGFyeSwgd2l0aEVycm9yQm91bmRhcnkgfTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwiQ29tcG9uZW50IiwiY3JlYXRlRWxlbWVudCIsInVzZUNvbnRleHQiLCJ1c2VTdGF0ZSIsInVzZU1lbW8iLCJmb3J3YXJkUmVmIiwiRXJyb3JCb3VuZGFyeUNvbnRleHQiLCJpbml0aWFsU3RhdGUiLCJkaWRDYXRjaCIsImVycm9yIiwiRXJyb3JCb3VuZGFyeSIsImNvbnN0cnVjdG9yIiwicHJvcHMiLCJyZXNldEVycm9yQm91bmRhcnkiLCJiaW5kIiwic3RhdGUiLCJnZXREZXJpdmVkU3RhdGVGcm9tRXJyb3IiLCJfdGhpcyRwcm9wcyRvblJlc2V0IiwiX3RoaXMkcHJvcHMiLCJfbGVuIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwiYXJncyIsIkFycmF5IiwiX2tleSIsIm9uUmVzZXQiLCJjYWxsIiwicmVhc29uIiwic2V0U3RhdGUiLCJjb21wb25lbnREaWRDYXRjaCIsImluZm8iLCJfdGhpcyRwcm9wcyRvbkVycm9yIiwiX3RoaXMkcHJvcHMyIiwib25FcnJvciIsImNvbXBvbmVudERpZFVwZGF0ZSIsInByZXZQcm9wcyIsInByZXZTdGF0ZSIsInJlc2V0S2V5cyIsImhhc0FycmF5Q2hhbmdlZCIsIl90aGlzJHByb3BzJG9uUmVzZXQyIiwiX3RoaXMkcHJvcHMzIiwibmV4dCIsInByZXYiLCJyZW5kZXIiLCJjaGlsZHJlbiIsImZhbGxiYWNrUmVuZGVyIiwiRmFsbGJhY2tDb21wb25lbnQiLCJmYWxsYmFjayIsImNoaWxkVG9SZW5kZXIiLCJ1bmRlZmluZWQiLCJjb25zb2xlIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsImEiLCJiIiwic29tZSIsIml0ZW0iLCJpbmRleCIsIk9iamVjdCIsImlzIiwiYXNzZXJ0RXJyb3JCb3VuZGFyeUNvbnRleHQiLCJFcnJvciIsInVzZUVycm9yQm91bmRhcnkiLCJjb250ZXh0IiwiaGFzRXJyb3IiLCJtZW1vaXplZCIsInJlc2V0Qm91bmRhcnkiLCJzaG93Qm91bmRhcnkiLCJ3aXRoRXJyb3JCb3VuZGFyeSIsImNvbXBvbmVudCIsImVycm9yQm91bmRhcnlQcm9wcyIsIldyYXBwZWQiLCJyZWYiLCJuYW1lIiwiZGlzcGxheU5hbWUiLCJjb25jYXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-error-boundary/dist/react-error-boundary.development.esm.js\n");

/***/ })

};
;