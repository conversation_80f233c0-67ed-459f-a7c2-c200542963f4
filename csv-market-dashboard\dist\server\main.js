"use strict";
// ============================================================================
// MAIN SERVER ENTRY POINT - CSV Market Dashboard Server
// ============================================================================
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
require("dotenv/config");
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
// import { csvService } from '@/services/CSVService';
const { SimpleCSVService } = require('@/services/SimpleCSVService');
const MarketDataService_1 = require("../services/MarketDataService");
class CSVMarketServer {
    app;
    server;
    io;
    port;
    instruments = [];
    connectedClients = new Set();
    csvService;
    constructor() {
        this.port = parseInt(process.env.PORT || '8081');
        this.app = (0, express_1.default)();
        this.server = (0, http_1.createServer)(this.app);
        this.io = new socket_io_1.Server(this.server, {
            cors: {
                origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:3001'],
                methods: ['GET', 'POST'],
                credentials: true,
            },
            transports: ['websocket'], // WebSocket only, no polling
            allowEIO3: true,
        });
        // Initialize CSV service
        this.csvService = new SimpleCSVService('./instruments.csv');
        this.setupMiddleware();
        this.setupRoutes();
        this.setupSocketHandlers();
        this.setupMarketDataHandlers();
    }
    /**
     * Setup Express middleware
     */
    setupMiddleware() {
        // Security middleware
        this.app.use((0, helmet_1.default)({
            contentSecurityPolicy: false, // Disable for development
        }));
        // CORS
        this.app.use((0, cors_1.default)({
            origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:3001'],
            credentials: true,
        }));
        // Rate limiting
        const limiter = (0, express_rate_limit_1.default)({
            windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
            max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
            message: 'Too many requests from this IP',
        });
        this.app.use('/api/', limiter);
        // Body parsing
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true }));
        // Static files
        this.app.use(express_1.default.static('public'));
    }
    /**
     * Setup API routes
     */
    setupRoutes() {
        // Health check
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'healthy',
                timestamp: Date.now(),
                server: 'CSV Market Dashboard',
                version: '1.0.0',
                instruments: this.instruments.length,
                connections: this.connectedClients.size,
                marketDataConnected: MarketDataService_1.marketDataService.connected,
            });
        });
        // Get all instruments
        this.app.get('/api/instruments', async (req, res) => {
            try {
                const filter = {
                    exchange: req.query.exchange ? req.query.exchange.split(',') : undefined,
                    instrumentType: req.query.instrumentType ? req.query.instrumentType.split(',') : undefined,
                    segment: req.query.segment ? req.query.segment.split(',') : undefined,
                    search: req.query.search,
                    isActive: req.query.isActive ? req.query.isActive === 'true' : undefined,
                };
                const result = await this.csvService.getInstruments(filter);
                res.json({
                    success: true,
                    data: result,
                    timestamp: Date.now(),
                });
            }
            catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message,
                    timestamp: Date.now(),
                });
            }
        });
        // Search instruments
        this.app.get('/api/instruments/search', async (req, res) => {
            try {
                const query = req.query.q;
                const limit = parseInt(req.query.limit) || 50;
                if (!query) {
                    return res.status(400).json({
                        success: false,
                        error: 'Search query is required',
                        timestamp: Date.now(),
                    });
                }
                const results = await this.csvService.searchInstruments(query, limit);
                res.json({
                    success: true,
                    data: results,
                    timestamp: Date.now(),
                });
            }
            catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message,
                    timestamp: Date.now(),
                });
            }
        });
        // Get instrument by ID
        this.app.get('/api/instruments/:id', async (req, res) => {
            try {
                const instrument = await this.csvService.getInstrumentById(req.params.id);
                if (!instrument) {
                    return res.status(404).json({
                        success: false,
                        error: 'Instrument not found',
                        timestamp: Date.now(),
                    });
                }
                res.json({
                    success: true,
                    data: instrument,
                    timestamp: Date.now(),
                });
            }
            catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message,
                    timestamp: Date.now(),
                });
            }
        });
        // Get exchanges
        this.app.get('/api/exchanges', async (req, res) => {
            try {
                const exchanges = await this.csvService.getExchanges();
                res.json({
                    success: true,
                    data: exchanges,
                    timestamp: Date.now(),
                });
            }
            catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message,
                    timestamp: Date.now(),
                });
            }
        });
        // Get instrument types
        this.app.get('/api/instrument-types', async (req, res) => {
            try {
                const types = await this.csvService.getInstrumentTypes();
                res.json({
                    success: true,
                    data: types,
                    timestamp: Date.now(),
                });
            }
            catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message,
                    timestamp: Date.now(),
                });
            }
        });
        // Get market data
        this.app.get('/api/market-data', (req, res) => {
            try {
                const allMarketData = MarketDataService_1.marketDataService.getAllMarketData();
                const marketDataArray = Array.from(allMarketData.values());
                res.json({
                    success: true,
                    data: {
                        connected: MarketDataService_1.marketDataService.connected,
                        instruments: marketDataArray,
                        totalInstruments: marketDataArray.length,
                        activeSubscriptions: MarketDataService_1.marketDataService.getSubscriptionCount(),
                        lastUpdate: Date.now(),
                    },
                    timestamp: Date.now(),
                });
            }
            catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message,
                    timestamp: Date.now(),
                });
            }
        });
        // Get NSE derivatives and index instruments
        this.app.get('/api/nse-derivatives', async (req, res) => {
            try {
                const instruments = await this.csvService.getNSEDerivativesAndIndex();
                const securityIds = instruments.map((inst) => inst.securityId);
                res.json({
                    success: true,
                    data: {
                        instruments,
                        securityIds,
                        count: instruments.length,
                        breakdown: instruments.reduce((acc, inst) => {
                            acc[inst.instrumentType] = (acc[inst.instrumentType] || 0) + 1;
                            return acc;
                        }, {}),
                    },
                    timestamp: Date.now(),
                });
            }
            catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message,
                    timestamp: Date.now(),
                });
            }
        });
        // Live WebSocket data viewer route
        this.app.get('/live-data', (req, res) => {
            const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Market Data - WebSocket Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }

        .header h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .status {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .status-item {
            background: #f8f9fa;
            padding: 15px 25px;
            border-radius: 10px;
            border-left: 4px solid #007bff;
            min-width: 200px;
            text-align: center;
        }

        .status-item.connected {
            border-left-color: #28a745;
            background: #d4edda;
        }

        .status-item.disconnected {
            border-left-color: #dc3545;
            background: #f8d7da;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        .data-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .data-container {
                grid-template-columns: 1fr;
            }
        }

        .data-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #e0e0e0;
        }

        .data-section h3 {
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }

        .data-list {
            max-height: 400px;
            overflow-y: auto;
            background: white;
            border-radius: 8px;
            padding: 15px;
        }

        .data-item {
            padding: 12px;
            margin-bottom: 10px;
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            border-left: 4px solid #007bff;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            animation: fadeIn 0.3s ease;
        }

        .data-item.new {
            border-left-color: #28a745;
            background: #d4edda;
        }

        .data-item .timestamp {
            color: #666;
            font-size: 10px;
            margin-bottom: 5px;
        }

        .data-item .content {
            color: #333;
            word-break: break-all;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Live Market Data Viewer</h1>
            <p>Real-time WebSocket data from CSV Market Dashboard</p>
        </div>

        <div class="status">
            <div class="status-item" id="connectionStatus">
                <strong>Connection:</strong> <span id="connectionText">Disconnected</span>
            </div>
            <div class="status-item">
                <strong>Server:</strong> <span id="serverUrl">http://localhost:8080</span>
            </div>
            <div class="status-item">
                <strong>Uptime:</strong> <span id="uptime">00:00:00</span>
            </div>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalMessages">0</div>
                <div class="stat-label">Total Messages</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="marketDataCount">0</div>
                <div class="stat-label">Market Data Updates</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="instrumentCount">0</div>
                <div class="stat-label">Instruments</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="messagesPerSecond">0</div>
                <div class="stat-label">Msg/Sec</div>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="connect()">Connect</button>
            <button class="btn btn-danger" onclick="disconnect()">Disconnect</button>
            <button class="btn btn-success" onclick="clearLogs()">Clear Logs</button>
            <button class="btn btn-primary" onclick="subscribeToSample()">Subscribe Sample</button>
        </div>

        <div class="data-container">
            <div class="data-section">
                <h3>📡 WebSocket Events</h3>
                <div class="data-list" id="eventsList"></div>
            </div>

            <div class="data-section">
                <h3>📊 Market Data Updates</h3>
                <div class="data-list" id="marketDataList"></div>
            </div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        let socket = null;
        let startTime = Date.now();
        let totalMessages = 0;
        let marketDataCount = 0;
        let instrumentCount = 0;
        let messageRate = 0;
        let lastSecondMessages = 0;
        let lastSecondTime = Date.now();

        // Update uptime every second
        setInterval(() => {
            const elapsed = Date.now() - startTime;
            const hours = Math.floor(elapsed / 3600000);
            const minutes = Math.floor((elapsed % 3600000) / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            document.getElementById('uptime').textContent =
                \`\${hours.toString().padStart(2, '0')}:\${minutes.toString().padStart(2, '0')}:\${seconds.toString().padStart(2, '0')}\`;

            // Calculate messages per second
            const now = Date.now();
            if (now - lastSecondTime >= 1000) {
                messageRate = Math.round((totalMessages - lastSecondMessages) / ((now - lastSecondTime) / 1000));
                lastSecondMessages = totalMessages;
                lastSecondTime = now;
                document.getElementById('messagesPerSecond').textContent = messageRate;
            }
        }, 1000);

        function updateStats() {
            document.getElementById('totalMessages').textContent = totalMessages;
            document.getElementById('marketDataCount').textContent = marketDataCount;
            document.getElementById('instrumentCount').textContent = instrumentCount;
        }

        function addEvent(type, data, isNew = true) {
            const eventsList = document.getElementById('eventsList');
            const item = document.createElement('div');
            item.className = 'data-item' + (isNew ? ' new' : '');

            const timestamp = new Date().toLocaleTimeString();
            item.innerHTML = \`
                <div class="timestamp">\${timestamp}</div>
                <div class="content"><strong>\${type}:</strong> \${JSON.stringify(data, null, 2)}</div>
            \`;

            eventsList.insertBefore(item, eventsList.firstChild);

            // Remove old items (keep last 50)
            while (eventsList.children.length > 50) {
                eventsList.removeChild(eventsList.lastChild);
            }

            // Remove 'new' class after animation
            setTimeout(() => item.classList.remove('new'), 300);

            totalMessages++;
            updateStats();
        }

        function addMarketData(data, isNew = true) {
            const marketDataList = document.getElementById('marketDataList');
            const item = document.createElement('div');
            item.className = 'data-item' + (isNew ? ' new' : '');

            const timestamp = new Date().toLocaleTimeString();
            item.innerHTML = \`
                <div class="timestamp">\${timestamp}</div>
                <div class="content">
                    <strong>\${data.securityId}</strong> - \${data.symbol || 'N/A'}<br>
                    LTP: ₹\${data.ltp || 'N/A'} | Vol: \${data.volume || 'N/A'} | Change: \${data.change || 'N/A'}
                </div>
            \`;

            marketDataList.insertBefore(item, marketDataList.firstChild);

            // Remove old items (keep last 50)
            while (marketDataList.children.length > 50) {
                marketDataList.removeChild(marketDataList.lastChild);
            }

            // Remove 'new' class after animation
            setTimeout(() => item.classList.remove('new'), 300);

            marketDataCount++;
            updateStats();
        }

        function connect() {
            if (socket) {
                socket.disconnect();
            }

            socket = io('http://localhost:8080', {
                transports: ['websocket', 'polling']
            });

            socket.on('connect', () => {
                document.getElementById('connectionStatus').className = 'status-item connected';
                document.getElementById('connectionText').textContent = 'Connected';
                addEvent('CONNECTED', { socketId: socket.id });
            });

            socket.on('disconnect', () => {
                document.getElementById('connectionStatus').className = 'status-item disconnected';
                document.getElementById('connectionText').textContent = 'Disconnected';
                addEvent('DISCONNECTED', {});
            });

            socket.on('initialData', (data) => {
                instrumentCount = data.totalInstruments || data.instruments?.length || 0;
                addEvent('INITIAL_DATA', {
                    instruments: data.instruments?.length || 0,
                    totalInstruments: data.totalInstruments,
                    marketData: data.marketData?.length || 0,
                    connected: data.connected
                });
                updateStats();
            });

            socket.on('marketData', (data) => {
                addMarketData(data);
            });

            socket.on('connectionStatus', (data) => {
                addEvent('CONNECTION_STATUS', data);
            });

            socket.on('error', (data) => {
                addEvent('ERROR', data);
            });

            socket.on('connect_error', (error) => {
                addEvent('CONNECT_ERROR', { message: error.message });
            });
        }

        function disconnect() {
            if (socket) {
                socket.disconnect();
                socket = null;
            }
        }

        function clearLogs() {
            document.getElementById('eventsList').innerHTML = '';
            document.getElementById('marketDataList').innerHTML = '';
            totalMessages = 0;
            marketDataCount = 0;
            lastSecondMessages = 0;
            updateStats();
        }

        function subscribeToSample() {
            if (socket && socket.connected) {
                // Subscribe to some sample NSE derivatives
                const sampleSecurityIds = [
                    '26000', '26001', '26002', '26003', '26004', // Sample IDs
                    '11536', '11537', '11538', '11539', '11540'  // More sample IDs
                ];

                socket.emit('subscribe', { securityIds: sampleSecurityIds });
                addEvent('SUBSCRIBE_REQUEST', { securityIds: sampleSecurityIds });
            } else {
                alert('Please connect first!');
            }
        }

        // Auto-connect on page load
        window.onload = () => {
            connect();
        };
    </script>
</body>
</html>
      `;
            res.send(html);
        });
    }
    /**
     * Setup Socket.IO handlers
     */
    setupSocketHandlers() {
        this.io.on('connection', (socket) => {
            this.connectedClients.add(socket.id);
            console.log(`👤 Client connected: ${socket.id} (Total: ${this.connectedClients.size})`);
            // Send initial connection status and stats
            socket.emit('initialData', {
                instruments: [], // Don't send instruments via socket - use API instead
                marketData: Array.from(MarketDataService_1.marketDataService.getAllMarketData().values()).slice(0, 100), // Limit market data
                connected: MarketDataService_1.marketDataService.connected,
                totalInstruments: this.instruments.length,
            });
            // Client subscriptions disabled - only server auto-subscription active
            socket.on('subscribe', (data) => {
                console.log(`🚫 Client subscription request ignored - using server auto-subscription only`);
                socket.emit('subscription_disabled', {
                    message: 'Client subscriptions disabled. Using server auto-subscription for NSE derivatives only.'
                });
            });
            // Client unsubscriptions disabled
            socket.on('unsubscribe', (data) => {
                console.log(`🚫 Client unsubscription request ignored - using server auto-subscription only`);
                socket.emit('unsubscription_disabled', {
                    message: 'Client unsubscriptions disabled. Using server auto-subscription for NSE derivatives only.'
                });
            });
            // Handle disconnection
            socket.on('disconnect', () => {
                this.connectedClients.delete(socket.id);
                console.log(`👤 Client disconnected: ${socket.id} (Total: ${this.connectedClients.size})`);
            });
        });
    }
    /**
     * Setup market data event handlers
     */
    setupMarketDataHandlers() {
        MarketDataService_1.marketDataService.on('connected', () => {
            console.log('📡 Market data service connected');
            this.io.emit('connectionStatus', { connected: true, timestamp: Date.now() });
        });
        MarketDataService_1.marketDataService.on('disconnected', () => {
            console.log('📡 Market data service disconnected');
            this.io.emit('connectionStatus', { connected: false, timestamp: Date.now() });
        });
        MarketDataService_1.marketDataService.on('marketData', (data) => {
            // Broadcast market data to all connected clients
            this.io.emit('marketData', data);
        });
        MarketDataService_1.marketDataService.on('error', (error) => {
            console.error('❌ Market data service error:', error);
            this.io.emit('error', { message: 'Market data error', error: error.message });
        });
    }
    /**
     * Start the server
     */
    async start() {
        try {
            console.log('🚀 Starting CSV Market Dashboard Server...');
            // Load instruments from CSV
            console.log('📊 Loading instruments from CSV...');
            this.instruments = await this.csvService.loadInstruments();
            console.log(`✅ Loaded ${this.instruments.length} instruments`);
            // Connect to market data service with credentials
            console.log('📡 Connecting to market data service...');
            const accessToken = process.env.ACCESS_TOKEN;
            const clientId = process.env.CLIENT_ID;
            if (accessToken && clientId) {
                console.log('🔑 Using Dhan credentials for live market data');
                // Update the service with credentials
                MarketDataService_1.marketDataService.accessToken = accessToken;
                MarketDataService_1.marketDataService.clientId = clientId;
            }
            else {
                console.log('⚠️ No Dhan credentials found - will use mock data');
            }
            await MarketDataService_1.marketDataService.connect();
            // Auto-subscribe to NSE derivatives and index instruments
            setTimeout(async () => {
                console.log('🎯 Auto-subscribing to NSE derivatives and index instruments...');
                try {
                    // Get all NSE derivatives and index instruments
                    const allNseDerivatives = await this.csvService.getNSEDerivativesAndIndex();
                    console.log(`🎯 Found ${allNseDerivatives.length} NSE derivatives and index instruments for subscription`);
                    // Show breakdown by instrument type
                    const breakdown = allNseDerivatives.reduce((acc, inst) => {
                        acc[inst.instrumentType] = (acc[inst.instrumentType] || 0) + 1;
                        return acc;
                    }, {});
                    console.log('📊 Breakdown by instrument type:', breakdown);
                    // ✅ Add NIFTY index instruments for spot price
                    const niftyIndexInstruments = [
                        {
                            securityId: '2',
                            symbol: 'NIFTY',
                            exchange: 'IDX_I',
                            exchangeCode: 6,
                            segment: 'I',
                            instrumentType: 'INDEX',
                            strikePrice: 0,
                            expiryDate: '',
                            optionType: ''
                        },
                        {
                            securityId: '13',
                            symbol: 'NIFTYFUTM2',
                            exchange: 'IDX_I',
                            exchangeCode: 6,
                            segment: 'I',
                            instrumentType: 'INDEX',
                            strikePrice: 0,
                            expiryDate: '',
                            optionType: ''
                        }
                    ];
                    // Add NIFTY index instruments to the list
                    allNseDerivatives.push(...niftyIndexInstruments);
                    console.log('✅ Added NIFTY index instruments (security IDs 2, 13) to subscription list');
                    // Subscribe to all NSE derivatives in batches
                    await MarketDataService_1.marketDataService.subscribe(allNseDerivatives);
                    console.log(`✅ Auto-subscribed to ${allNseDerivatives.length} NSE derivatives and index instruments`);
                }
                catch (error) {
                    console.error('❌ Error auto-subscribing to instruments:', error);
                }
            }, 1000); // Wait 1 second for connection to stabilize
            // Start HTTP server
            await new Promise((resolve, reject) => {
                this.server.listen(this.port, (error) => {
                    if (error) {
                        reject(error);
                    }
                    else {
                        console.log(`🌐 Server running on http://localhost:${this.port}`);
                        resolve();
                    }
                });
            });
            console.log('🎉 CSV Market Dashboard Server started successfully!');
        }
        catch (error) {
            console.error('❌ Failed to start server:', error);
            throw error;
        }
    }
    /**
     * Graceful shutdown
     */
    async shutdown() {
        console.log('🛑 Shutting down server...');
        MarketDataService_1.marketDataService.disconnect();
        await new Promise((resolve) => {
            this.server.close(() => {
                console.log('✅ Server shutdown complete');
                resolve();
            });
        });
    }
}
// Global error handlers
process.on('uncaughtException', (error) => {
    console.error('💥 Uncaught Exception:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
// Graceful shutdown handlers
let server = null;
const gracefulShutdown = async (signal) => {
    console.log(`📡 Received ${signal}, starting graceful shutdown...`);
    if (server) {
        try {
            await server.shutdown();
            process.exit(0);
        }
        catch (error) {
            console.error('❌ Error during shutdown:', error);
            process.exit(1);
        }
    }
    else {
        process.exit(0);
    }
};
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
// Start the server
async function main() {
    try {
        server = new CSVMarketServer();
        await server.start();
    }
    catch (error) {
        console.error('❌ Failed to start application:', error);
        process.exit(1);
    }
}
// Run the application
if (require.main === module) {
    main().catch(console.error);
}
//# sourceMappingURL=main.js.map