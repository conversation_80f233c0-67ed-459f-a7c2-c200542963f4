"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const LMPOP_1 = __importStar(require("./LMPOP"));
exports.default = {
    IS_READ_ONLY: false,
    /**
     * Pops elements from multiple lists; blocks until elements are available
     * @param parser - The Redis command parser
     * @param timeout - Timeout in seconds, 0 to block indefinitely
     * @param args - Additional arguments for LMPOP command
     */
    parseCommand(parser, timeout, ...args) {
        parser.push('BLMPOP', timeout.toString());
        (0, LMPOP_1.parseLMPopArguments)(parser, ...args);
    },
    transformReply: LMPOP_1.default.transformReply
};
//# sourceMappingURL=BLMPOP.js.map