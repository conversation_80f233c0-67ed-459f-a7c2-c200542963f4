"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[249],{4298:(t,e,s)=>{let r,i;s.d(e,{io:()=>tO});var n,o={};s.r(o),s.d(o,{Decoder:()=>tm,Encoder:()=>ty,PacketType:()=>n,protocol:()=>tf});let a=Object.create(null);a.open="0",a.close="1",a.ping="2",a.pong="3",a.message="4",a.upgrade="5",a.noop="6";let h=Object.create(null);Object.keys(a).forEach(t=>{h[a[t]]=t});let c={type:"error",data:"parser error"},l="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),u="function"==typeof ArrayBuffer,p=t=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer instanceof ArrayBuffer,d=({type:t,data:e},s,r)=>{if(l&&e instanceof Blob)if(s)return r(e);else return f(e,r);if(u&&(e instanceof ArrayBuffer||p(e)))if(s)return r(e);else return f(new Blob([e]),r);return r(a[t]+(e||""))},f=(t,e)=>{let s=new FileReader;return s.onload=function(){e("b"+(s.result.split(",")[1]||""))},s.readAsDataURL(t)};function y(t){return t instanceof Uint8Array?t:t instanceof ArrayBuffer?new Uint8Array(t):new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}let g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",m="undefined"==typeof Uint8Array?[]:new Uint8Array(256);for(let t=0;t<g.length;t++)m[g.charCodeAt(t)]=t;let _=t=>{let e=.75*t.length,s=t.length,r,i=0,n,o,a,h;"="===t[t.length-1]&&(e--,"="===t[t.length-2]&&e--);let c=new ArrayBuffer(e),l=new Uint8Array(c);for(r=0;r<s;r+=4)n=m[t.charCodeAt(r)],o=m[t.charCodeAt(r+1)],a=m[t.charCodeAt(r+2)],h=m[t.charCodeAt(r+3)],l[i++]=n<<2|o>>4,l[i++]=(15&o)<<4|a>>2,l[i++]=(3&a)<<6|63&h;return c},b="function"==typeof ArrayBuffer,v=(t,e)=>{if("string"!=typeof t)return{type:"message",data:w(t,e)};let s=t.charAt(0);return"b"===s?{type:"message",data:k(t.substring(1),e)}:h[s]?t.length>1?{type:h[s],data:t.substring(1)}:{type:h[s]}:c},k=(t,e)=>b?w(_(t),e):{base64:!0,data:t},w=(t,e)=>"blob"===e?t instanceof Blob?t:new Blob([t]):t instanceof ArrayBuffer?t:t.buffer,E=(t,e)=>{let s=t.length,r=Array(s),i=0;t.forEach((t,n)=>{d(t,!1,t=>{r[n]=t,++i===s&&e(r.join("\x1e"))})})},A=(t,e)=>{let s=t.split("\x1e"),r=[];for(let t=0;t<s.length;t++){let i=v(s[t],e);if(r.push(i),"error"===i.type)break}return r};function O(t){return t.reduce((t,e)=>t+e.length,0)}function T(t,e){if(t[0].length===e)return t.shift();let s=new Uint8Array(e),r=0;for(let i=0;i<e;i++)s[i]=t[0][r++],r===t[0].length&&(t.shift(),r=0);return t.length&&r<t[0].length&&(t[0]=t[0].slice(r)),s}function R(t){if(t){var e=t;for(var s in R.prototype)e[s]=R.prototype[s];return e}}R.prototype.on=R.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},R.prototype.once=function(t,e){function s(){this.off(t,s),e.apply(this,arguments)}return s.fn=e,this.on(t,s),this},R.prototype.off=R.prototype.removeListener=R.prototype.removeAllListeners=R.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var s,r=this._callbacks["$"+t];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var i=0;i<r.length;i++)if((s=r[i])===e||s.fn===e){r.splice(i,1);break}return 0===r.length&&delete this._callbacks["$"+t],this},R.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=Array(arguments.length-1),s=this._callbacks["$"+t],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(s){s=s.slice(0);for(var r=0,i=s.length;r<i;++r)s[r].apply(this,e)}return this},R.prototype.emitReserved=R.prototype.emit,R.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},R.prototype.hasListeners=function(t){return!!this.listeners(t).length};let C="function"==typeof Promise&&"function"==typeof Promise.resolve?t=>Promise.resolve().then(t):(t,e)=>e(t,0),S="undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")();function B(t,...e){return e.reduce((e,s)=>(t.hasOwnProperty(s)&&(e[s]=t[s]),e),{})}let x=S.setTimeout,N=S.clearTimeout;function L(t,e){e.useNativeTimers?(t.setTimeoutFn=x.bind(S),t.clearTimeoutFn=N.bind(S)):(t.setTimeoutFn=S.setTimeout.bind(S),t.clearTimeoutFn=S.clearTimeout.bind(S))}function q(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}class P extends Error{constructor(t,e,s){super(t),this.description=e,this.context=s,this.type="TransportError"}}class j extends R{constructor(t){super(),this.writable=!1,L(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,e,s){return super.emitReserved("error",new P(t,e,s)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return("opening"===this.readyState||"open"===this.readyState)&&(this.doClose(),this.onClose()),this}send(t){"open"===this.readyState&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){let e=v(t,this.socket.binaryType);this.onPacket(e)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,e={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(e)}_hostname(){let t=this.opts.hostname;return -1===t.indexOf(":")?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(t){let e=function(t){let e="";for(let s in t)t.hasOwnProperty(s)&&(e.length&&(e+="&"),e+=encodeURIComponent(s)+"="+encodeURIComponent(t[s]));return e}(t);return e.length?"?"+e:""}}class I extends j{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(t){this.readyState="pausing";let e=()=>{this.readyState="paused",t()};if(this._polling||!this.writable){let t=0;this._polling&&(t++,this.once("pollComplete",function(){--t||e()})),this.writable||(t++,this.once("drain",function(){--t||e()}))}else e()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){A(t,this.socket.binaryType).forEach(t=>{if("opening"===this.readyState&&"open"===t.type&&this.onOpen(),"close"===t.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(t)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this._poll())}doClose(){let t=()=>{this.write([{type:"close"}])};"open"===this.readyState?t():this.once("open",t)}write(t){this.writable=!1,E(t,t=>{this.doWrite(t,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){let t=this.opts.secure?"https":"http",e=this.query||{};return!1!==this.opts.timestampRequests&&(e[this.opts.timestampParam]=q()),this.supportsBinary||e.sid||(e.b64=1),this.createUri(t,e)}}let U=!1;try{U="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(t){}let D=U;function F(){}class V extends I{constructor(t){if(super(t),"undefined"!=typeof location){let e="https:"===location.protocol,s=location.port;s||(s=e?"443":"80"),this.xd="undefined"!=typeof location&&t.hostname!==location.hostname||s!==t.port}}doWrite(t,e){let s=this.request({method:"POST",data:t});s.on("success",e),s.on("error",(t,e)=>{this.onError("xhr post error",t,e)})}doPoll(){let t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(t,e)=>{this.onError("xhr poll error",t,e)}),this.pollXhr=t}}class M extends R{constructor(t,e,s){super(),this.createRequest=t,L(this,s),this._opts=s,this._method=s.method||"GET",this._uri=e,this._data=void 0!==s.data?s.data:null,this._create()}_create(){var t;let e=B(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");e.xdomain=!!this._opts.xd;let s=this._xhr=this.createRequest(e);try{s.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders)for(let t in s.setDisableHeaderCheck&&s.setDisableHeaderCheck(!0),this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(t)&&s.setRequestHeader(t,this._opts.extraHeaders[t])}catch(t){}if("POST"===this._method)try{s.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(t){}try{s.setRequestHeader("Accept","*/*")}catch(t){}null==(t=this._opts.cookieJar)||t.addCookies(s),"withCredentials"in s&&(s.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(s.timeout=this._opts.requestTimeout),s.onreadystatechange=()=>{var t;3===s.readyState&&(null==(t=this._opts.cookieJar)||t.parseCookies(s.getResponseHeader("set-cookie"))),4===s.readyState&&(200===s.status||1223===s.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"==typeof s.status?s.status:0)},0))},s.send(this._data)}catch(t){this.setTimeoutFn(()=>{this._onError(t)},0);return}"undefined"!=typeof document&&(this._index=M.requestsCount++,M.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(void 0!==this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=F,t)try{this._xhr.abort()}catch(t){}"undefined"!=typeof document&&delete M.requests[this._index],this._xhr=null}}_onLoad(){let t=this._xhr.responseText;null!==t&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}function H(){for(let t in M.requests)M.requests.hasOwnProperty(t)&&M.requests[t].abort()}M.requestsCount=0,M.requests={},"undefined"!=typeof document&&("function"==typeof attachEvent?attachEvent("onunload",H):"function"==typeof addEventListener&&addEventListener("onpagehide"in S?"pagehide":"unload",H,!1));let K=function(){let t=W({xdomain:!1});return t&&null!==t.responseType}();class z extends V{constructor(t){super(t);let e=t&&t.forceBase64;this.supportsBinary=K&&!e}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new M(W,this.uri(),t)}}function W(t){let e=t.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!e||D))return new XMLHttpRequest}catch(t){}if(!e)try{return new S[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch(t){}}let Y="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class J extends j{get name(){return"websocket"}doOpen(){let t=this.uri(),e=this.opts.protocols,s=Y?{}:B(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(s.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,e,s)}catch(t){return this.emitReserved("error",t)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let e=0;e<t.length;e++){let s=t[e],r=e===t.length-1;d(s,this.supportsBinary,t=>{try{this.doWrite(s,t)}catch(t){}r&&C(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){let t=this.opts.secure?"wss":"ws",e=this.query||{};return this.opts.timestampRequests&&(e[this.opts.timestampParam]=q()),this.supportsBinary||(e.b64=1),this.createUri(t,e)}}let $=S.WebSocket||S.MozWebSocket;class Q extends J{createSocket(t,e,s){return Y?new $(t,e,s):e?new $(t,e):new $(t)}doWrite(t,e){this.ws.send(e)}}class X extends j{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this._transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(t=>{let e=function(t,e){i||(i=new TextDecoder);let s=[],r=0,n=-1,o=!1;return new TransformStream({transform(a,h){for(s.push(a);;){if(0===r){if(1>O(s))break;let t=T(s,1);o=(128&t[0])==128,r=(n=127&t[0])<126?3:126===n?1:2}else if(1===r){if(2>O(s))break;let t=T(s,2);n=new DataView(t.buffer,t.byteOffset,t.length).getUint16(0),r=3}else if(2===r){if(8>O(s))break;let t=T(s,8),e=new DataView(t.buffer,t.byteOffset,t.length),i=e.getUint32(0);if(i>2097151){h.enqueue(c);break}n=0x100000000*i+e.getUint32(4),r=3}else{if(O(s)<n)break;let t=T(s,n);h.enqueue(v(o?t:i.decode(t),e)),r=0}if(0===n||n>t){h.enqueue(c);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),s=t.readable.pipeThrough(e).getReader(),n=new TransformStream({transform(t,e){var s;s=s=>{let r,i=s.length;if(i<126)new DataView((r=new Uint8Array(1)).buffer).setUint8(0,i);else if(i<65536){let t=new DataView((r=new Uint8Array(3)).buffer);t.setUint8(0,126),t.setUint16(1,i)}else{let t=new DataView((r=new Uint8Array(9)).buffer);t.setUint8(0,127),t.setBigUint64(1,BigInt(i))}t.data&&"string"!=typeof t.data&&(r[0]|=128),e.enqueue(r),e.enqueue(s)},l&&t.data instanceof Blob?t.data.arrayBuffer().then(y).then(s):u&&(t.data instanceof ArrayBuffer||p(t.data))?s(y(t.data)):d(t,!1,t=>{r||(r=new TextEncoder),s(r.encode(t))})}});n.readable.pipeTo(t.writable),this._writer=n.writable.getWriter();let o=()=>{s.read().then(({done:t,value:e})=>{t||(this.onPacket(e),o())}).catch(t=>{})};o();let a={type:"open"};this.query.sid&&(a.data=`{"sid":"${this.query.sid}"}`),this._writer.write(a).then(()=>this.onOpen())})})}write(t){this.writable=!1;for(let e=0;e<t.length;e++){let s=t[e],r=e===t.length-1;this._writer.write(s).then(()=>{r&&C(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;null==(t=this._transport)||t.close()}}let G={websocket:Q,webtransport:X,polling:z},Z=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,tt=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function te(t){if(t.length>8e3)throw"URI too long";let e=t,s=t.indexOf("["),r=t.indexOf("]");-1!=s&&-1!=r&&(t=t.substring(0,s)+t.substring(s,r).replace(/:/g,";")+t.substring(r,t.length));let i=Z.exec(t||""),n={},o=14;for(;o--;)n[tt[o]]=i[o]||"";return -1!=s&&-1!=r&&(n.source=e,n.host=n.host.substring(1,n.host.length-1).replace(/;/g,":"),n.authority=n.authority.replace("[","").replace("]","").replace(/;/g,":"),n.ipv6uri=!0),n.pathNames=function(t,e){let s=e.replace(/\/{2,9}/g,"/").split("/");return("/"==e.slice(0,1)||0===e.length)&&s.splice(0,1),"/"==e.slice(-1)&&s.splice(s.length-1,1),s}(0,n.path),n.queryKey=function(t,e){let s={};return e.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(t,e,r){e&&(s[e]=r)}),s}(0,n.query),n}let ts="function"==typeof addEventListener&&"function"==typeof removeEventListener,tr=[];ts&&addEventListener("offline",()=>{tr.forEach(t=>t())},!1);class ti extends R{constructor(t,e){if(super(),this.binaryType="arraybuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&"object"==typeof t&&(e=t,t=null),t){let s=te(t);e.hostname=s.host,e.secure="https"===s.protocol||"wss"===s.protocol,e.port=s.port,s.query&&(e.query=s.query)}else e.host&&(e.hostname=te(e.host).host);L(this,e),this.secure=null!=e.secure?e.secure:"undefined"!=typeof location&&"https:"===location.protocol,e.hostname&&!e.port&&(e.port=this.secure?"443":"80"),this.hostname=e.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=e.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},e.transports.forEach(t=>{let e=t.prototype.name;this.transports.push(e),this._transportsByName[e]=t}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},e),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(t){let e={},s=t.split("&");for(let t=0,r=s.length;t<r;t++){let r=s[t].split("=");e[decodeURIComponent(r[0])]=decodeURIComponent(r[1])}return e}(this.opts.query)),ts&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},tr.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){let e=Object.assign({},this.opts.query);e.EIO=4,e.transport=t,this.id&&(e.sid=this.id);let s=Object.assign({},this.opts,{query:e,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](s)}_open(){if(0===this.transports.length)return void this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);let t=this.opts.rememberUpgrade&&ti.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";let e=this.createTransport(t);e.open(),this.setTransport(e)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",t=>this._onClose("transport close",t))}onOpen(){this.readyState="open",ti.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":let e=Error("server error");e.code=t.data,this._onError(e);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data)}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);let t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){let t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let t=1;for(let e=0;e<this.writeBuffer.length;e++){let s=this.writeBuffer[e].data;if(s&&(t+="string"==typeof s?function(t){let e=0,s=0;for(let r=0,i=t.length;r<i;r++)(e=t.charCodeAt(r))<128?s+=1:e<2048?s+=2:e<55296||e>=57344?s+=3:(r++,s+=4);return s}(s):Math.ceil(1.33*(s.byteLength||s.size))),e>0&&t>this._maxPayload)return this.writeBuffer.slice(0,e);t+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;let t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,C(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,e,s){return this._sendPacket("message",t,e,s),this}send(t,e,s){return this._sendPacket("message",t,e,s),this}_sendPacket(t,e,s,r){if("function"==typeof e&&(r=e,e=void 0),"function"==typeof s&&(r=s,s=null),"closing"===this.readyState||"closed"===this.readyState)return;(s=s||{}).compress=!1!==s.compress;let i={type:t,data:e,options:s};this.emitReserved("packetCreate",i),this.writeBuffer.push(i),r&&this.once("flush",r),this.flush()}close(){let t=()=>{this._onClose("forced close"),this.transport.close()},e=()=>{this.off("upgrade",e),this.off("upgradeError",e),t()},s=()=>{this.once("upgrade",e),this.once("upgradeError",e)};return("opening"===this.readyState||"open"===this.readyState)&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?s():t()}):this.upgrading?s():t()),this}_onError(t){if(ti.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),ts&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){let t=tr.indexOf(this._offlineEventListener);-1!==t&&tr.splice(t,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,e),this.writeBuffer=[],this._prevBufferLen=0}}}ti.protocol=4;class tn extends ti{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade)for(let t=0;t<this._upgrades.length;t++)this._probe(this._upgrades[t])}_probe(t){let e=this.createTransport(t),s=!1;ti.priorWebsocketSuccess=!1;let r=()=>{s||(e.send([{type:"ping",data:"probe"}]),e.once("packet",t=>{if(!s)if("pong"===t.type&&"probe"===t.data){if(this.upgrading=!0,this.emitReserved("upgrading",e),!e)return;ti.priorWebsocketSuccess="websocket"===e.name,this.transport.pause(()=>{s||"closed"!==this.readyState&&(c(),this.setTransport(e),e.send([{type:"upgrade"}]),this.emitReserved("upgrade",e),e=null,this.upgrading=!1,this.flush())})}else{let t=Error("probe error");t.transport=e.name,this.emitReserved("upgradeError",t)}}))};function i(){s||(s=!0,c(),e.close(),e=null)}let n=t=>{let s=Error("probe error: "+t);s.transport=e.name,i(),this.emitReserved("upgradeError",s)};function o(){n("transport closed")}function a(){n("socket closed")}function h(t){e&&t.name!==e.name&&i()}let c=()=>{e.removeListener("open",r),e.removeListener("error",n),e.removeListener("close",o),this.off("close",a),this.off("upgrading",h)};e.once("open",r),e.once("error",n),e.once("close",o),this.once("close",a),this.once("upgrading",h),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==t?this.setTimeoutFn(()=>{s||e.open()},200):e.open()}onHandshake(t){this._upgrades=this._filterUpgrades(t.upgrades),super.onHandshake(t)}_filterUpgrades(t){let e=[];for(let s=0;s<t.length;s++)~this.transports.indexOf(t[s])&&e.push(t[s]);return e}}class to extends tn{constructor(t,e={}){let s="object"==typeof t?t:e;(!s.transports||s.transports&&"string"==typeof s.transports[0])&&(s.transports=(s.transports||["polling","websocket","webtransport"]).map(t=>G[t]).filter(t=>!!t)),super(t,s)}}to.protocol;let ta="function"==typeof ArrayBuffer,th=t=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(t):t.buffer instanceof ArrayBuffer,tc=Object.prototype.toString,tl="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===tc.call(Blob),tu="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===tc.call(File);function tp(t){return ta&&(t instanceof ArrayBuffer||th(t))||tl&&t instanceof Blob||tu&&t instanceof File}let td=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],tf=5;!function(t){t[t.CONNECT=0]="CONNECT",t[t.DISCONNECT=1]="DISCONNECT",t[t.EVENT=2]="EVENT",t[t.ACK=3]="ACK",t[t.CONNECT_ERROR=4]="CONNECT_ERROR",t[t.BINARY_EVENT=5]="BINARY_EVENT",t[t.BINARY_ACK=6]="BINARY_ACK"}(n||(n={}));class ty{constructor(t){this.replacer=t}encode(t){return(t.type===n.EVENT||t.type===n.ACK)&&function t(e,s){if(!e||"object"!=typeof e)return!1;if(Array.isArray(e)){for(let s=0,r=e.length;s<r;s++)if(t(e[s]))return!0;return!1}if(tp(e))return!0;if(e.toJSON&&"function"==typeof e.toJSON&&1==arguments.length)return t(e.toJSON(),!0);for(let s in e)if(Object.prototype.hasOwnProperty.call(e,s)&&t(e[s]))return!0;return!1}(t)?this.encodeAsBinary({type:t.type===n.EVENT?n.BINARY_EVENT:n.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let e=""+t.type;return(t.type===n.BINARY_EVENT||t.type===n.BINARY_ACK)&&(e+=t.attachments+"-"),t.nsp&&"/"!==t.nsp&&(e+=t.nsp+","),null!=t.id&&(e+=t.id),null!=t.data&&(e+=JSON.stringify(t.data,this.replacer)),e}encodeAsBinary(t){let e=function(t){let e=[],s=t.data;return t.data=function t(e,s){if(!e)return e;if(tp(e)){let t={_placeholder:!0,num:s.length};return s.push(e),t}if(Array.isArray(e)){let r=Array(e.length);for(let i=0;i<e.length;i++)r[i]=t(e[i],s);return r}if("object"==typeof e&&!(e instanceof Date)){let r={};for(let i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=t(e[i],s));return r}return e}(s,e),t.attachments=e.length,{packet:t,buffers:e}}(t),s=this.encodeAsString(e.packet),r=e.buffers;return r.unshift(s),r}}function tg(t){return"[object Object]"===Object.prototype.toString.call(t)}class tm extends R{constructor(t){super(),this.reviver=t}add(t){let e;if("string"==typeof t){if(this.reconstructor)throw Error("got plaintext data when reconstructing a packet");let s=(e=this.decodeString(t)).type===n.BINARY_EVENT;s||e.type===n.BINARY_ACK?(e.type=s?n.EVENT:n.ACK,this.reconstructor=new t_(e),0===e.attachments&&super.emitReserved("decoded",e)):super.emitReserved("decoded",e)}else if(tp(t)||t.base64)if(this.reconstructor)(e=this.reconstructor.takeBinaryData(t))&&(this.reconstructor=null,super.emitReserved("decoded",e));else throw Error("got binary data when not reconstructing a packet");else throw Error("Unknown type: "+t)}decodeString(t){let e=0,s={type:Number(t.charAt(0))};if(void 0===n[s.type])throw Error("unknown packet type "+s.type);if(s.type===n.BINARY_EVENT||s.type===n.BINARY_ACK){let r=e+1;for(;"-"!==t.charAt(++e)&&e!=t.length;);let i=t.substring(r,e);if(i!=Number(i)||"-"!==t.charAt(e))throw Error("Illegal attachments");s.attachments=Number(i)}if("/"===t.charAt(e+1)){let r=e+1;for(;++e&&","!==t.charAt(e)&&e!==t.length;);s.nsp=t.substring(r,e)}else s.nsp="/";let r=t.charAt(e+1);if(""!==r&&Number(r)==r){let r=e+1;for(;++e;){let s=t.charAt(e);if(null==s||Number(s)!=s){--e;break}if(e===t.length)break}s.id=Number(t.substring(r,e+1))}if(t.charAt(++e)){let r=this.tryParse(t.substr(e));if(tm.isPayloadValid(s.type,r))s.data=r;else throw Error("invalid payload")}return s}tryParse(t){try{return JSON.parse(t,this.reviver)}catch(t){return!1}}static isPayloadValid(t,e){switch(t){case n.CONNECT:return tg(e);case n.DISCONNECT:return void 0===e;case n.CONNECT_ERROR:return"string"==typeof e||tg(e);case n.EVENT:case n.BINARY_EVENT:return Array.isArray(e)&&("number"==typeof e[0]||"string"==typeof e[0]&&-1===td.indexOf(e[0]));case n.ACK:case n.BINARY_ACK:return Array.isArray(e)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class t_{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){var e,s;let t=(e=this.reconPack,s=this.buffers,e.data=function t(e,s){if(!e)return e;if(e&&!0===e._placeholder){if("number"==typeof e.num&&e.num>=0&&e.num<s.length)return s[e.num];throw Error("illegal attachments")}if(Array.isArray(e))for(let r=0;r<e.length;r++)e[r]=t(e[r],s);else if("object"==typeof e)for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&(e[r]=t(e[r],s));return e}(e.data,s),delete e.attachments,e);return this.finishedReconstruction(),t}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function tb(t,e,s){return t.on(e,s),function(){t.off(e,s)}}let tv=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class tk extends R{constructor(t,e,s){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=e,s&&s.auth&&(this.auth=s.auth),this._opts=Object.assign({},s),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;let t=this.io;this.subs=[tb(t,"open",this.onopen.bind(this)),tb(t,"packet",this.onpacket.bind(this)),tb(t,"error",this.onerror.bind(this)),tb(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...e){var s,r,i;if(tv.hasOwnProperty(t))throw Error('"'+t.toString()+'" is a reserved event name');if(e.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(e),this;let o={type:n.EVENT,data:e};if(o.options={},o.options.compress=!1!==this.flags.compress,"function"==typeof e[e.length-1]){let t=this.ids++,s=e.pop();this._registerAckCallback(t,s),o.id=t}let a=null==(r=null==(s=this.io.engine)?void 0:s.transport)?void 0:r.writable,h=this.connected&&!(null==(i=this.io.engine)?void 0:i._hasPingExpired());return this.flags.volatile&&!a||(h?(this.notifyOutgoingListeners(o),this.packet(o)):this.sendBuffer.push(o)),this.flags={},this}_registerAckCallback(t,e){var s;let r=null!=(s=this.flags.timeout)?s:this._opts.ackTimeout;if(void 0===r){this.acks[t]=e;return}let i=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let e=0;e<this.sendBuffer.length;e++)this.sendBuffer[e].id===t&&this.sendBuffer.splice(e,1);e.call(this,Error("operation has timed out"))},r),n=(...t)=>{this.io.clearTimeoutFn(i),e.apply(this,t)};n.withError=!0,this.acks[t]=n}emitWithAck(t,...e){return new Promise((s,r)=>{let i=(t,e)=>t?r(t):s(e);i.withError=!0,e.push(i),this.emit(t,...e)})}_addToQueue(t){let e;"function"==typeof t[t.length-1]&&(e=t.pop());let s={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((t,...r)=>{if(s===this._queue[0])return null!==t?s.tryCount>this._opts.retries&&(this._queue.shift(),e&&e(t)):(this._queue.shift(),e&&e(null,...r)),s.pending=!1,this._drainQueue()}),this._queue.push(s),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||0===this._queue.length)return;let e=this._queue[0];(!e.pending||t)&&(e.pending=!0,e.tryCount++,this.flags=e.flags,this.emit.apply(this,e.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){"function"==typeof this.auth?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:n.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,e){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,e),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(e=>String(e.id)===t)){let e=this.acks[t];delete this.acks[t],e.withError&&e.call(this,Error("socket has been disconnected"))}})}onpacket(t){if(t.nsp===this.nsp)switch(t.type){case n.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case n.EVENT:case n.BINARY_EVENT:this.onevent(t);break;case n.ACK:case n.BINARY_ACK:this.onack(t);break;case n.DISCONNECT:this.ondisconnect();break;case n.CONNECT_ERROR:this.destroy();let e=Error(t.data.message);e.data=t.data.data,this.emitReserved("connect_error",e)}}onevent(t){let e=t.data||[];null!=t.id&&e.push(this.ack(t.id)),this.connected?this.emitEvent(e):this.receiveBuffer.push(Object.freeze(e))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length)for(let e of this._anyListeners.slice())e.apply(this,t);super.emit.apply(this,t),this._pid&&t.length&&"string"==typeof t[t.length-1]&&(this._lastOffset=t[t.length-1])}ack(t){let e=this,s=!1;return function(...r){s||(s=!0,e.packet({type:n.ACK,id:t,data:r}))}}onack(t){let e=this.acks[t.id];"function"==typeof e&&(delete this.acks[t.id],e.withError&&t.data.unshift(null),e.apply(this,t.data))}onconnect(t,e){this.id=t,this.recovered=e&&this._pid===e,this._pid=e,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:n.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){let e=this._anyListeners;for(let s=0;s<e.length;s++)if(t===e[s]){e.splice(s,1);break}}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){let e=this._anyOutgoingListeners;for(let s=0;s<e.length;s++)if(t===e[s]){e.splice(s,1);break}}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length)for(let e of this._anyOutgoingListeners.slice())e.apply(this,t.data)}}function tw(t){t=t||{},this.ms=t.min||100,this.max=t.max||1e4,this.factor=t.factor||2,this.jitter=t.jitter>0&&t.jitter<=1?t.jitter:0,this.attempts=0}tw.prototype.duration=function(){var t=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var e=Math.random(),s=Math.floor(e*this.jitter*t);t=(1&Math.floor(10*e))==0?t-s:t+s}return 0|Math.min(t,this.max)},tw.prototype.reset=function(){this.attempts=0},tw.prototype.setMin=function(t){this.ms=t},tw.prototype.setMax=function(t){this.max=t},tw.prototype.setJitter=function(t){this.jitter=t};class tE extends R{constructor(t,e){var s;super(),this.nsps={},this.subs=[],t&&"object"==typeof t&&(e=t,t=void 0),(e=e||{}).path=e.path||"/socket.io",this.opts=e,L(this,e),this.reconnection(!1!==e.reconnection),this.reconnectionAttempts(e.reconnectionAttempts||1/0),this.reconnectionDelay(e.reconnectionDelay||1e3),this.reconnectionDelayMax(e.reconnectionDelayMax||5e3),this.randomizationFactor(null!=(s=e.randomizationFactor)?s:.5),this.backoff=new tw({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==e.timeout?2e4:e.timeout),this._readyState="closed",this.uri=t;let r=e.parser||o;this.encoder=new r.Encoder,this.decoder=new r.Decoder,this._autoConnect=!1!==e.autoConnect,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,t||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(t){return void 0===t?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var e;return void 0===t?this._reconnectionDelay:(this._reconnectionDelay=t,null==(e=this.backoff)||e.setMin(t),this)}randomizationFactor(t){var e;return void 0===t?this._randomizationFactor:(this._randomizationFactor=t,null==(e=this.backoff)||e.setJitter(t),this)}reconnectionDelayMax(t){var e;return void 0===t?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,null==(e=this.backoff)||e.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new to(this.uri,this.opts);let e=this.engine,s=this;this._readyState="opening",this.skipReconnect=!1;let r=tb(e,"open",function(){s.onopen(),t&&t()}),i=e=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",e),t?t(e):this.maybeReconnectOnOpen()},n=tb(e,"error",i);if(!1!==this._timeout){let t=this._timeout,s=this.setTimeoutFn(()=>{r(),i(Error("timeout")),e.close()},t);this.opts.autoUnref&&s.unref(),this.subs.push(()=>{this.clearTimeoutFn(s)})}return this.subs.push(r),this.subs.push(n),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");let t=this.engine;this.subs.push(tb(t,"ping",this.onping.bind(this)),tb(t,"data",this.ondata.bind(this)),tb(t,"error",this.onerror.bind(this)),tb(t,"close",this.onclose.bind(this)),tb(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(t){this.onclose("parse error",t)}}ondecoded(t){C(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,e){let s=this.nsps[t];return s?this._autoConnect&&!s.active&&s.connect():(s=new tk(this,t,e),this.nsps[t]=s),s}_destroy(t){for(let t of Object.keys(this.nsps))if(this.nsps[t].active)return;this._close()}_packet(t){let e=this.encoder.encode(t);for(let s=0;s<e.length;s++)this.engine.write(e[s],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(t,e){var s;this.cleanup(),null==(s=this.engine)||s.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,e),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;let t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{let e=this.backoff.duration();this._reconnecting=!0;let s=this.setTimeoutFn(()=>{!t.skipReconnect&&(this.emitReserved("reconnect_attempt",t.backoff.attempts),t.skipReconnect||t.open(e=>{e?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",e)):t.onreconnect()}))},e);this.opts.autoUnref&&s.unref(),this.subs.push(()=>{this.clearTimeoutFn(s)})}}onreconnect(){let t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}let tA={};function tO(t,e){let s;"object"==typeof t&&(e=t,t=void 0);let r=function(t,e="",s){let r=t;s=s||"undefined"!=typeof location&&location,null==t&&(t=s.protocol+"//"+s.host),"string"==typeof t&&("/"===t.charAt(0)&&(t="/"===t.charAt(1)?s.protocol+t:s.host+t),/^(https?|wss?):\/\//.test(t)||(t=void 0!==s?s.protocol+"//"+t:"https://"+t),r=te(t)),!r.port&&(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";let i=-1!==r.host.indexOf(":")?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+i+":"+r.port+e,r.href=r.protocol+"://"+i+(s&&s.port===r.port?"":":"+r.port),r}(t,(e=e||{}).path||"/socket.io"),i=r.source,n=r.id,o=r.path,a=tA[n]&&o in tA[n].nsps;return e.forceNew||e["force new connection"]||!1===e.multiplex||a?s=new tE(i,e):(tA[n]||(tA[n]=new tE(i,e)),s=tA[n]),r.query&&!e.query&&(e.query=r.queryKey),s.socket(r.path,e)}Object.assign(tO,{Manager:tE,Socket:tk,io:tO,connect:tO})},5453:(t,e,s)=>{s.d(e,{v:()=>h});var r=s(2115);let i=t=>{let e,s=new Set,r=(t,r)=>{let i="function"==typeof t?t(e):t;if(!Object.is(i,e)){let t=e;e=(null!=r?r:"object"!=typeof i||null===i)?i:Object.assign({},e,i),s.forEach(s=>s(e,t))}},i=()=>e,n={setState:r,getState:i,getInitialState:()=>o,subscribe:t=>(s.add(t),()=>s.delete(t))},o=e=t(r,i,n);return n},n=t=>t?i(t):i,o=t=>t,a=t=>{let e=n(t),s=t=>(function(t,e=o){let s=r.useSyncExternalStore(t.subscribe,()=>e(t.getState()),()=>e(t.getInitialState()));return r.useDebugValue(s),s})(e,t);return Object.assign(s,e),s},h=t=>t?a(t):a},6786:(t,e,s)=>{function r(t,e){let s;try{s=t()}catch(t){return}return{getItem:t=>{var r;let i=t=>null===t?null:JSON.parse(t,null==e?void 0:e.reviver),n=null!=(r=s.getItem(t))?r:null;return n instanceof Promise?n.then(i):i(n)},setItem:(t,r)=>s.setItem(t,JSON.stringify(r,null==e?void 0:e.replacer)),removeItem:t=>s.removeItem(t)}}s.d(e,{KU:()=>r,Zr:()=>n});let i=t=>e=>{try{let s=t(e);if(s instanceof Promise)return s;return{then:t=>i(t)(s),catch(t){return this}}}catch(t){return{then(t){return this},catch:e=>i(e)(t)}}},n=(t,e)=>(s,n,o)=>{let a,h={storage:r(()=>localStorage),partialize:t=>t,version:0,merge:(t,e)=>({...e,...t}),...e},c=!1,l=new Set,u=new Set,p=h.storage;if(!p)return t((...t)=>{console.warn(`[zustand persist middleware] Unable to update item '${h.name}', the given storage is currently unavailable.`),s(...t)},n,o);let d=()=>{let t=h.partialize({...n()});return p.setItem(h.name,{state:t,version:h.version})},f=o.setState;o.setState=(t,e)=>{f(t,e),d()};let y=t((...t)=>{s(...t),d()},n,o);o.getInitialState=()=>y;let g=()=>{var t,e;if(!p)return;c=!1,l.forEach(t=>{var e;return t(null!=(e=n())?e:y)});let r=(null==(e=h.onRehydrateStorage)?void 0:e.call(h,null!=(t=n())?t:y))||void 0;return i(p.getItem.bind(p))(h.name).then(t=>{if(t)if("number"!=typeof t.version||t.version===h.version)return[!1,t.state];else{if(h.migrate){let e=h.migrate(t.state,t.version);return e instanceof Promise?e.then(t=>[!0,t]):[!0,e]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(t=>{var e;let[r,i]=t;if(s(a=h.merge(i,null!=(e=n())?e:y),!0),r)return d()}).then(()=>{null==r||r(a,void 0),a=n(),c=!0,u.forEach(t=>t(a))}).catch(t=>{null==r||r(void 0,t)})};return o.persist={setOptions:t=>{h={...h,...t},t.storage&&(p=t.storage)},clearStorage:()=>{null==p||p.removeItem(h.name)},getOptions:()=>h,rehydrate:()=>g(),hasHydrated:()=>c,onHydrate:t=>(l.add(t),()=>{l.delete(t)}),onFinishHydration:t=>(u.add(t),()=>{u.delete(t)})},h.skipHydration||g(),a||y}}}]);