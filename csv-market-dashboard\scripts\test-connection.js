require('dotenv').config();
const { Client } = require('pg');

async function testConnection() {
  console.log('🔌 Testing PostgreSQL connection with pg client...');
  
  const connectionString = process.env.DATABASE_URL;
  console.log('🔗 Using connection string:', connectionString ? 'Found' : 'Not found');
  
  if (!connectionString) {
    console.error('❌ DATABASE_URL not found in environment');
    return;
  }
  
  const client = new Client({
    connectionString: connectionString,
    ssl: {
      rejectUnauthorized: false
    }
  });
  
  try {
    await client.connect();
    console.log('✅ Successfully connected to PostgreSQL!');
    
    const result = await client.query('SELECT version()');
    console.log('📊 Database version:', result.rows[0].version);
    
    console.log('🎉 Connection test successful!');
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    console.error('Code:', error.code);
    
    if (error.message.includes('password authentication failed')) {
      console.log('\n💡 Password authentication failed - check your password');
    } else if (error.message.includes('Tenant or user not found')) {
      console.log('\n💡 User not found - check your username and database name');
    }
    
  } finally {
    await client.end();
  }
}

testConnection();
