/**
 * Database Instrument Service
 * Provides instrument data from PostgreSQL database with CSV fallback
 */

import { PrismaClient } from '../../generated/prisma';
import * as fs from 'fs';
import * as path from 'path';
import * as csv from 'csv-parser';

export interface InstrumentData {
  securityId: string;
  symbol: string;
  exchange: string;
  exchangeCode: number;
  segment: string;
  instrumentType: string;
  strikePrice?: number;
  expiryDate?: string;
  optionType?: string;
  lotSize?: number;
  tickSize?: number;
}

export interface InstrumentFilter {
  symbol?: string;
  exchange?: string;
  instrumentType?: string;
  expiryDate?: string;
  strikePrice?: number;
  optionType?: string;
}

/**
 * Database Instrument Service
 * Handles instrument data retrieval with database and CSV fallback
 */
export class DatabaseInstrumentService {
  private prisma: PrismaClient | null = null;
  private csvCache: Map<string, InstrumentData> = new Map();
  private csvLoaded = false;
  private dbConnected = false;

  constructor() {
    this.initializeDatabase();
  }

  /**
   * Initialize database connection
   */
  private async initializeDatabase() {
    try {
      this.prisma = new PrismaClient();
      await this.prisma.$connect();
      this.dbConnected = true;
      console.log('✅ Database connected for instrument service');
    } catch (error) {
      console.warn('⚠️ Database connection failed, using CSV fallback:', error);
      this.dbConnected = false;
      await this.loadCSVData();
    }
  }

  /**
   * Load CSV data as fallback
   */
  private async loadCSVData(): Promise<void> {
    if (this.csvLoaded) return;

    const csvFilePath = path.join(process.cwd(), 'instruments.csv');
    
    if (!fs.existsSync(csvFilePath)) {
      console.error('❌ CSV file not found:', csvFilePath);
      return;
    }

    console.log('📁 Loading instruments from CSV...');

    return new Promise((resolve, reject) => {
      let count = 0;
      
      fs.createReadStream(csvFilePath)
        .pipe(csv())
        .on('data', (row: any) => {
          try {
            const instrument = this.parseCSVRow(row);
            if (instrument) {
              this.csvCache.set(instrument.securityId, instrument);
              count++;
            }
          } catch (error) {
            // Skip invalid rows
          }
        })
        .on('end', () => {
          this.csvLoaded = true;
          console.log(`✅ Loaded ${count} instruments from CSV`);
          resolve();
        })
        .on('error', (error) => {
          console.error('❌ CSV loading error:', error);
          reject(error);
        });
    });
  }

  /**
   * Parse CSV row to instrument data
   * Updated for actual CSV format: EXCH_ID,SEGMENT,SECURITY_ID,ISIN,INSTRUMENT,UNDERLYING_SECURITY_ID,UNDERLYING_SYMBOL,SYMBOL_NAME,DISPLAY_NAME,INSTRUMENT_TYPE,SERIES,LOT_SIZE,SM_EXPIRY_DATE,STRIKE_PRICE,OPTION_TYPE,TICK_SIZE...
   */
  private parseCSVRow(row: any): InstrumentData | null {
    try {
      const exchangeCodeMap: { [key: string]: number } = {
        'NSE': 1, 'BSE': 2, 'MCX': 3, 'NCDEX': 4, 'CDS': 5, 'IDX_I': 6
      };

      // 🚀 FIXED: Use actual CSV column names
      const exchange = row.EXCH_ID || 'NSE';
      const exchangeCode = exchangeCodeMap[exchange] || 1;

      const strikePrice = row.STRIKE_PRICE ? parseFloat(row.STRIKE_PRICE) : undefined;
      const lotSize = row.LOT_SIZE ? parseFloat(row.LOT_SIZE) : undefined;
      const tickSize = row.TICK_SIZE ? parseFloat(row.TICK_SIZE) : undefined;

      // Format expiry date from SM_EXPIRY_DATE (YYYY-MM-DD format)
      let expiryDate: string | undefined;
      if (row.SM_EXPIRY_DATE && row.SM_EXPIRY_DATE !== '1979-12-31' && row.SM_EXPIRY_DATE !== '') {
        expiryDate = row.SM_EXPIRY_DATE;
      }

      // Get symbol from SYMBOL_NAME or UNDERLYING_SYMBOL
      const symbol = row.SYMBOL_NAME || row.UNDERLYING_SYMBOL || row.DISPLAY_NAME || '';

      return {
        securityId: row.SECURITY_ID,
        symbol: symbol,
        exchange,
        exchangeCode,
        segment: row.SEGMENT || 'E',
        instrumentType: row.INSTRUMENT_TYPE || row.INSTRUMENT || 'EQ',
        strikePrice: strikePrice && strikePrice > 0 ? strikePrice : undefined,
        expiryDate,
        optionType: row.OPTION_TYPE && row.OPTION_TYPE !== 'XX' && row.OPTION_TYPE !== '' ? row.OPTION_TYPE : undefined,
        lotSize: lotSize && lotSize > 0 ? lotSize : undefined,
        tickSize: tickSize && tickSize > 0 ? tickSize : undefined
      };
    } catch (error) {
      console.error('Error parsing CSV row:', error, row);
      return null;
    }
  }

  /**
   * Get instrument by security ID
   */
  async getInstrument(securityId: string): Promise<InstrumentData | null> {
    if (this.dbConnected && this.prisma) {
      try {
        const instrument = await this.prisma.instrument.findUnique({
          where: { securityId }
        });
        
        if (instrument) {
          return {
            securityId: instrument.securityId,
            symbol: instrument.symbol,
            exchange: instrument.exchange,
            exchangeCode: instrument.exchangeCode,
            segment: instrument.segment,
            instrumentType: instrument.instrumentType,
            strikePrice: instrument.strikePrice || undefined,
            expiryDate: instrument.expiryDate || undefined,
            optionType: instrument.optionType || undefined,
            lotSize: instrument.lotSize || undefined,
            tickSize: instrument.tickSize || undefined
          };
        }
      } catch (error) {
        console.warn('⚠️ Database query failed, using CSV fallback:', error);
      }
    }

    // Fallback to CSV
    if (!this.csvLoaded) {
      await this.loadCSVData();
    }
    
    return this.csvCache.get(securityId) || null;
  }

  /**
   * Get instruments by filter
   */
  async getInstruments(filter: InstrumentFilter = {}, limit = 1000): Promise<InstrumentData[]> {
    if (this.dbConnected && this.prisma) {
      try {
        const where: any = {};
        
        if (filter.symbol) where.symbol = { contains: filter.symbol };
        if (filter.exchange) where.exchange = filter.exchange;
        if (filter.instrumentType) where.instrumentType = filter.instrumentType;
        if (filter.expiryDate) where.expiryDate = filter.expiryDate;
        if (filter.strikePrice) where.strikePrice = filter.strikePrice;
        if (filter.optionType) where.optionType = filter.optionType;

        const instruments = await this.prisma.instrument.findMany({
          where,
          take: limit,
          orderBy: { symbol: 'asc' }
        });

        return instruments.map(instrument => ({
          securityId: instrument.securityId,
          symbol: instrument.symbol,
          exchange: instrument.exchange,
          exchangeCode: instrument.exchangeCode,
          segment: instrument.segment,
          instrumentType: instrument.instrumentType,
          strikePrice: instrument.strikePrice || undefined,
          expiryDate: instrument.expiryDate || undefined,
          optionType: instrument.optionType || undefined,
          lotSize: instrument.lotSize || undefined,
          tickSize: instrument.tickSize || undefined
        }));
      } catch (error) {
        console.warn('⚠️ Database query failed, using CSV fallback:', error);
      }
    }

    // Fallback to CSV
    if (!this.csvLoaded) {
      await this.loadCSVData();
    }

    const results: InstrumentData[] = [];
    let count = 0;

    for (const instrument of this.csvCache.values()) {
      if (count >= limit) break;

      let matches = true;

      // 🚀 FIXED: Add null checks for instrument properties
      if (filter.symbol && (!instrument.symbol || !instrument.symbol.includes(filter.symbol))) matches = false;
      if (filter.exchange && instrument.exchange !== filter.exchange) matches = false;
      if (filter.instrumentType && instrument.instrumentType !== filter.instrumentType) matches = false;
      if (filter.expiryDate && instrument.expiryDate !== filter.expiryDate) matches = false;
      if (filter.strikePrice && instrument.strikePrice !== filter.strikePrice) matches = false;
      if (filter.optionType && instrument.optionType !== filter.optionType) matches = false;

      if (matches) {
        results.push(instrument);
        count++;
      }
    }

    return results.sort((a, b) => a.symbol.localeCompare(b.symbol));
  }

  /**
   * Get NIFTY options for option chain using UNDERLYING_SYMBOL from database
   */
  async getNiftyOptions(expiryDate?: string): Promise<InstrumentData[]> {
    console.log('🔍 [DatabaseInstrumentService] Searching for NIFTY options by UNDERLYING_SYMBOL...');

    try {
      if (!this.prisma || !this.dbConnected) {
        throw new Error('Database not connected');
      }

      // Use raw query to search by UNDERLYING_SYMBOL in actual database structure
      let query = `
        SELECT * FROM "Instruments"
        WHERE "UNDERLYING_SYMBOL" = 'NIFTY'
        AND "INSTRUMENT_TYPE" = 'OPTIDX'
      `;

      if (expiryDate) {
        query += ` AND "SM_EXPIRY_DATE" = '${expiryDate}'`;
      }

      query += ` ORDER BY "SM_EXPIRY_DATE" ASC, CAST("STRIKE_PRICE" AS NUMERIC) ASC, "OPTION_TYPE" ASC`;

      const results = await this.prisma.$queryRawUnsafe(query);

      if (Array.isArray(results) && results.length > 0) {
        console.log(`✅ Found ${results.length} NIFTY options from database`);

        // Convert database results to InstrumentData format
        return results.map((row: any) => ({
          securityId: row.SECURITY_ID?.toString() || '',
          symbol: row.SYMBOL_NAME || '',
          exchange: row.EXCH_ID || 'NSE',
          exchangeCode: 2, // NSE_FNO
          segment: row.SEGMENT || 'D',
          instrumentType: row.INSTRUMENT_TYPE || 'OPTIDX',
          strikePrice: row.STRIKE_PRICE ? parseFloat(row.STRIKE_PRICE) : undefined,
          expiryDate: row.SM_EXPIRY_DATE || undefined,
          optionType: row.OPTION_TYPE || undefined,
          lotSize: row.LOT_SIZE ? parseInt(row.LOT_SIZE) : undefined,
          tickSize: row.TICK_SIZE ? parseFloat(row.TICK_SIZE) : undefined,
          underlyingSymbol: row.UNDERLYING_SYMBOL || undefined,
          displayName: row.DISPLAY_NAME || row.SYMBOL_NAME
        }));
      }

      console.log('⚠️ No NIFTY options found in database');
      return [];

    } catch (error) {
      console.error('❌ Error getting NIFTY options:', error);
      throw error;
    }
  }

  /**
   * Get NIFTY expiry dates using UNDERLYING_SYMBOL
   */
  async getNiftyExpiryDates(): Promise<string[]> {
    console.log('📅 [DatabaseInstrumentService] Getting NIFTY expiry dates from database...');

    try {
      if (!this.prisma || !this.dbConnected) {
        throw new Error('Database not connected');
      }

      // Use raw query to get expiry dates from UNDERLYING_SYMBOL = 'NIFTY'
      const results = await this.prisma.$queryRawUnsafe(`
        SELECT DISTINCT "SM_EXPIRY_DATE"
        FROM "Instruments"
        WHERE "UNDERLYING_SYMBOL" = 'NIFTY'
        AND "INSTRUMENT_TYPE" = 'OPTIDX'
        AND "SM_EXPIRY_DATE" IS NOT NULL
        AND "SM_EXPIRY_DATE" != ''
        ORDER BY "SM_EXPIRY_DATE" ASC
      `);

      if (Array.isArray(results) && results.length > 0) {
        const expiries = results.map((row: any) => row.SM_EXPIRY_DATE).filter(Boolean);
        console.log(`📅 Found ${expiries.length} NIFTY expiry dates from database`);
        return expiries;
      }

      console.log('⚠️ No NIFTY expiry dates found in database');
      return [];

    } catch (error) {
      console.error('❌ Error getting NIFTY expiry dates:', error);
      throw error;
    }
  }



  /**
   * Get connection status
   */
  getStatus() {
    return {
      dbConnected: this.dbConnected,
      csvLoaded: this.csvLoaded,
      csvCacheSize: this.csvCache.size
    };
  }

  /**
   * Disconnect
   */
  async disconnect() {
    if (this.prisma) {
      await this.prisma.$disconnect();
      this.dbConnected = false;
    }
  }
}

// Export singleton instance
export const databaseInstrumentService = new DatabaseInstrumentService();
export default databaseInstrumentService;
