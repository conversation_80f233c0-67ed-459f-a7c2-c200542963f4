{"compilerOptions": {"lib": ["dom", "dom.iterable", "es2015"], "target": "es2015", "downlevelIteration": true, "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/contexts/*": ["./src/contexts/*"], "@/lib/*": ["./src/lib/*"], "@/types/*": ["./src/types/*"], "@/services/*": ["./src/services/*"], "@/utils/*": ["./src/utils/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "dist", "src/server/**/*", "scripts/**/*"]}