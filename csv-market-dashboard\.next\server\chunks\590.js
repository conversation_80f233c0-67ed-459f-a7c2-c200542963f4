exports.id=590,exports.ids=[590],exports.modules={1055:(e,t,r)=>{Promise.resolve().then(r.bind(r,5041))},1719:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},1735:(e,t,r)=>{Promise.resolve().then(r.bind(r,4431))},2457:(e,t,r)=>{"use strict";r.d(t,{aq:()=>n,ko:()=>i});var s=r(6420);class a{static{this.instance=null}isBrowser(){return!1}static getInstance(){return a.instance||(a.instance=new a),a.instance}initializeClientSide(){this.isBrowser()&&(console.log("\uD83D\uDE80 DataCache: Initializing client-side cache"),this.cleanupExpired())}constructor(){this.version="1.0.0",this.compressionSupported=!1,this.checkCompressionSupport(),this.startCleanupInterval()}checkCompressionSupport(){try{this.compressionSupported="undefined"!=typeof CompressionStream}catch{this.compressionSupported=!1}}async set(e,t,r={}){try{if(!this.isBrowser())return console.log(`⚠️ DataCache: Skipping cache on server side for ${e}`),!1;let{ttl:a=s.WS.DEFAULT_TTL,useCompression:o=!1,storage:n="localStorage"}=r,i={data:t,timestamp:Date.now(),ttl:a,version:this.version},l=JSON.stringify(i);o&&this.compressionSupported&&(l=await this.compress(l));let c="localStorage"===n?localStorage:sessionStorage,h=this.getFullKey(e);return c.setItem(h,l),console.log(`💾 DataCache: Cached ${e} (${l.length} bytes)`),!0}catch(t){return console.error(`❌ DataCache: Failed to cache ${e}:`,t),!1}}async get(e,t={}){try{if(!this.isBrowser())return null;let{storage:r="localStorage"}=t,s="localStorage"===r?localStorage:sessionStorage,a=this.getFullKey(e),o=s.getItem(a);if(!o)return null;let n=o;this.compressionSupported&&this.isCompressed(o)&&(n=await this.decompress(o));let i=JSON.parse(n);if(i.version!==this.version)return console.warn(`⚠️ DataCache: Version mismatch for ${e}, removing`),this.remove(e,t),null;if(Date.now()-i.timestamp>i.ttl)return console.log(`⏰ DataCache: ${e} expired, removing`),this.remove(e,t),null;return console.log(`📖 DataCache: Retrieved ${e} from cache`),i.data}catch(r){return console.error(`❌ DataCache: Failed to retrieve ${e}:`,r),this.remove(e,t),null}}remove(e,t={}){try{if(!this.isBrowser())return;let{storage:r="localStorage"}=t,s="localStorage"===r?localStorage:sessionStorage,a=this.getFullKey(e);s.removeItem(a),console.log(`🗑️ DataCache: Removed ${e}`)}catch(t){console.error(`❌ DataCache: Failed to remove ${e}:`,t)}}clear(e="localStorage"){try{if(!this.isBrowser())return;let t="localStorage"===e?localStorage:sessionStorage,r=[];for(let e=0;e<t.length;e++){let s=t.key(e);s&&s.startsWith("csv_market_dashboard_cache_")&&r.push(s)}r.forEach(e=>t.removeItem(e)),console.log(`🧹 DataCache: Cleared ${r.length} entries from ${e}`)}catch(t){console.error(`❌ DataCache: Failed to clear ${e}:`,t)}}getStats(e="localStorage"){if(!this.isBrowser())return{totalEntries:0,totalSize:0,oldestEntry:null,newestEntry:null};let t="localStorage"===e?localStorage:sessionStorage,r=0,s=0,a=1/0,o=0;try{for(let e=0;e<t.length;e++){let n=t.key(e);if(n&&n.startsWith("csv_market_dashboard_cache_")){let e=t.getItem(n);if(e){r++,s+=e.length;try{let t=JSON.parse(e);t.timestamp&&(a=Math.min(a,t.timestamp),o=Math.max(o,t.timestamp))}catch{}}}}}catch(e){console.error("❌ DataCache: Failed to get stats:",e)}return{totalEntries:r,totalSize:s,oldestEntry:a===1/0?null:new Date(a),newestEntry:0===o?null:new Date(o)}}startCleanupInterval(){this.isBrowser()&&setInterval(()=>{this.cleanupExpired()},s.WS.EXPIRY_CHECK_INTERVAL)}cleanupExpired(){try{if(!this.isBrowser())return;["localStorage","sessionStorage"].forEach(e=>{let t="localStorage"===e?localStorage:sessionStorage,r=[];for(let e=0;e<t.length;e++){let s=t.key(e);if(s&&s.startsWith("csv_market_dashboard_cache_"))try{let e=t.getItem(s);if(e){let t=JSON.parse(e);Date.now()-t.timestamp>t.ttl&&r.push(s)}}catch{r.push(s)}}r.forEach(e=>t.removeItem(e)),r.length>0&&console.log(`🧹 DataCache: Cleaned up ${r.length} expired entries from ${e}`)})}catch(e){console.error("❌ DataCache: Cleanup failed:",e)}}getFullKey(e){return`csv_market_dashboard_cache_${e}`}isCompressed(e){return e.startsWith("H4sI")||e.startsWith("eJy")}async compress(e){return e}async decompress(e){return e}}let o=a.getInstance(),n=s.WS.KEYS,i={cacheMarketData:async(e,t)=>o.set(e,t,{ttl:s.WS.MARKET_DATA_TTL,storage:"localStorage"}),cacheStaticData:async(e,t)=>o.set(e,t,{ttl:s.WS.STATIC_DATA_TTL,storage:"localStorage"}),getCachedMarketData:async e=>o.get(e,{storage:"localStorage"}),getCachedStaticData:async e=>o.get(e,{storage:"localStorage"})}},4263:()=>{},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\layout.tsx","default")},5041:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(687),a=r(4369),o=r.n(a),n=r(7590),i=r(2918);function l({error:e,resetErrorBoundary:t}){return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsxs)("div",{className:"max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg border border-red-200",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3",children:(0,s.jsx)("span",{className:"text-red-600 text-lg",children:"⚠"})}),(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Application Error"})]}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"An unexpected error occurred. Please try refreshing the page or contact support if the problem persists."}),(0,s.jsxs)("details",{className:"mb-4",children:[(0,s.jsx)("summary",{className:"cursor-pointer text-sm text-gray-500 hover:text-gray-700",children:"Technical Details"}),(0,s.jsx)("pre",{className:"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto max-h-32",children:e.message})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsx)("button",{onClick:t,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"Try Again"}),(0,s.jsx)("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors",children:"Refresh Page"})]})]})})}function c({children:e}){return(0,s.jsxs)("html",{lang:"en",children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("title",{children:"CSV Market Dashboard"}),(0,s.jsx)("meta",{name:"description",content:"Real-time market data dashboard with enhanced WebSocket management and data caching"}),(0,s.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,s.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,s.jsx)("body",{className:o().className,children:(0,s.jsx)(i.tH,{FallbackComponent:l,onError:(e,t)=>{console.error("Application Error:",e,t)},onReset:()=>{localStorage.removeItem("enhanced-market-data"),localStorage.removeItem("enhanced-market-timestamp"),sessionStorage.clear()},children:(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20",children:[e,(0,s.jsx)(n.l$,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{style:{background:"#10B981"}},error:{style:{background:"#EF4444"}}}})]})})})]})}r(4263)},5631:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},6420:(e,t,r)=>{"use strict";r.d(t,{WS:()=>o,i3:()=>s,ld:()=>a});let s={BASE_URL:process.env.NEXT_PUBLIC_API_URL||"http://localhost:8080",TIMEOUT:3e4,RETRY_ATTEMPTS:3,RETRY_DELAY:1e3},a={RECONNECT_INTERVAL:3e3,MAX_RECONNECT_ATTEMPTS:15,PING_INTERVAL:25e3,PONG_TIMEOUT:1e4,CONNECTION_TIMEOUT:2e4,HEARTBEAT_INTERVAL:3e4,MAX_LISTENERS_PER_EVENT:10,CLEANUP_INTERVAL:6e4},o={DEFAULT_TTL:3e5,MARKET_DATA_TTL:6e5,STATIC_DATA_TTL:18e5,EXPIRY_CHECK_INTERVAL:6e4,MAX_CACHE_SIZE:0x3200000,KEYS:{INSTRUMENTS:"instruments",MARKET_DATA:"market_data",OPTION_CHAIN:"option_chain",EXPIRY_DATES:"expiry_dates",NIFTY_SPOT:"nifty_spot",USER_SETTINGS:"user_settings",USER_PREFERENCES:"user_preferences"}};parseInt(process.env.PORT||"8080"),parseInt(process.env.NEXT_PORT||"3000"),process.env.LOG_LEVEL,process.env.ENABLE_METRICS}};