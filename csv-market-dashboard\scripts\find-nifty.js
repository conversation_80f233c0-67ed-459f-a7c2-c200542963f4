require('dotenv').config({ path: '.env.database' });
const { Client } = require('pg');

async function findNifty() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: { rejectUnauthorized: false }
  });
  
  try {
    await client.connect();
    console.log('✅ Connected to PostgreSQL');
    
    // Search for NIFTY in all possible fields
    console.log('\n🔍 Searching for NIFTY in all fields...');
    
    const niftySearch = await client.query(`
      SELECT "INSTRUMENT_TYPE", COUNT(*) as count 
      FROM "Instruments" 
      WHERE "UNDERLYING_SYMBOL" LIKE '%NIFTY%' 
         OR "SYMBOL_NAME" LIKE '%NIFTY%'
         OR "DISPLAY_NAME" LIKE '%NIFTY%'
         OR "INSTRUMENT" LIKE '%NIFTY%'
      GROUP BY "INSTRUMENT_TYPE"
      ORDER BY count DESC
    `);
    
    console.log('📊 NIFTY instruments by type:');
    niftySearch.rows.forEach(type => {
      console.log(`   ${type.INSTRUMENT_TYPE}: ${type.count}`);
    });
    
    // Get sample NIFTY instruments
    const niftySample = await client.query(`
      SELECT "SECURITY_ID", "INSTRUMENT_TYPE", "UNDERLYING_SYMBOL", "SYMBOL_NAME", "DISPLAY_NAME", "STRIKE_PRICE", "OPTION_TYPE", "SM_EXPIRY_DATE"
      FROM "Instruments" 
      WHERE "UNDERLYING_SYMBOL" LIKE '%NIFTY%' 
         OR "SYMBOL_NAME" LIKE '%NIFTY%'
         OR "DISPLAY_NAME" LIKE '%NIFTY%'
      ORDER BY "INSTRUMENT_TYPE", "SM_EXPIRY_DATE", "STRIKE_PRICE"
      LIMIT 20
    `);
    
    console.log('\n📋 Sample NIFTY instruments:');
    niftySample.rows.forEach(inst => {
      console.log(`   ${inst.INSTRUMENT_TYPE} | ${inst.UNDERLYING_SYMBOL} | ${inst.SYMBOL_NAME} | ${inst.STRIKE_PRICE} ${inst.OPTION_TYPE} (${inst.SM_EXPIRY_DATE}) - ID: ${inst.SECURITY_ID}`);
    });
    
    // Check if there are any options with NIFTY in the name
    const niftyOptions = await client.query(`
      SELECT COUNT(*) as count 
      FROM "Instruments" 
      WHERE ("UNDERLYING_SYMBOL" LIKE '%NIFTY%' 
         OR "SYMBOL_NAME" LIKE '%NIFTY%'
         OR "DISPLAY_NAME" LIKE '%NIFTY%')
      AND ("OPTION_TYPE" = 'CE' OR "OPTION_TYPE" = 'PE')
    `);
    
    console.log(`\n🎯 Total NIFTY options (CE/PE): ${niftyOptions.rows[0].count}`);
    
    // Check NSE instruments specifically
    const nseNifty = await client.query(`
      SELECT "INSTRUMENT_TYPE", COUNT(*) as count 
      FROM "Instruments" 
      WHERE "EXCH_ID" = 'NSE'
      AND ("UNDERLYING_SYMBOL" LIKE '%NIFTY%' 
         OR "SYMBOL_NAME" LIKE '%NIFTY%'
         OR "DISPLAY_NAME" LIKE '%NIFTY%')
      GROUP BY "INSTRUMENT_TYPE"
      ORDER BY count DESC
    `);
    
    console.log('\n📊 NSE NIFTY instruments by type:');
    nseNifty.rows.forEach(type => {
      console.log(`   ${type.INSTRUMENT_TYPE}: ${type.count}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await client.end();
  }
}

findNifty();
