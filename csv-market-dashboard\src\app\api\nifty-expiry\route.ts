import { NextRequest, NextResponse } from 'next/server';
import { niftyExpiryService } from '../../../services/NiftyExpiryService';

/**
 * API endpoint to get NIFTY expiry dates with database caching
 * GET /api/nifty-expiry - Get cached expiry dates
 * POST /api/nifty-expiry - Force refresh from Dhan API
 */
export async function GET(request: NextRequest) {
  try {
    console.log('📅 Getting NIFTY expiry dates (cached)...');

    const expiryDates = await niftyExpiryService.getNiftyExpiryDates();
    const status = niftyExpiryService.getStatus();

    console.log(`✅ Found ${expiryDates.length} NIFTY expiry dates`);

    return NextResponse.json({
      success: true,
      data: {
        underlying: 'NIFTY',
        expiries: expiryDates,
        count: expiryDates.length,
        source: 'cached',
        dbConnected: status.dbConnected
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error getting NIFTY expiry dates:', error);

    return NextResponse.json({
      success: false,
      error: 'Failed to get NIFTY expiry dates',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Force refresh NIFTY expiry dates from Dhan API
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Force refreshing NIFTY expiry dates from Dhan API...');

    const expiryDates = await niftyExpiryService.refreshExpiries();

    console.log(`✅ Refreshed ${expiryDates.length} NIFTY expiry dates`);

    return NextResponse.json({
      success: true,
      data: {
        underlying: 'NIFTY',
        expiries: expiryDates,
        count: expiryDates.length,
        source: 'fresh_api'
      },
      message: 'NIFTY expiry dates refreshed successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error refreshing NIFTY expiry dates:', error);

    return NextResponse.json({
      success: false,
      error: 'Failed to refresh NIFTY expiry dates',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Handle OPTIONS for CORS
 */
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
