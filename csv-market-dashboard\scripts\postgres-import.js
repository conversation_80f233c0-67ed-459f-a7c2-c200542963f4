const { PrismaClient } = require('../generated/prisma');
const fs = require('fs');
const csv = require('csv-parser');

const prisma = new PrismaClient();

console.log('🚀 Starting PostgreSQL CSV import...');

async function importCSV() {
  const results = [];
  
  return new Promise((resolve, reject) => {
    fs.createReadStream('instruments.csv')
      .pipe(csv())
      .on('data', (data) => {
        // Map CSV columns directly to our database fields
        const instrument = {
          securityId: data.SECURITY_ID,
          symbol: data.UNDERLYING_SYMBOL || data.SYMBOL_NAME,
          exchange: data.EXCH_ID,
          exchangeCode: data.EXCH_ID === 'NSE' ? 1 : data.EXCH_ID === 'BSE' ? 2 : 3,
          segment: data.SEGMENT,
          instrumentType: data.INSTRUMENT_TYPE,
          strikePrice: data.STRIKE_PRICE && data.STRIKE_PRICE !== '-0.01000' ? parseFloat(data.STRIKE_PRICE) : null,
          expiryDate: data.SM_EXPIRY_DATE && data.SM_EXPIRY_DATE !== '1979-12-31' ? data.SM_EXPIRY_DATE : null,
          optionType: data.OPTION_TYPE && data.OPTION_TYPE !== 'XX' ? data.OPTION_TYPE : null,
          lotSize: data.LOT_SIZE ? parseInt(parseFloat(data.LOT_SIZE)) : null,
          tickSize: data.TICK_SIZE ? parseFloat(data.TICK_SIZE) : null,
        };
        
        // Only add if we have required fields
        if (instrument.securityId && instrument.symbol && instrument.exchange) {
          results.push(instrument);
        }
      })
      .on('end', async () => {
        console.log(`📊 Parsed ${results.length} instruments`);
        
        try {
          // Test connection first
          console.log('🔌 Testing PostgreSQL connection...');
          await prisma.$connect();
          console.log('✅ Connected to PostgreSQL database');
          
          // Clear existing data
          console.log('🗑️ Clearing existing data...');
          await prisma.instrument.deleteMany();
          
          // Insert in batches
          const batchSize = 1000;
          let inserted = 0;
          
          for (let i = 0; i < results.length; i += batchSize) {
            const batch = results.slice(i, i + batchSize);
            
            await prisma.instrument.createMany({
              data: batch,
              skipDuplicates: true,
            });
            
            inserted += batch.length;
            console.log(`✅ Inserted batch: ${inserted}/${results.length}`);
          }
          
          // Show final stats
          const total = await prisma.instrument.count();
          const niftyOptions = await prisma.instrument.count({
            where: {
              symbol: 'NIFTY',
              instrumentType: 'OP'
            }
          });
          
          console.log(`\n🎉 PostgreSQL import completed!`);
          console.log(`📊 Total instruments: ${total}`);
          console.log(`🎯 NIFTY options: ${niftyOptions}`);
          
          resolve();
        } catch (error) {
          console.error('❌ PostgreSQL error:', error);
          reject(error);
        }
      })
      .on('error', (error) => {
        console.error('❌ CSV reading error:', error);
        reject(error);
      });
  });
}

// Run the import
importCSV()
  .then(() => {
    console.log('✅ All done!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Import failed:', error);
    process.exit(1);
  })
  .finally(() => {
    prisma.$disconnect();
  });
