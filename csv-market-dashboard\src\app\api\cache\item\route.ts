/**
 * API Route for Individual Cache Item Operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { cacheMarketData, getMarketDataEntry, getRedisClient } from '@/lib/redis-client';
import { getFallbackCache } from '@/lib/fallback-cache';
import { MarketData } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { key, data } = await request.json();
    
    if (!key || typeof key !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Key is required and must be a string' },
        { status: 400 }
      );
    }

    if (!data) {
      return NextResponse.json(
        { success: false, error: 'Data is required' },
        { status: 400 }
      );
    }

    // Try Redis first, then fallback cache
    let success = await cacheMarketData(key, data);
    if (!success) {
      const fallbackCache = getFallbackCache();
      success = fallbackCache.set(key, data);
    }
    
    return NextResponse.json({
      success: true,
      message: `Cached data for key: ${key}`,
      cached: success
    });
  } catch (error) {
    console.error('❌ API: Failed to cache item:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to cache item' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const securityId = searchParams.get('securityId');
    
    if (!securityId) {
      return NextResponse.json(
        { success: false, error: 'securityId is required as a query parameter' },
        { status: 400 }
      );
    }
    
    // Try Redis first
    let data = await getMarketDataEntry(securityId);
    
    // If Redis fails, try fallback cache
    if (!data) {
      const fallbackCache = getFallbackCache();
      data = fallbackCache.get(`market_data:${securityId}`);
    }
    
    if (!data) {
      return NextResponse.json(
        { success: false, error: 'No data found for securityId' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ success: true, data });
  } catch (error) {
    console.error('❌ API: Failed to get item:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get item' },
      { status: 500 }
    );
  }
}
