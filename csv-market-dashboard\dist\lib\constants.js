"use strict";
/**
 * Application Constants
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULTS = exports.ENV_CONFIG = exports.SECURITY_CONFIG = exports.CACHE_CONFIG = exports.LOGGING_CONFIG = exports.PERFORMANCE_CONFIG = exports.FEATURE_FLAGS = exports.STORAGE_KEYS = exports.SUCCESS_MESSAGES = exports.ERROR_MESSAGES = exports.VALIDATION = exports.CHART_CONFIG = exports.COLORS = exports.UI_CONFIG = exports.REQUEST_CODES = exports.PACKET_TYPES = exports.OPTION_TYPES = exports.EXCHANGE_SEGMENTS = exports.INSTRUMENT_TYPES = exports.MARKET_CONFIG = exports.DHAN_CONFIG = exports.WEBSOCKET_CONFIG = exports.API_CONFIG = void 0;
// API Configuration
exports.API_CONFIG = {
    BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080',
    TIMEOUT: 30000,
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000,
};
// WebSocket Configuration
exports.WEBSOCKET_CONFIG = {
    RECONNECT_INTERVAL: 3000,
    MAX_RECONNECT_ATTEMPTS: 15,
    PING_INTERVAL: 25000,
    PONG_TIMEOUT: 10000,
    CONNECTION_TIMEOUT: 20000,
    HEARTBEAT_INTERVAL: 30000,
    MAX_LISTENERS_PER_EVENT: 10,
    CLEANUP_INTERVAL: 60000,
};
// Dhan API Configuration
exports.DHAN_CONFIG = {
    BASE_URL: 'https://api.dhan.co',
    WEBSOCKET_URL: 'wss://api.dhan.co/v2/wsapi',
    MAX_INSTRUMENTS_PER_CONNECTION: 5000,
    MAX_INSTRUMENTS_PER_MESSAGE: 100,
    RATE_LIMIT: {
        REQUESTS_PER_SECOND: 10,
        REQUESTS_PER_MINUTE: 600,
    },
};
// Market Configuration
exports.MARKET_CONFIG = {
    TRADING_HOURS: {
        START: { hour: 9, minute: 15 }, // 9:15 AM IST
        END: { hour: 15, minute: 30 }, // 3:30 PM IST
    },
    TRADING_DAYS: [1, 2, 3, 4, 5], // Monday to Friday
    REFRESH_INTERVAL: 1000, // 1 second
    BATCH_SIZE: 100,
};
// Instrument Types
exports.INSTRUMENT_TYPES = {
    EQUITY: 'EQUITY',
    INDEX: 'INDEX',
    FUTIDX: 'FUTIDX',
    OPTIDX: 'OPTIDX',
    FUTSTK: 'FUTSTK',
    OPTSTK: 'OPTSTK',
    FUTCUR: 'FUTCUR',
    OPTCUR: 'OPTCUR',
    FUTCOM: 'FUTCOM',
    OPTFUT: 'OPTFUT',
};
// Exchange Segments
exports.EXCHANGE_SEGMENTS = {
    NSE_EQ: 'NSE_EQ',
    NSE_FNO: 'NSE_FNO',
    BSE_EQ: 'BSE_EQ',
    MCX_COMM: 'MCX_COMM',
    IDX_I: 'IDX_I',
};
// Option Types
exports.OPTION_TYPES = {
    CALL: 'CE',
    PUT: 'PE',
};
// Market Data Packet Types
exports.PACKET_TYPES = {
    TOUCHLINE: 1,
    QUOTE: 2,
    SNAP_QUOTE: 3,
    FULL_PACKET: 4,
    OI: 5,
};
// WebSocket Request Codes
exports.REQUEST_CODES = {
    SUBSCRIBE: 21,
    UNSUBSCRIBE: 22,
    SNAP_QUOTE: 23,
};
// UI Configuration
exports.UI_CONFIG = {
    DEBOUNCE_DELAY: 300,
    THROTTLE_DELAY: 100,
    ANIMATION_DURATION: 200,
    TOAST_DURATION: 3000,
    PAGINATION: {
        DEFAULT_PAGE_SIZE: 50,
        PAGE_SIZE_OPTIONS: [25, 50, 100, 200],
    },
};
// Color Schemes
exports.COLORS = {
    SUCCESS: '#10B981',
    ERROR: '#EF4444',
    WARNING: '#F59E0B',
    INFO: '#3B82F6',
    NEUTRAL: '#6B7280',
    BID: '#10B981', // Green for bid prices
    ASK: '#EF4444', // Red for ask prices
    SPOT: '#3B82F6', // Blue for spot prices
};
// Chart Configuration
exports.CHART_CONFIG = {
    DEFAULT_HEIGHT: 400,
    COLORS: {
        PRIMARY: '#3B82F6',
        SECONDARY: '#10B981',
        ACCENT: '#F59E0B',
        GRID: '#E5E7EB',
        TEXT: '#374151',
    },
    ANIMATION: {
        DURATION: 300,
        EASING: 'ease-in-out',
    },
};
// Data Validation
exports.VALIDATION = {
    MIN_STRIKE_PRICE: 1,
    MAX_STRIKE_PRICE: 100000,
    MIN_OPTION_PRICE: 0.01,
    MAX_OPTION_PRICE: 50000,
    MIN_VOLUME: 0,
    MAX_VOLUME: 10000000,
};
// Error Messages
exports.ERROR_MESSAGES = {
    NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
    API_ERROR: 'API request failed. Please try again later.',
    WEBSOCKET_ERROR: 'WebSocket connection failed. Attempting to reconnect...',
    DATA_PARSING_ERROR: 'Failed to parse market data. Please refresh the page.',
    SUBSCRIPTION_ERROR: 'Failed to subscribe to market data. Please try again.',
    INVALID_INSTRUMENT: 'Invalid instrument selected.',
    MARKET_CLOSED: 'Market is currently closed.',
    RATE_LIMIT_EXCEEDED: 'Too many requests. Please wait before trying again.',
};
// Success Messages
exports.SUCCESS_MESSAGES = {
    CONNECTION_ESTABLISHED: 'Successfully connected to market data feed.',
    SUBSCRIPTION_SUCCESS: 'Successfully subscribed to market data.',
    DATA_UPDATED: 'Market data updated successfully.',
    SETTINGS_SAVED: 'Settings saved successfully.',
};
// Local Storage Keys
exports.STORAGE_KEYS = {
    USER_PREFERENCES: 'csv_market_dashboard_preferences',
    SELECTED_INSTRUMENTS: 'csv_market_dashboard_selected_instruments',
    THEME: 'csv_market_dashboard_theme',
    LAYOUT: 'csv_market_dashboard_layout',
};
// Feature Flags
exports.FEATURE_FLAGS = {
    ENABLE_CHARTS: true,
    ENABLE_ALERTS: true,
    ENABLE_EXPORT: true,
    ENABLE_DARK_MODE: true,
    ENABLE_REAL_TIME_UPDATES: true,
    ENABLE_OPTION_GREEKS: false, // Disabled until proper calculation is implemented
};
// Performance Monitoring
exports.PERFORMANCE_CONFIG = {
    MEMORY_WARNING_THRESHOLD: 100 * 1024 * 1024, // 100MB
    CPU_WARNING_THRESHOLD: 80, // 80%
    NETWORK_TIMEOUT_WARNING: 5000, // 5 seconds
    MAX_CONCURRENT_REQUESTS: 10,
};
// Logging Configuration
exports.LOGGING_CONFIG = {
    LEVELS: {
        ERROR: 0,
        WARN: 1,
        INFO: 2,
        DEBUG: 3,
    },
    MAX_LOG_SIZE: 10 * 1024 * 1024, // 10MB
    MAX_LOG_FILES: 5,
    LOG_ROTATION_INTERVAL: 24 * 60 * 60 * 1000, // 24 hours
};
// Cache Configuration
exports.CACHE_CONFIG = {
    DEFAULT_TTL: 5 * 60 * 1000, // 5 minutes
    MARKET_DATA_TTL: 10 * 60 * 1000, // 10 minutes for market data (increased for page refresh persistence)
    STATIC_DATA_TTL: 30 * 60 * 1000, // 30 minutes for static data
    EXPIRY_CHECK_INTERVAL: 60 * 1000, // 1 minute
    MAX_CACHE_SIZE: 50 * 1024 * 1024, // 50MB max cache size
    KEYS: {
        INSTRUMENTS: 'instruments',
        MARKET_DATA: 'market_data',
        OPTION_CHAIN: 'option_chain',
        EXPIRY_DATES: 'expiry_dates',
        NIFTY_SPOT: 'nifty_spot',
        USER_SETTINGS: 'user_settings',
        USER_PREFERENCES: 'user_preferences',
    },
};
// Security Configuration
exports.SECURITY_CONFIG = {
    RATE_LIMITING: {
        WINDOW_MS: 15 * 60 * 1000, // 15 minutes
        MAX_REQUESTS: 1000,
    },
    CORS: {
        ORIGIN: process.env.NODE_ENV === 'production'
            ? ['https://yourdomain.com']
            : ['http://localhost:3000', 'http://localhost:3001'],
        CREDENTIALS: true,
    },
    HEADERS: {
        CONTENT_SECURITY_POLICY: "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';",
        X_FRAME_OPTIONS: 'DENY',
        X_CONTENT_TYPE_OPTIONS: 'nosniff',
    },
};
// Environment Configuration
exports.ENV_CONFIG = {
    NODE_ENV: process.env.NODE_ENV || 'development',
    PORT: parseInt(process.env.PORT || '8080'),
    NEXT_PORT: parseInt(process.env.NEXT_PORT || '3000'),
    LOG_LEVEL: process.env.LOG_LEVEL || 'info',
    ENABLE_METRICS: process.env.ENABLE_METRICS === 'true',
};
// Default Values
exports.DEFAULTS = {
    NIFTY_SPOT_PRICE: 24850,
    SELECTED_EXPIRY: '2025-06-19',
    STRIKES_TO_SHOW: 20,
    REFRESH_INTERVAL: 1000,
    CHART_TIMEFRAME: '1D',
    TABLE_PAGE_SIZE: 50,
};
//# sourceMappingURL=constants.js.map