{"version": 4, "routes": {"/option-chain": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/option-chain", "dataRoute": "/option-chain.rsc"}, "/subscribed": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/subscribed", "dataRoute": "/subscribed.rsc"}, "/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}, "/api/cache/all-latest": {"initialHeaders": {"content-type": "application/json", "x-next-cache-tags": "_N_T_/layout,_N_T_/api/layout,_N_T_/api/cache/layout,_N_T_/api/cache/all-latest/layout,_N_T_/api/cache/all-latest/route,_N_T_/api/cache/all-latest"}, "experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/api/cache/all-latest", "dataRoute": null}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "3e27e3330cf15b0167c41cd394439f57", "previewModeSigningKey": "509222f233dea389538bd5116beaf0db7d4fe554ed899c032ca09f9c56e2c054", "previewModeEncryptionKey": "8d21e2fb2e58f86bcf32aee9f83e9fda33f55ba2732524051bcbdbd080d58c4f"}}