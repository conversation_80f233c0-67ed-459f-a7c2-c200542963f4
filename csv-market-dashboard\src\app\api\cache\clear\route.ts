/**
 * API Route for Cache Clear Operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { clearCache } from '@/lib/redis-client';
import { getFallbackCache } from '@/lib/fallback-cache';

export async function POST(request: NextRequest) {
  try {
    const { pattern } = await request.json();
    
    if (!pattern || typeof pattern !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Pattern is required and must be a string' },
        { status: 400 }
      );
    }

    // Clear Redis cache
    let redisCleared = 0;
    try {
      redisCleared = await clearCache(pattern);
    } catch (error) {
      console.warn('⚠️ Redis clear failed, clearing fallback cache only');
    }
    
    // Clear fallback cache
    const fallbackCache = getFallbackCache();
    const fallbackCleared = fallbackCache.clear(pattern);
    
    return NextResponse.json({
      success: true,
      message: `Cleared cache with pattern: ${pattern}`,
      redis: redisCleared,
      fallback: fallbackCleared
    });
  } catch (error) {
    console.error('❌ API: Failed to clear cache:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to clear cache' },
      { status: 500 }
    );
  }
}
