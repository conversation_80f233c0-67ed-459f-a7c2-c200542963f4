"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const ADD_1 = __importDefault(require("./ADD"));
const BYRANK_1 = __importDefault(require("./BYRANK"));
const BYREVRANK_1 = __importDefault(require("./BYREVRANK"));
const CDF_1 = __importDefault(require("./CDF"));
const CREATE_1 = __importDefault(require("./CREATE"));
const INFO_1 = __importDefault(require("./INFO"));
const MAX_1 = __importDefault(require("./MAX"));
const MERGE_1 = __importDefault(require("./MERGE"));
const MIN_1 = __importDefault(require("./MIN"));
const QUANTILE_1 = __importDefault(require("./QUANTILE"));
const RANK_1 = __importDefault(require("./RANK"));
const RESET_1 = __importDefault(require("./RESET"));
const REVRANK_1 = __importDefault(require("./REVRANK"));
const TRIMMED_MEAN_1 = __importDefault(require("./TRIMMED_MEAN"));
exports.default = {
    ADD: ADD_1.default,
    add: ADD_1.default,
    BYRANK: BYRANK_1.default,
    byRank: BYRANK_1.default,
    BYREVRANK: BYREVRANK_1.default,
    byRevRank: BYREVRANK_1.default,
    CDF: CDF_1.default,
    cdf: CDF_1.default,
    CREATE: CREATE_1.default,
    create: CREATE_1.default,
    INFO: INFO_1.default,
    info: INFO_1.default,
    MAX: MAX_1.default,
    max: MAX_1.default,
    MERGE: MERGE_1.default,
    merge: MERGE_1.default,
    MIN: MIN_1.default,
    min: MIN_1.default,
    QUANTILE: QUANTILE_1.default,
    quantile: QUANTILE_1.default,
    RANK: RANK_1.default,
    rank: RANK_1.default,
    RESET: RESET_1.default,
    reset: RESET_1.default,
    REVRANK: REVRANK_1.default,
    revRank: REVRANK_1.default,
    TRIMMED_MEAN: TRIMMED_MEAN_1.default,
    trimmedMean: TRIMMED_MEAN_1.default
};
//# sourceMappingURL=index.js.map