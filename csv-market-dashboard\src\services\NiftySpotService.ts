/**
 * NIFTY Spot Service - Integration with dedicated spot calculator
 * Provides live NIFTY spot price data for the option chain
 */

import { EventEmitter } from 'events';

// Type definitions for NIFTY spot data
export interface NiftySpotData {
  symbol: string;
  securityId: string;
  ltp: number;
  previousClose: number;
  changePoints: number;
  changePercent: number;
  lastUpdateTime: Date | null;
  totalUpdates: number;
  isReady: boolean;
  formatted: {
    points: string;
    percent: string;
    color: string;
    isPositive: boolean;
  };
}

export interface NiftySpotStatus {
  isConnected: boolean;
  messageCount: number;
  runtime: string;
  updateRate: string;
  isDataReady: boolean;
}

/**
 * NIFTY Spot Service
 * Manages the dedicated NIFTY spot calculator and provides data to the frontend
 */
export class NiftySpotService extends EventEmitter {
  private calculator: any = null;
  private isInitialized = false;
  private currentData: NiftySpotData | null = null;
  private connectionStatus: NiftySpotStatus | null = null;

  constructor() {
    super();
    this.initializeCalculator();
  }

  /**
   * Initialize the NIFTY spot calculator
   */
  private async initializeCalculator() {
    try {
      console.log('🚀 Initializing NIFTY Spot Calculator...');
      
      // Dynamically import the calculator (Node.js module)
      const { NiftySpotCalculator } = require('../lib/nifty-spot-calculator.js');
      
      this.calculator = new NiftySpotCalculator();
      
      // Set up data callback
      this.calculator.onData((data: NiftySpotData) => {
        this.currentData = data;
        this.emit('data', data);
        console.log(`📊 [NiftySpotService] Data Update: LTP=₹${data.ltp.toFixed(2)}, Change=${data.formatted.points}`);
      });
      
      // Set up status callback
      this.calculator.onStatus((connected: boolean, error?: string) => {
        this.connectionStatus = this.calculator.getStatus();
        this.emit('status', { connected, error, status: this.connectionStatus });
        console.log(`📡 [NiftySpotService] Status: ${connected ? '🟢 Connected' : '🔴 Disconnected'}${error ? ` - ${error}` : ''}`);
      });
      
      this.isInitialized = true;
      console.log('✅ NIFTY Spot Calculator initialized successfully');
      
    } catch (error) {
      console.error('❌ Failed to initialize NIFTY Spot Calculator:', error);
      this.emit('error', error);
    }
  }

  /**
   * Connect to the NIFTY spot data feed
   */
  async connect(): Promise<void> {
    if (!this.isInitialized || !this.calculator) {
      throw new Error('NIFTY Spot Calculator not initialized');
    }

    try {
      console.log('🔌 Connecting to NIFTY spot data feed...');
      await this.calculator.connect();
      console.log('✅ Connected to NIFTY spot data feed');
    } catch (error) {
      console.error('❌ Failed to connect to NIFTY spot data feed:', error);
      throw error;
    }
  }

  /**
   * Disconnect from the NIFTY spot data feed
   */
  disconnect(): void {
    if (this.calculator) {
      console.log('🔌 Disconnecting from NIFTY spot data feed...');
      this.calculator.disconnect();
      this.currentData = null;
      this.connectionStatus = null;
    }
  }

  /**
   * Get current NIFTY spot data
   */
  getCurrentData(): NiftySpotData | null {
    return this.currentData;
  }

  /**
   * Get current connection status
   */
  getStatus(): NiftySpotStatus | null {
    if (this.calculator) {
      return this.calculator.getStatus();
    }
    return this.connectionStatus;
  }

  /**
   * Check if the service is connected and has data
   */
  isReady(): boolean {
    return this.currentData?.isReady === true;
  }

  /**
   * Get the current NIFTY LTP (Last Traded Price)
   */
  getCurrentLTP(): number {
    return this.currentData?.ltp || 0;
  }

  /**
   * Get formatted change data
   */
  getFormattedChange(): { points: string; percent: string; color: string; isPositive: boolean } | null {
    return this.currentData?.formatted || null;
  }

  /**
   * Display current status (for debugging)
   */
  displayStatus(): void {
    if (this.calculator) {
      this.calculator.displayStatus();
    } else {
      console.log('❌ NIFTY Spot Calculator not initialized');
    }
  }

  /**
   * Start the service (connect and begin data flow)
   */
  async start(): Promise<void> {
    try {
      if (!this.isInitialized) {
        await this.initializeCalculator();
      }
      await this.connect();
      console.log('🚀 NIFTY Spot Service started successfully');
    } catch (error) {
      console.error('❌ Failed to start NIFTY Spot Service:', error);
      throw error;
    }
  }

  /**
   * Stop the service (disconnect and cleanup)
   */
  stop(): void {
    this.disconnect();
    this.removeAllListeners();
    console.log('🛑 NIFTY Spot Service stopped');
  }
}

// Export singleton instance
export const niftySpotService = new NiftySpotService();

// Auto-start the service in server environment
if (typeof window === 'undefined') {
  // Server-side: Auto-start the service
  niftySpotService.start().catch(error => {
    console.error('❌ Failed to auto-start NIFTY Spot Service:', error);
  });
  
  // Graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n👋 Shutting down NIFTY Spot Service...');
    niftySpotService.displayStatus();
    niftySpotService.stop();
  });
}

export default niftySpotService;
