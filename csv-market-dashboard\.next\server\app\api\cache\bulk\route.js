"use strict";(()=>{var e={};e.id=534,e.ids=[534],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3199:(e,t,a)=>{a.r(t),a.d(t,{originalPathname:()=>f,patchFetch:()=>D,requestAsyncStorage:()=>p,routeModule:()=>h,serverHooks:()=>m,staticGenerationAsyncStorage:()=>g});var r={};a.r(r),a.d(r,{GET:()=>u,POST:()=>d});var n=a(9303),c=a(8716),o=a(670),l=a(7070),s=a(1036),i=a(8205);async function u(){try{let e=await (0,s.$4)();return e&&0!==e.length||(e=(0,i.E)().get("market_data:bulk")||[]),l.NextResponse.json({success:!0,data:e,count:e.length,source:e.length>0?"cache":"none"})}catch(e){return console.error("❌ API: Failed to get bulk market data from cache:",e),l.NextResponse.json({success:!1,error:"Failed to get cached data"},{status:500})}}async function d(e){try{let t=await e.json();if(!Array.isArray(t))return l.NextResponse.json({success:!1,error:"Data must be an array"},{status:400});let a=!1;try{a=await (0,s._1)(t)}catch(e){console.warn("⚠️ Redis cache failed, using fallback cache")}return(0,i.E)().set("market_data:bulk",t,600),l.NextResponse.json({success:!0,message:`Cached ${t.length} market data items`,redis:a,fallback:!0})}catch(e){return console.error("❌ API: Failed to cache bulk market data:",e),l.NextResponse.json({success:!1,error:"Failed to cache data"},{status:500})}}let h=new n.AppRouteRouteModule({definition:{kind:c.x.APP_ROUTE,page:"/api/cache/bulk/route",pathname:"/api/cache/bulk",filename:"route",bundlePath:"app/api/cache/bulk/route"},resolvedPagePath:"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\api\\cache\\bulk\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:p,staticGenerationAsyncStorage:g,serverHooks:m}=h,f="/api/cache/bulk/route";function D(){return(0,o.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:g})}},8205:(e,t,a)=>{a.d(t,{E:()=>c});class r{constructor(){this.cache=new Map,this.cleanupInterval=null,this.startCleanup()}startCleanup(){this.cleanupInterval=setInterval(()=>{this.cleanup()},3e4)}cleanup(){let e=Date.now(),t=0;Array.from(this.cache.entries()).forEach(([a,r])=>{e>r.timestamp+1e3*r.ttl&&(this.cache.delete(a),t++)}),t>0&&console.log(`🧹 FallbackCache: Cleaned ${t} expired entries`)}set(e,t,a=600){try{return this.cache.set(e,{data:t,timestamp:Date.now(),ttl:a}),console.log(`💾 FallbackCache: Cached ${e} (TTL: ${a}s)`),!0}catch(t){return console.error(`❌ FallbackCache: Failed to cache ${e}:`,t),!1}}get(e){try{let t=this.cache.get(e);if(!t)return null;if(Date.now()>t.timestamp+1e3*t.ttl)return this.cache.delete(e),null;return console.log(`📖 FallbackCache: Retrieved ${e} from cache`),t.data}catch(t){return console.error(`❌ FallbackCache: Failed to retrieve ${e}:`,t),null}}delete(e){let t=this.cache.delete(e);return t&&console.log(`🗑️ FallbackCache: Removed ${e}`),t}clear(e){if(!e){let e=this.cache.size;return this.cache.clear(),console.log(`🧹 FallbackCache: Cleared all ${e} entries`),e}let t=0,a=new RegExp(e.replace("*",".*"));return Array.from(this.cache.keys()).forEach(e=>{a.test(e)&&(this.cache.delete(e),t++)}),console.log(`🧹 FallbackCache: Cleared ${t} entries matching ${e}`),t}getStats(){return{totalKeys:this.cache.size,memoryUsage:`${Math.round(JSON.stringify(Array.from(this.cache.entries())).length/1024)}KB`,connectedClients:1,uptime:process.uptime()}}destroy(){this.cleanupInterval&&clearInterval(this.cleanupInterval),this.cache.clear(),console.log("\uD83D\uDC4B FallbackCache: Destroyed")}}let n=null;function c(){return n||(n=new r,console.log("\uD83D\uDE80 FallbackCache: Initialized in-memory cache for development")),n}},1036:(e,t,a)=>{a.d(t,{_1:()=>u,q8:()=>s,LK:()=>p,Kl:()=>g,$4:()=>d,oK:()=>h});let r=require("redis"),n=null,c=null;async function o(){if(n&&n.isOpen)return n;if(c)return c;c=l();try{return n=await c,c=null,n}catch(e){throw c=null,e}}async function l(){try{if("phase-production-build"===process.env.NEXT_PHASE)return console.log("\uD83D\uDD17 Redis: Skipping Redis connection during build phase"),null;if(!process.env.RUNTIME)return console.log("\uD83D\uDD17 Redis: Skipping Redis connection during static generation"),null;let e=process.env.REDIS_URL?process.env.REDIS_URL:(console.error("❌ Redis: REDIS_URL environment variable is required in production"),null);if(!e)return console.log("\uD83D\uDD17 Redis: No Redis URL provided, skipping Redis connection"),null;return console.log("\uD83D\uDD17 Redis: Connecting to Redis..."),console.log("\uD83D\uDD17 Redis: URL:",e.replace(/\/\/.*@/,"//***@")),(n=(0,r.createClient)({url:e,socket:{connectTimeout:5e3,reconnectStrategy:e=>e>3?(console.error("❌ Redis: Max reconnection attempts reached"),!1):Math.min(100*e,3e3)}})).on("error",e=>{console.error("❌ Redis Client Error:",e)}),n.on("connect",()=>{console.log("\uD83D\uDD17 Redis Client Connected")}),n.on("ready",()=>{console.log("✅ Redis Client Ready")}),n.on("end",()=>{console.log("\uD83D\uDD0C Redis Client Disconnected")}),await n.connect(),console.log("\uD83D\uDE80 Redis Cache Manager initialized"),n}catch(e){return console.error("❌ Failed to connect to Redis:",e),n=null,null}}async function s(e,t,a=600){try{let r=await o();if(!r)return!1;let n=JSON.stringify({data:t,timestamp:Date.now(),version:"1.0.0"});return await r.setEx(e,a,n),console.log(`💾 Redis: Cached ${e} (${n.length} bytes, TTL: ${a}s)`),!0}catch(t){return console.error(`❌ Redis: Failed to cache ${e}:`,t),!1}}async function i(e){try{let t=await o();if(!t)return null;let a=await t.get(e);if(!a)return null;let r=JSON.parse(a);return console.log(`📖 Redis: Retrieved ${e} from cache`),r.data}catch(t){return console.error(`❌ Redis: Failed to retrieve ${e}:`,t),null}}async function u(e,t=600){try{let a=await o();if(!a)return!1;let r=await s("market_data:bulk",e,t),n=a.multi(),c=Date.now();return e.forEach(e=>{let a=`market_data:${e.securityId}`,r=JSON.stringify({data:e,timestamp:c,version:"1.0.0"});n.setEx(a,t,r)}),await n.exec(),console.log(`💾 Redis: Cached ${e.length} individual market data entries`),r}catch(e){return console.error("❌ Redis: Failed to cache bulk market data:",e),!1}}async function d(){return await i("market_data:bulk")||[]}async function h(e){return i(`market_data:${e}`)}async function p(e="market_data:*"){try{let t=await o();if(!t)return 0;let a=await t.keys(e);if(0===a.length)return 0;let r=await t.del(a);return console.log(`🧹 Redis: Cleared ${r} entries matching ${e}`),r}catch(e){return console.error(`❌ Redis: Failed to clear cache:`,e),0}}async function g(){let e=await o();if(!e)return[];let t=await e.keys("market_data:*");return t.length?(await e.mGet(t)).map(e=>e&&JSON.parse(e)?.data).filter(Boolean):[]}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[276,972],()=>a(3199));module.exports=r})();