/**
 * ✅ ENHANCED LOGGING SERVICE
 * Provides structured logging for market data debugging
 */
export declare enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3
}
export declare enum LogCategory {
    SPOT = "SPOT",
    DEPTH = "DEPTH",
    ASK = "ASK",
    BID = "BID",
    STRIKE = "STRIKE",
    EXPIRY = "EXPIRY",
    SUBSCRIPTION = "SUBSCRIPTION",
    CONNECTION = "CONNECTION",
    PARSING = "PARSING"
}
interface LogEntry {
    timestamp: number;
    level: LogLevel;
    category: LogCategory;
    message: string;
    data?: any;
}
declare class LoggingService {
    private logs;
    private maxLogs;
    private currentLevel;
    private enabledCategories;
    /**
     * Set minimum log level
     */
    setLevel(level: LogLevel): void;
    /**
     * Enable/disable specific log categories
     */
    setCategories(categories: LogCategory[]): void;
    /**
     * Log debug message
     */
    debug(category: LogCategory, message: string, data?: any): void;
    /**
     * Log info message
     */
    info(category: LogCategory, message: string, data?: any): void;
    /**
     * Log warning message
     */
    warn(category: LogCategory, message: string, data?: any): void;
    /**
     * Log error message
     */
    error(category: LogCategory, message: string, data?: any): void;
    /**
     * Internal logging method
     */
    private log;
    /**
     * Get recent logs
     */
    getRecentLogs(count?: number): LogEntry[];
    /**
     * Get logs by category
     */
    getLogsByCategory(category: LogCategory, count?: number): LogEntry[];
    /**
     * Clear all logs
     */
    clear(): void;
    /**
     * Export logs as JSON
     */
    exportLogs(): string;
    /**
     * Log market data packet details
     */
    logMarketDataPacket(symbol: string, responseCode: number, bufferLength: number, parsedData: any): void;
    /**
     * Log market depth details
     */
    logMarketDepth(symbol: string, marketDepth: any[]): void;
    /**
     * Log NIFTY spot price updates
     */
    logNiftySpotUpdate(price: number, source: string, securityId?: string): void;
    /**
     * Log subscription details
     */
    logSubscription(instruments: any[], batchSize: number): void;
    /**
     * Log connection events
     */
    logConnection(event: string, details?: any): void;
    /**
     * Log option chain building
     */
    logOptionChainBuild(expiry: string, spotPrice: number, strikeCount: number): void;
}
export declare const loggingService: LoggingService;
export {};
//# sourceMappingURL=LoggingService.d.ts.map