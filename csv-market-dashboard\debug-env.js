const fs = require('fs');

const content = fs.readFileSync('.env', 'utf8');
console.log('Raw content around line 40:');
content.split('\n').slice(35, 45).forEach((line, i) => {
  const lineNum = 35 + i + 1;
  console.log(`${lineNum}: '${line}' (${line.length} chars)`);
  if (line.includes('DATABASE_URL')) {
    console.log('  -> Found DATABASE_URL line!');
    console.log('  -> Char codes:', line.split('').map(c => c.charCodeAt(0)).slice(0, 20).join(','));
  }
});

console.log('\nTrying to load with dotenv:');
require('dotenv').config();
console.log('DATABASE_URL found:', !!process.env.DATABASE_URL);
