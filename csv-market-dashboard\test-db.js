require('dotenv').config();
const { Client } = require('pg');

async function testDatabase() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: { rejectUnauthorized: false }
  });

  try {
    await client.connect();
    console.log('✅ Database connected successfully');

    // Check total instruments
    const countResult = await client.query('SELECT COUNT(*) FROM "Instruments"');
    console.log('📊 Total instruments:', countResult.rows[0].count);

    // Check NIFTY instruments
    const niftyResult = await client.query('SELECT * FROM "Instruments" WHERE symbol = \'NIFTY\' LIMIT 5');
    console.log('🎯 NIFTY instruments:', niftyResult.rows.length);
    niftyResult.rows.forEach(row => {
      console.log('  -', row.symbol, row.instrument_type, row.expiry_date, row.strike_price, row.option_type);
    });

    // Check expiry dates
    const expiryResult = await client.query('SELECT DISTINCT expiry_date FROM "Instruments" WHERE symbol = \'NIFTY\' AND instrument_type = \'OP\' ORDER BY expiry_date LIMIT 10');
    console.log('📅 NIFTY expiry dates:', expiryResult.rows.length);
    expiryResult.rows.forEach(row => {
      console.log('  -', row.expiry_date);
    });

  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await client.end();
  }
}

testDatabase();
