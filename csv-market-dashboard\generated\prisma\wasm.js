
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.11.0
 * Query Engine version: 9c30299f5a0ea26a96790e13f796dc6094db3173
 */
Prisma.prismaVersion = {
  client: "6.11.0",
  engine: "9c30299f5a0ea26a96790e13f796dc6094db3173"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.InstrumentScalarFieldEnum = {
  SECURITY_ID: 'SECURITY_ID',
  SYMBOL_NAME: 'SYMBOL_NAME',
  EXCH_ID: 'EXCH_ID',
  SEGMENT: 'SEGMENT',
  INSTRUMENT_TYPE: 'INSTRUMENT_TYPE',
  STRIKE_PRICE: 'STRIKE_PRICE',
  SM_EXPIRY_DATE: 'SM_EXPIRY_DATE',
  OPTION_TYPE: 'OPTION_TYPE',
  LOT_SIZE: 'LOT_SIZE',
  TICK_SIZE: 'TICK_SIZE',
  ISIN: 'ISIN',
  INSTRUMENT: 'INSTRUMENT',
  UNDERLYING_SECURITY_ID: 'UNDERLYING_SECURITY_ID',
  UNDERLYING_SYMBOL: 'UNDERLYING_SYMBOL',
  DISPLAY_NAME: 'DISPLAY_NAME',
  SERIES: 'SERIES',
  EXPIRY_FLAG: 'EXPIRY_FLAG',
  BRACKET_FLAG: 'BRACKET_FLAG',
  COVER_FLAG: 'COVER_FLAG',
  ASM_GSM_FLAG: 'ASM_GSM_FLAG',
  ASM_GSM_CATEGORY: 'ASM_GSM_CATEGORY',
  BUY_SELL_INDICATOR: 'BUY_SELL_INDICATOR',
  BUY_CO_MIN_MARGIN_PER: 'BUY_CO_MIN_MARGIN_PER',
  SELL_CO_MIN_MARGIN_PER: 'SELL_CO_MIN_MARGIN_PER',
  BUY_CO_SL_RANGE_MAX_PERC: 'BUY_CO_SL_RANGE_MAX_PERC',
  SELL_CO_SL_RANGE_MAX_PERC: 'SELL_CO_SL_RANGE_MAX_PERC',
  BUY_CO_SL_RANGE_MIN_PERC: 'BUY_CO_SL_RANGE_MIN_PERC',
  SELL_CO_SL_RANGE_MIN_PERC: 'SELL_CO_SL_RANGE_MIN_PERC',
  BUY_BO_MIN_MARGIN_PER: 'BUY_BO_MIN_MARGIN_PER',
  SELL_BO_MIN_MARGIN_PER: 'SELL_BO_MIN_MARGIN_PER',
  BUY_BO_SL_RANGE_MAX_PERC: 'BUY_BO_SL_RANGE_MAX_PERC',
  SELL_BO_SL_RANGE_MAX_PERC: 'SELL_BO_SL_RANGE_MAX_PERC',
  BUY_BO_SL_RANGE_MIN_PERC: 'BUY_BO_SL_RANGE_MIN_PERC',
  SELL_BO_SL_MIN_RANGE: 'SELL_BO_SL_MIN_RANGE',
  BUY_BO_PROFIT_RANGE_MAX_PERC: 'BUY_BO_PROFIT_RANGE_MAX_PERC',
  SELL_BO_PROFIT_RANGE_MAX_PERC: 'SELL_BO_PROFIT_RANGE_MAX_PERC',
  BUY_BO_PROFIT_RANGE_MIN_PERC: 'BUY_BO_PROFIT_RANGE_MIN_PERC',
  SELL_BO_PROFIT_RANGE_MIN_PERC: 'SELL_BO_PROFIT_RANGE_MIN_PERC',
  MTF_LEVERAGE: 'MTF_LEVERAGE'
};

exports.Prisma.MarketDataScalarFieldEnum = {
  id: 'id',
  securityId: 'securityId',
  symbol: 'symbol',
  ltp: 'ltp',
  change: 'change',
  changePercent: 'changePercent',
  volume: 'volume',
  openInterest: 'openInterest',
  bid: 'bid',
  ask: 'ask',
  high: 'high',
  low: 'low',
  open: 'open',
  close: 'close',
  lastUpdateTime: 'lastUpdateTime',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};


exports.Prisma.ModelName = {
  Instrument: 'Instrument',
  MarketData: 'MarketData'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
