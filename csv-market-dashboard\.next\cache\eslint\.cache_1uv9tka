[{"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\api\\cache\\all-latest\\route.ts": "1", "D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\api\\cache\\bulk\\route.ts": "2", "D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\api\\cache\\clear\\route.ts": "3", "D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\api\\cache\\item\\route.ts": "4", "D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\api\\nifty-expiry\\route.ts": "5", "D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\layout.tsx": "6", "D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\option-chain\\page.tsx": "7", "D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\page.tsx": "8", "D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\subscribed\\page.tsx": "9", "D:\\love\\dashboard\\csv-market-dashboard\\src\\components\\ConnectionStatus.tsx": "10", "D:\\love\\dashboard\\csv-market-dashboard\\src\\components\\FilterPanel.tsx": "11", "D:\\love\\dashboard\\csv-market-dashboard\\src\\components\\InstrumentTable.tsx": "12", "D:\\love\\dashboard\\csv-market-dashboard\\src\\components\\OptionChain.tsx": "13", "D:\\love\\dashboard\\csv-market-dashboard\\src\\components\\Stats.tsx": "14", "D:\\love\\dashboard\\csv-market-dashboard\\src\\contexts\\WebSocketContext.tsx": "15", "D:\\love\\dashboard\\csv-market-dashboard\\src\\hooks\\useEnhancedMarketData.ts": "16", "D:\\love\\dashboard\\csv-market-dashboard\\src\\hooks\\useMarketData.ts": "17", "D:\\love\\dashboard\\csv-market-dashboard\\src\\hooks\\useWebSocket.ts": "18", "D:\\love\\dashboard\\csv-market-dashboard\\src\\lib\\constants.ts": "19", "D:\\love\\dashboard\\csv-market-dashboard\\src\\lib\\data-cache.ts": "20", "D:\\love\\dashboard\\csv-market-dashboard\\src\\lib\\fallback-cache.ts": "21", "D:\\love\\dashboard\\csv-market-dashboard\\src\\lib\\performance.ts": "22", "D:\\love\\dashboard\\csv-market-dashboard\\src\\lib\\redis-client.ts": "23", "D:\\love\\dashboard\\csv-market-dashboard\\src\\lib\\utils.ts": "24", "D:\\love\\dashboard\\csv-market-dashboard\\src\\lib\\websocket-manager.ts": "25", "D:\\love\\dashboard\\csv-market-dashboard\\src\\server\\main.ts": "26", "D:\\love\\dashboard\\csv-market-dashboard\\src\\server\\socket-config.ts": "27", "D:\\love\\dashboard\\csv-market-dashboard\\src\\services\\CSVService.ts": "28", "D:\\love\\dashboard\\csv-market-dashboard\\src\\services\\DhanAPIService.ts": "29", "D:\\love\\dashboard\\csv-market-dashboard\\src\\services\\LoggingService.ts": "30", "D:\\love\\dashboard\\csv-market-dashboard\\src\\services\\MarketDataService.ts": "31", "D:\\love\\dashboard\\csv-market-dashboard\\src\\services\\SimpleCSVService.js": "32", "D:\\love\\dashboard\\csv-market-dashboard\\src\\store\\marketStore.ts": "33", "D:\\love\\dashboard\\csv-market-dashboard\\src\\types\\index.ts": "34"}, {"size": 381, "mtime": 1751446051957, "results": "35", "hashOfConfig": "36"}, {"size": 2073, "mtime": 1751444557529, "results": "37", "hashOfConfig": "36"}, {"size": 1308, "mtime": 1751444870672, "results": "38", "hashOfConfig": "36"}, {"size": 2408, "mtime": 1751469770412, "results": "39", "hashOfConfig": "36"}, {"size": 1674, "mtime": 1751441904295, "results": "40", "hashOfConfig": "36"}, {"size": 3912, "mtime": 1751443959908, "results": "41", "hashOfConfig": "36"}, {"size": 1871, "mtime": 1751446157037, "results": "42", "hashOfConfig": "36"}, {"size": 8909, "mtime": 1751518509224, "results": "43", "hashOfConfig": "36"}, {"size": 12697, "mtime": 1751517845915, "results": "44", "hashOfConfig": "36"}, {"size": 1026, "mtime": 1751441904296, "results": "45", "hashOfConfig": "36"}, {"size": 8474, "mtime": 1751441904296, "results": "46", "hashOfConfig": "36"}, {"size": 8541, "mtime": 1751441904296, "results": "47", "hashOfConfig": "36"}, {"size": 25294, "mtime": 1751518588602, "results": "48", "hashOfConfig": "36"}, {"size": 5471, "mtime": 1751444107010, "results": "49", "hashOfConfig": "36"}, {"size": 12278, "mtime": 1751441904296, "results": "50", "hashOfConfig": "36"}, {"size": 9738, "mtime": 1751518614197, "results": "51", "hashOfConfig": "36"}, {"size": 1005, "mtime": 1751518632084, "results": "52", "hashOfConfig": "36"}, {"size": 9751, "mtime": 1751518646449, "results": "53", "hashOfConfig": "36"}, {"size": 6911, "mtime": 1751441904296, "results": "54", "hashOfConfig": "36"}, {"size": 11597, "mtime": 1751441904296, "results": "55", "hashOfConfig": "36"}, {"size": 3530, "mtime": 1751517664405, "results": "56", "hashOfConfig": "36"}, {"size": 9666, "mtime": 1751441904296, "results": "57", "hashOfConfig": "36"}, {"size": 10685, "mtime": 1751518671171, "results": "58", "hashOfConfig": "36"}, {"size": 9170, "mtime": 1751441904296, "results": "59", "hashOfConfig": "36"}, {"size": 16147, "mtime": 1751517590812, "results": "60", "hashOfConfig": "36"}, {"size": 30747, "mtime": 1751441904300, "results": "61", "hashOfConfig": "36"}, {"size": 8791, "mtime": 1751441904300, "results": "62", "hashOfConfig": "36"}, {"size": 14470, "mtime": 1751441904300, "results": "63", "hashOfConfig": "36"}, {"size": 5014, "mtime": 1751441904300, "results": "64", "hashOfConfig": "36"}, {"size": 6476, "mtime": 1751441904300, "results": "65", "hashOfConfig": "36"}, {"size": 29364, "mtime": 1751441904300, "results": "66", "hashOfConfig": "36"}, {"size": 8482, "mtime": 1751441904300, "results": "67", "hashOfConfig": "36"}, {"size": 11393, "mtime": 1751446058761, "results": "68", "hashOfConfig": "36"}, {"size": 10087, "mtime": 1751441904300, "results": "69", "hashOfConfig": "36"}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kzcitg", {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\api\\cache\\all-latest\\route.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\api\\cache\\bulk\\route.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\api\\cache\\clear\\route.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\api\\cache\\item\\route.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\api\\nifty-expiry\\route.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\layout.tsx", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\option-chain\\page.tsx", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\page.tsx", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\subscribed\\page.tsx", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\components\\ConnectionStatus.tsx", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\components\\FilterPanel.tsx", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\components\\InstrumentTable.tsx", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\components\\OptionChain.tsx", ["172"], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\components\\Stats.tsx", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\contexts\\WebSocketContext.tsx", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\hooks\\useEnhancedMarketData.ts", ["173"], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\hooks\\useMarketData.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\hooks\\useWebSocket.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\lib\\constants.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\lib\\data-cache.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\lib\\fallback-cache.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\lib\\performance.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\lib\\redis-client.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\lib\\utils.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\lib\\websocket-manager.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\server\\main.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\server\\socket-config.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\services\\CSVService.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\services\\DhanAPIService.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\services\\LoggingService.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\services\\MarketDataService.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\services\\SimpleCSVService.js", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\store\\marketStore.ts", [], [], "D:\\love\\dashboard\\csv-market-dashboard\\src\\types\\index.ts", [], [], {"ruleId": "174", "severity": 1, "message": "175", "line": 183, "column": 6, "nodeType": "176", "endLine": 183, "endColumn": 50, "suggestions": "177"}, {"ruleId": "174", "severity": 1, "message": "178", "line": 49, "column": 9, "nodeType": "179", "endLine": 55, "endColumn": 4}, "react-hooks/exhaustive-deps", "React Hook useCallback has missing dependencies: 'findOptionData' and 'generateStrikes'. Either include them or remove the dependency array.", "ArrayExpression", ["180"], "The 'ui' object makes the dependencies of useCallback Hook (at line 189) change on every render. To fix this, wrap the initialization of 'ui' in its own useMemo() Hook.", "VariableDeclarator", {"desc": "181", "fix": "182"}, "Update the dependencies array to be: [selectedExpiry, niftySpotPrice, generateStrikes, findOptionData]", {"range": "183", "text": "184"}, [7382, 7426], "[selectedExpiry, niftySpotPrice, generateStrikes, findOptionData]"]