"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/cache/all-latest/route";
exports.ids = ["app/api/cache/all-latest/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "redis":
/*!************************!*\
  !*** external "redis" ***!
  \************************/
/***/ ((module) => {

module.exports = require("redis");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcache%2Fall-latest%2Froute&page=%2Fapi%2Fcache%2Fall-latest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcache%2Fall-latest%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcache%2Fall-latest%2Froute&page=%2Fapi%2Fcache%2Fall-latest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcache%2Fall-latest%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_love_dashboard_csv_market_dashboard_src_app_api_cache_all_latest_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/cache/all-latest/route.ts */ \"(rsc)/./src/app/api/cache/all-latest/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/cache/all-latest/route\",\n        pathname: \"/api/cache/all-latest\",\n        filename: \"route\",\n        bundlePath: \"app/api/cache/all-latest/route\"\n    },\n    resolvedPagePath: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\api\\\\cache\\\\all-latest\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_love_dashboard_csv_market_dashboard_src_app_api_cache_all_latest_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/cache/all-latest/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcache%2Fall-latest%2Froute&page=%2Fapi%2Fcache%2Fall-latest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcache%2Fall-latest%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/cache/all-latest/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/cache/all-latest/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_redis_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/redis-client */ \"(rsc)/./src/lib/redis-client.ts\");\n\n\nasync function GET() {\n    try {\n        const data = await (0,_lib_redis_client__WEBPACK_IMPORTED_MODULE_1__.getAllLatestTicks)();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9jYWNoZS9hbGwtbGF0ZXN0L3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF3RDtBQUNEO0FBRWhELGVBQWVFO0lBQ3BCLElBQUk7UUFDRixNQUFNQyxPQUFPLE1BQU1GLG9FQUFpQkE7UUFDcEMsT0FBT0QscURBQVlBLENBQUNJLElBQUksQ0FBQztZQUFFQyxTQUFTO1lBQU1GO1FBQUs7SUFDakQsRUFBRSxPQUFPRyxPQUFZO1FBQ25CLE9BQU9OLHFEQUFZQSxDQUFDSSxJQUFJLENBQUM7WUFBRUMsU0FBUztZQUFPQyxPQUFPQSxNQUFNQyxPQUFPO1FBQUMsR0FBRztZQUFFQyxRQUFRO1FBQUk7SUFDbkY7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2Nzdi1tYXJrZXQtZGFzaGJvYXJkLy4vc3JjL2FwcC9hcGkvY2FjaGUvYWxsLWxhdGVzdC9yb3V0ZS50cz9kYTUxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XHJcbmltcG9ydCB7IGdldEFsbExhdGVzdFRpY2tzIH0gZnJvbSAnQC9saWIvcmVkaXMtY2xpZW50JztcclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQoKSB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBnZXRBbGxMYXRlc3RUaWNrcygpO1xyXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgc3VjY2VzczogdHJ1ZSwgZGF0YSB9KTtcclxuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfSwgeyBzdGF0dXM6IDUwMCB9KTtcclxuICB9XHJcbn0gIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImdldEFsbExhdGVzdFRpY2tzIiwiR0VUIiwiZGF0YSIsImpzb24iLCJzdWNjZXNzIiwiZXJyb3IiLCJtZXNzYWdlIiwic3RhdHVzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/cache/all-latest/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/redis-client.ts":
/*!*********************************!*\
  !*** ./src/lib/redis-client.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cacheBulkMarketData: () => (/* binding */ cacheBulkMarketData),\n/* harmony export */   cacheMarketData: () => (/* binding */ cacheMarketData),\n/* harmony export */   cacheMarketDataEntry: () => (/* binding */ cacheMarketDataEntry),\n/* harmony export */   clearCache: () => (/* binding */ clearCache),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   disconnectRedis: () => (/* binding */ disconnectRedis),\n/* harmony export */   getAllLatestTicks: () => (/* binding */ getAllLatestTicks),\n/* harmony export */   getBulkMarketData: () => (/* binding */ getBulkMarketData),\n/* harmony export */   getCacheStats: () => (/* binding */ getCacheStats),\n/* harmony export */   getCachedMarketData: () => (/* binding */ getCachedMarketData),\n/* harmony export */   getMarketDataEntry: () => (/* binding */ getMarketDataEntry),\n/* harmony export */   getRedisClient: () => (/* binding */ getRedisClient),\n/* harmony export */   redisCacheHelpers: () => (/* binding */ redisCacheHelpers),\n/* harmony export */   removeFromCache: () => (/* binding */ removeFromCache)\n/* harmony export */ });\n/* harmony import */ var redis__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! redis */ \"redis\");\n/* harmony import */ var redis__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(redis__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * Enhanced Redis Client for Next.js - Market Data Caching\r\n * Features:\r\n * - Automatic connection management\r\n * - Connection pooling\r\n * - Error recovery\r\n * - Performance monitoring\r\n * - Configurable TTL and compression\r\n */ \n// Global Redis client instance\nlet redis = null;\nlet connectionPromise = null;\nlet reconnectAttempts = 0;\nconst MAX_RECONNECT_ATTEMPTS = 5;\nconst RECONNECT_DELAY = 1000; // Base delay in ms\n// Redis configuration\nconst REDIS_CONFIG = {\n    maxRetries: 3,\n    retryDelayOnFailover: 100,\n    enableReadyCheck: false,\n    maxRetriesPerRequest: 3,\n    lazyConnect: true,\n    keepAlive: 30000,\n    connectTimeout: 10000,\n    commandTimeout: 5000\n};\n/**\r\n * Get Redis URL from environment with fallback\r\n */ function getRedisUrl() {\n    // Try environment variable first\n    if (process.env.REDIS_URL) {\n        return process.env.REDIS_URL;\n    }\n    // Check if we're in development mode\n    if (true) {\n        console.warn(\"⚠️ Redis: No REDIS_URL found. Running in development mode without Redis.\");\n        return null;\n    }\n    // In production, we need Redis\n    console.error(\"❌ Redis: REDIS_URL environment variable is required in production\");\n    return null;\n}\n/**\r\n * Get or create Redis client instance with enhanced connection management\r\n */ async function getRedisClient() {\n    // Return existing connection if available\n    if (redis && redis.isOpen) {\n        return redis;\n    }\n    // Return existing connection promise if connecting\n    if (connectionPromise) {\n        return connectionPromise;\n    }\n    // Create new connection promise\n    connectionPromise = createRedisConnection();\n    try {\n        redis = await connectionPromise;\n        connectionPromise = null;\n        return redis;\n    } catch (error) {\n        connectionPromise = null;\n        throw error;\n    }\n}\n/**\r\n * Create new Redis connection with enhanced error handling\r\n */ async function createRedisConnection() {\n    try {\n        // Skip Redis connection during build phase\n        if (false) {}\n        // Skip Redis connection during static generation\n        if ( true && !process.env.RUNTIME) {\n            console.log(\"\\uD83D\\uDD17 Redis: Skipping Redis connection during static generation\");\n            return null;\n        }\n        const redisUrl = getRedisUrl();\n        // If no Redis URL, return null (development mode)\n        if (!redisUrl) {\n            console.log(\"\\uD83D\\uDD17 Redis: No Redis URL provided, skipping Redis connection\");\n            return null;\n        }\n        console.log(\"\\uD83D\\uDD17 Redis: Connecting to Redis...\");\n        console.log(\"\\uD83D\\uDD17 Redis: URL:\", redisUrl.replace(/\\/\\/.*@/, \"//***@\")); // Hide credentials in logs\n        redis = (0,redis__WEBPACK_IMPORTED_MODULE_0__.createClient)({\n            url: redisUrl,\n            socket: {\n                connectTimeout: 5000,\n                reconnectStrategy: (retries)=>{\n                    if (retries > 3) {\n                        console.error(\"❌ Redis: Max reconnection attempts reached\");\n                        return false;\n                    }\n                    return Math.min(retries * 100, 3000);\n                }\n            }\n        });\n        redis.on(\"error\", (err)=>{\n            console.error(\"❌ Redis Client Error:\", err);\n        });\n        redis.on(\"connect\", ()=>{\n            console.log(\"\\uD83D\\uDD17 Redis Client Connected\");\n        });\n        redis.on(\"ready\", ()=>{\n            console.log(\"✅ Redis Client Ready\");\n        });\n        redis.on(\"end\", ()=>{\n            console.log(\"\\uD83D\\uDD0C Redis Client Disconnected\");\n        });\n        await redis.connect();\n        console.log(\"\\uD83D\\uDE80 Redis Cache Manager initialized\");\n        return redis;\n    } catch (error) {\n        console.error(\"❌ Failed to connect to Redis:\", error);\n        redis = null;\n        return null;\n    }\n}\n/**\r\n * Cache market data to Redis\r\n */ async function cacheMarketData(key, data, ttlSeconds = 600) {\n    try {\n        const client = await getRedisClient();\n        if (!client) {\n            return false;\n        }\n        const serializedData = JSON.stringify({\n            data,\n            timestamp: Date.now(),\n            version: \"1.0.0\"\n        });\n        await client.setEx(key, ttlSeconds, serializedData);\n        console.log(`💾 Redis: Cached ${key} (${serializedData.length} bytes, TTL: ${ttlSeconds}s)`);\n        return true;\n    } catch (error) {\n        console.error(`❌ Redis: Failed to cache ${key}:`, error);\n        return false;\n    }\n}\n/**\r\n * Get market data from Redis\r\n */ async function getCachedMarketData(key) {\n    try {\n        const client = await getRedisClient();\n        if (!client) {\n            return null;\n        }\n        const serializedData = await client.get(key);\n        if (!serializedData) {\n            return null;\n        }\n        const parsed = JSON.parse(serializedData);\n        console.log(`📖 Redis: Retrieved ${key} from cache`);\n        return parsed.data;\n    } catch (error) {\n        console.error(`❌ Redis: Failed to retrieve ${key}:`, error);\n        return null;\n    }\n}\n/**\r\n * Cache bulk market data efficiently\r\n */ async function cacheBulkMarketData(data, ttlSeconds = 600) {\n    try {\n        const client = await getRedisClient();\n        if (!client) {\n            return false;\n        }\n        // Store bulk data\n        const bulkSuccess = await cacheMarketData(\"market_data:bulk\", data, ttlSeconds);\n        // Also store individual entries for quick access using pipeline\n        const pipeline = client.multi();\n        const timestamp = Date.now();\n        data.forEach((entry)=>{\n            const key = `market_data:${entry.securityId}`;\n            const entryData = JSON.stringify({\n                data: entry,\n                timestamp,\n                version: \"1.0.0\"\n            });\n            pipeline.setEx(key, ttlSeconds, entryData);\n        });\n        await pipeline.exec();\n        console.log(`💾 Redis: Cached ${data.length} individual market data entries`);\n        return bulkSuccess;\n    } catch (error) {\n        console.error(\"❌ Redis: Failed to cache bulk market data:\", error);\n        return false;\n    }\n}\n/**\r\n * Get bulk market data from Redis\r\n */ async function getBulkMarketData() {\n    const data = await getCachedMarketData(\"market_data:bulk\");\n    return data || [];\n}\n/**\r\n * Cache individual market data entry\r\n */ async function cacheMarketDataEntry(securityId, data, ttlSeconds = 600) {\n    return cacheMarketData(`market_data:${securityId}`, data, ttlSeconds);\n}\n/**\r\n * Get individual market data entry\r\n */ async function getMarketDataEntry(securityId) {\n    return getCachedMarketData(`market_data:${securityId}`);\n}\n/**\r\n * Remove data from Redis\r\n */ async function removeFromCache(key) {\n    try {\n        const client = await getRedisClient();\n        if (!client) {\n            return false;\n        }\n        const result = await client.del(key);\n        console.log(`🗑️ Redis: Removed ${key}`);\n        return result > 0;\n    } catch (error) {\n        console.error(`❌ Redis: Failed to remove ${key}:`, error);\n        return false;\n    }\n}\n/**\r\n * Clear all cache entries with pattern\r\n */ async function clearCache(pattern = \"market_data:*\") {\n    try {\n        const client = await getRedisClient();\n        if (!client) {\n            return 0;\n        }\n        const keys = await client.keys(pattern);\n        if (keys.length === 0) {\n            return 0;\n        }\n        const result = await client.del(keys);\n        console.log(`🧹 Redis: Cleared ${result} entries matching ${pattern}`);\n        return result;\n    } catch (error) {\n        console.error(`❌ Redis: Failed to clear cache:`, error);\n        return 0;\n    }\n}\n/**\r\n * Get Redis cache statistics\r\n */ async function getCacheStats() {\n    try {\n        const client = await getRedisClient();\n        if (!client) {\n            return {\n                totalKeys: 0,\n                memoryUsage: \"0B\",\n                connectedClients: 0,\n                uptime: 0\n            };\n        }\n        const info = await client.info();\n        const dbSize = await client.dbSize();\n        // Parse Redis info\n        const lines = info.split(\"\\r\\n\");\n        const stats = {};\n        lines.forEach((line)=>{\n            if (line.includes(\":\")) {\n                const [key, value] = line.split(\":\");\n                stats[key] = value;\n            }\n        });\n        return {\n            totalKeys: dbSize,\n            memoryUsage: stats.used_memory_human || \"0B\",\n            connectedClients: parseInt(stats.connected_clients) || 0,\n            uptime: parseInt(stats.uptime_in_seconds) || 0\n        };\n    } catch (error) {\n        console.error(\"❌ Redis: Failed to get stats:\", error);\n        return {\n            totalKeys: 0,\n            memoryUsage: \"0B\",\n            connectedClients: 0,\n            uptime: 0\n        };\n    }\n}\n/**\r\n * Disconnect from Redis\r\n */ async function disconnectRedis() {\n    try {\n        if (redis) {\n            await redis.quit();\n            redis = null;\n            console.log(\"\\uD83D\\uDC4B Redis Cache Manager disconnected\");\n        }\n    } catch (error) {\n        console.error(\"❌ Error disconnecting from Redis:\", error);\n    }\n}\n/**\r\n * Get all latest ticks from Redis\r\n */ async function getAllLatestTicks() {\n    const client = await getRedisClient();\n    if (!client) return [];\n    const keys = await client.keys(\"market_data:*\");\n    if (!keys.length) return [];\n    const results = await client.mGet(keys);\n    return results.map((str)=>str && JSON.parse(str)?.data).filter(Boolean);\n}\n// Helper functions for backward compatibility\nconst redisCacheHelpers = {\n    cacheMarketData,\n    getCachedMarketData,\n    cacheBulkMarketData,\n    getBulkMarketData,\n    cacheStaticData: (key, data)=>cacheMarketData(key, data, 1800)\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    getRedisClient,\n    cacheMarketData,\n    getCachedMarketData,\n    cacheBulkMarketData,\n    getBulkMarketData,\n    cacheMarketDataEntry,\n    getMarketDataEntry,\n    removeFromCache,\n    clearCache,\n    getCacheStats,\n    disconnectRedis,\n    redisCacheHelpers,\n    getAllLatestTicks\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/redis-client.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcache%2Fall-latest%2Froute&page=%2Fapi%2Fcache%2Fall-latest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcache%2Fall-latest%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();