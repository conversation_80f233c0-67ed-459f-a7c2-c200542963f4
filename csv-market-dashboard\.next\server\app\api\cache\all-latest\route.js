"use strict";(()=>{var e={};e.id=70,e.ids=[70],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},2593:(e,t,n)=>{n.r(t),n.d(t,{originalPathname:()=>h,patchFetch:()=>g,requestAsyncStorage:()=>d,routeModule:()=>u,serverHooks:()=>R,staticGenerationAsyncStorage:()=>p});var r={};n.r(r),n.d(r,{GET:()=>c});var a=n(9303),o=n(8716),i=n(670),s=n(7070),l=n(1036);async function c(){try{let e=await (0,l.Kl)();return s.NextResponse.json({success:!0,data:e})}catch(e){return s.NextResponse.json({success:!1,error:e.message},{status:500})}}let u=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/cache/all-latest/route",pathname:"/api/cache/all-latest",filename:"route",bundlePath:"app/api/cache/all-latest/route"},resolvedPagePath:"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\api\\cache\\all-latest\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:d,staticGenerationAsyncStorage:p,serverHooks:R}=u,h="/api/cache/all-latest/route";function g(){return(0,i.patchFetch)({serverHooks:R,staticGenerationAsyncStorage:p})}},1036:(e,t,n)=>{n.d(t,{_1:()=>u,q8:()=>l,LK:()=>R,Kl:()=>h,$4:()=>d,oK:()=>p});let r=require("redis"),a=null,o=null;async function i(){if(a&&a.isOpen)return a;if(o)return o;o=s();try{return a=await o,o=null,a}catch(e){throw o=null,e}}async function s(){try{if("phase-production-build"===process.env.NEXT_PHASE)return console.log("\uD83D\uDD17 Redis: Skipping Redis connection during build phase"),null;if(!process.env.RUNTIME)return console.log("\uD83D\uDD17 Redis: Skipping Redis connection during static generation"),null;let e=process.env.REDIS_URL?process.env.REDIS_URL:(console.error("❌ Redis: REDIS_URL environment variable is required in production"),null);if(!e)return console.log("\uD83D\uDD17 Redis: No Redis URL provided, skipping Redis connection"),null;return console.log("\uD83D\uDD17 Redis: Connecting to Redis..."),console.log("\uD83D\uDD17 Redis: URL:",e.replace(/\/\/.*@/,"//***@")),(a=(0,r.createClient)({url:e,socket:{connectTimeout:5e3,reconnectStrategy:e=>e>3?(console.error("❌ Redis: Max reconnection attempts reached"),!1):Math.min(100*e,3e3)}})).on("error",e=>{console.error("❌ Redis Client Error:",e)}),a.on("connect",()=>{console.log("\uD83D\uDD17 Redis Client Connected")}),a.on("ready",()=>{console.log("✅ Redis Client Ready")}),a.on("end",()=>{console.log("\uD83D\uDD0C Redis Client Disconnected")}),await a.connect(),console.log("\uD83D\uDE80 Redis Cache Manager initialized"),a}catch(e){return console.error("❌ Failed to connect to Redis:",e),a=null,null}}async function l(e,t,n=600){try{let r=await i();if(!r)return!1;let a=JSON.stringify({data:t,timestamp:Date.now(),version:"1.0.0"});return await r.setEx(e,n,a),console.log(`💾 Redis: Cached ${e} (${a.length} bytes, TTL: ${n}s)`),!0}catch(t){return console.error(`❌ Redis: Failed to cache ${e}:`,t),!1}}async function c(e){try{let t=await i();if(!t)return null;let n=await t.get(e);if(!n)return null;let r=JSON.parse(n);return console.log(`📖 Redis: Retrieved ${e} from cache`),r.data}catch(t){return console.error(`❌ Redis: Failed to retrieve ${e}:`,t),null}}async function u(e,t=600){try{let n=await i();if(!n)return!1;let r=await l("market_data:bulk",e,t),a=n.multi(),o=Date.now();return e.forEach(e=>{let n=`market_data:${e.securityId}`,r=JSON.stringify({data:e,timestamp:o,version:"1.0.0"});a.setEx(n,t,r)}),await a.exec(),console.log(`💾 Redis: Cached ${e.length} individual market data entries`),r}catch(e){return console.error("❌ Redis: Failed to cache bulk market data:",e),!1}}async function d(){return await c("market_data:bulk")||[]}async function p(e){return c(`market_data:${e}`)}async function R(e="market_data:*"){try{let t=await i();if(!t)return 0;let n=await t.keys(e);if(0===n.length)return 0;let r=await t.del(n);return console.log(`🧹 Redis: Cleared ${r} entries matching ${e}`),r}catch(e){return console.error(`❌ Redis: Failed to clear cache:`,e),0}}async function h(){let e=await i();if(!e)return[];let t=await e.keys("market_data:*");return t.length?(await e.mGet(t)).map(e=>e&&JSON.parse(e)?.data).filter(Boolean):[]}}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[276,972],()=>n(2593));module.exports=r})();