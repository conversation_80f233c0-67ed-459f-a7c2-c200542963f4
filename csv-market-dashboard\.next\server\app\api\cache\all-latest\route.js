/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/cache/all-latest/route";
exports.ids = ["app/api/cache/all-latest/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcache%2Fall-latest%2Froute&page=%2Fapi%2Fcache%2Fall-latest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcache%2Fall-latest%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcache%2Fall-latest%2Froute&page=%2Fapi%2Fcache%2Fall-latest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcache%2Fall-latest%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_love_dashboard_csv_market_dashboard_src_app_api_cache_all_latest_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/cache/all-latest/route.ts */ \"(rsc)/./src/app/api/cache/all-latest/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/cache/all-latest/route\",\n        pathname: \"/api/cache/all-latest\",\n        filename: \"route\",\n        bundlePath: \"app/api/cache/all-latest/route\"\n    },\n    resolvedPagePath: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\api\\\\cache\\\\all-latest\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_love_dashboard_csv_market_dashboard_src_app_api_cache_all_latest_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZjYWNoZSUyRmFsbC1sYXRlc3QlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmNhY2hlJTJGYWxsLWxhdGVzdCUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmNhY2hlJTJGYWxsLWxhdGVzdCUyRnJvdXRlLnRzJmFwcERpcj1EJTNBJTVDbG92ZSU1Q2Rhc2hib2FyZCU1Q2Nzdi1tYXJrZXQtZGFzaGJvYXJkJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1EJTNBJTVDbG92ZSU1Q2Rhc2hib2FyZCU1Q2Nzdi1tYXJrZXQtZGFzaGJvYXJkJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUNxQztBQUNsSDtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiRDpcXFxcbG92ZVxcXFxkYXNoYm9hcmRcXFxcY3N2LW1hcmtldC1kYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcY2FjaGVcXFxcYWxsLWxhdGVzdFxcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvY2FjaGUvYWxsLWxhdGVzdC9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2NhY2hlL2FsbC1sYXRlc3RcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2NhY2hlL2FsbC1sYXRlc3Qvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJEOlxcXFxsb3ZlXFxcXGRhc2hib2FyZFxcXFxjc3YtbWFya2V0LWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxjYWNoZVxcXFxhbGwtbGF0ZXN0XFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcache%2Fall-latest%2Froute&page=%2Fapi%2Fcache%2Fall-latest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcache%2Fall-latest%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/cache/all-latest/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/cache/all-latest/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_redis_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/redis-client */ \"(rsc)/./src/lib/redis-client.ts\");\n\n\nasync function GET() {\n    try {\n        const data = await (0,_lib_redis_client__WEBPACK_IMPORTED_MODULE_1__.getAllLatestTicks)();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9jYWNoZS9hbGwtbGF0ZXN0L3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF3RDtBQUNEO0FBRWhELGVBQWVFO0lBQ3BCLElBQUk7UUFDRixNQUFNQyxPQUFPLE1BQU1GLG9FQUFpQkE7UUFDcEMsT0FBT0QscURBQVlBLENBQUNJLElBQUksQ0FBQztZQUFFQyxTQUFTO1lBQU1GO1FBQUs7SUFDakQsRUFBRSxPQUFPRyxPQUFZO1FBQ25CLE9BQU9OLHFEQUFZQSxDQUFDSSxJQUFJLENBQUM7WUFBRUMsU0FBUztZQUFPQyxPQUFPQSxNQUFNQyxPQUFPO1FBQUMsR0FBRztZQUFFQyxRQUFRO1FBQUk7SUFDbkY7QUFDRiIsInNvdXJjZXMiOlsiRDpcXGxvdmVcXGRhc2hib2FyZFxcY3N2LW1hcmtldC1kYXNoYm9hcmRcXHNyY1xcYXBwXFxhcGlcXGNhY2hlXFxhbGwtbGF0ZXN0XFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xyXG5pbXBvcnQgeyBnZXRBbGxMYXRlc3RUaWNrcyB9IGZyb20gJ0AvbGliL3JlZGlzLWNsaWVudCc7XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKCkge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCBkYXRhID0gYXdhaXQgZ2V0QWxsTGF0ZXN0VGlja3MoKTtcclxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IHN1Y2Nlc3M6IHRydWUsIGRhdGEgfSk7XHJcbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBlcnJvci5tZXNzYWdlIH0sIHsgc3RhdHVzOiA1MDAgfSk7XHJcbiAgfVxyXG59ICJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJnZXRBbGxMYXRlc3RUaWNrcyIsIkdFVCIsImRhdGEiLCJqc29uIiwic3VjY2VzcyIsImVycm9yIiwibWVzc2FnZSIsInN0YXR1cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/cache/all-latest/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/redis-client.ts":
/*!*********************************!*\
  !*** ./src/lib/redis-client.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cacheBulkMarketData: () => (/* binding */ cacheBulkMarketData),\n/* harmony export */   cacheMarketData: () => (/* binding */ cacheMarketData),\n/* harmony export */   cacheMarketDataEntry: () => (/* binding */ cacheMarketDataEntry),\n/* harmony export */   clearCache: () => (/* binding */ clearCache),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   disconnectRedis: () => (/* binding */ disconnectRedis),\n/* harmony export */   getAllLatestTicks: () => (/* binding */ getAllLatestTicks),\n/* harmony export */   getBulkMarketData: () => (/* binding */ getBulkMarketData),\n/* harmony export */   getCacheStats: () => (/* binding */ getCacheStats),\n/* harmony export */   getCachedMarketData: () => (/* binding */ getCachedMarketData),\n/* harmony export */   getMarketDataEntry: () => (/* binding */ getMarketDataEntry),\n/* harmony export */   getRedisClient: () => (/* binding */ getRedisClient),\n/* harmony export */   redisCacheHelpers: () => (/* binding */ redisCacheHelpers),\n/* harmony export */   removeFromCache: () => (/* binding */ removeFromCache)\n/* harmony export */ });\n/* harmony import */ var redis__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! redis */ \"redis\");\n/* harmony import */ var redis__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(redis__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * Enhanced Redis Client for Next.js - Market Data Caching\r\n * Features:\r\n * - Automatic connection management\r\n * - Connection pooling\r\n * - Error recovery\r\n * - Performance monitoring\r\n * - Configurable TTL and compression\r\n */ \n// Global Redis client instance\nlet redis = null;\nlet connectionPromise = null;\nlet reconnectAttempts = 0;\nconst MAX_RECONNECT_ATTEMPTS = 5;\nconst RECONNECT_DELAY = 1000; // Base delay in ms\n// Redis configuration\nconst REDIS_CONFIG = {\n    maxRetries: 3,\n    retryDelayOnFailover: 100,\n    enableReadyCheck: false,\n    maxRetriesPerRequest: 3,\n    lazyConnect: true,\n    keepAlive: 30000,\n    connectTimeout: 10000,\n    commandTimeout: 5000\n};\n/**\r\n * Get Redis URL from environment with fallback\r\n */ function getRedisUrl() {\n    // Try environment variable first\n    if (process.env.REDIS_URL) {\n        return process.env.REDIS_URL;\n    }\n    // Check if we're in development mode\n    if (true) {\n        console.warn('⚠️ Redis: No REDIS_URL found. Running in development mode without Redis.');\n        return null;\n    }\n    // In production, we need Redis\n    console.error('❌ Redis: REDIS_URL environment variable is required in production');\n    return null;\n}\n/**\r\n * Get or create Redis client instance with enhanced connection management\r\n */ async function getRedisClient() {\n    // Return existing connection if available\n    if (redis && redis.isOpen) {\n        return redis;\n    }\n    // Return existing connection promise if connecting\n    if (connectionPromise) {\n        return connectionPromise;\n    }\n    // Create new connection promise\n    connectionPromise = createRedisConnection();\n    try {\n        redis = await connectionPromise;\n        connectionPromise = null;\n        return redis;\n    } catch (error) {\n        connectionPromise = null;\n        throw error;\n    }\n}\n/**\r\n * Create new Redis connection with enhanced error handling\r\n */ async function createRedisConnection() {\n    try {\n        // Skip Redis connection during build phase\n        if (false) {}\n        // Skip Redis connection during static generation\n        if ( true && !process.env.RUNTIME) {\n            console.log('🔗 Redis: Skipping Redis connection during static generation');\n            return null;\n        }\n        const redisUrl = getRedisUrl();\n        // If no Redis URL, return null (development mode)\n        if (!redisUrl) {\n            console.log('🔗 Redis: No Redis URL provided, skipping Redis connection');\n            return null;\n        }\n        console.log('🔗 Redis: Connecting to Redis...');\n        console.log('🔗 Redis: URL:', redisUrl.replace(/\\/\\/.*@/, '//***@')); // Hide credentials in logs\n        redis = (0,redis__WEBPACK_IMPORTED_MODULE_0__.createClient)({\n            url: redisUrl,\n            socket: {\n                connectTimeout: 5000,\n                reconnectStrategy: (retries)=>{\n                    if (retries > 3) {\n                        console.error('❌ Redis: Max reconnection attempts reached');\n                        return false;\n                    }\n                    return Math.min(retries * 100, 3000);\n                }\n            }\n        });\n        redis.on('error', (err)=>{\n            console.error('❌ Redis Client Error:', err);\n        });\n        redis.on('connect', ()=>{\n            console.log('🔗 Redis Client Connected');\n        });\n        redis.on('ready', ()=>{\n            console.log('✅ Redis Client Ready');\n        });\n        redis.on('end', ()=>{\n            console.log('🔌 Redis Client Disconnected');\n        });\n        await redis.connect();\n        console.log('🚀 Redis Cache Manager initialized');\n        return redis;\n    } catch (error) {\n        console.error('❌ Failed to connect to Redis:', error);\n        redis = null;\n        return null;\n    }\n}\n/**\r\n * Cache market data to Redis\r\n */ async function cacheMarketData(key, data, ttlSeconds = 600) {\n    try {\n        const client = await getRedisClient();\n        if (!client) {\n            return false;\n        }\n        const serializedData = JSON.stringify({\n            data,\n            timestamp: Date.now(),\n            version: '1.0.0'\n        });\n        await client.setEx(key, ttlSeconds, serializedData);\n        console.log(`💾 Redis: Cached ${key} (${serializedData.length} bytes, TTL: ${ttlSeconds}s)`);\n        return true;\n    } catch (error) {\n        console.error(`❌ Redis: Failed to cache ${key}:`, error);\n        return false;\n    }\n}\n/**\r\n * Get market data from Redis\r\n */ async function getCachedMarketData(key) {\n    try {\n        const client = await getRedisClient();\n        if (!client) {\n            return null;\n        }\n        const serializedData = await client.get(key);\n        if (!serializedData) {\n            return null;\n        }\n        const parsed = JSON.parse(serializedData);\n        console.log(`📖 Redis: Retrieved ${key} from cache`);\n        return parsed.data;\n    } catch (error) {\n        console.error(`❌ Redis: Failed to retrieve ${key}:`, error);\n        return null;\n    }\n}\n/**\r\n * Cache bulk market data efficiently\r\n */ async function cacheBulkMarketData(data, ttlSeconds = 600) {\n    try {\n        const client = await getRedisClient();\n        if (!client) {\n            return false;\n        }\n        // Store bulk data\n        const bulkSuccess = await cacheMarketData('market_data:bulk', data, ttlSeconds);\n        // Also store individual entries for quick access using pipeline\n        const pipeline = client.multi();\n        const timestamp = Date.now();\n        data.forEach((entry)=>{\n            const key = `market_data:${entry.securityId}`;\n            const entryData = JSON.stringify({\n                data: entry,\n                timestamp,\n                version: '1.0.0'\n            });\n            pipeline.setEx(key, ttlSeconds, entryData);\n        });\n        await pipeline.exec();\n        console.log(`💾 Redis: Cached ${data.length} individual market data entries`);\n        return bulkSuccess;\n    } catch (error) {\n        console.error('❌ Redis: Failed to cache bulk market data:', error);\n        return false;\n    }\n}\n/**\r\n * Get bulk market data from Redis\r\n */ async function getBulkMarketData() {\n    const data = await getCachedMarketData('market_data:bulk');\n    return data || [];\n}\n/**\r\n * Cache individual market data entry\r\n */ async function cacheMarketDataEntry(securityId, data, ttlSeconds = 600) {\n    return cacheMarketData(`market_data:${securityId}`, data, ttlSeconds);\n}\n/**\r\n * Get individual market data entry\r\n */ async function getMarketDataEntry(securityId) {\n    return getCachedMarketData(`market_data:${securityId}`);\n}\n/**\r\n * Remove data from Redis\r\n */ async function removeFromCache(key) {\n    try {\n        const client = await getRedisClient();\n        if (!client) {\n            return false;\n        }\n        const result = await client.del(key);\n        console.log(`🗑️ Redis: Removed ${key}`);\n        return result > 0;\n    } catch (error) {\n        console.error(`❌ Redis: Failed to remove ${key}:`, error);\n        return false;\n    }\n}\n/**\r\n * Clear all cache entries with pattern\r\n */ async function clearCache(pattern = 'market_data:*') {\n    try {\n        const client = await getRedisClient();\n        if (!client) {\n            return 0;\n        }\n        const keys = await client.keys(pattern);\n        if (keys.length === 0) {\n            return 0;\n        }\n        const result = await client.del(keys);\n        console.log(`🧹 Redis: Cleared ${result} entries matching ${pattern}`);\n        return result;\n    } catch (error) {\n        console.error(`❌ Redis: Failed to clear cache:`, error);\n        return 0;\n    }\n}\n/**\r\n * Get Redis cache statistics\r\n */ async function getCacheStats() {\n    try {\n        const client = await getRedisClient();\n        if (!client) {\n            return {\n                totalKeys: 0,\n                memoryUsage: '0B',\n                connectedClients: 0,\n                uptime: 0\n            };\n        }\n        const info = await client.info();\n        const dbSize = await client.dbSize();\n        // Parse Redis info\n        const lines = info.split('\\r\\n');\n        const stats = {};\n        lines.forEach((line)=>{\n            if (line.includes(':')) {\n                const [key, value] = line.split(':');\n                stats[key] = value;\n            }\n        });\n        return {\n            totalKeys: dbSize,\n            memoryUsage: stats.used_memory_human || '0B',\n            connectedClients: parseInt(stats.connected_clients) || 0,\n            uptime: parseInt(stats.uptime_in_seconds) || 0\n        };\n    } catch (error) {\n        console.error('❌ Redis: Failed to get stats:', error);\n        return {\n            totalKeys: 0,\n            memoryUsage: '0B',\n            connectedClients: 0,\n            uptime: 0\n        };\n    }\n}\n/**\r\n * Disconnect from Redis\r\n */ async function disconnectRedis() {\n    try {\n        if (redis) {\n            await redis.quit();\n            redis = null;\n            console.log('👋 Redis Cache Manager disconnected');\n        }\n    } catch (error) {\n        console.error('❌ Error disconnecting from Redis:', error);\n    }\n}\n/**\r\n * Get all latest ticks from Redis\r\n */ async function getAllLatestTicks() {\n    const client = await getRedisClient();\n    if (!client) return [];\n    const keys = await client.keys('market_data:*');\n    if (!keys.length) return [];\n    const results = await client.mGet(keys);\n    return results.map((str)=>str && JSON.parse(str)?.data).filter(Boolean);\n}\n// Helper functions for backward compatibility\nconst redisCacheHelpers = {\n    cacheMarketData,\n    getCachedMarketData,\n    cacheBulkMarketData,\n    getBulkMarketData,\n    cacheStaticData: (key, data)=>cacheMarketData(key, data, 1800)\n};\nconst redisClient = {\n    getRedisClient,\n    cacheMarketData,\n    getCachedMarketData,\n    cacheBulkMarketData,\n    getBulkMarketData,\n    cacheMarketDataEntry,\n    getMarketDataEntry,\n    removeFromCache,\n    clearCache,\n    getCacheStats,\n    disconnectRedis,\n    redisCacheHelpers,\n    getAllLatestTicks\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (redisClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/redis-client.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "redis":
/*!************************!*\
  !*** external "redis" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("redis");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcache%2Fall-latest%2Froute&page=%2Fapi%2Fcache%2Fall-latest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcache%2Fall-latest%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();