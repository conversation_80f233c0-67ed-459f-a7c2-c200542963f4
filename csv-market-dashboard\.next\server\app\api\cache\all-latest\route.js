(()=>{var e={};e.id=700,e.ids=[700],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1457:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>u,serverHooks:()=>R,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var n={};r.r(n),r.d(n,{GET:()=>l});var a=r(6559),o=r(8088),s=r(7719),i=r(2190),c=r(7155);async function l(){try{let e=await (0,c.vX)();return i.NextResponse.json({success:!0,data:e})}catch(e){return i.NextResponse.json({success:!1,error:e.message},{status:500})}}let u=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/cache/all-latest/route",pathname:"/api/cache/all-latest",filename:"route",bundlePath:"app/api/cache/all-latest/route"},resolvedPagePath:"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\api\\cache\\all-latest\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:R}=u;function g(){return(0,s.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},7155:(e,t,r)=>{"use strict";r.d(t,{No:()=>u,bt:()=>c,IL:()=>R,vX:()=>g,vI:()=>d,T7:()=>p});let n=require("redis"),a=null,o=null;async function s(){if(a&&a.isOpen)return a;if(o)return o;o=i();try{return a=await o,o=null,a}catch(e){throw o=null,e}}async function i(){try{if("phase-production-build"===process.env.NEXT_PHASE)return console.log("\uD83D\uDD17 Redis: Skipping Redis connection during build phase"),null;if(!process.env.RUNTIME)return console.log("\uD83D\uDD17 Redis: Skipping Redis connection during static generation"),null;let e=process.env.REDIS_URL?process.env.REDIS_URL:(console.error("❌ Redis: REDIS_URL environment variable is required in production"),null);if(!e)return console.log("\uD83D\uDD17 Redis: No Redis URL provided, skipping Redis connection"),null;return console.log("\uD83D\uDD17 Redis: Connecting to Redis..."),console.log("\uD83D\uDD17 Redis: URL:",e.replace(/\/\/.*@/,"//***@")),(a=(0,n.createClient)({url:e,socket:{connectTimeout:5e3,reconnectStrategy:e=>e>3?(console.error("❌ Redis: Max reconnection attempts reached"),!1):Math.min(100*e,3e3)}})).on("error",e=>{console.error("❌ Redis Client Error:",e)}),a.on("connect",()=>{console.log("\uD83D\uDD17 Redis Client Connected")}),a.on("ready",()=>{console.log("✅ Redis Client Ready")}),a.on("end",()=>{console.log("\uD83D\uDD0C Redis Client Disconnected")}),await a.connect(),console.log("\uD83D\uDE80 Redis Cache Manager initialized"),a}catch(e){return console.error("❌ Failed to connect to Redis:",e),a=null,null}}async function c(e,t,r=600){try{let n=await s();if(!n)return!1;let a=JSON.stringify({data:t,timestamp:Date.now(),version:"1.0.0"});return await n.setEx(e,r,a),console.log(`💾 Redis: Cached ${e} (${a.length} bytes, TTL: ${r}s)`),!0}catch(t){return console.error(`❌ Redis: Failed to cache ${e}:`,t),!1}}async function l(e){try{let t=await s();if(!t)return null;let r=await t.get(e);if(!r)return null;let n=JSON.parse(r);return console.log(`📖 Redis: Retrieved ${e} from cache`),n.data}catch(t){return console.error(`❌ Redis: Failed to retrieve ${e}:`,t),null}}async function u(e,t=600){try{let r=await s();if(!r)return!1;let n=await c("market_data:bulk",e,t),a=r.multi(),o=Date.now();return e.forEach(e=>{let r=`market_data:${e.securityId}`,n=JSON.stringify({data:e,timestamp:o,version:"1.0.0"});a.setEx(r,t,n)}),await a.exec(),console.log(`💾 Redis: Cached ${e.length} individual market data entries`),n}catch(e){return console.error("❌ Redis: Failed to cache bulk market data:",e),!1}}async function d(){return await l("market_data:bulk")||[]}async function p(e){return l(`market_data:${e}`)}async function R(e="market_data:*"){try{let t=await s();if(!t)return 0;let r=await t.keys(e);if(0===r.length)return 0;let n=await t.del(r);return console.log(`🧹 Redis: Cleared ${n} entries matching ${e}`),n}catch(e){return console.error(`❌ Redis: Failed to clear cache:`,e),0}}async function g(){let e=await s();if(!e)return[];let t=await e.keys("market_data:*");return t.length?(await e.mGet(t)).map(e=>e&&JSON.parse(e)?.data).filter(Boolean):[]}},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,580],()=>r(1457));module.exports=n})();