/**
 * Enhanced Redis Client for Next.js - Market Data Caching
 * Features:
 * - Automatic connection management
 * - Connection pooling
 * - Error recovery
 * - Performance monitoring
 * - Configurable TTL and compression
 */

import { createClient, RedisClientType } from 'redis';
import { MarketData } from '@/types';

// Global Redis client instance
let redis: RedisClientType | null = null;
let connectionPromise: Promise<RedisClientType | null> | null = null;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_DELAY = 1000; // Base delay in ms

// Redis configuration
const REDIS_CONFIG = {
  maxRetries: 3,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  keepAlive: 30000,
  connectTimeout: 10000,
  commandTimeout: 5000,
};

/**
 * Get Redis URL from environment with fallback
 */
function getRedisUrl(): string | null {
  // Try environment variable first
  if (process.env.REDIS_URL) {
    return process.env.REDIS_URL;
  }
  
  // Check if we're in development mode
  if (process.env.NODE_ENV === 'development') {
    console.warn('⚠️ Redis: No REDIS_URL found. Running in development mode without Redis.');
    return null;
  }
  
  // In production, we need Redis
  console.error('❌ Redis: REDIS_URL environment variable is required in production');
  return null;
}

/**
 * Get or create Redis client instance with enhanced connection management
 */
export async function getRedisClient(): Promise<RedisClientType | null> {
  // Return existing connection if available
  if (redis && redis.isOpen) {
    return redis;
  }

  // Return existing connection promise if connecting
  if (connectionPromise) {
    return connectionPromise;
  }

  // Create new connection promise
  connectionPromise = createRedisConnection();
  
  try {
    redis = await connectionPromise;
    connectionPromise = null;
    return redis;
  } catch (error) {
    connectionPromise = null;
    throw error;
  }
}

/**
 * Create new Redis connection with enhanced error handling
 */
async function createRedisConnection(): Promise<RedisClientType | null> {
  try {
    // Skip Redis connection during build phase
    if (process.env.NODE_ENV === 'production' && process.env.NEXT_PHASE === 'phase-production-build') {
      console.log('🔗 Redis: Skipping Redis connection during build phase');
      return null;
    }

    // Skip Redis connection during static generation
    if (typeof window === 'undefined' && !process.env.RUNTIME) {
      console.log('🔗 Redis: Skipping Redis connection during static generation');
      return null;
    }

    const redisUrl = getRedisUrl();

    // If no Redis URL, return null (development mode)
    if (!redisUrl) {
      console.log('🔗 Redis: No Redis URL provided, skipping Redis connection');
      return null;
    }

    console.log('🔗 Redis: Connecting to Redis...');
    console.log('🔗 Redis: URL:', redisUrl.replace(/\/\/.*@/, '//***@')); // Hide credentials in logs

    redis = createClient({
      url: redisUrl,
      socket: {
        connectTimeout: 5000,
        reconnectStrategy: (retries) => {
          if (retries > 3) {
            console.error('❌ Redis: Max reconnection attempts reached');
            return false;
          }
          return Math.min(retries * 100, 3000);
        }
      }
    });

    redis.on('error', (err) => {
      console.error('❌ Redis Client Error:', err);
    });

    redis.on('connect', () => {
      console.log('🔗 Redis Client Connected');
    });

    redis.on('ready', () => {
      console.log('✅ Redis Client Ready');
    });

    redis.on('end', () => {
      console.log('🔌 Redis Client Disconnected');
    });

    await redis.connect();
    console.log('🚀 Redis Cache Manager initialized');
    return redis;
  } catch (error) {
    console.error('❌ Failed to connect to Redis:', error);
    redis = null;
    return null;
  }
}

/**
 * Cache market data to Redis
 */
export async function cacheMarketData(key: string, data: MarketData | MarketData[], ttlSeconds = 600): Promise<boolean> {
  try {
    const client = await getRedisClient();
    if (!client) {
      return false;
    }

    const serializedData = JSON.stringify({
      data,
      timestamp: Date.now(),
      version: '1.0.0'
    });

    await client.setEx(key, ttlSeconds, serializedData);
    console.log(`💾 Redis: Cached ${key} (${serializedData.length} bytes, TTL: ${ttlSeconds}s)`);
    return true;
  } catch (error) {
    console.error(`❌ Redis: Failed to cache ${key}:`, error);
    return false;
  }
}

/**
 * Get market data from Redis
 */
export async function getCachedMarketData<T = MarketData | MarketData[]>(key: string): Promise<T | null> {
  try {
    const client = await getRedisClient();
    if (!client) {
      return null;
    }

    const serializedData = await client.get(key);
    if (!serializedData) {
      return null;
    }

    const parsed = JSON.parse(serializedData);
    console.log(`📖 Redis: Retrieved ${key} from cache`);
    return parsed.data as T;
  } catch (error) {
    console.error(`❌ Redis: Failed to retrieve ${key}:`, error);
    return null;
  }
}

/**
 * Cache bulk market data efficiently
 */
export async function cacheBulkMarketData(data: MarketData[], ttlSeconds = 600): Promise<boolean> {
  try {
    const client = await getRedisClient();
    if (!client) {
      return false;
    }

    // Store bulk data
    const bulkSuccess = await cacheMarketData('market_data:bulk', data, ttlSeconds);
    
    // Also store individual entries for quick access using pipeline
    const pipeline = client.multi();
    const timestamp = Date.now();

    data.forEach(entry => {
      const key = `market_data:${entry.securityId}`;
      const entryData = JSON.stringify({
        data: entry,
        timestamp,
        version: '1.0.0'
      });
      pipeline.setEx(key, ttlSeconds, entryData);
    });

    await pipeline.exec();
    console.log(`💾 Redis: Cached ${data.length} individual market data entries`);
    
    return bulkSuccess;
  } catch (error) {
    console.error('❌ Redis: Failed to cache bulk market data:', error);
    return false;
  }
}

/**
 * Get bulk market data from Redis
 */
export async function getBulkMarketData(): Promise<MarketData[]> {
  const data = await getCachedMarketData<MarketData[]>('market_data:bulk');
  return data || [];
}

/**
 * Cache individual market data entry
 */
export async function cacheMarketDataEntry(securityId: string, data: MarketData, ttlSeconds = 600): Promise<boolean> {
  return cacheMarketData(`market_data:${securityId}`, data, ttlSeconds);
}

/**
 * Get individual market data entry
 */
export async function getMarketDataEntry(securityId: string): Promise<MarketData | null> {
  return getCachedMarketData<MarketData>(`market_data:${securityId}`);
}

/**
 * Remove data from Redis
 */
export async function removeFromCache(key: string): Promise<boolean> {
  try {
    const client = await getRedisClient();
    if (!client) {
      return false;
    }

    const result = await client.del(key);
    console.log(`🗑️ Redis: Removed ${key}`);
    return result > 0;
  } catch (error) {
    console.error(`❌ Redis: Failed to remove ${key}:`, error);
    return false;
  }
}

/**
 * Clear all cache entries with pattern
 */
export async function clearCache(pattern = 'market_data:*'): Promise<number> {
  try {
    const client = await getRedisClient();
    if (!client) {
      return 0;
    }

    const keys = await client.keys(pattern);
    if (keys.length === 0) {
      return 0;
    }

    const result = await client.del(keys);
    console.log(`🧹 Redis: Cleared ${result} entries matching ${pattern}`);
    return result;
  } catch (error) {
    console.error(`❌ Redis: Failed to clear cache:`, error);
    return 0;
  }
}

/**
 * Get Redis cache statistics
 */
export async function getCacheStats(): Promise<{
  totalKeys: number;
  memoryUsage: string;
  connectedClients: number;
  uptime: number;
}> {
  try {
    const client = await getRedisClient();
    if (!client) {
      return {
        totalKeys: 0,
        memoryUsage: '0B',
        connectedClients: 0,
        uptime: 0
      };
    }

    const info = await client.info();
    const dbSize = await client.dbSize();
    
    // Parse Redis info
    const lines = info.split('\r\n');
    const stats: any = {};
    
    lines.forEach((line: string) => {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        stats[key] = value;
      }
    });

    return {
      totalKeys: dbSize,
      memoryUsage: stats.used_memory_human || '0B',
      connectedClients: parseInt(stats.connected_clients) || 0,
      uptime: parseInt(stats.uptime_in_seconds) || 0
    };
  } catch (error) {
    console.error('❌ Redis: Failed to get stats:', error);
    return {
      totalKeys: 0,
      memoryUsage: '0B',
      connectedClients: 0,
      uptime: 0
    };
  }
}

/**
 * Disconnect from Redis
 */
export async function disconnectRedis(): Promise<void> {
  try {
    if (redis) {
      await redis.quit();
      redis = null;
      console.log('👋 Redis Cache Manager disconnected');
    }
  } catch (error) {
    console.error('❌ Error disconnecting from Redis:', error);
  }
}

/**
 * Get all latest ticks from Redis
 */
export async function getAllLatestTicks(): Promise<MarketData[]> {
  const client = await getRedisClient();
  if (!client) return [];
  const keys = await client.keys('market_data:*');
  if (!keys.length) return [];
  const results = await client.mGet(keys);
  return results
    .map(str => str && JSON.parse(str)?.data)
    .filter(Boolean);
}

// Helper functions for backward compatibility
export const redisCacheHelpers = {
  cacheMarketData,
  getCachedMarketData,
  cacheBulkMarketData,
  getBulkMarketData,
  cacheStaticData: (key: string, data: any) => cacheMarketData(key, data, 1800), // 30 minutes
};

export default {
  getRedisClient,
  cacheMarketData,
  getCachedMarketData,
  cacheBulkMarketData,
  getBulkMarketData,
  cacheMarketDataEntry,
  getMarketDataEntry,
  removeFromCache,
  clearCache,
  getCacheStats,
  disconnectRedis,
  redisCacheHelpers,
  getAllLatestTicks
};
