const { PrismaClient } = require('../generated/prisma');
const prisma = new PrismaClient();

async function testDatabase() {
  try {
    console.log('🔍 Testing database...');
    
    // Check total count
    const total = await prisma.instrument.count();
    console.log(`📊 Total instruments: ${total}`);
    
    // Check NIFTY instruments
    const niftyInstruments = await prisma.instrument.findMany({
      where: {
        symbol: { contains: 'NIFTY' }
      },
      take: 10
    });
    
    console.log(`🎯 NIFTY instruments found: ${niftyInstruments.length}`);
    niftyInstruments.forEach(r => {
      console.log(`   - ${r.symbol} (${r.instrumentType}) - ID: ${r.securityId}`);
    });
    
    // Check OPTIDX count
    const optidxCount = await prisma.instrument.count({
      where: { instrumentType: 'OPTIDX' }
    });
    console.log(`📈 Total OPTIDX instruments: ${optidxCount}`);
    
    // Check sample OPTIDX instruments
    const sampleOptidx = await prisma.instrument.findMany({
      where: { instrumentType: 'OPTIDX' },
      take: 10
    });

    console.log(`📈 Sample OPTIDX instruments:`);
    sampleOptidx.forEach(r => {
      console.log(`   - ${r.symbol} ${r.strikePrice} ${r.optionType} (${r.expiryDate}) - ID: ${r.securityId}`);
    });

    // Check specific NIFTY options
    const niftyOptions = await prisma.instrument.findMany({
      where: {
        AND: [
          { symbol: { contains: 'NIFTY' } },
          { instrumentType: 'OPTIDX' }
        ]
      },
      take: 5
    });

    console.log(`🎯 NIFTY OPTIDX found: ${niftyOptions.length}`);
    niftyOptions.forEach(r => {
      console.log(`   - ${r.symbol} ${r.strikePrice} ${r.optionType} (${r.expiryDate})`);
    });
    
    // Check all instrument types
    const instrumentTypes = await prisma.instrument.groupBy({
      by: ['instrumentType'],
      _count: { instrumentType: true }
    });
    
    console.log('\n📊 Instrument types:');
    instrumentTypes.forEach(type => {
      console.log(`   ${type.instrumentType}: ${type._count.instrumentType}`);
    });
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDatabase();
