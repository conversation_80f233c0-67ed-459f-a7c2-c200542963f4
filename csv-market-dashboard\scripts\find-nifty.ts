// ============================================================================
// FIND NIFTY SPOT SCRIPT - Search for NIFTY spot instrument in CSV
// ============================================================================

import { CSVService } from '../src/services/CSVService';

async function main() {
  console.log('🔍 Searching for NIFTY Spot Instrument');
  console.log('=' .repeat(50));

  try {
    // Initialize CSV service
    const csvService = new CSVService('./instruments.csv');

    // Load instruments
    console.log('\n📊 Loading instruments from CSV...');
    const instruments = await csvService.loadInstruments();
    console.log(`✅ Loaded ${instruments.length} instruments`);

    // Search for NIFTY instruments
    console.log('\n🎯 Searching for NIFTY instruments...');
    
    // Search by symbol containing NIFTY
    const niftyInstruments = instruments.filter(inst => 
      inst.symbol.includes('NIFTY') && !inst.symbol.includes('-')
    );

    console.log(`Found ${niftyInstruments.length} NIFTY instruments without dashes:`);
    niftyInstruments.forEach((inst, index) => {
      console.log(`${index + 1}. ${inst.symbol} (${inst.exchange}) - ${inst.displayName}`);
      console.log(`   Security ID: ${inst.securityId}`);
      console.log(`   Type: ${inst.instrumentType}, Segment: ${inst.segment}`);
      console.log('');
    });

    // Search specifically for security ID 13
    console.log('\n🎯 Searching for Security ID 13...');
    const securityId13 = instruments.find(inst => inst.securityId === '13');
    if (securityId13) {
      console.log('✅ Found Security ID 13:');
      console.log(`   Symbol: ${securityId13.symbol}`);
      console.log(`   Exchange: ${securityId13.exchange}`);
      console.log(`   Type: ${securityId13.instrumentType}`);
      console.log(`   Display Name: ${securityId13.displayName}`);
      console.log(`   Segment: ${securityId13.segment}`);
      console.log(`   Exchange Code: ${securityId13.exchangeCode}`);
      console.log(`   Is Active: ${securityId13.isActive}`);
    } else {
      console.log('❌ Security ID 13 not found');
    }

    // Test NSE derivatives and index filtering
    console.log('\n🎯 Testing NSE derivatives and index filtering...');
    const nseDerivatives = await csvService.getNSEDerivativesAndIndex();
    console.log(`Found ${nseDerivatives.length} NSE derivatives and index instruments`);

    const niftyInNSEDerivatives = nseDerivatives.find(inst => inst.securityId === '13');
    if (niftyInNSEDerivatives) {
      console.log('✅ NIFTY spot (ID 13) is included in NSE derivatives filter');
    } else {
      console.log('❌ NIFTY spot (ID 13) is NOT included in NSE derivatives filter');
    }

    // Search for INDEX instruments
    console.log('\n📊 Searching for INDEX instruments...');
    const indexInstruments = instruments.filter(inst => 
      inst.instrumentType === 'INDEX'
    );

    console.log(`Found ${indexInstruments.length} INDEX instruments:`);
    indexInstruments.slice(0, 10).forEach((inst, index) => {
      console.log(`${index + 1}. ${inst.symbol} (${inst.exchange}) - ${inst.displayName}`);
      console.log(`   Security ID: ${inst.securityId}`);
      console.log(`   Segment: ${inst.segment}`);
      console.log('');
    });

    // Search for IDX_I exchange
    console.log('\n🏛️ Searching for IDX_I exchange instruments...');
    const idxInstruments = instruments.filter(inst => 
      inst.exchange === 'IDX_I'
    );

    console.log(`Found ${idxInstruments.length} IDX_I instruments:`);
    idxInstruments.slice(0, 10).forEach((inst, index) => {
      console.log(`${index + 1}. ${inst.symbol} (${inst.exchange}) - ${inst.displayName}`);
      console.log(`   Security ID: ${inst.securityId}`);
      console.log(`   Type: ${inst.instrumentType}`);
      console.log('');
    });

    console.log('\n✅ NIFTY search completed!');

  } catch (error) {
    console.error('❌ Error during NIFTY search:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}
