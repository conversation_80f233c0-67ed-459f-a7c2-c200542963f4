require('dotenv').config();
const fs = require('fs');

console.log('🔍 Debugging environment variables...');

// Check if variables are loaded
console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Found' : 'Not found');
console.log('DIRECT_URL:', process.env.DIRECT_URL ? 'Found' : 'Not found');

// Read .env file directly
const content = fs.readFileSync('.env', 'utf8');
const lines = content.split('\n');

console.log('\n📄 .env file content (last 10 lines):');
lines.slice(-10).forEach((line, i) => {
  const lineNum = lines.length - 10 + i + 1;
  console.log(`${lineNum}: "${line}" (${line.length} chars)`);
});

console.log('\n🔍 Lines containing DIRECT_URL:');
lines.forEach((line, i) => {
  if (line.includes('DIRECT_URL')) {
    console.log(`Line ${i+1}: "${line}"`);
  }
});
