require('dotenv').config({ path: '.env.database' });
const { Client } = require('pg');

async function checkTableStructure() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: { rejectUnauthorized: false }
  });
  
  try {
    await client.connect();
    console.log('✅ Connected to PostgreSQL');
    
    // Check table structure
    console.log('\n📊 Checking Instruments table structure...');
    const columns = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'Instruments'
      ORDER BY ordinal_position
    `);
    
    console.log('Table columns:');
    columns.rows.forEach(col => {
      console.log(`   ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(NOT NULL)' : '(NULLABLE)'}`);
    });
    
    // Check row count
    const countResult = await client.query('SELECT COUNT(*) as count FROM "Instruments"');
    console.log(`\n📈 Total records: ${countResult.rows[0].count}`);
    
    // Get sample data
    if (parseInt(countResult.rows[0].count) > 0) {
      console.log('\n📋 Sample records:');
      const sampleData = await client.query('SELECT * FROM "Instruments" LIMIT 5');
      sampleData.rows.forEach((row, i) => {
        console.log(`   Record ${i + 1}:`, Object.keys(row).slice(0, 5).map(key => `${key}: ${row[key]}`).join(', '));
      });
      
      // Check for NIFTY options
      const niftyCheck = await client.query(`
        SELECT COUNT(*) as count 
        FROM "Instruments" 
        WHERE "symbol" = 'NIFTY' OR "underlying_symbol" = 'NIFTY' OR "symbol_name" LIKE '%NIFTY%'
      `);
      console.log(`\n🎯 NIFTY-related records: ${niftyCheck.rows[0].count}`);
      
      // Check instrument types
      const instrumentTypes = await client.query(`
        SELECT "instrument_type", COUNT(*) as count 
        FROM "Instruments" 
        GROUP BY "instrument_type" 
        ORDER BY count DESC 
        LIMIT 10
      `);
      console.log('\n📊 Instrument types:');
      instrumentTypes.rows.forEach(type => {
        console.log(`   ${type.instrument_type}: ${type.count}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await client.end();
  }
}

checkTableStructure();
