/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/option-chain/page";
exports.ids = ["app/option-chain/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Foption-chain%2Fpage&page=%2Foption-chain%2Fpage&appPaths=%2Foption-chain%2Fpage&pagePath=private-next-app-dir%2Foption-chain%2Fpage.tsx&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Foption-chain%2Fpage&page=%2Foption-chain%2Fpage&appPaths=%2Foption-chain%2Fpage&pagePath=private-next-app-dir%2Foption-chain%2Fpage.tsx&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/option-chain/page.tsx */ \"(rsc)/./src/app/option-chain/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'option-chain',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/option-chain/page\",\n        pathname: \"/option-chain\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Foption-chain%2Fpage&page=%2Foption-chain%2Fpage&appPaths=%2Foption-chain%2Fpage&pagePath=private-next-app-dir%2Foption-chain%2Fpage.tsx&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNsb3ZlJTVDJTVDZGFzaGJvYXJkJTVDJTVDY3N2LW1hcmtldC1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBb0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGxvdmVcXFxcZGFzaGJvYXJkXFxcXGNzdi1tYXJrZXQtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Coption-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Coption-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/option-chain/page.tsx */ \"(rsc)/./src/app/option-chain/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNsb3ZlJTVDJTVDZGFzaGJvYXJkJTVDJTVDY3N2LW1hcmtldC1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNvcHRpb24tY2hhaW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEtBQWdIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxsb3ZlXFxcXGRhc2hib2FyZFxcXFxjc3YtbWFya2V0LWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXG9wdGlvbi1jaGFpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Coption-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/option-chain/page.tsx":
/*!***************************************!*\
  !*** ./src/app/option-chain/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\option-chain\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNsb3ZlJTVDJTVDZGFzaGJvYXJkJTVDJTVDY3N2LW1hcmtldC1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBb0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGxvdmVcXFxcZGFzaGJvYXJkXFxcXGNzdi1tYXJrZXQtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Coption-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Coption-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/option-chain/page.tsx */ \"(ssr)/./src/app/option-chain/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNsb3ZlJTVDJTVDZGFzaGJvYXJkJTVDJTVDY3N2LW1hcmtldC1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNvcHRpb24tY2hhaW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEtBQWdIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxsb3ZlXFxcXGRhc2hib2FyZFxcXFxjc3YtbWFya2V0LWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXG9wdGlvbi1jaGFpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Coption-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"146531be5b05\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcbG92ZVxcZGFzaGJvYXJkXFxjc3YtbWFya2V0LWRhc2hib2FyZFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMTQ2NTMxYmU1YjA1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var react_error_boundary__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-error-boundary */ \"(ssr)/./node_modules/react-error-boundary/dist/react-error-boundary.development.esm.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Error fallback component\nfunction ErrorFallback({ error, resetErrorBoundary }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg border border-red-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-600 text-lg\",\n                                children: \"⚠\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Application Error\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-4\",\n                    children: \"An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"cursor-pointer text-sm text-gray-500 hover:text-gray-700\",\n                            children: \"Technical Details\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto max-h-32\",\n                            children: error.message\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: resetErrorBoundary,\n                            className: \"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\",\n                            children: \"Try Again\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors\",\n                            children: \"Refresh Page\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"CSV Market Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Real-time market data dashboard with enhanced WebSocket management and data caching\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_error_boundary__WEBPACK_IMPORTED_MODULE_4__.ErrorBoundary, {\n                    FallbackComponent: ErrorFallback,\n                    onError: (error, errorInfo)=>{\n                        console.error('Application Error:', error, errorInfo);\n                    // Here you could send the error to an error reporting service\n                    },\n                    onReset: ()=>{\n                        // Clear any state that might be causing the error\n                        localStorage.removeItem('enhanced-market-data');\n                        localStorage.removeItem('enhanced-market-timestamp');\n                        sessionStorage.clear();\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20\",\n                        children: [\n                            children,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                                position: \"top-right\",\n                                toastOptions: {\n                                    duration: 4000,\n                                    style: {\n                                        background: '#363636',\n                                        color: '#fff'\n                                    },\n                                    success: {\n                                        style: {\n                                            background: '#10B981'\n                                        }\n                                    },\n                                    error: {\n                                        style: {\n                                            background: '#EF4444'\n                                        }\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/option-chain/page.tsx":
/*!***************************************!*\
  !*** ./src/app/option-chain/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OptionChainPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_OptionChain__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/OptionChain */ \"(ssr)/./src/components/OptionChain.tsx\");\n/* harmony import */ var _hooks_useMarketData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../hooks/useMarketData */ \"(ssr)/./src/hooks/useMarketData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction OptionChainPage() {\n    const { marketData } = (0,_hooks_useMarketData__WEBPACK_IMPORTED_MODULE_2__.useMarketData)();\n    // Convert array to Map for OptionChain component\n    const marketDataMap = new Map();\n    marketData.forEach((item)=>{\n        if (item.securityId) {\n            marketDataMap.set(item.securityId, item);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/\",\n                                    className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors\",\n                                    children: \"← Main Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/subscribed\",\n                                    className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors\",\n                                    children: \"\\uD83D\\uDCCA Subscribed Data\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        marketData.length,\n                                        \" instruments\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        marketData.filter((d)=>(d.ltp ?? 0) > 0).length,\n                                        \" active\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OptionChain__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                marketData: marketDataMap\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/option-chain/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/OptionChain.tsx":
/*!****************************************!*\
  !*** ./src/components/OptionChain.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OptionChain)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/data-cache */ \"(ssr)/./src/lib/data-cache.ts\");\n/* harmony import */ var _hooks_useNiftySpot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useNiftySpot */ \"(ssr)/./src/hooks/useNiftySpot.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction OptionChain({ marketData }) {\n    const [expiryData, setExpiryData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedExpiry, setSelectedExpiry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [optionChain, setOptionChain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [niftySpotPrice, setNiftySpotPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [cacheLoaded, setCacheLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 🚀 NEW: Use dedicated NIFTY spot calculator\n    const { ltp: niftyLTP, isConnected: niftyConnected, isReady: niftyReady, formattedChange: niftyChange, error: niftyError, refresh: refreshNiftySpot } = (0,_hooks_useNiftySpot__WEBPACK_IMPORTED_MODULE_4__.useNiftySpot)();\n    // Load cached data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptionChain.useEffect\": ()=>{\n            const loadCachedData = {\n                \"OptionChain.useEffect.loadCachedData\": async ()=>{\n                    try {\n                        console.log('📖 OptionChain: Loading cached data...');\n                        // Load cached option chain data\n                        const cachedOptionChain = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.getCachedStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.OPTION_CHAIN);\n                        if (cachedOptionChain && typeof cachedOptionChain === 'object' && 'rows' in cachedOptionChain) {\n                            setOptionChain(cachedOptionChain);\n                            console.log('✅ OptionChain: Loaded option chain from cache');\n                        }\n                        // Load cached expiry data\n                        const cachedExpiryData = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.getCachedStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.EXPIRY_DATES);\n                        if (cachedExpiryData && typeof cachedExpiryData === 'object' && 'expiries' in cachedExpiryData && Array.isArray(cachedExpiryData.expiries)) {\n                            setExpiryData(cachedExpiryData);\n                            if (cachedExpiryData.expiries.length > 0) {\n                                setSelectedExpiry(cachedExpiryData.expiries[0]);\n                            }\n                            console.log('✅ OptionChain: Loaded expiry data from cache');\n                        }\n                        // Load cached NIFTY spot price\n                        const cachedNiftySpot = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.getCachedMarketData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.NIFTY_SPOT);\n                        if (cachedNiftySpot && typeof cachedNiftySpot === 'object' && 'ltp' in cachedNiftySpot && typeof cachedNiftySpot.ltp === 'number') {\n                            setNiftySpotPrice(cachedNiftySpot.ltp);\n                            console.log('✅ OptionChain: Loaded NIFTY spot from cache:', cachedNiftySpot.ltp);\n                        }\n                        setCacheLoaded(true);\n                    } catch (error) {\n                        console.error('❌ OptionChain: Failed to load cached data:', error);\n                        setCacheLoaded(true);\n                    }\n                }\n            }[\"OptionChain.useEffect.loadCachedData\"];\n            loadCachedData();\n        }\n    }[\"OptionChain.useEffect\"], []);\n    // 🚀 NEW: Use dedicated NIFTY spot calculator as primary source\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptionChain.useEffect\": ()=>{\n            if (niftyReady && niftyLTP > 0) {\n                setNiftySpotPrice(niftyLTP);\n                console.log('[SPOT] 🎯 NIFTY Spot Price from Dedicated Calculator:', niftyLTP, niftyConnected ? '(Connected)' : '(Cached)');\n                return;\n            }\n            // Fallback 1: Direct lookup for NIFTY spot (security ID 13) from market data\n            const niftyData = marketData.get('13');\n            if (niftyData && niftyData.ltp > 0) {\n                setNiftySpotPrice(niftyData.ltp);\n                _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.cacheMarketData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.NIFTY_SPOT, niftyData);\n                console.log('[SPOT] 🎯 NIFTY Spot Price from Market Data (ID 13):', niftyData.ltp);\n                return;\n            }\n            // Fallback 2: Search for NIFTY index data with multiple criteria\n            const niftySearchCriteria = [\n                {\n                    securityId: '2',\n                    symbol: 'NIFTY'\n                },\n                {\n                    securityId: '5',\n                    symbol: 'NIFTYFUTM1'\n                },\n                {\n                    securityId: '14',\n                    symbol: 'NIFTYFUTM3'\n                } // NIFTY future M3\n            ];\n            for (const criteria of niftySearchCriteria){\n                const data = marketData.get(criteria.securityId);\n                if (data && data.ltp > 0) {\n                    setNiftySpotPrice(data.ltp);\n                    _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.cacheMarketData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.NIFTY_SPOT, data);\n                    console.log('[SPOT] 🎯 NIFTY Spot Price Found:', data.ltp, 'from', criteria.symbol, '(ID:', criteria.securityId, ')');\n                    return;\n                }\n            }\n            // Fallback 3: Search by symbol pattern\n            for (const [securityId, data] of Array.from(marketData.entries())){\n                if ((data.symbol === 'NIFTY' || data.symbol.includes('NIFTY')) && data.instrumentType === 'INDEX' && data.ltp > 0) {\n                    setNiftySpotPrice(data.ltp);\n                    _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.cacheMarketData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.NIFTY_SPOT, data);\n                    console.log('[SPOT] 🎯 NIFTY Spot Price Found (fallback):', data.ltp, 'from security:', securityId, 'symbol:', data.symbol);\n                    return;\n                }\n            }\n            // ✅ FIXED: Only use mock price as absolute last resort\n            if (niftySpotPrice === 0 && cacheLoaded) {\n                console.error('[SPOT] ❌ No NIFTY spot price available from live data or cache!');\n                console.log('[SPOT] 🔍 Available market data symbols:', Array.from(marketData.values()).slice(0, 10).map({\n                    \"OptionChain.useEffect\": (d)=>`${d.symbol} (${d.securityId})`\n                }[\"OptionChain.useEffect\"]));\n                // Try to fetch from API as fallback\n                fetch('/api/nifty-spot').then({\n                    \"OptionChain.useEffect\": (res)=>res.json()\n                }[\"OptionChain.useEffect\"]).then({\n                    \"OptionChain.useEffect\": (data)=>{\n                        if (data.success && data.data.ltp) {\n                            setNiftySpotPrice(data.data.ltp);\n                            console.log('[SPOT] ✅ Retrieved NIFTY spot from API:', data.data.ltp);\n                        } else {\n                            // Absolute last resort - use mock price\n                            const mockPrice = 24850;\n                            setNiftySpotPrice(mockPrice);\n                            console.warn('[SPOT] ⚠️ Using mock NIFTY Spot Price:', mockPrice, '(No live data available)');\n                        }\n                    }\n                }[\"OptionChain.useEffect\"]).catch({\n                    \"OptionChain.useEffect\": ()=>{\n                        const mockPrice = 24850;\n                        setNiftySpotPrice(mockPrice);\n                        console.warn('[SPOT] ⚠️ Using mock NIFTY Spot Price:', mockPrice, '(API fallback failed)');\n                    }\n                }[\"OptionChain.useEffect\"]);\n            }\n        }\n    }[\"OptionChain.useEffect\"], [\n        marketData,\n        cacheLoaded,\n        niftySpotPrice,\n        niftyReady,\n        niftyLTP,\n        niftyConnected\n    ]);\n    // Helper function for expiry formatting\n    const formatExpiryForSymbol = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"OptionChain.useCallback[formatExpiryForSymbol]\": (expiry)=>{\n            // Convert YYYY-MM-DD to format used in symbols (e.g., Jun2025)\n            const date = new Date(expiry);\n            const months = [\n                'Jan',\n                'Feb',\n                'Mar',\n                'Apr',\n                'May',\n                'Jun',\n                'Jul',\n                'Aug',\n                'Sep',\n                'Oct',\n                'Nov',\n                'Dec'\n            ];\n            return `${months[date.getMonth()]}${date.getFullYear()}`;\n        }\n    }[\"OptionChain.useCallback[formatExpiryForSymbol]\"], []);\n    // Helper functions defined first to avoid hoisting issues\n    const generateStrikes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"OptionChain.useCallback[generateStrikes]\": (spotPrice)=>{\n            // First, get all available strikes from market data for the selected expiry\n            const availableStrikes = new Set();\n            for (const [securityId, data] of Array.from(marketData.entries())){\n                if (data.symbol.includes('NIFTY') && data.symbol.includes(formatExpiryForSymbol(selectedExpiry))) {\n                    // Extract strike from symbol (e.g., NIFTY-Jun2025-24850-CE)\n                    const symbolParts = data.symbol.split('-');\n                    if (symbolParts.length >= 4) {\n                        const strike = parseFloat(symbolParts[2]);\n                        if (!isNaN(strike)) {\n                            availableStrikes.add(strike);\n                        }\n                    }\n                }\n            }\n            // Convert to sorted array\n            const allStrikes = Array.from(availableStrikes).sort({\n                \"OptionChain.useCallback[generateStrikes].allStrikes\": (a, b)=>a - b\n            }[\"OptionChain.useCallback[generateStrikes].allStrikes\"]);\n            if (allStrikes.length === 0) {\n                console.warn('[STRIKE] ⚠️ No strikes found for expiry:', selectedExpiry);\n                // Fallback: generate strikes around spot price\n                const strikes = [];\n                const baseStrike = Math.round(spotPrice / 50) * 50; // Round to nearest 50\n                for(let i = -12; i <= 12; i++){\n                    strikes.push(baseStrike + i * 50);\n                }\n                return strikes;\n            }\n            // Find ATM strike (closest to spot price)\n            const atmStrike = allStrikes.reduce({\n                \"OptionChain.useCallback[generateStrikes].atmStrike\": (prev, curr)=>Math.abs(curr - spotPrice) < Math.abs(prev - spotPrice) ? curr : prev\n            }[\"OptionChain.useCallback[generateStrikes].atmStrike\"]);\n            const atmIndex = allStrikes.indexOf(atmStrike);\n            // Select strikes around ATM (12 on each side)\n            const selectedStrikes = [\n                ...allStrikes.slice(Math.max(0, atmIndex - 12), atmIndex),\n                atmStrike,\n                ...allStrikes.slice(atmIndex + 1, atmIndex + 13)\n            ];\n            console.log(`[STRIKE] ✅ Selected ${selectedStrikes.length} strikes around ATM:`, selectedStrikes);\n            return selectedStrikes;\n        }\n    }[\"OptionChain.useCallback[generateStrikes]\"], [\n        selectedExpiry,\n        marketData,\n        formatExpiryForSymbol\n    ]);\n    const findOptionData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"OptionChain.useCallback[findOptionData]\": (strike, optionType)=>{\n            // ✅ FIXED: Match by exact expiry date from CSV SM_EXPIRY_DATE column\n            for (const [securityId, data] of Array.from(marketData.entries())){\n                // Check if this is a NIFTY option with matching criteria\n                if (data.symbol.includes('NIFTY-') && // Exact NIFTY options (not BANKNIFTY, etc.)\n                data.symbol.includes(`-${strike}-${optionType}`) && data.expiryDate === selectedExpiry) {\n                    console.log(`[OPTION] ✅ Found ${optionType} ${strike}: ${data.symbol} (Expiry: ${data.expiryDate})`);\n                    return {\n                        securityId,\n                        symbol: data.symbol,\n                        exchange: data.exchange,\n                        strikePrice: strike,\n                        optionType,\n                        expiryDate: selectedExpiry,\n                        ltp: data.ltp || 0,\n                        change: data.change || 0,\n                        changePercent: data.changePercent || 0,\n                        volume: data.volume || 0,\n                        openInterest: data.openInterest,\n                        bid: data.marketDepth?.[0]?.bidPrice || data.bid,\n                        ask: data.marketDepth?.[0]?.askPrice || data.ask,\n                        bidQty: data.marketDepth?.[0]?.bidQty || data.bidQty,\n                        askQty: data.marketDepth?.[0]?.askQty || data.askQty,\n                        high: data.high,\n                        low: data.low,\n                        open: data.open,\n                        close: data.close,\n                        timestamp: data.timestamp\n                    };\n                }\n            }\n            // Debug: Log when option not found\n            if (true) {\n                console.log(`[OPTION] ❌ Not found ${optionType} ${strike} for expiry ${selectedExpiry}`);\n                // Show available options for debugging\n                const availableOptions = Array.from(marketData.values()).filter({\n                    \"OptionChain.useCallback[findOptionData].availableOptions\": (data)=>data.symbol.includes('NIFTY-') && data.symbol.includes(`-${optionType}`)\n                }[\"OptionChain.useCallback[findOptionData].availableOptions\"]).slice(0, 5);\n                console.log(`[DEBUG] Available ${optionType} options:`, availableOptions.map({\n                    \"OptionChain.useCallback[findOptionData]\": (opt)=>`${opt.symbol} (Expiry: ${opt.expiryDate})`\n                }[\"OptionChain.useCallback[findOptionData]\"]));\n            }\n            return null;\n        }\n    }[\"OptionChain.useCallback[findOptionData]\"], [\n        selectedExpiry,\n        marketData\n    ]);\n    const buildOptionChain = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"OptionChain.useCallback[buildOptionChain]\": async ()=>{\n            if (!selectedExpiry || niftySpotPrice <= 0) return;\n            console.log('🔗 Building option chain for expiry:', selectedExpiry, 'spot:', niftySpotPrice);\n            setLoading(true);\n            try {\n                // 🚀 NEW: Fetch option chain data from database API\n                const response = await fetch(`/api/option-chain?expiry=${selectedExpiry}&symbol=NIFTY`);\n                if (response.ok) {\n                    const result = await response.json();\n                    if (result.success && result.data.rows) {\n                        console.log('✅ OptionChain: Loaded from database API:', result.data.totalOptions, 'options');\n                        // Convert API data to our format\n                        const optionChainData = {\n                            underlying: 'NIFTY',\n                            spotPrice: niftySpotPrice,\n                            expiry: selectedExpiry,\n                            rows: result.data.rows.map({\n                                \"OptionChain.useCallback[buildOptionChain]\": (row)=>({\n                                        strikePrice: row.strike,\n                                        call: row.CE ? {\n                                            securityId: row.CE.securityId,\n                                            symbol: row.CE.symbol,\n                                            exchange: 'NSE',\n                                            instrumentType: 'OPTIDX',\n                                            expiryDate: row.CE.expiryDate,\n                                            strikePrice: row.CE.strikePrice,\n                                            optionType: 'CE',\n                                            ltp: marketData.get(row.CE.securityId)?.ltp || 0,\n                                            bid: marketData.get(row.CE.securityId)?.bid || 0,\n                                            ask: marketData.get(row.CE.securityId)?.ask || 0,\n                                            volume: marketData.get(row.CE.securityId)?.volume || 0,\n                                            openInterest: marketData.get(row.CE.securityId)?.openInterest || 0,\n                                            change: marketData.get(row.CE.securityId)?.change || 0,\n                                            changePercent: marketData.get(row.CE.securityId)?.changePercent || 0\n                                        } : null,\n                                        put: row.PE ? {\n                                            securityId: row.PE.securityId,\n                                            symbol: row.PE.symbol,\n                                            exchange: 'NSE',\n                                            instrumentType: 'OPTIDX',\n                                            expiryDate: row.PE.expiryDate,\n                                            strikePrice: row.PE.strikePrice,\n                                            optionType: 'PE',\n                                            ltp: marketData.get(row.PE.securityId)?.ltp || 0,\n                                            bid: marketData.get(row.PE.securityId)?.bid || 0,\n                                            ask: marketData.get(row.PE.securityId)?.ask || 0,\n                                            volume: marketData.get(row.PE.securityId)?.volume || 0,\n                                            openInterest: marketData.get(row.PE.securityId)?.openInterest || 0,\n                                            change: marketData.get(row.PE.securityId)?.change || 0,\n                                            changePercent: marketData.get(row.PE.securityId)?.changePercent || 0\n                                        } : null\n                                    })\n                            }[\"OptionChain.useCallback[buildOptionChain]\"]),\n                            timestamp: Date.now()\n                        };\n                        setOptionChain(optionChainData);\n                        // Cache the option chain data\n                        _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.cacheStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.OPTION_CHAIN, optionChainData);\n                        console.log('💾 OptionChain: Cached option chain data from', result.data.source);\n                        setLoading(false);\n                        return;\n                    }\n                }\n                // Fallback to old method if API fails\n                console.warn('⚠️ Database API failed, using fallback method');\n                // Generate strike prices around spot price\n                const strikes = generateStrikes(niftySpotPrice);\n                // Build option chain rows using market data\n                const rows = strikes.map({\n                    \"OptionChain.useCallback[buildOptionChain].rows\": (strike)=>{\n                        const callData = findOptionData(strike, 'CE');\n                        const putData = findOptionData(strike, 'PE');\n                        return {\n                            strikePrice: strike,\n                            call: callData,\n                            put: putData\n                        };\n                    }\n                }[\"OptionChain.useCallback[buildOptionChain].rows\"]);\n                const optionChainData = {\n                    underlying: 'NIFTY',\n                    spotPrice: niftySpotPrice,\n                    expiry: selectedExpiry,\n                    rows,\n                    timestamp: Date.now()\n                };\n                setOptionChain(optionChainData);\n                // Cache the option chain data\n                _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.cacheStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.OPTION_CHAIN, optionChainData);\n                console.log('💾 OptionChain: Cached option chain data (fallback method)');\n            } catch (error) {\n                console.error('❌ Error building option chain:', error);\n                setError('Failed to load option chain data');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"OptionChain.useCallback[buildOptionChain]\"], [\n        selectedExpiry,\n        niftySpotPrice,\n        generateStrikes,\n        findOptionData,\n        marketData\n    ]);\n    // Fetch expiry dates on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptionChain.useEffect\": ()=>{\n            fetchExpiryDates();\n        }\n    }[\"OptionChain.useEffect\"], []);\n    // Build option chain when expiry is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptionChain.useEffect\": ()=>{\n            if (selectedExpiry && niftySpotPrice > 0) {\n                buildOptionChain();\n            }\n        }\n    }[\"OptionChain.useEffect\"], [\n        selectedExpiry,\n        niftySpotPrice,\n        buildOptionChain\n    ]);\n    const fetchExpiryDates = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Check cache first\n            const cachedExpiryData = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.getCachedStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.EXPIRY_DATES);\n            if (cachedExpiryData && typeof cachedExpiryData === 'object' && 'expiries' in cachedExpiryData && Array.isArray(cachedExpiryData.expiries)) {\n                console.log('✅ OptionChain: Using cached expiry data');\n                setExpiryData(cachedExpiryData);\n                if (cachedExpiryData.expiries.length > 0) {\n                    setSelectedExpiry(cachedExpiryData.expiries[0]);\n                }\n                setLoading(false);\n                return;\n            }\n            console.log('🌐 OptionChain: Fetching fresh expiry data from database API');\n            const response = await fetch('/api/option-chain', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    symbol: 'NIFTY'\n                })\n            });\n            if (!response.ok) {\n                throw new Error(`Failed to fetch expiry dates: ${response.statusText}`);\n            }\n            const result = await response.json();\n            if (result.success && result.data.expiries) {\n                const expiryData = {\n                    expiries: result.data.expiries,\n                    count: result.data.count,\n                    source: result.data.source\n                };\n                setExpiryData(expiryData);\n                // Cache the expiry data\n                await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.cacheStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.EXPIRY_DATES, expiryData);\n                console.log('💾 OptionChain: Cached expiry data from', result.data.source);\n                // Auto-select first expiry\n                if (result.data.expiries.length > 0) {\n                    setSelectedExpiry(result.data.expiries[0]);\n                }\n            } else {\n                throw new Error(result.error || 'Failed to fetch expiry dates');\n            }\n        } catch (err) {\n            console.error('❌ OptionChain: Error fetching expiry dates:', err);\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Use centralized formatters for consistency\n    const formatPrice = _lib_utils__WEBPACK_IMPORTED_MODULE_2__.MarketFormatters.price;\n    const formatNumber = _lib_utils__WEBPACK_IMPORTED_MODULE_2__.MarketFormatters.number;\n    // Use centralized formatters for consistency\n    const getChangeColor = (change)=>{\n        if (!change) return 'text-gray-400';\n        return change > 0 ? 'text-green-400' : change < 0 ? 'text-red-400' : 'text-gray-400';\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading option chain...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 459,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 458,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-red-800 font-medium\",\n                    children: \"Error Loading Option Chain\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 470,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 text-sm mt-1\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 471,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: fetchExpiryDates,\n                    className: \"mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700\",\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 472,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 469,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Options\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md\",\n                                            children: \"\\uD83D\\uDD0D NIFTY\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Strategy builder\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Class\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Volatility\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"By expiration | by strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: formatPrice(niftySpotPrice)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 15\n                                        }, this),\n                                        niftyReady && niftyConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-green-600 font-medium\",\n                                                    children: \"\\uD83D\\uDFE2 Live\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 19\n                                                }, this),\n                                                niftyChange && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-xs font-medium ${niftyChange.isPositive ? 'text-green-600' : 'text-red-600'}`,\n                                                    children: [\n                                                        niftyChange.points,\n                                                        \" (\",\n                                                        niftyChange.percent,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 17\n                                        }, this) : niftyError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-red-500\",\n                                            children: \"❌ Error\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 17\n                                        }, this) : niftySpotPrice === 24850 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-orange-500\",\n                                            children: \"⚠️ Mock\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-blue-500\",\n                                            children: \"\\uD83D\\uDCCA Market Data\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: refreshNiftySpot,\n                                            className: \"text-xs text-gray-400 hover:text-gray-600 ml-1\",\n                                            title: \"Refresh NIFTY Spot Price\",\n                                            children: \"\\uD83D\\uDD04\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 486,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 485,\n                columnNumber: 7\n            }, this),\n            expiryData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 overflow-x-auto pb-2\",\n                        children: expiryData.expiries.slice(0, 15).map((expiry, index)=>{\n                            const date = new Date(expiry);\n                            const isSelected = expiry === selectedExpiry;\n                            const isToday = date.toDateString() === new Date().toDateString();\n                            const isCurrentWeek = Math.abs(date.getTime() - new Date().getTime()) < 7 * 24 * 60 * 60 * 1000;\n                            // Enhanced date formatting\n                            const currentYear = new Date().getFullYear();\n                            const expiryYear = date.getFullYear();\n                            const monthShort = date.toLocaleDateString('en-US', {\n                                month: 'short'\n                            });\n                            const day = date.getDate();\n                            // Show year if different from current year\n                            const showYear = expiryYear !== currentYear;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedExpiry(expiry),\n                                className: `flex-shrink-0 px-3 py-2 text-xs font-medium transition-colors border ${isSelected ? 'bg-black text-white border-black' : isToday ? 'bg-orange-500 text-white border-orange-500' : isCurrentWeek ? 'bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center min-w-[40px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-normal text-gray-600\",\n                                            children: [\n                                                monthShort,\n                                                showYear && ` ${expiryYear.toString().slice(-2)}`\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-bold text-sm\",\n                                            children: day\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 19\n                                }, this)\n                            }, expiry, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 569,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 552,\n                        columnNumber: 11\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-xs text-gray-500 bg-gray-100 p-3 rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Selected Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        selectedExpiry\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" ₹\",\n                                        niftySpotPrice,\n                                        \" \",\n                                        niftySpotPrice === 24850 ? '(Mock)' : '(Live)'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Available Strikes:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        optionChain?.rows.length || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Market Data Size:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        marketData.size,\n                                        \" instruments\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Options:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes('NIFTY-')).length,\n                                        \" found\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 603,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Options with Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes('NIFTY-') && d.expiryDate).length,\n                                        \" matched\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 598,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 597,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 551,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-50 border-b border-gray-200 text-xs font-medium text-gray-700 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 font-bold\",\n                                    children: \"Calls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold\",\n                                    children: \"Strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-600 font-bold\",\n                                    children: \"Puts\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 625,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 615,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-100 border-b border-gray-200 text-xs font-medium text-gray-600 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 633,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 635,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 636,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 637,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1 border-r border-gray-200\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-2 px-1 border-r border-gray-200 font-bold\",\n                                children: \"Strike\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 641,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 644,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 645,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 647,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 648,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 649,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 631,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-100\",\n                        children: optionChain.rows.map((row, index)=>{\n                            const isATM = Math.abs(row.strikePrice - niftySpotPrice) <= 50;\n                            const isITM_Call = row.strikePrice < niftySpotPrice;\n                            const isITM_Put = row.strikePrice > niftySpotPrice;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `flex hover:bg-gray-50 transition-colors ${isATM ? 'bg-yellow-50' : index % 2 === 0 ? 'bg-white' : 'bg-gray-25'}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm ${isITM_Call ? 'text-green-700 font-medium' : 'text-gray-700'}`,\n                                        children: formatNumber(row.call?.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm ${isITM_Call ? 'text-green-700 font-medium' : 'text-gray-700'}`,\n                                        children: formatNumber(row.call?.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice(row.call?.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice(row.call?.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm font-medium ${getChangeColor(row.call?.change)}`,\n                                        children: row.call?.change ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.call.change > 0 ? '+' : '',\n                                                row.call.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.call.changePercent > 0 ? '+' : '',\n                                                        row.call.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 683,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : '-'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 ${isITM_Call ? 'text-green-600' : 'text-gray-700'}`,\n                                        children: formatPrice(row.call?.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-20 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 ${isATM ? 'bg-yellow-100 text-yellow-800' : 'text-gray-900'}`,\n                                        children: row.strikePrice\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm font-bold ${isITM_Put ? 'text-red-600' : 'text-gray-700'}`,\n                                        children: formatPrice(row.put?.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm font-medium ${getChangeColor(row.put?.change)}`,\n                                        children: row.put?.change ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.put.change > 0 ? '+' : '',\n                                                row.put.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.put.changePercent > 0 ? '+' : '',\n                                                        row.put.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : '-'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 708,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice(row.put?.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 718,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice(row.put?.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm ${isITM_Put ? 'text-red-700 font-medium' : 'text-gray-700'}`,\n                                        children: formatNumber(row.put?.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm ${isITM_Put ? 'text-red-700 font-medium' : 'text-gray-700'}`,\n                                        children: formatNumber(row.put?.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 727,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, row.strikePrice, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 660,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 653,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 613,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 border-t border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Show all\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 742,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 743,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Showing \",\n                                        optionChain.rows.length,\n                                        \" strikes around ATM\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 744,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 741,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"Last updated: \",\n                                new Date(optionChain.timestamp).toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 746,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 740,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 739,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n        lineNumber: 483,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/OptionChain.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useMarketData.ts":
/*!************************************!*\
  !*** ./src/hooks/useMarketData.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useMarketData: () => (/* binding */ useMarketData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_marketStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/marketStore */ \"(ssr)/./src/store/marketStore.ts\");\n/**\r\n * Custom hook for Market Data Management\r\n * Provides a clean interface to the Zustand market store\r\n */ \n\nconst useMarketData = ()=>{\n    const store = (0,_store_marketStore__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    // Hydrate from Redis on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMarketData.useEffect\": ()=>{\n            fetch('/api/cache/all-latest').then({\n                \"useMarketData.useEffect\": (res)=>res.json()\n            }[\"useMarketData.useEffect\"]).then({\n                \"useMarketData.useEffect\": ({ data })=>{\n                    if (Array.isArray(data)) store.hydrateFromRedis(data);\n                }\n            }[\"useMarketData.useEffect\"]);\n        }\n    }[\"useMarketData.useEffect\"], [\n        store\n    ]);\n    // WebSocket tick handling (example, adapt as needed)\n    // ws.on('tick', tick => store.updateMarketData(tick));\n    // ws.on('batch', ticks => store.updateMarketDataBatch(ticks));\n    return {\n        marketData: Object.values(store.marketData),\n        updateMarketData: store.updateMarketData,\n        updateMarketDataBatch: store.updateMarketDataBatch,\n        hydrateFromRedis: store.hydrateFromRedis\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useMarketData);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useMarketData.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useNiftySpot.ts":
/*!***********************************!*\
  !*** ./src/hooks/useNiftySpot.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useNiftySpot: () => (/* binding */ useNiftySpot)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * useNiftySpot Hook\n * React hook for accessing live NIFTY spot price data\n */ \n/**\n * Custom hook for NIFTY spot price data\n */ const useNiftySpot = ()=>{\n    const [niftyData, setNiftyData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Fetch data from API endpoint\n    const fetchNiftyData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useNiftySpot.useCallback[fetchNiftyData]\": async ()=>{\n            try {\n                const response = await fetch('/api/nifty-spot');\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.success && data.data) {\n                        // Convert API response to NiftySpotData format\n                        const niftySpotData = {\n                            symbol: data.data.symbol || 'NIFTY',\n                            securityId: data.data.securityId || '13',\n                            ltp: data.data.ltp || 0,\n                            previousClose: data.data.close || 0,\n                            changePoints: data.data.change || 0,\n                            changePercent: data.data.changePercent || 0,\n                            lastUpdateTime: new Date(data.timestamp),\n                            totalUpdates: 1,\n                            isReady: true,\n                            formatted: {\n                                points: `${data.data.change >= 0 ? '+' : ''}₹${(data.data.change || 0).toFixed(2)}`,\n                                percent: `${data.data.changePercent >= 0 ? '+' : ''}${(data.data.changePercent || 0).toFixed(3)}%`,\n                                color: (data.data.change || 0) >= 0 ? '🟢' : '🔴',\n                                isPositive: (data.data.change || 0) >= 0\n                            }\n                        };\n                        setNiftyData(niftySpotData);\n                        setIsConnected(true);\n                        setError(null);\n                        console.log('✅ [useNiftySpot] Fetched NIFTY data from API:', niftySpotData.ltp);\n                    }\n                } else {\n                    throw new Error(`API responded with status: ${response.status}`);\n                }\n            } catch (err) {\n                console.error('❌ [useNiftySpot] Failed to fetch NIFTY data:', err);\n                setError(err instanceof Error ? err.message : 'Failed to fetch NIFTY data');\n                setIsConnected(false);\n            }\n        }\n    }[\"useNiftySpot.useCallback[fetchNiftyData]\"], []);\n    // Fetch data from server market data API as fallback\n    const fetchFromMarketData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useNiftySpot.useCallback[fetchFromMarketData]\": async ()=>{\n            try {\n                const serverUrl = \"http://localhost:8081\" || 0;\n                const response = await fetch(`${serverUrl}/api/market-data`);\n                if (response.ok) {\n                    const data = await response.json();\n                    // Look for NIFTY index data\n                    const niftyInstrument = data.data.instruments.find({\n                        \"useNiftySpot.useCallback[fetchFromMarketData].niftyInstrument\": (item)=>item.securityId === '13' || item.securityId === '2' || item.symbol === 'NIFTY' && item.instrumentType === 'INDEX'\n                    }[\"useNiftySpot.useCallback[fetchFromMarketData].niftyInstrument\"]);\n                    if (niftyInstrument && niftyInstrument.ltp > 0) {\n                        const niftySpotData = {\n                            symbol: niftyInstrument.symbol,\n                            securityId: niftyInstrument.securityId,\n                            ltp: niftyInstrument.ltp,\n                            previousClose: niftyInstrument.close || 0,\n                            changePoints: niftyInstrument.change || 0,\n                            changePercent: niftyInstrument.changePercent || 0,\n                            lastUpdateTime: new Date(niftyInstrument.timestamp),\n                            totalUpdates: 1,\n                            isReady: true,\n                            formatted: {\n                                points: `${niftyInstrument.change >= 0 ? '+' : ''}₹${(niftyInstrument.change || 0).toFixed(2)}`,\n                                percent: `${niftyInstrument.changePercent >= 0 ? '+' : ''}${(niftyInstrument.changePercent || 0).toFixed(3)}%`,\n                                color: (niftyInstrument.change || 0) >= 0 ? '🟢' : '🔴',\n                                isPositive: (niftyInstrument.change || 0) >= 0\n                            }\n                        };\n                        setNiftyData(niftySpotData);\n                        setIsConnected(true);\n                        setError(null);\n                        console.log('✅ [useNiftySpot] Fetched NIFTY data from market data API:', niftySpotData.ltp);\n                        return true;\n                    }\n                }\n                return false;\n            } catch (err) {\n                console.error('❌ [useNiftySpot] Failed to fetch from market data API:', err);\n                return false;\n            }\n        }\n    }[\"useNiftySpot.useCallback[fetchFromMarketData]\"], []);\n    // Refresh data\n    const refresh = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useNiftySpot.useCallback[refresh]\": async ()=>{\n            console.log('🔄 [useNiftySpot] Refreshing NIFTY spot data...');\n            // Try primary API first\n            await fetchNiftyData();\n            // If no data, try market data API\n            if (!niftyData || niftyData.ltp === 0) {\n                await fetchFromMarketData();\n            }\n        }\n    }[\"useNiftySpot.useCallback[refresh]\"], [\n        fetchNiftyData,\n        fetchFromMarketData,\n        niftyData\n    ]);\n    // Connect (same as refresh for API-based approach)\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useNiftySpot.useCallback[connect]\": async ()=>{\n            await refresh();\n        }\n    }[\"useNiftySpot.useCallback[connect]\"], [\n        refresh\n    ]);\n    // Disconnect\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useNiftySpot.useCallback[disconnect]\": ()=>{\n            setNiftyData(null);\n            setIsConnected(false);\n            setError(null);\n            console.log('🔌 [useNiftySpot] Disconnected');\n        }\n    }[\"useNiftySpot.useCallback[disconnect]\"], []);\n    // Auto-fetch data on mount and set up polling\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useNiftySpot.useEffect\": ()=>{\n            // Initial fetch\n            refresh();\n            // Set up polling every 5 seconds during market hours\n            const interval = setInterval({\n                \"useNiftySpot.useEffect.interval\": ()=>{\n                    const now = new Date();\n                    const hour = now.getHours();\n                    const minute = now.getMinutes();\n                    const timeInMinutes = hour * 60 + minute;\n                    // Market hours: 9:15 AM to 3:30 PM IST (555 to 930 minutes)\n                    const marketStart = 9 * 60 + 15; // 9:15 AM\n                    const marketEnd = 15 * 60 + 30; // 3:30 PM\n                    if (timeInMinutes >= marketStart && timeInMinutes <= marketEnd) {\n                        refresh();\n                    }\n                }\n            }[\"useNiftySpot.useEffect.interval\"], 5000); // Poll every 5 seconds\n            return ({\n                \"useNiftySpot.useEffect\": ()=>{\n                    clearInterval(interval);\n                }\n            })[\"useNiftySpot.useEffect\"];\n        }\n    }[\"useNiftySpot.useEffect\"], [\n        refresh\n    ]);\n    return {\n        // Data\n        niftyData,\n        ltp: niftyData?.ltp || 0,\n        previousClose: niftyData?.previousClose || 0,\n        changePoints: niftyData?.changePoints || 0,\n        changePercent: niftyData?.changePercent || 0,\n        // Formatted data\n        formattedChange: niftyData?.formatted || null,\n        // Status\n        isConnected,\n        isReady: niftyData?.isReady || false,\n        status,\n        error,\n        // Actions\n        refresh,\n        connect,\n        disconnect\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useNiftySpot);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useNiftySpot.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/constants.ts":
/*!******************************!*\
  !*** ./src/lib/constants.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: () => (/* binding */ API_CONFIG),\n/* harmony export */   CACHE_CONFIG: () => (/* binding */ CACHE_CONFIG),\n/* harmony export */   CHART_CONFIG: () => (/* binding */ CHART_CONFIG),\n/* harmony export */   COLORS: () => (/* binding */ COLORS),\n/* harmony export */   DEFAULTS: () => (/* binding */ DEFAULTS),\n/* harmony export */   DHAN_CONFIG: () => (/* binding */ DHAN_CONFIG),\n/* harmony export */   ENV_CONFIG: () => (/* binding */ ENV_CONFIG),\n/* harmony export */   ERROR_MESSAGES: () => (/* binding */ ERROR_MESSAGES),\n/* harmony export */   EXCHANGE_SEGMENTS: () => (/* binding */ EXCHANGE_SEGMENTS),\n/* harmony export */   FEATURE_FLAGS: () => (/* binding */ FEATURE_FLAGS),\n/* harmony export */   INSTRUMENT_TYPES: () => (/* binding */ INSTRUMENT_TYPES),\n/* harmony export */   LOGGING_CONFIG: () => (/* binding */ LOGGING_CONFIG),\n/* harmony export */   MARKET_CONFIG: () => (/* binding */ MARKET_CONFIG),\n/* harmony export */   OPTION_TYPES: () => (/* binding */ OPTION_TYPES),\n/* harmony export */   PACKET_TYPES: () => (/* binding */ PACKET_TYPES),\n/* harmony export */   PERFORMANCE_CONFIG: () => (/* binding */ PERFORMANCE_CONFIG),\n/* harmony export */   REQUEST_CODES: () => (/* binding */ REQUEST_CODES),\n/* harmony export */   SECURITY_CONFIG: () => (/* binding */ SECURITY_CONFIG),\n/* harmony export */   STORAGE_KEYS: () => (/* binding */ STORAGE_KEYS),\n/* harmony export */   SUCCESS_MESSAGES: () => (/* binding */ SUCCESS_MESSAGES),\n/* harmony export */   UI_CONFIG: () => (/* binding */ UI_CONFIG),\n/* harmony export */   VALIDATION: () => (/* binding */ VALIDATION),\n/* harmony export */   WEBSOCKET_CONFIG: () => (/* binding */ WEBSOCKET_CONFIG)\n/* harmony export */ });\n/**\r\n * Application Constants\r\n */ // API Configuration\nconst API_CONFIG = {\n    BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080',\n    TIMEOUT: 30000,\n    RETRY_ATTEMPTS: 3,\n    RETRY_DELAY: 1000\n};\n// WebSocket Configuration\nconst WEBSOCKET_CONFIG = {\n    RECONNECT_INTERVAL: 3000,\n    MAX_RECONNECT_ATTEMPTS: 15,\n    PING_INTERVAL: 25000,\n    PONG_TIMEOUT: 10000,\n    CONNECTION_TIMEOUT: 20000,\n    HEARTBEAT_INTERVAL: 30000,\n    MAX_LISTENERS_PER_EVENT: 10,\n    CLEANUP_INTERVAL: 60000\n};\n// Dhan API Configuration\nconst DHAN_CONFIG = {\n    BASE_URL: 'https://api.dhan.co',\n    WEBSOCKET_URL: 'wss://api.dhan.co/v2/wsapi',\n    MAX_INSTRUMENTS_PER_CONNECTION: 5000,\n    MAX_INSTRUMENTS_PER_MESSAGE: 100,\n    RATE_LIMIT: {\n        REQUESTS_PER_SECOND: 10,\n        REQUESTS_PER_MINUTE: 600\n    }\n};\n// Market Configuration\nconst MARKET_CONFIG = {\n    TRADING_HOURS: {\n        START: {\n            hour: 9,\n            minute: 15\n        },\n        END: {\n            hour: 15,\n            minute: 30\n        }\n    },\n    TRADING_DAYS: [\n        1,\n        2,\n        3,\n        4,\n        5\n    ],\n    REFRESH_INTERVAL: 1000,\n    BATCH_SIZE: 100\n};\n// Instrument Types\nconst INSTRUMENT_TYPES = {\n    EQUITY: 'EQUITY',\n    INDEX: 'INDEX',\n    FUTIDX: 'FUTIDX',\n    OPTIDX: 'OPTIDX',\n    FUTSTK: 'FUTSTK',\n    OPTSTK: 'OPTSTK',\n    FUTCUR: 'FUTCUR',\n    OPTCUR: 'OPTCUR',\n    FUTCOM: 'FUTCOM',\n    OPTFUT: 'OPTFUT'\n};\n// Exchange Segments\nconst EXCHANGE_SEGMENTS = {\n    NSE_EQ: 'NSE_EQ',\n    NSE_FNO: 'NSE_FNO',\n    BSE_EQ: 'BSE_EQ',\n    MCX_COMM: 'MCX_COMM',\n    IDX_I: 'IDX_I'\n};\n// Option Types\nconst OPTION_TYPES = {\n    CALL: 'CE',\n    PUT: 'PE'\n};\n// Market Data Packet Types\nconst PACKET_TYPES = {\n    TOUCHLINE: 1,\n    QUOTE: 2,\n    SNAP_QUOTE: 3,\n    FULL_PACKET: 4,\n    OI: 5\n};\n// WebSocket Request Codes\nconst REQUEST_CODES = {\n    SUBSCRIBE: 21,\n    UNSUBSCRIBE: 22,\n    SNAP_QUOTE: 23\n};\n// UI Configuration\nconst UI_CONFIG = {\n    DEBOUNCE_DELAY: 300,\n    THROTTLE_DELAY: 100,\n    ANIMATION_DURATION: 200,\n    TOAST_DURATION: 3000,\n    PAGINATION: {\n        DEFAULT_PAGE_SIZE: 50,\n        PAGE_SIZE_OPTIONS: [\n            25,\n            50,\n            100,\n            200\n        ]\n    }\n};\n// Color Schemes\nconst COLORS = {\n    SUCCESS: '#10B981',\n    ERROR: '#EF4444',\n    WARNING: '#F59E0B',\n    INFO: '#3B82F6',\n    NEUTRAL: '#6B7280',\n    BID: '#10B981',\n    ASK: '#EF4444',\n    SPOT: '#3B82F6'\n};\n// Chart Configuration\nconst CHART_CONFIG = {\n    DEFAULT_HEIGHT: 400,\n    COLORS: {\n        PRIMARY: '#3B82F6',\n        SECONDARY: '#10B981',\n        ACCENT: '#F59E0B',\n        GRID: '#E5E7EB',\n        TEXT: '#374151'\n    },\n    ANIMATION: {\n        DURATION: 300,\n        EASING: 'ease-in-out'\n    }\n};\n// Data Validation\nconst VALIDATION = {\n    MIN_STRIKE_PRICE: 1,\n    MAX_STRIKE_PRICE: 100000,\n    MIN_OPTION_PRICE: 0.01,\n    MAX_OPTION_PRICE: 50000,\n    MIN_VOLUME: 0,\n    MAX_VOLUME: 10000000\n};\n// Error Messages\nconst ERROR_MESSAGES = {\n    NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',\n    API_ERROR: 'API request failed. Please try again later.',\n    WEBSOCKET_ERROR: 'WebSocket connection failed. Attempting to reconnect...',\n    DATA_PARSING_ERROR: 'Failed to parse market data. Please refresh the page.',\n    SUBSCRIPTION_ERROR: 'Failed to subscribe to market data. Please try again.',\n    INVALID_INSTRUMENT: 'Invalid instrument selected.',\n    MARKET_CLOSED: 'Market is currently closed.',\n    RATE_LIMIT_EXCEEDED: 'Too many requests. Please wait before trying again.'\n};\n// Success Messages\nconst SUCCESS_MESSAGES = {\n    CONNECTION_ESTABLISHED: 'Successfully connected to market data feed.',\n    SUBSCRIPTION_SUCCESS: 'Successfully subscribed to market data.',\n    DATA_UPDATED: 'Market data updated successfully.',\n    SETTINGS_SAVED: 'Settings saved successfully.'\n};\n// Local Storage Keys\nconst STORAGE_KEYS = {\n    USER_PREFERENCES: 'csv_market_dashboard_preferences',\n    SELECTED_INSTRUMENTS: 'csv_market_dashboard_selected_instruments',\n    THEME: 'csv_market_dashboard_theme',\n    LAYOUT: 'csv_market_dashboard_layout'\n};\n// Feature Flags\nconst FEATURE_FLAGS = {\n    ENABLE_CHARTS: true,\n    ENABLE_ALERTS: true,\n    ENABLE_EXPORT: true,\n    ENABLE_DARK_MODE: true,\n    ENABLE_REAL_TIME_UPDATES: true,\n    ENABLE_OPTION_GREEKS: false\n};\n// Performance Monitoring\nconst PERFORMANCE_CONFIG = {\n    MEMORY_WARNING_THRESHOLD: 100 * 1024 * 1024,\n    CPU_WARNING_THRESHOLD: 80,\n    NETWORK_TIMEOUT_WARNING: 5000,\n    MAX_CONCURRENT_REQUESTS: 10\n};\n// Logging Configuration\nconst LOGGING_CONFIG = {\n    LEVELS: {\n        ERROR: 0,\n        WARN: 1,\n        INFO: 2,\n        DEBUG: 3\n    },\n    MAX_LOG_SIZE: 10 * 1024 * 1024,\n    MAX_LOG_FILES: 5,\n    LOG_ROTATION_INTERVAL: 24 * 60 * 60 * 1000\n};\n// Cache Configuration\nconst CACHE_CONFIG = {\n    DEFAULT_TTL: 5 * 60 * 1000,\n    MARKET_DATA_TTL: 10 * 60 * 1000,\n    STATIC_DATA_TTL: 30 * 60 * 1000,\n    EXPIRY_CHECK_INTERVAL: 60 * 1000,\n    MAX_CACHE_SIZE: 50 * 1024 * 1024,\n    KEYS: {\n        INSTRUMENTS: 'instruments',\n        MARKET_DATA: 'market_data',\n        OPTION_CHAIN: 'option_chain',\n        EXPIRY_DATES: 'expiry_dates',\n        NIFTY_SPOT: 'nifty_spot',\n        USER_SETTINGS: 'user_settings',\n        USER_PREFERENCES: 'user_preferences'\n    }\n};\n// Security Configuration\nconst SECURITY_CONFIG = {\n    RATE_LIMITING: {\n        WINDOW_MS: 15 * 60 * 1000,\n        MAX_REQUESTS: 1000\n    },\n    CORS: {\n        ORIGIN:  false ? 0 : [\n            'http://localhost:3000',\n            'http://localhost:3001'\n        ],\n        CREDENTIALS: true\n    },\n    HEADERS: {\n        CONTENT_SECURITY_POLICY: \"default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';\",\n        X_FRAME_OPTIONS: 'DENY',\n        X_CONTENT_TYPE_OPTIONS: 'nosniff'\n    }\n};\n// Environment Configuration\nconst ENV_CONFIG = {\n    NODE_ENV: \"development\" || 0,\n    PORT: parseInt(process.env.PORT || '8080'),\n    NEXT_PORT: parseInt(process.env.NEXT_PORT || '3000'),\n    LOG_LEVEL: process.env.LOG_LEVEL || 'info',\n    ENABLE_METRICS: process.env.ENABLE_METRICS === 'true'\n};\n// Default Values\nconst DEFAULTS = {\n    NIFTY_SPOT_PRICE: 24850,\n    SELECTED_EXPIRY: '2025-06-19',\n    STRIKES_TO_SHOW: 20,\n    REFRESH_INTERVAL: 1000,\n    CHART_TIMEFRAME: '1D',\n    TABLE_PAGE_SIZE: 50\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/constants.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/data-cache.ts":
/*!*******************************!*\
  !*** ./src/lib/data-cache.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MARKET_DATA_CACHE_KEYS: () => (/* binding */ MARKET_DATA_CACHE_KEYS),\n/* harmony export */   cacheHelpers: () => (/* binding */ cacheHelpers),\n/* harmony export */   dataCache: () => (/* binding */ dataCache)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(ssr)/./src/lib/constants.ts\");\n/**\r\n * Data Caching System for Market Data Persistence\r\n * Handles localStorage/sessionStorage with TTL and compression\r\n */ \nclass DataCache {\n    static{\n        this.instance = null;\n    }\n    /**\r\n   * Check if we're in a browser environment\r\n   */ isBrowser() {\n        return  false && 0;\n    }\n    static getInstance() {\n        if (!DataCache.instance) {\n            DataCache.instance = new DataCache();\n        }\n        return DataCache.instance;\n    }\n    /**\r\n   * Initialize cache for client-side usage\r\n   * Call this method when the component mounts on the client side\r\n   */ initializeClientSide() {\n        if (this.isBrowser()) {\n            console.log('🚀 DataCache: Initializing client-side cache');\n            // Perform any client-side specific initialization here\n            this.cleanupExpired() // Run initial cleanup\n            ;\n        }\n    }\n    constructor(){\n        this.version = '1.0.0';\n        this.compressionSupported = false;\n        this.checkCompressionSupport();\n        this.startCleanupInterval();\n    }\n    /**\r\n   * Check if compression is supported\r\n   */ checkCompressionSupport() {\n        try {\n            // Check if CompressionStream is available (modern browsers)\n            this.compressionSupported = typeof CompressionStream !== 'undefined';\n        } catch  {\n            this.compressionSupported = false;\n        }\n    }\n    /**\r\n   * Set data in cache with TTL\r\n   */ async set(key, data, options = {}) {\n        try {\n            // Skip caching on server side\n            if (!this.isBrowser()) {\n                console.log(`⚠️ DataCache: Skipping cache on server side for ${key}`);\n                return false;\n            }\n            const { ttl = _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.DEFAULT_TTL, useCompression = false, storage = 'localStorage' } = options;\n            const entry = {\n                data,\n                timestamp: Date.now(),\n                ttl,\n                version: this.version\n            };\n            let serializedData = JSON.stringify(entry);\n            // Apply compression if requested and supported\n            if (useCompression && this.compressionSupported) {\n                serializedData = await this.compress(serializedData);\n            }\n            const storageObj = storage === 'localStorage' ? localStorage : sessionStorage;\n            const fullKey = this.getFullKey(key);\n            storageObj.setItem(fullKey, serializedData);\n            console.log(`💾 DataCache: Cached ${key} (${serializedData.length} bytes)`);\n            return true;\n        } catch (error) {\n            console.error(`❌ DataCache: Failed to cache ${key}:`, error);\n            return false;\n        }\n    }\n    /**\r\n   * Get data from cache\r\n   */ async get(key, options = {}) {\n        try {\n            // Return null on server side\n            if (!this.isBrowser()) {\n                return null;\n            }\n            const { storage = 'localStorage' } = options;\n            const storageObj = storage === 'localStorage' ? localStorage : sessionStorage;\n            const fullKey = this.getFullKey(key);\n            const serializedData = storageObj.getItem(fullKey);\n            if (!serializedData) {\n                return null;\n            }\n            let decompressedData = serializedData;\n            // Try to decompress if it looks compressed\n            if (this.compressionSupported && this.isCompressed(serializedData)) {\n                decompressedData = await this.decompress(serializedData);\n            }\n            const entry = JSON.parse(decompressedData);\n            // Check version compatibility\n            if (entry.version !== this.version) {\n                console.warn(`⚠️ DataCache: Version mismatch for ${key}, removing`);\n                this.remove(key, options);\n                return null;\n            }\n            // Check TTL\n            if (Date.now() - entry.timestamp > entry.ttl) {\n                console.log(`⏰ DataCache: ${key} expired, removing`);\n                this.remove(key, options);\n                return null;\n            }\n            console.log(`📖 DataCache: Retrieved ${key} from cache`);\n            return entry.data;\n        } catch (error) {\n            console.error(`❌ DataCache: Failed to retrieve ${key}:`, error);\n            // Remove corrupted entry\n            this.remove(key, options);\n            return null;\n        }\n    }\n    /**\r\n   * Remove data from cache\r\n   */ remove(key, options = {}) {\n        try {\n            // Skip on server side\n            if (!this.isBrowser()) {\n                return;\n            }\n            const { storage = 'localStorage' } = options;\n            const storageObj = storage === 'localStorage' ? localStorage : sessionStorage;\n            const fullKey = this.getFullKey(key);\n            storageObj.removeItem(fullKey);\n            console.log(`🗑️ DataCache: Removed ${key}`);\n        } catch (error) {\n            console.error(`❌ DataCache: Failed to remove ${key}:`, error);\n        }\n    }\n    /**\r\n   * Clear all cache entries\r\n   */ clear(storage = 'localStorage') {\n        try {\n            // Skip on server side\n            if (!this.isBrowser()) {\n                return;\n            }\n            const storageObj = storage === 'localStorage' ? localStorage : sessionStorage;\n            const keysToRemove = [];\n            // Find all our cache keys\n            for(let i = 0; i < storageObj.length; i++){\n                const key = storageObj.key(i);\n                if (key && key.startsWith('csv_market_dashboard_cache_')) {\n                    keysToRemove.push(key);\n                }\n            }\n            // Remove them\n            keysToRemove.forEach((key)=>storageObj.removeItem(key));\n            console.log(`🧹 DataCache: Cleared ${keysToRemove.length} entries from ${storage}`);\n        } catch (error) {\n            console.error(`❌ DataCache: Failed to clear ${storage}:`, error);\n        }\n    }\n    /**\r\n   * Get cache statistics\r\n   */ getStats(storage = 'localStorage') {\n        // Return empty stats on server side\n        if (!this.isBrowser()) {\n            return {\n                totalEntries: 0,\n                totalSize: 0,\n                oldestEntry: null,\n                newestEntry: null\n            };\n        }\n        const storageObj = storage === 'localStorage' ? localStorage : sessionStorage;\n        let totalEntries = 0;\n        let totalSize = 0;\n        let oldestTimestamp = Infinity;\n        let newestTimestamp = 0;\n        try {\n            for(let i = 0; i < storageObj.length; i++){\n                const key = storageObj.key(i);\n                if (key && key.startsWith('csv_market_dashboard_cache_')) {\n                    const value = storageObj.getItem(key);\n                    if (value) {\n                        totalEntries++;\n                        totalSize += value.length;\n                        try {\n                            const entry = JSON.parse(value);\n                            if (entry.timestamp) {\n                                oldestTimestamp = Math.min(oldestTimestamp, entry.timestamp);\n                                newestTimestamp = Math.max(newestTimestamp, entry.timestamp);\n                            }\n                        } catch  {\n                        // Ignore parsing errors for stats\n                        }\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('❌ DataCache: Failed to get stats:', error);\n        }\n        return {\n            totalEntries,\n            totalSize,\n            oldestEntry: oldestTimestamp === Infinity ? null : new Date(oldestTimestamp),\n            newestEntry: newestTimestamp === 0 ? null : new Date(newestTimestamp)\n        };\n    }\n    /**\r\n   * Start cleanup interval to remove expired entries\r\n   */ startCleanupInterval() {\n        // Only start cleanup interval on client side\n        if (this.isBrowser()) {\n            setInterval(()=>{\n                this.cleanupExpired();\n            }, _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.EXPIRY_CHECK_INTERVAL);\n        }\n    }\n    /**\r\n   * Clean up expired entries\r\n   */ cleanupExpired() {\n        try {\n            // Skip cleanup on server side\n            if (!this.isBrowser()) {\n                return;\n            }\n            const storages = [\n                'localStorage',\n                'sessionStorage'\n            ];\n            storages.forEach((storageType)=>{\n                const storageObj = storageType === 'localStorage' ? localStorage : sessionStorage;\n                const keysToRemove = [];\n                for(let i = 0; i < storageObj.length; i++){\n                    const key = storageObj.key(i);\n                    if (key && key.startsWith('csv_market_dashboard_cache_')) {\n                        try {\n                            const value = storageObj.getItem(key);\n                            if (value) {\n                                const entry = JSON.parse(value);\n                                if (Date.now() - entry.timestamp > entry.ttl) {\n                                    keysToRemove.push(key);\n                                }\n                            }\n                        } catch  {\n                            // Remove corrupted entries\n                            keysToRemove.push(key);\n                        }\n                    }\n                }\n                keysToRemove.forEach((key)=>storageObj.removeItem(key));\n                if (keysToRemove.length > 0) {\n                    console.log(`🧹 DataCache: Cleaned up ${keysToRemove.length} expired entries from ${storageType}`);\n                }\n            });\n        } catch (error) {\n            console.error('❌ DataCache: Cleanup failed:', error);\n        }\n    }\n    /**\r\n   * Generate full cache key\r\n   */ getFullKey(key) {\n        return `csv_market_dashboard_cache_${key}`;\n    }\n    /**\r\n   * Check if data is compressed\r\n   */ isCompressed(data) {\n        // Simple heuristic: compressed data usually starts with specific bytes\n        return data.startsWith('H4sI') || data.startsWith('eJy') // Common compression signatures\n        ;\n    }\n    /**\r\n   * Compress data (placeholder for future implementation)\r\n   */ async compress(data) {\n        // For now, return as-is. Can implement actual compression later\n        return data;\n    }\n    /**\r\n   * Decompress data (placeholder for future implementation)\r\n   */ async decompress(data) {\n        // For now, return as-is. Can implement actual decompression later\n        return data;\n    }\n}\n// Export singleton instance\nconst dataCache = DataCache.getInstance();\n// Export cache keys for market data\nconst MARKET_DATA_CACHE_KEYS = _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.KEYS;\n// Export helper functions\nconst cacheHelpers = {\n    /**\r\n   * Cache market data with medium TTL for page refresh persistence\r\n   */ cacheMarketData: async (key, data)=>{\n        return dataCache.set(key, data, {\n            ttl: _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.MARKET_DATA_TTL,\n            storage: 'localStorage' // Use localStorage for page refresh persistence\n        });\n    },\n    /**\r\n   * Cache static data with long TTL\r\n   */ cacheStaticData: async (key, data)=>{\n        return dataCache.set(key, data, {\n            ttl: _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.STATIC_DATA_TTL,\n            storage: 'localStorage' // Use local storage for persistent data\n        });\n    },\n    /**\r\n   * Get cached market data\r\n   */ getCachedMarketData: async (key)=>{\n        return dataCache.get(key, {\n            storage: 'localStorage'\n        });\n    },\n    /**\r\n   * Get cached static data\r\n   */ getCachedStaticData: async (key)=>{\n        return dataCache.get(key, {\n            storage: 'localStorage'\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/data-cache.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketFormatters: () => (/* binding */ MarketFormatters),\n/* harmony export */   calculateImpliedVolatility: () => (/* binding */ calculateImpliedVolatility),\n/* harmony export */   capitalize: () => (/* binding */ capitalize),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatIndianNumber: () => (/* binding */ formatIndianNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   formatValueWithFallback: () => (/* binding */ formatValueWithFallback),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getRelativeTime: () => (/* binding */ getRelativeTime),\n/* harmony export */   getValueColor: () => (/* binding */ getValueColor),\n/* harmony export */   isEmpty: () => (/* binding */ isEmpty),\n/* harmony export */   isMarketHours: () => (/* binding */ isMarketHours),\n/* harmony export */   retry: () => (/* binding */ retry),\n/* harmony export */   safeJsonParse: () => (/* binding */ safeJsonParse),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   toTitleCase: () => (/* binding */ toTitleCase),\n/* harmony export */   truncate: () => (/* binding */ truncate)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\r\n * Utility function to merge Tailwind CSS classes\r\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\r\n * Format number as Indian currency\r\n */ function formatCurrency(value) {\n    return new Intl.NumberFormat('en-IN', {\n        style: 'currency',\n        currency: 'INR',\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    }).format(value);\n}\n/**\r\n * Format number with Indian number system (lakhs, crores)\r\n */ function formatIndianNumber(value) {\n    if (value >= 10000000) {\n        return `${(value / 10000000).toFixed(2)} Cr`;\n    } else if (value >= 100000) {\n        return `${(value / 100000).toFixed(2)} L`;\n    } else if (value >= 1000) {\n        return `${(value / 1000).toFixed(2)} K`;\n    }\n    return value.toString();\n}\n/**\r\n * Format percentage with proper sign\r\n */ function formatPercentage(value) {\n    const sign = value >= 0 ? '+' : '';\n    return `${sign}${value.toFixed(2)}%`;\n}\n/**\r\n * Get color class based on value (green for positive, red for negative)\r\n */ function getValueColor(value) {\n    if (value > 0) return 'text-green-600';\n    if (value < 0) return 'text-red-600';\n    return 'text-gray-600';\n}\n/**\r\n * Debounce function for performance optimization\r\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\r\n * Throttle function for performance optimization\r\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\r\n * Deep clone object\r\n */ function deepClone(obj) {\n    if (obj === null || typeof obj !== 'object') return obj;\n    if (obj instanceof Date) return new Date(obj.getTime());\n    if (obj instanceof Array) return obj.map((item)=>deepClone(item));\n    if (typeof obj === 'object') {\n        const clonedObj = {};\n        for(const key in obj){\n            if (obj.hasOwnProperty(key)) {\n                clonedObj[key] = deepClone(obj[key]);\n            }\n        }\n        return clonedObj;\n    }\n    return obj;\n}\n/**\r\n * Generate unique ID\r\n */ function generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n/**\r\n * Sleep function for async operations\r\n */ function sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\r\n * Retry function with exponential backoff\r\n */ async function retry(fn, maxAttempts = 3, baseDelay = 1000) {\n    let lastError;\n    for(let attempt = 1; attempt <= maxAttempts; attempt++){\n        try {\n            return await fn();\n        } catch (error) {\n            lastError = error;\n            if (attempt === maxAttempts) {\n                throw lastError;\n            }\n            const delay = baseDelay * Math.pow(2, attempt - 1);\n            await sleep(delay);\n        }\n    }\n    throw lastError;\n}\n/**\r\n * Safe JSON parse with fallback\r\n */ function safeJsonParse(json, fallback) {\n    try {\n        return JSON.parse(json);\n    } catch  {\n        return fallback;\n    }\n}\n/**\r\n * Check if value is empty (null, undefined, empty string, empty array, empty object)\r\n */ function isEmpty(value) {\n    if (value == null) return true;\n    if (typeof value === 'string') return value.trim() === '';\n    if (Array.isArray(value)) return value.length === 0;\n    if (typeof value === 'object') return Object.keys(value).length === 0;\n    return false;\n}\n/**\r\n * Format value with fallback to dash for empty values\r\n */ function formatValueWithFallback(value, formatter) {\n    if (isEmpty(value) || value === 0) return '-';\n    return formatter ? formatter(value) : String(value);\n}\n/**\r\n * Market data formatting utilities with consistent dash fallbacks\r\n */ const MarketFormatters = {\n    /**\r\n   * Format price with currency symbol\r\n   */ price: (price)=>{\n        if (!price || price <= 0) return '-';\n        return `₹${price.toFixed(2)}`;\n    },\n    /**\r\n   * Format number with locale formatting\r\n   */ number: (num)=>{\n        if (num === undefined || num === null) return '-';\n        return num.toLocaleString();\n    },\n    /**\r\n   * Format percentage with sign\r\n   */ percentage: (value)=>{\n        if (!value || value === 0) return '-';\n        const sign = value >= 0 ? '+' : '';\n        return `${sign}${value.toFixed(2)}%`;\n    },\n    /**\r\n   * Format change with price and percentage\r\n   */ change: (change, changePercent)=>{\n        if (!change || change === 0) return '-';\n        const sign = change > 0 ? '+' : '';\n        const percentStr = changePercent ? ` (${sign}${changePercent.toFixed(2)}%)` : '';\n        return `${sign}${change.toFixed(2)}${percentStr}`;\n    },\n    /**\r\n   * Format volume with Indian number system\r\n   */ volume: (volume)=>{\n        if (volume === undefined || volume === null) return '-';\n        return formatIndianNumber(volume);\n    },\n    /**\r\n   * Format time from timestamp\r\n   */ time: (timestamp)=>{\n        if (!timestamp) return '-';\n        return new Date(timestamp).toLocaleTimeString();\n    },\n    /**\r\n   * Format date from timestamp\r\n   */ date: (timestamp)=>{\n        if (!timestamp) return '-';\n        return new Date(timestamp).toLocaleDateString();\n    },\n    /**\r\n   * Format bid/ask with quantity\r\n   */ bidAsk: (price, qty)=>{\n        if (!price || price <= 0) return '-';\n        const qtyStr = qty ? ` (${qty})` : '';\n        return `₹${price.toFixed(2)}${qtyStr}`;\n    }\n};\n/**\r\n * Capitalize first letter of string\r\n */ function capitalize(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n}\n/**\r\n * Convert string to title case\r\n */ function toTitleCase(str) {\n    return str.replace(/\\w\\S*/g, (txt)=>txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());\n}\n/**\r\n * Truncate string with ellipsis\r\n */ function truncate(str, length) {\n    if (str.length <= length) return str;\n    return str.slice(0, length) + '...';\n}\n/**\r\n * Format file size in human readable format\r\n */ function formatFileSize(bytes) {\n    const sizes = [\n        'Bytes',\n        'KB',\n        'MB',\n        'GB',\n        'TB'\n    ];\n    if (bytes === 0) return '0 Bytes';\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n}\n/**\r\n * Get relative time string (e.g., \"2 minutes ago\")\r\n */ function getRelativeTime(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) return 'just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;\n    return date.toLocaleDateString();\n}\n/**\r\n * Check if current time is within market hours\r\n */ function isMarketHours() {\n    const now = new Date();\n    const hours = now.getHours();\n    const minutes = now.getMinutes();\n    const currentTime = hours * 60 + minutes;\n    // Market hours: 9:15 AM to 3:30 PM (IST)\n    const marketOpen = 9 * 60 + 15 // 9:15 AM\n    ;\n    const marketClose = 15 * 60 + 30 // 3:30 PM\n    ;\n    // Check if it's a weekday (Monday = 1, Sunday = 0)\n    const dayOfWeek = now.getDay();\n    const isWeekday = dayOfWeek >= 1 && dayOfWeek <= 5;\n    return isWeekday && currentTime >= marketOpen && currentTime <= marketClose;\n}\n/**\r\n * Calculate option Greeks (simplified)\r\n */ function calculateImpliedVolatility(optionPrice, spotPrice, strikePrice, timeToExpiry, riskFreeRate = 0.06) {\n    // Simplified Black-Scholes implied volatility calculation\n    // This is a basic approximation - use proper financial libraries for production\n    const moneyness = spotPrice / strikePrice;\n    const timeValue = optionPrice / spotPrice;\n    // Basic approximation formula\n    const iv = Math.sqrt(2 * Math.PI / timeToExpiry) * timeValue / moneyness;\n    return Math.max(0.01, Math.min(5.0, iv)) // Clamp between 1% and 500%\n    ;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/websocket-manager.ts":
/*!**************************************!*\
  !*** ./src/lib/websocket-manager.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebSocketManager: () => (/* binding */ WebSocketManager),\n/* harmony export */   createWebSocketConnection: () => (/* binding */ createWebSocketConnection),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   emitWebSocketEvent: () => (/* binding */ emitWebSocketEvent),\n/* harmony export */   getWebSocketStats: () => (/* binding */ getWebSocketStats),\n/* harmony export */   isWebSocketConnected: () => (/* binding */ isWebSocketConnected),\n/* harmony export */   removeWebSocketConnection: () => (/* binding */ removeWebSocketConnection),\n/* harmony export */   websocketManager: () => (/* binding */ websocketManager)\n/* harmony export */ });\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(ssr)/./src/lib/constants.ts\");\n/**\r\n * Robust WebSocket Connection Manager\r\n * Handles connection pooling, reconnection, and proper cleanup\r\n * FIXED: Enhanced singleton pattern with proper connection sharing\r\n */ \n\nclass WebSocketManager {\n    static{\n        this.instance = null;\n    }\n    static getInstance() {\n        if (!WebSocketManager.instance) {\n            WebSocketManager.instance = new WebSocketManager();\n            console.log('🔧 WebSocketManager: New singleton instance created');\n        }\n        return WebSocketManager.instance;\n    }\n    // Prevent direct instantiation\n    constructor(){\n        this.socket = null;\n        this.isConnecting = false;\n        this.clientCount = 0;\n        this.listeners = new Map();\n        this.clientCallbacks = new Map();\n        this.stats = {\n            connected: false,\n            clients: 0,\n            reconnectAttempts: 0,\n            lastConnected: null,\n            totalMessages: 0,\n            errors: 0,\n            connectionId: null\n        };\n        this.heartbeatInterval = null;\n        this.cleanupInterval = null;\n        this.reconnectTimeout = null;\n        console.log('🔧 WebSocketManager: Constructor called');\n    }\n    /**\r\n   * Get or create WebSocket connection with enhanced singleton behavior\r\n   */ async connect(options = {}) {\n        const clientId = this.generateClientId();\n        console.log(`🔌 WebSocketManager: Client ${clientId} requesting connection`);\n        // Store client callbacks for later use\n        this.clientCallbacks.set(clientId, options);\n        // Return existing connection if available and connected\n        if (this.socket && this.socket.connected) {\n            console.log(`✅ WebSocketManager: Reusing existing connection for client ${clientId}`);\n            this.addClient(options, clientId);\n            return this.socket;\n        }\n        // Prevent multiple simultaneous connection attempts\n        if (this.isConnecting) {\n            console.log(`⏳ WebSocketManager: Connection in progress, waiting for client ${clientId}`);\n            return new Promise((resolve, reject)=>{\n                const checkConnection = ()=>{\n                    if (this.socket && this.socket.connected) {\n                        console.log(`✅ WebSocketManager: Connection ready for waiting client ${clientId}`);\n                        this.addClient(options, clientId);\n                        resolve(this.socket);\n                    } else if (!this.isConnecting) {\n                        console.log(`❌ WebSocketManager: Connection failed for waiting client ${clientId}`);\n                        reject(new Error('Connection failed'));\n                    } else {\n                        setTimeout(checkConnection, 100);\n                    }\n                };\n                checkConnection();\n            });\n        }\n        console.log(`🚀 WebSocketManager: Creating new connection for client ${clientId}`);\n        this.isConnecting = true;\n        try {\n            await this.createConnection(options);\n            this.addClient(options, clientId);\n            return this.socket;\n        } catch (error) {\n            this.isConnecting = false;\n            this.clientCallbacks.delete(clientId);\n            console.error(`❌ WebSocketManager: Connection failed for client ${clientId}:`, error);\n            throw error;\n        }\n    }\n    /**\r\n   * Generate unique client ID for tracking\r\n   */ generateClientId() {\n        return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    /**\r\n   * Create new WebSocket connection with enhanced error handling\r\n   */ async createConnection(options) {\n        return new Promise((resolve, reject)=>{\n            const serverUrl = _constants__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.BASE_URL;\n            console.log(`🔌 WebSocketManager: Creating connection to ${serverUrl}`);\n            // Clean up existing connection\n            if (this.socket) {\n                console.log('🧹 WebSocketManager: Cleaning up existing connection');\n                this.socket.removeAllListeners();\n                this.socket.disconnect();\n            }\n            // Create new connection with optimized settings\n            this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(serverUrl, {\n                transports: [\n                    'websocket',\n                    'polling'\n                ],\n                upgrade: true,\n                rememberUpgrade: false,\n                timeout: _constants__WEBPACK_IMPORTED_MODULE_1__.WEBSOCKET_CONFIG.CONNECTION_TIMEOUT,\n                forceNew: false,\n                reconnection: true,\n                reconnectionAttempts: _constants__WEBPACK_IMPORTED_MODULE_1__.WEBSOCKET_CONFIG.MAX_RECONNECT_ATTEMPTS,\n                reconnectionDelay: _constants__WEBPACK_IMPORTED_MODULE_1__.WEBSOCKET_CONFIG.RECONNECT_INTERVAL,\n                reconnectionDelayMax: _constants__WEBPACK_IMPORTED_MODULE_1__.WEBSOCKET_CONFIG.RECONNECT_INTERVAL * 4,\n                randomizationFactor: 0.5,\n                autoConnect: true\n            });\n            // Connection event handlers\n            this.socket.on('connect', ()=>{\n                console.log('✅ WebSocketManager: Connected successfully');\n                this.isConnecting = false;\n                this.stats.connected = true;\n                this.stats.lastConnected = new Date();\n                this.stats.reconnectAttempts = 0;\n                this.stats.connectionId = this.socket?.id || null;\n                this.startHeartbeat();\n                // Notify all clients\n                this.notifyAllClients('onConnect');\n                resolve();\n            });\n            this.socket.on('disconnect', (reason)=>{\n                console.log(`❌ WebSocketManager: Disconnected - ${reason}`);\n                this.stats.connected = false;\n                this.stats.connectionId = null;\n                this.stopHeartbeat();\n                // Notify all clients\n                this.notifyAllClients('onDisconnect', reason);\n                // Attempt auto-reconnection for certain disconnect reasons\n                if (reason !== 'io client disconnect') {\n                    this.scheduleReconnection();\n                }\n            });\n            this.socket.on('connect_error', (error)=>{\n                console.error('🔥 WebSocketManager: Connection error:', error.message);\n                this.stats.errors++;\n                this.isConnecting = false;\n                // Notify all clients\n                this.notifyAllClients('onError', error);\n                reject(error);\n            });\n            this.socket.on('reconnect', (attemptNumber)=>{\n                console.log(`🔄 WebSocketManager: Reconnected after ${attemptNumber} attempts`);\n                this.stats.reconnectAttempts = attemptNumber;\n                this.stats.connected = true;\n                this.stats.lastConnected = new Date();\n                this.stats.connectionId = this.socket?.id || null;\n                // Notify all clients\n                this.notifyAllClients('onReconnect', attemptNumber);\n            });\n            this.socket.on('reconnect_attempt', (attemptNumber)=>{\n                console.log(`🔄 WebSocketManager: Reconnection attempt ${attemptNumber}`);\n                this.stats.reconnectAttempts = attemptNumber;\n            });\n            this.socket.on('reconnect_failed', ()=>{\n                console.error('💥 WebSocketManager: Reconnection failed');\n                this.stats.connected = false;\n                this.isConnecting = false;\n                this.stats.connectionId = null;\n            });\n            // Market data handlers\n            this.socket.on('marketData', (data)=>{\n                this.stats.totalMessages++;\n                this.notifyListeners('marketData', data);\n            });\n            this.socket.on('marketDataBatch', (data)=>{\n                this.stats.totalMessages += data.length;\n                this.notifyListeners('marketDataBatch', data);\n            });\n            // Start cleanup interval\n            this.startCleanupInterval();\n            // Connection timeout\n            setTimeout(()=>{\n                if (this.isConnecting) {\n                    this.isConnecting = false;\n                    reject(new Error('Connection timeout'));\n                }\n            }, _constants__WEBPACK_IMPORTED_MODULE_1__.WEBSOCKET_CONFIG.CONNECTION_TIMEOUT);\n        });\n    }\n    /**\r\n   * Add client and register listeners with enhanced tracking\r\n   */ addClient(options, clientId) {\n        this.clientCount++;\n        this.stats.clients = this.clientCount;\n        // Register listeners\n        if (options.onMarketData) {\n            this.addListener('marketData', options.onMarketData);\n        }\n        if (options.onMarketDataBatch) {\n            this.addListener('marketDataBatch', options.onMarketDataBatch);\n        }\n        console.log(`📊 WebSocketManager: Client ${clientId} added (Total: ${this.clientCount})`);\n    }\n    /**\r\n   * Notify all clients of connection events\r\n   */ notifyAllClients(eventType, data) {\n        this.clientCallbacks.forEach((callbacks, clientId)=>{\n            try {\n                switch(eventType){\n                    case 'onConnect':\n                        callbacks.onConnect?.();\n                        break;\n                    case 'onDisconnect':\n                        callbacks.onDisconnect?.(data);\n                        break;\n                    case 'onError':\n                        callbacks.onError?.(data);\n                        break;\n                    case 'onReconnect':\n                        callbacks.onReconnect?.(data);\n                        break;\n                }\n            } catch (error) {\n                console.error(`❌ WebSocketManager: Error notifying client ${clientId}:`, error);\n            }\n        });\n    }\n    /**\r\n   * Schedule reconnection with exponential backoff\r\n   */ scheduleReconnection() {\n        if (this.reconnectTimeout) {\n            clearTimeout(this.reconnectTimeout);\n        }\n        const delay = Math.min(_constants__WEBPACK_IMPORTED_MODULE_1__.WEBSOCKET_CONFIG.RECONNECT_INTERVAL * Math.pow(2, this.stats.reconnectAttempts), 30000 // Max 30 seconds\n        );\n        console.log(`🔄 WebSocketManager: Scheduling reconnection in ${delay}ms`);\n        this.reconnectTimeout = setTimeout(()=>{\n            if (!this.stats.connected && this.clientCount > 0) {\n                console.log('🔄 WebSocketManager: Attempting auto-reconnection');\n                this.connect().catch((error)=>{\n                    console.error('❌ WebSocketManager: Auto-reconnection failed:', error);\n                });\n            }\n        }, delay);\n    }\n    /**\r\n   * Remove client and cleanup listeners with enhanced tracking\r\n   */ removeClient(options) {\n        if (this.clientCount > 0) {\n            this.clientCount--;\n            this.stats.clients = this.clientCount;\n            // Find and remove client callback\n            let removedClientId = 'unknown';\n            for (const [clientId, callbacks] of this.clientCallbacks.entries()){\n                if (callbacks === options) {\n                    this.clientCallbacks.delete(clientId);\n                    removedClientId = clientId;\n                    break;\n                }\n            }\n            // Remove listeners\n            if (options.onMarketData) {\n                this.removeListener('marketData', options.onMarketData);\n            }\n            if (options.onMarketDataBatch) {\n                this.removeListener('marketDataBatch', options.onMarketDataBatch);\n            }\n            console.log(`📊 WebSocketManager: Client ${removedClientId} removed (Total: ${this.clientCount})`);\n            // Disconnect if no clients with grace period\n            if (this.clientCount === 0) {\n                console.log('⏳ WebSocketManager: No clients remaining, scheduling disconnect');\n                setTimeout(()=>{\n                    if (this.clientCount === 0) {\n                        console.log('🔌 WebSocketManager: Disconnecting due to no clients');\n                        this.disconnect();\n                    }\n                }, 5000) // 5 second grace period\n                ;\n            }\n        }\n    }\n    /**\r\n   * Add event listener\r\n   */ addListener(event, listener) {\n        if (!this.listeners.has(event)) {\n            this.listeners.set(event, new Set());\n        }\n        const eventListeners = this.listeners.get(event);\n        if (eventListeners.size >= _constants__WEBPACK_IMPORTED_MODULE_1__.WEBSOCKET_CONFIG.MAX_LISTENERS_PER_EVENT) {\n            console.warn(`⚠️ Maximum listeners reached for event: ${event}`);\n            return;\n        }\n        eventListeners.add(listener);\n    }\n    /**\r\n   * Remove event listener\r\n   */ removeListener(event, listener) {\n        const eventListeners = this.listeners.get(event);\n        if (eventListeners) {\n            eventListeners.delete(listener);\n            if (eventListeners.size === 0) {\n                this.listeners.delete(event);\n            }\n        }\n    }\n    /**\r\n   * Notify all listeners for an event\r\n   */ notifyListeners(event, data) {\n        const eventListeners = this.listeners.get(event);\n        if (eventListeners) {\n            eventListeners.forEach((listener)=>{\n                try {\n                    listener(data);\n                } catch (error) {\n                    console.error(`Error in ${event} listener:`, error);\n                }\n            });\n        }\n    }\n    /**\r\n   * Start heartbeat to keep connection alive\r\n   */ startHeartbeat() {\n        this.stopHeartbeat();\n        this.heartbeatInterval = setInterval(()=>{\n            if (this.socket && this.socket.connected) {\n                this.socket.emit('ping');\n            }\n        }, _constants__WEBPACK_IMPORTED_MODULE_1__.WEBSOCKET_CONFIG.HEARTBEAT_INTERVAL);\n    }\n    /**\r\n   * Stop heartbeat\r\n   */ stopHeartbeat() {\n        if (this.heartbeatInterval) {\n            clearInterval(this.heartbeatInterval);\n            this.heartbeatInterval = null;\n        }\n    }\n    /**\r\n   * Start cleanup interval\r\n   */ startCleanupInterval() {\n        this.cleanupInterval = setInterval(()=>{\n            this.cleanup();\n        }, _constants__WEBPACK_IMPORTED_MODULE_1__.WEBSOCKET_CONFIG.CLEANUP_INTERVAL);\n    }\n    /**\r\n   * Cleanup stale listeners and connections\r\n   */ cleanup() {\n        // Remove empty listener sets\n        const eventsToDelete = [];\n        this.listeners.forEach((listeners, event)=>{\n            if (listeners.size === 0) {\n                eventsToDelete.push(event);\n            }\n        });\n        eventsToDelete.forEach((event)=>{\n            this.listeners.delete(event);\n        });\n        // Log stats\n        console.log('📊 WebSocket Stats:', this.getStats());\n    }\n    /**\r\n   * Disconnect WebSocket with enhanced cleanup\r\n   */ disconnect() {\n        console.log('🔌 WebSocketManager: Disconnecting...');\n        this.stopHeartbeat();\n        if (this.cleanupInterval) {\n            clearInterval(this.cleanupInterval);\n            this.cleanupInterval = null;\n        }\n        if (this.reconnectTimeout) {\n            clearTimeout(this.reconnectTimeout);\n            this.reconnectTimeout = null;\n        }\n        if (this.socket) {\n            this.socket.removeAllListeners();\n            this.socket.disconnect();\n            this.socket = null;\n        }\n        this.listeners.clear();\n        this.clientCallbacks.clear();\n        this.clientCount = 0;\n        this.stats.connected = false;\n        this.stats.clients = 0;\n        this.stats.connectionId = null;\n        this.isConnecting = false;\n        console.log('✅ WebSocketManager: Disconnected and cleaned up');\n    }\n    /**\r\n   * Get connection statistics\r\n   */ getStats() {\n        return {\n            ...this.stats\n        };\n    }\n    /**\r\n   * Check if connected\r\n   */ isConnected() {\n        return this.socket?.connected || false;\n    }\n    /**\r\n   * Emit event to server\r\n   */ emit(event, data) {\n        if (this.socket && this.socket.connected) {\n            this.socket.emit(event, data);\n        } else {\n            console.warn(`Cannot emit ${event}: WebSocket not connected`);\n        }\n    }\n}\n// Export singleton instance and helper functions\nconst websocketManager = WebSocketManager.getInstance();\nconst createWebSocketConnection = (options = {})=>{\n    return websocketManager.connect(options);\n};\nconst removeWebSocketConnection = (options = {})=>{\n    websocketManager.removeClient(options);\n};\nconst getWebSocketStats = ()=>{\n    return websocketManager.getStats();\n};\nconst isWebSocketConnected = ()=>{\n    return websocketManager.isConnected();\n};\nconst emitWebSocketEvent = (event, data)=>{\n    websocketManager.emit(event, data);\n};\n// Export the WebSocketManager class for direct usage\n\n// Also export as default for easier importing\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WebSocketManager);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/websocket-manager.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/marketStore.ts":
/*!**********************************!*\
  !*** ./src/store/marketStore.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _lib_websocket_manager__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/websocket-manager */ \"(ssr)/./src/lib/websocket-manager.ts\");\n/**\r\n * Zustand Store for Market Data Management\r\n * Handles state persistence, WebSocket connections, and caching\r\n */ \n\n\nconst useMarketStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.persist)((set, get)=>({\n        // Initial State\n        marketData: {},\n        isConnected: false,\n        isLoading: true,\n        lastUpdate: null,\n        connectionStatus: 'disconnected',\n        error: null,\n        cacheLoaded: false,\n        wsManager: null,\n        // State Setters\n        setMarketData: (data)=>{\n            set({\n                marketData: data.reduce((acc, item)=>{\n                    acc[item.securityId] = item;\n                    return acc;\n                }, {}),\n                lastUpdate: new Date(),\n                isLoading: false\n            });\n            get().saveToLocalStorage();\n        },\n        updateMarketData: (tick)=>set((state)=>({\n                    marketData: {\n                        ...state.marketData,\n                        [tick.securityId]: {\n                            ...state.marketData[tick.securityId],\n                            ...tick\n                        }\n                    }\n                })),\n        updateMarketDataBatch: (ticks)=>set((state)=>{\n                const updated = {\n                    ...state.marketData\n                };\n                for (const tick of ticks){\n                    updated[tick.securityId] = {\n                        ...updated[tick.securityId],\n                        ...tick\n                    };\n                }\n                return {\n                    marketData: updated\n                };\n            }),\n        hydrateFromRedis: (initial)=>set({\n                marketData: initial.reduce((acc, tick)=>{\n                    acc[tick.securityId] = tick;\n                    return acc;\n                }, {})\n            }),\n        setConnectionStatus: (status)=>{\n            set({\n                connectionStatus: status,\n                isConnected: status === 'connected',\n                error: status === 'error' ? get().error : null\n            });\n        },\n        setError: (error)=>set({\n                error\n            }),\n        setLoading: (loading)=>set({\n                isLoading: loading\n            }),\n        setCacheLoaded: (loaded)=>set({\n                cacheLoaded: loaded\n            }),\n        // WebSocket Management\n        initializeWebSocket: ()=>{\n            const wsManager = _lib_websocket_manager__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance();\n            set({\n                wsManager\n            });\n            // Connect with proper options and event handlers\n            wsManager.connect({\n                onConnect: ()=>{\n                    console.log('🔌 MarketStore: WebSocket connected');\n                    get().setConnectionStatus('connected');\n                },\n                onDisconnect: (reason)=>{\n                    console.log('🔌 MarketStore: WebSocket disconnected:', reason);\n                    get().setConnectionStatus('disconnected');\n                },\n                onError: (error)=>{\n                    console.error('❌ MarketStore: WebSocket error:', error);\n                    get().setConnectionStatus('error');\n                    get().setError(error.message || 'WebSocket connection error');\n                },\n                onReconnect: (attemptNumber)=>{\n                    console.log('🔄 MarketStore: WebSocket reconnected after', attemptNumber, 'attempts');\n                    get().setConnectionStatus('connected');\n                    get().setError(null);\n                },\n                onMarketData: (data)=>{\n                    if (data && typeof data === 'object') {\n                        get().updateMarketData(data);\n                    }\n                },\n                onMarketDataBatch: (data)=>{\n                    if (Array.isArray(data) && data.length > 0) {\n                        get().updateMarketDataBatch(data);\n                    }\n                }\n            });\n        },\n        connect: ()=>{\n            const { wsManager } = get();\n            if (wsManager && !wsManager.isConnected()) {\n                get().setConnectionStatus('connecting');\n                // WebSocket manager handles connection automatically when initialized\n                console.log('🔌 MarketStore: Connection request - WebSocket manager will handle');\n            }\n        },\n        disconnect: ()=>{\n            const { wsManager } = get();\n            if (wsManager) {\n                wsManager.disconnect();\n                get().setConnectionStatus('disconnected');\n            }\n        },\n        // Cache Management\n        loadFromCache: async ()=>{\n            try {\n                set({\n                    isLoading: true\n                });\n                console.log('📖 MarketStore: Loading cached market data...');\n                // First try localStorage for instant display\n                const hasLocalData = get().loadFromLocalStorage();\n                // Then load from Redis\n                const response = await fetch('/api/cache/bulk');\n                if (response.ok) {\n                    const result = await response.json();\n                    if (result.success && result.data && Array.isArray(result.data) && result.data.length > 0) {\n                        get().setMarketData(result.data);\n                        console.log(`✅ MarketStore: Loaded ${result.data.length} items from Redis cache`);\n                    } else if (!hasLocalData) {\n                        console.log('📭 MarketStore: No cached data found');\n                    }\n                } else if (!hasLocalData) {\n                    console.log('📭 MarketStore: Failed to load cached data');\n                }\n                set({\n                    cacheLoaded: true,\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error('❌ MarketStore: Failed to load cached data:', error);\n                set({\n                    cacheLoaded: true,\n                    isLoading: false\n                });\n            }\n        },\n        refreshFromCache: async ()=>{\n            try {\n                const response = await fetch('/api/cache/bulk');\n                if (response.ok) {\n                    const result = await response.json();\n                    if (result.success && result.data && Array.isArray(result.data) && result.data.length > 0) {\n                        get().setMarketData(result.data);\n                        console.log(`✅ MarketStore: Refreshed ${result.data.length} items from cache`);\n                    }\n                }\n            } catch (error) {\n                console.error('❌ MarketStore: Failed to refresh from cache:', error);\n            }\n        },\n        clearCache: async ()=>{\n            try {\n                const response = await fetch('/api/cache/clear', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        pattern: 'market_data:*'\n                    })\n                });\n                if (response.ok) {\n                    set({\n                        marketData: {},\n                        lastUpdate: null\n                    });\n                    localStorage.removeItem('marketData');\n                    localStorage.removeItem('marketDataTimestamp');\n                    console.log('🧹 MarketStore: Cache cleared');\n                }\n            } catch (error) {\n                console.error('❌ MarketStore: Failed to clear cache:', error);\n            }\n        },\n        // Local Storage Management\n        saveToLocalStorage: ()=>{\n            const { marketData } = get();\n            if (Object.keys(marketData).length > 0) {\n                try {\n                    localStorage.setItem('marketData', JSON.stringify(Object.values(marketData)));\n                    localStorage.setItem('marketDataTimestamp', new Date().toISOString());\n                } catch (error) {\n                    console.warn('⚠️ MarketStore: Failed to save to localStorage:', error);\n                }\n            }\n        },\n        loadFromLocalStorage: ()=>{\n            try {\n                const localData = localStorage.getItem('marketData');\n                const localTimestamp = localStorage.getItem('marketDataTimestamp');\n                if (localData && localTimestamp) {\n                    const parsedData = JSON.parse(localData);\n                    const timestamp = new Date(localTimestamp);\n                    const now = new Date();\n                    const ageMinutes = (now.getTime() - timestamp.getTime()) / (1000 * 60);\n                    // Use localStorage data if it's less than 10 minutes old\n                    if (ageMinutes < 10 && Array.isArray(parsedData) && parsedData.length > 0) {\n                        set({\n                            marketData: parsedData.reduce((acc, item)=>{\n                                acc[item.securityId] = item;\n                                return acc;\n                            }, {}),\n                            lastUpdate: timestamp,\n                            isLoading: false\n                        });\n                        console.log(`⚡ MarketStore: Loaded ${parsedData.length} items from localStorage (${ageMinutes.toFixed(1)}min old)`);\n                        return true;\n                    }\n                }\n            } catch (error) {\n                console.warn('⚠️ MarketStore: Failed to load from localStorage:', error);\n                localStorage.removeItem('marketData');\n                localStorage.removeItem('marketDataTimestamp');\n            }\n            return false;\n        },\n        // Utility Functions\n        reset: ()=>{\n            set({\n                marketData: {},\n                isConnected: false,\n                isLoading: true,\n                lastUpdate: null,\n                connectionStatus: 'disconnected',\n                error: null,\n                cacheLoaded: false\n            });\n            localStorage.removeItem('marketData');\n            localStorage.removeItem('marketDataTimestamp');\n        },\n        getMarketDataBySymbol: (symbol)=>{\n            return Object.values(get().marketData).find((item)=>item.symbol === symbol);\n        },\n        getMarketDataBySecurityId: (securityId)=>{\n            return get().marketData[securityId];\n        }\n    }), {\n    name: 'market-store',\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.createJSONStorage)(()=>localStorage),\n    partialize: (state)=>({\n            marketData: state.marketData\n        })\n}));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useMarketStore);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/marketStore.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?abbf":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?ed2e":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/socket.io-parser","vendor-chunks/@swc","vendor-chunks/engine.io-parser","vendor-chunks/zustand","vendor-chunks/tailwind-merge","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/clsx","vendor-chunks/@socket.io","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/supports-color","vendor-chunks/react-error-boundary","vendor-chunks/ms","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Foption-chain%2Fpage&page=%2Foption-chain%2Fpage&appPaths=%2Foption-chain%2Fpage&pagePath=private-next-app-dir%2Foption-chain%2Fpage.tsx&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();