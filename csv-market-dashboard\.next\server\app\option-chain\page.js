/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/option-chain/page";
exports.ids = ["app/option-chain/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?ed2e":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?abbf":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Foption-chain%2Fpage&page=%2Foption-chain%2Fpage&appPaths=%2Foption-chain%2Fpage&pagePath=private-next-app-dir%2Foption-chain%2Fpage.tsx&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Foption-chain%2Fpage&page=%2Foption-chain%2Fpage&appPaths=%2Foption-chain%2Fpage&pagePath=private-next-app-dir%2Foption-chain%2Fpage.tsx&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'option-chain',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/option-chain/page.tsx */ \"(rsc)/./src/app/option-chain/page.tsx\")), \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/option-chain/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/option-chain/page\",\n        pathname: \"/option-chain\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Foption-chain%2Fpage&page=%2Foption-chain%2Fpage&appPaths=%2Foption-chain%2Fpage&pagePath=private-next-app-dir%2Foption-chain%2Fpage.tsx&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNsb3ZlJTVDJTVDZGFzaGJvYXJkJTVDJTVDY3N2LW1hcmtldC1kYXNoYm9hcmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhcHAtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNsb3ZlJTVDJTVDZGFzaGJvYXJkJTVDJTVDY3N2LW1hcmtldC1kYXNoYm9hcmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDbG92ZSU1QyU1Q2Rhc2hib2FyZCU1QyU1Q2Nzdi1tYXJrZXQtZGFzaGJvYXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q2xvdmUlNUMlNUNkYXNoYm9hcmQlNUMlNUNjc3YtbWFya2V0LWRhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q2xvdmUlNUMlNUNkYXNoYm9hcmQlNUMlNUNjc3YtbWFya2V0LWRhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q25vdC1mb3VuZC1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDbG92ZSU1QyU1Q2Rhc2hib2FyZCU1QyU1Q2Nzdi1tYXJrZXQtZGFzaGJvYXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQTJJO0FBQzNJO0FBQ0Esb09BQTRJO0FBQzVJO0FBQ0EsME9BQStJO0FBQy9JO0FBQ0Esd09BQThJO0FBQzlJO0FBQ0Esa1BBQW1KO0FBQ25KO0FBQ0Esc1FBQTZKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3N2LW1hcmtldC1kYXNoYm9hcmQvP2RkMjciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxsb3ZlXFxcXGRhc2hib2FyZFxcXFxjc3YtbWFya2V0LWRhc2hib2FyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGFwcC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGxvdmVcXFxcZGFzaGJvYXJkXFxcXGNzdi1tYXJrZXQtZGFzaGJvYXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGxvdmVcXFxcZGFzaGJvYXJkXFxcXGNzdi1tYXJrZXQtZGFzaGJvYXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGxvdmVcXFxcZGFzaGJvYXJkXFxcXGNzdi1tYXJrZXQtZGFzaGJvYXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcbG92ZVxcXFxkYXNoYm9hcmRcXFxcY3N2LW1hcmtldC1kYXNoYm9hcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGxvdmVcXFxcZGFzaGJvYXJkXFxcXGNzdi1tYXJrZXQtZGFzaGJvYXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNsb3ZlJTVDJTVDZGFzaGJvYXJkJTVDJTVDY3N2LW1hcmtldC1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBb0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jc3YtbWFya2V0LWRhc2hib2FyZC8/N2ZhYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGxvdmVcXFxcZGFzaGJvYXJkXFxcXGNzdi1tYXJrZXQtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Coption-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Coption-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/option-chain/page.tsx */ \"(ssr)/./src/app/option-chain/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNsb3ZlJTVDJTVDZGFzaGJvYXJkJTVDJTVDY3N2LW1hcmtldC1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNvcHRpb24tY2hhaW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEtBQWdIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3N2LW1hcmtldC1kYXNoYm9hcmQvPzZlY2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxsb3ZlXFxcXGRhc2hib2FyZFxcXFxjc3YtbWFya2V0LWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXG9wdGlvbi1jaGFpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clove%5C%5Cdashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Coption-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var react_error_boundary__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-error-boundary */ \"(ssr)/./node_modules/react-error-boundary/dist/react-error-boundary.development.esm.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Error fallback component\nfunction ErrorFallback({ error, resetErrorBoundary }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg border border-red-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-600 text-lg\",\n                                children: \"⚠\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Application Error\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-4\",\n                    children: \"An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"cursor-pointer text-sm text-gray-500 hover:text-gray-700\",\n                            children: \"Technical Details\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto max-h-32\",\n                            children: error.message\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: resetErrorBoundary,\n                            className: \"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\",\n                            children: \"Try Again\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors\",\n                            children: \"Refresh Page\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"CSV Market Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Real-time market data dashboard with enhanced WebSocket management and data caching\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_error_boundary__WEBPACK_IMPORTED_MODULE_4__.ErrorBoundary, {\n                    FallbackComponent: ErrorFallback,\n                    onError: (error, errorInfo)=>{\n                        console.error(\"Application Error:\", error, errorInfo);\n                    // Here you could send the error to an error reporting service\n                    },\n                    onReset: ()=>{\n                        // Clear any state that might be causing the error\n                        localStorage.removeItem(\"enhanced-market-data\");\n                        localStorage.removeItem(\"enhanced-market-timestamp\");\n                        sessionStorage.clear();\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20\",\n                        children: [\n                            children,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                                position: \"top-right\",\n                                toastOptions: {\n                                    duration: 4000,\n                                    style: {\n                                        background: \"#363636\",\n                                        color: \"#fff\"\n                                    },\n                                    success: {\n                                        style: {\n                                            background: \"#10B981\"\n                                        }\n                                    },\n                                    error: {\n                                        style: {\n                                            background: \"#EF4444\"\n                                        }\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/option-chain/page.tsx":
/*!***************************************!*\
  !*** ./src/app/option-chain/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OptionChainPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_OptionChain__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/OptionChain */ \"(ssr)/./src/components/OptionChain.tsx\");\n/* harmony import */ var _hooks_useMarketData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../hooks/useMarketData */ \"(ssr)/./src/hooks/useMarketData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction OptionChainPage() {\n    const { marketData } = (0,_hooks_useMarketData__WEBPACK_IMPORTED_MODULE_2__.useMarketData)();\n    // Convert array to Map for OptionChain component\n    const marketDataMap = new Map();\n    marketData.forEach((item)=>{\n        if (item.securityId) {\n            marketDataMap.set(item.securityId, item);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/\",\n                                    className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors\",\n                                    children: \"← Main Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/subscribed\",\n                                    className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors\",\n                                    children: \"\\uD83D\\uDCCA Subscribed Data\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        marketData.length,\n                                        \" instruments\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        marketData.filter((d)=>(d.ltp ?? 0) > 0).length,\n                                        \" active\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OptionChain__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                marketData: marketDataMap\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/option-chain/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/OptionChain.tsx":
/*!****************************************!*\
  !*** ./src/components/OptionChain.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OptionChain)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/data-cache */ \"(ssr)/./src/lib/data-cache.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction OptionChain({ marketData }) {\n    const [expiryData, setExpiryData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedExpiry, setSelectedExpiry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [optionChain, setOptionChain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [niftySpotPrice, setNiftySpotPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [cacheLoaded, setCacheLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load cached data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadCachedData = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDCD6 OptionChain: Loading cached data...\");\n                // Load cached option chain data\n                const cachedOptionChain = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.getCachedStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.OPTION_CHAIN);\n                if (cachedOptionChain && typeof cachedOptionChain === \"object\" && \"rows\" in cachedOptionChain) {\n                    setOptionChain(cachedOptionChain);\n                    console.log(\"✅ OptionChain: Loaded option chain from cache\");\n                }\n                // Load cached expiry data\n                const cachedExpiryData = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.getCachedStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.EXPIRY_DATES);\n                if (cachedExpiryData && typeof cachedExpiryData === \"object\" && \"expiries\" in cachedExpiryData && Array.isArray(cachedExpiryData.expiries)) {\n                    setExpiryData(cachedExpiryData);\n                    if (cachedExpiryData.expiries.length > 0) {\n                        setSelectedExpiry(cachedExpiryData.expiries[0]);\n                    }\n                    console.log(\"✅ OptionChain: Loaded expiry data from cache\");\n                }\n                // Load cached NIFTY spot price\n                const cachedNiftySpot = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.getCachedMarketData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.NIFTY_SPOT);\n                if (cachedNiftySpot && typeof cachedNiftySpot === \"object\" && \"ltp\" in cachedNiftySpot && typeof cachedNiftySpot.ltp === \"number\") {\n                    setNiftySpotPrice(cachedNiftySpot.ltp);\n                    console.log(\"✅ OptionChain: Loaded NIFTY spot from cache:\", cachedNiftySpot.ltp);\n                }\n                setCacheLoaded(true);\n            } catch (error) {\n                console.error(\"❌ OptionChain: Failed to load cached data:\", error);\n                setCacheLoaded(true);\n            }\n        };\n        loadCachedData();\n    }, []);\n    // ✅ ENHANCED: Get NIFTY spot price from subscribed market data with caching\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // ✅ FIXED: Direct lookup for NIFTY spot (security ID 13) - should be subscribed with IDX_I exchange\n        const niftyData = marketData.get(\"13\");\n        if (niftyData && niftyData.ltp > 0) {\n            setNiftySpotPrice(niftyData.ltp);\n            // Cache the NIFTY spot price\n            _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.cacheMarketData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.NIFTY_SPOT, niftyData);\n            console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Updated:\", niftyData.ltp, \"from security ID 13 (IDX_I)\");\n            return;\n        }\n        // ✅ ENHANCED: Fallback search for any NIFTY index data with better logging\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol === \"NIFTY\" && data.ltp > 0) {\n                setNiftySpotPrice(data.ltp);\n                // Cache the NIFTY spot price\n                _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.cacheMarketData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.NIFTY_SPOT, data);\n                console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Found:\", data.ltp, \"from security:\", securityId, \"exchange:\", data.exchange);\n                return;\n            }\n        }\n        // ✅ FIXED: Only use mock price if absolutely no real data is available and no cached data\n        if (niftySpotPrice === 0 && cacheLoaded) {\n            const mockPrice = 24850;\n            setNiftySpotPrice(mockPrice);\n            console.warn(\"[SPOT] ⚠️ Using mock NIFTY Spot Price:\", mockPrice, \"(INDEX data not available - check subscription)\");\n        }\n    }, [\n        marketData,\n        cacheLoaded,\n        niftySpotPrice\n    ]);\n    // Fetch expiry dates on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchExpiryDates();\n    }, []);\n    // Build option chain when expiry is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedExpiry && niftySpotPrice > 0) {\n            buildOptionChain();\n        }\n    }, [\n        selectedExpiry,\n        niftySpotPrice,\n        marketData,\n        buildOptionChain\n    ]);\n    const fetchExpiryDates = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Check cache first\n            const cachedExpiryData = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.getCachedStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.EXPIRY_DATES);\n            if (cachedExpiryData && typeof cachedExpiryData === \"object\" && \"expiries\" in cachedExpiryData && Array.isArray(cachedExpiryData.expiries)) {\n                console.log(\"✅ OptionChain: Using cached expiry data\");\n                setExpiryData(cachedExpiryData);\n                if (cachedExpiryData.expiries.length > 0) {\n                    setSelectedExpiry(cachedExpiryData.expiries[0]);\n                }\n                setLoading(false);\n                return;\n            }\n            console.log(\"\\uD83C\\uDF10 OptionChain: Fetching fresh expiry data from API\");\n            const response = await fetch(\"/api/nifty-expiry\");\n            if (!response.ok) {\n                throw new Error(`Failed to fetch expiry dates: ${response.statusText}`);\n            }\n            const result = await response.json();\n            if (result.success) {\n                setExpiryData(result.data);\n                // Cache the expiry data\n                await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.cacheStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.EXPIRY_DATES, result.data);\n                console.log(\"\\uD83D\\uDCBE OptionChain: Cached expiry data\");\n                // Auto-select first expiry\n                if (result.data.expiries.length > 0) {\n                    setSelectedExpiry(result.data.expiries[0]);\n                }\n            } else {\n                throw new Error(result.message || \"Failed to fetch expiry dates\");\n            }\n        } catch (err) {\n            console.error(\"❌ OptionChain: Error fetching expiry dates:\", err);\n            setError(err instanceof Error ? err.message : \"Unknown error\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const buildOptionChain = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!selectedExpiry || niftySpotPrice <= 0) return;\n        console.log(\"\\uD83D\\uDD17 Building option chain for expiry:\", selectedExpiry, \"spot:\", niftySpotPrice);\n        // Generate strike prices around spot price\n        const strikes = generateStrikes(niftySpotPrice);\n        // Build option chain rows\n        const rows = strikes.map((strike)=>{\n            const callData = findOptionData(strike, \"CE\");\n            const putData = findOptionData(strike, \"PE\");\n            return {\n                strikePrice: strike,\n                call: callData,\n                put: putData\n            };\n        });\n        const optionChainData = {\n            underlying: \"NIFTY\",\n            spotPrice: niftySpotPrice,\n            expiry: selectedExpiry,\n            rows,\n            timestamp: Date.now()\n        };\n        setOptionChain(optionChainData);\n        // Cache the option chain data\n        _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.cacheStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.OPTION_CHAIN, optionChainData);\n        console.log(\"\\uD83D\\uDCBE OptionChain: Cached option chain data for\", selectedExpiry);\n    }, [\n        selectedExpiry,\n        niftySpotPrice,\n        marketData\n    ]);\n    const generateStrikes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((spotPrice)=>{\n        // First, get all available strikes from market data for the selected expiry\n        const availableStrikes = new Set();\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol.includes(\"NIFTY\") && data.symbol.includes(formatExpiryForSymbol(selectedExpiry))) {\n                // Extract strike from symbol (e.g., NIFTY-Jun2025-24850-CE)\n                const symbolParts = data.symbol.split(\"-\");\n                if (symbolParts.length >= 4) {\n                    const strike = parseFloat(symbolParts[2]);\n                    if (!isNaN(strike)) {\n                        availableStrikes.add(strike);\n                    }\n                }\n            }\n        }\n        if (availableStrikes.size === 0) {\n            // Fallback: generate strikes around spot price\n            const strikes = [];\n            const baseStrike = Math.round(spotPrice / 50) * 50;\n            for(let i = -10; i <= 10; i++){\n                strikes.push(baseStrike + i * 50);\n            }\n            return strikes.sort((a, b)=>a - b);\n        }\n        // Convert to sorted array\n        const allStrikes = Array.from(availableStrikes).sort((a, b)=>a - b);\n        // Find ATM strike (closest to spot price)\n        const atmStrike = allStrikes.reduce((prev, curr)=>Math.abs(curr - spotPrice) < Math.abs(prev - spotPrice) ? curr : prev);\n        console.log(`[STRIKE] 🎯 ATM Strike identified: ${atmStrike} (Spot: ${spotPrice})`);\n        console.log(`[STRIKE] 📊 Available strikes: ${allStrikes.length}`);\n        // ✅ ENHANCED: Select more strikes around ATM (±12 strikes for better coverage)\n        const atmIndex = allStrikes.indexOf(atmStrike);\n        const selectedStrikes = [\n            ...allStrikes.slice(Math.max(0, atmIndex - 12), atmIndex),\n            atmStrike,\n            ...allStrikes.slice(atmIndex + 1, atmIndex + 13)\n        ];\n        console.log(`[STRIKE] ✅ Selected ${selectedStrikes.length} strikes around ATM:`, selectedStrikes);\n        return selectedStrikes;\n    }, [\n        selectedExpiry,\n        marketData\n    ]);\n    const findOptionData = (strike, optionType)=>{\n        // ✅ FIXED: Match by exact expiry date from CSV SM_EXPIRY_DATE column\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            // Check if this is a NIFTY option with matching criteria\n            if (data.symbol.includes(\"NIFTY-\") && // Exact NIFTY options (not BANKNIFTY, etc.)\n            data.symbol.includes(`-${strike}-${optionType}`) && data.expiryDate === selectedExpiry) {\n                console.log(`[OPTION] ✅ Found ${optionType} ${strike}: ${data.symbol} (Expiry: ${data.expiryDate})`);\n                return {\n                    securityId,\n                    symbol: data.symbol,\n                    exchange: data.exchange,\n                    strikePrice: strike,\n                    optionType,\n                    expiryDate: selectedExpiry,\n                    ltp: data.ltp || 0,\n                    change: data.change || 0,\n                    changePercent: data.changePercent || 0,\n                    volume: data.volume || 0,\n                    openInterest: data.openInterest,\n                    bid: data.marketDepth?.[0]?.bidPrice || data.bid,\n                    ask: data.marketDepth?.[0]?.askPrice || data.ask,\n                    bidQty: data.marketDepth?.[0]?.bidQty || data.bidQty,\n                    askQty: data.marketDepth?.[0]?.askQty || data.askQty,\n                    high: data.high,\n                    low: data.low,\n                    open: data.open,\n                    close: data.close,\n                    timestamp: data.timestamp\n                };\n            }\n        }\n        // Debug: Log when option not found\n        if (true) {\n            console.log(`[OPTION] ❌ Not found ${optionType} ${strike} for expiry ${selectedExpiry}`);\n            // Show available options for debugging\n            const availableOptions = Array.from(marketData.values()).filter((data)=>data.symbol.includes(\"NIFTY-\") && data.symbol.includes(`-${optionType}`)).slice(0, 5);\n            console.log(`[DEBUG] Available ${optionType} options:`, availableOptions.map((opt)=>`${opt.symbol} (Expiry: ${opt.expiryDate})`));\n        }\n        return null;\n    };\n    const formatExpiryForSymbol = (expiry)=>{\n        // Convert YYYY-MM-DD to format used in symbols (e.g., Jun2025)\n        const date = new Date(expiry);\n        const months = [\n            \"Jan\",\n            \"Feb\",\n            \"Mar\",\n            \"Apr\",\n            \"May\",\n            \"Jun\",\n            \"Jul\",\n            \"Aug\",\n            \"Sep\",\n            \"Oct\",\n            \"Nov\",\n            \"Dec\"\n        ];\n        return `${months[date.getMonth()]}${date.getFullYear()}`;\n    };\n    // Use centralized formatters for consistency\n    const formatPrice = _lib_utils__WEBPACK_IMPORTED_MODULE_2__.MarketFormatters.price;\n    const formatNumber = _lib_utils__WEBPACK_IMPORTED_MODULE_2__.MarketFormatters.number;\n    // Use centralized formatters for consistency\n    const getChangeColor = (change)=>{\n        if (!change) return \"text-gray-400\";\n        return change > 0 ? \"text-green-400\" : change < 0 ? \"text-red-400\" : \"text-gray-400\";\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading option chain...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 312,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 311,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-red-800 font-medium\",\n                    children: \"Error Loading Option Chain\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 text-sm mt-1\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: fetchExpiryDates,\n                    className: \"mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700\",\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 322,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Options\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md\",\n                                            children: \"\\uD83D\\uDD0D NIFTY\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Strategy builder\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Class\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Volatility\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"By expiration | by strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: formatPrice(niftySpotPrice)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this),\n                                        niftySpotPrice === 24850 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"(Mock)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this),\n            expiryData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 overflow-x-auto pb-2\",\n                        children: expiryData.expiries.slice(0, 15).map((expiry, index)=>{\n                            const date = new Date(expiry);\n                            const isSelected = expiry === selectedExpiry;\n                            const isToday = date.toDateString() === new Date().toDateString();\n                            const isCurrentWeek = Math.abs(date.getTime() - new Date().getTime()) < 7 * 24 * 60 * 60 * 1000;\n                            // Enhanced date formatting\n                            const currentYear = new Date().getFullYear();\n                            const expiryYear = date.getFullYear();\n                            const monthShort = date.toLocaleDateString(\"en-US\", {\n                                month: \"short\"\n                            });\n                            const day = date.getDate();\n                            // Show year if different from current year\n                            const showYear = expiryYear !== currentYear;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedExpiry(expiry),\n                                className: `flex-shrink-0 px-3 py-2 text-xs font-medium transition-colors border ${isSelected ? \"bg-black text-white border-black\" : isToday ? \"bg-orange-500 text-white border-orange-500\" : isCurrentWeek ? \"bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200\" : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center min-w-[40px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-normal text-gray-600\",\n                                            children: [\n                                                monthShort,\n                                                showYear && ` ${expiryYear.toString().slice(-2)}`\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-bold text-sm\",\n                                            children: day\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 19\n                                }, this)\n                            }, expiry, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 11\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-xs text-gray-500 bg-gray-100 p-3 rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Selected Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        selectedExpiry\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" ₹\",\n                                        niftySpotPrice,\n                                        \" \",\n                                        niftySpotPrice === 24850 ? \"(Mock)\" : \"(Live)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Available Strikes:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        optionChain?.rows.length || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Market Data Size:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        marketData.size,\n                                        \" instruments\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Options:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\")).length,\n                                        \" found\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Options with Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\") && d.expiryDate).length,\n                                        \" matched\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 427,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 380,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-50 border-b border-gray-200 text-xs font-medium text-gray-700 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 font-bold\",\n                                    children: \"Calls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold\",\n                                    children: \"Strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-600 font-bold\",\n                                    children: \"Puts\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-100 border-b border-gray-200 text-xs font-medium text-gray-600 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1 border-r border-gray-200\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-2 px-1 border-r border-gray-200 font-bold\",\n                                children: \"Strike\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-100\",\n                        children: optionChain.rows.map((row, index)=>{\n                            const isATM = Math.abs(row.strikePrice - niftySpotPrice) <= 50;\n                            const isITM_Call = row.strikePrice < niftySpotPrice;\n                            const isITM_Put = row.strikePrice > niftySpotPrice;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `flex hover:bg-gray-50 transition-colors ${isATM ? \"bg-yellow-50\" : index % 2 === 0 ? \"bg-white\" : \"bg-gray-25\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm ${isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"}`,\n                                        children: formatNumber(row.call?.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm ${isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"}`,\n                                        children: formatNumber(row.call?.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice(row.call?.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice(row.call?.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm font-medium ${getChangeColor(row.call?.change)}`,\n                                        children: row.call?.change ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.call.change > 0 ? \"+\" : \"\",\n                                                row.call.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.call.changePercent > 0 ? \"+\" : \"\",\n                                                        row.call.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 ${isITM_Call ? \"text-green-600\" : \"text-gray-700\"}`,\n                                        children: formatPrice(row.call?.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-20 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 ${isATM ? \"bg-yellow-100 text-yellow-800\" : \"text-gray-900\"}`,\n                                        children: row.strikePrice\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm font-bold ${isITM_Put ? \"text-red-600\" : \"text-gray-700\"}`,\n                                        children: formatPrice(row.put?.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm font-medium ${getChangeColor(row.put?.change)}`,\n                                        children: row.put?.change ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.put.change > 0 ? \"+\" : \"\",\n                                                row.put.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.put.changePercent > 0 ? \"+\" : \"\",\n                                                        row.put.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice(row.put?.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice(row.put?.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm ${isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"}`,\n                                        children: formatNumber(row.put?.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm ${isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"}`,\n                                        children: formatNumber(row.put?.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, row.strikePrice, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 482,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 442,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 border-t border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Show all\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Showing \",\n                                        optionChain.rows.length,\n                                        \" strikes around ATM\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 570,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"Last updated: \",\n                                new Date(optionChain.timestamp).toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 575,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 569,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 568,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n        lineNumber: 336,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/OptionChain.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useMarketData.ts":
/*!************************************!*\
  !*** ./src/hooks/useMarketData.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useMarketData: () => (/* binding */ useMarketData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_marketStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/marketStore */ \"(ssr)/./src/store/marketStore.ts\");\n/**\r\n * Custom hook for Market Data Management\r\n * Provides a clean interface to the Zustand market store\r\n */ \n\nconst useMarketData = ()=>{\n    const store = (0,_store_marketStore__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    // Hydrate from Redis on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        fetch(\"/api/cache/all-latest\").then((res)=>res.json()).then(({ data })=>{\n            if (Array.isArray(data)) store.hydrateFromRedis(data);\n        });\n    }, [\n        store\n    ]);\n    // WebSocket tick handling (example, adapt as needed)\n    // ws.on('tick', tick => store.updateMarketData(tick));\n    // ws.on('batch', ticks => store.updateMarketDataBatch(ticks));\n    return {\n        marketData: Object.values(store.marketData),\n        updateMarketData: store.updateMarketData,\n        updateMarketDataBatch: store.updateMarketDataBatch,\n        hydrateFromRedis: store.hydrateFromRedis\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useMarketData);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useMarketData.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/constants.ts":
/*!******************************!*\
  !*** ./src/lib/constants.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: () => (/* binding */ API_CONFIG),\n/* harmony export */   CACHE_CONFIG: () => (/* binding */ CACHE_CONFIG),\n/* harmony export */   CHART_CONFIG: () => (/* binding */ CHART_CONFIG),\n/* harmony export */   COLORS: () => (/* binding */ COLORS),\n/* harmony export */   DEFAULTS: () => (/* binding */ DEFAULTS),\n/* harmony export */   DHAN_CONFIG: () => (/* binding */ DHAN_CONFIG),\n/* harmony export */   ENV_CONFIG: () => (/* binding */ ENV_CONFIG),\n/* harmony export */   ERROR_MESSAGES: () => (/* binding */ ERROR_MESSAGES),\n/* harmony export */   EXCHANGE_SEGMENTS: () => (/* binding */ EXCHANGE_SEGMENTS),\n/* harmony export */   FEATURE_FLAGS: () => (/* binding */ FEATURE_FLAGS),\n/* harmony export */   INSTRUMENT_TYPES: () => (/* binding */ INSTRUMENT_TYPES),\n/* harmony export */   LOGGING_CONFIG: () => (/* binding */ LOGGING_CONFIG),\n/* harmony export */   MARKET_CONFIG: () => (/* binding */ MARKET_CONFIG),\n/* harmony export */   OPTION_TYPES: () => (/* binding */ OPTION_TYPES),\n/* harmony export */   PACKET_TYPES: () => (/* binding */ PACKET_TYPES),\n/* harmony export */   PERFORMANCE_CONFIG: () => (/* binding */ PERFORMANCE_CONFIG),\n/* harmony export */   REQUEST_CODES: () => (/* binding */ REQUEST_CODES),\n/* harmony export */   SECURITY_CONFIG: () => (/* binding */ SECURITY_CONFIG),\n/* harmony export */   STORAGE_KEYS: () => (/* binding */ STORAGE_KEYS),\n/* harmony export */   SUCCESS_MESSAGES: () => (/* binding */ SUCCESS_MESSAGES),\n/* harmony export */   UI_CONFIG: () => (/* binding */ UI_CONFIG),\n/* harmony export */   VALIDATION: () => (/* binding */ VALIDATION),\n/* harmony export */   WEBSOCKET_CONFIG: () => (/* binding */ WEBSOCKET_CONFIG)\n/* harmony export */ });\n/**\r\n * Application Constants\r\n */ // API Configuration\nconst API_CONFIG = {\n    BASE_URL: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8080\",\n    TIMEOUT: 30000,\n    RETRY_ATTEMPTS: 3,\n    RETRY_DELAY: 1000\n};\n// WebSocket Configuration\nconst WEBSOCKET_CONFIG = {\n    RECONNECT_INTERVAL: 3000,\n    MAX_RECONNECT_ATTEMPTS: 15,\n    PING_INTERVAL: 25000,\n    PONG_TIMEOUT: 10000,\n    CONNECTION_TIMEOUT: 20000,\n    HEARTBEAT_INTERVAL: 30000,\n    MAX_LISTENERS_PER_EVENT: 10,\n    CLEANUP_INTERVAL: 60000\n};\n// Dhan API Configuration\nconst DHAN_CONFIG = {\n    BASE_URL: \"https://api.dhan.co\",\n    WEBSOCKET_URL: \"wss://api.dhan.co/v2/wsapi\",\n    MAX_INSTRUMENTS_PER_CONNECTION: 5000,\n    MAX_INSTRUMENTS_PER_MESSAGE: 100,\n    RATE_LIMIT: {\n        REQUESTS_PER_SECOND: 10,\n        REQUESTS_PER_MINUTE: 600\n    }\n};\n// Market Configuration\nconst MARKET_CONFIG = {\n    TRADING_HOURS: {\n        START: {\n            hour: 9,\n            minute: 15\n        },\n        END: {\n            hour: 15,\n            minute: 30\n        }\n    },\n    TRADING_DAYS: [\n        1,\n        2,\n        3,\n        4,\n        5\n    ],\n    REFRESH_INTERVAL: 1000,\n    BATCH_SIZE: 100\n};\n// Instrument Types\nconst INSTRUMENT_TYPES = {\n    EQUITY: \"EQUITY\",\n    INDEX: \"INDEX\",\n    FUTIDX: \"FUTIDX\",\n    OPTIDX: \"OPTIDX\",\n    FUTSTK: \"FUTSTK\",\n    OPTSTK: \"OPTSTK\",\n    FUTCUR: \"FUTCUR\",\n    OPTCUR: \"OPTCUR\",\n    FUTCOM: \"FUTCOM\",\n    OPTFUT: \"OPTFUT\"\n};\n// Exchange Segments\nconst EXCHANGE_SEGMENTS = {\n    NSE_EQ: \"NSE_EQ\",\n    NSE_FNO: \"NSE_FNO\",\n    BSE_EQ: \"BSE_EQ\",\n    MCX_COMM: \"MCX_COMM\",\n    IDX_I: \"IDX_I\"\n};\n// Option Types\nconst OPTION_TYPES = {\n    CALL: \"CE\",\n    PUT: \"PE\"\n};\n// Market Data Packet Types\nconst PACKET_TYPES = {\n    TOUCHLINE: 1,\n    QUOTE: 2,\n    SNAP_QUOTE: 3,\n    FULL_PACKET: 4,\n    OI: 5\n};\n// WebSocket Request Codes\nconst REQUEST_CODES = {\n    SUBSCRIBE: 21,\n    UNSUBSCRIBE: 22,\n    SNAP_QUOTE: 23\n};\n// UI Configuration\nconst UI_CONFIG = {\n    DEBOUNCE_DELAY: 300,\n    THROTTLE_DELAY: 100,\n    ANIMATION_DURATION: 200,\n    TOAST_DURATION: 3000,\n    PAGINATION: {\n        DEFAULT_PAGE_SIZE: 50,\n        PAGE_SIZE_OPTIONS: [\n            25,\n            50,\n            100,\n            200\n        ]\n    }\n};\n// Color Schemes\nconst COLORS = {\n    SUCCESS: \"#10B981\",\n    ERROR: \"#EF4444\",\n    WARNING: \"#F59E0B\",\n    INFO: \"#3B82F6\",\n    NEUTRAL: \"#6B7280\",\n    BID: \"#10B981\",\n    ASK: \"#EF4444\",\n    SPOT: \"#3B82F6\"\n};\n// Chart Configuration\nconst CHART_CONFIG = {\n    DEFAULT_HEIGHT: 400,\n    COLORS: {\n        PRIMARY: \"#3B82F6\",\n        SECONDARY: \"#10B981\",\n        ACCENT: \"#F59E0B\",\n        GRID: \"#E5E7EB\",\n        TEXT: \"#374151\"\n    },\n    ANIMATION: {\n        DURATION: 300,\n        EASING: \"ease-in-out\"\n    }\n};\n// Data Validation\nconst VALIDATION = {\n    MIN_STRIKE_PRICE: 1,\n    MAX_STRIKE_PRICE: 100000,\n    MIN_OPTION_PRICE: 0.01,\n    MAX_OPTION_PRICE: 50000,\n    MIN_VOLUME: 0,\n    MAX_VOLUME: 10000000\n};\n// Error Messages\nconst ERROR_MESSAGES = {\n    NETWORK_ERROR: \"Network connection failed. Please check your internet connection.\",\n    API_ERROR: \"API request failed. Please try again later.\",\n    WEBSOCKET_ERROR: \"WebSocket connection failed. Attempting to reconnect...\",\n    DATA_PARSING_ERROR: \"Failed to parse market data. Please refresh the page.\",\n    SUBSCRIPTION_ERROR: \"Failed to subscribe to market data. Please try again.\",\n    INVALID_INSTRUMENT: \"Invalid instrument selected.\",\n    MARKET_CLOSED: \"Market is currently closed.\",\n    RATE_LIMIT_EXCEEDED: \"Too many requests. Please wait before trying again.\"\n};\n// Success Messages\nconst SUCCESS_MESSAGES = {\n    CONNECTION_ESTABLISHED: \"Successfully connected to market data feed.\",\n    SUBSCRIPTION_SUCCESS: \"Successfully subscribed to market data.\",\n    DATA_UPDATED: \"Market data updated successfully.\",\n    SETTINGS_SAVED: \"Settings saved successfully.\"\n};\n// Local Storage Keys\nconst STORAGE_KEYS = {\n    USER_PREFERENCES: \"csv_market_dashboard_preferences\",\n    SELECTED_INSTRUMENTS: \"csv_market_dashboard_selected_instruments\",\n    THEME: \"csv_market_dashboard_theme\",\n    LAYOUT: \"csv_market_dashboard_layout\"\n};\n// Feature Flags\nconst FEATURE_FLAGS = {\n    ENABLE_CHARTS: true,\n    ENABLE_ALERTS: true,\n    ENABLE_EXPORT: true,\n    ENABLE_DARK_MODE: true,\n    ENABLE_REAL_TIME_UPDATES: true,\n    ENABLE_OPTION_GREEKS: false\n};\n// Performance Monitoring\nconst PERFORMANCE_CONFIG = {\n    MEMORY_WARNING_THRESHOLD: 100 * 1024 * 1024,\n    CPU_WARNING_THRESHOLD: 80,\n    NETWORK_TIMEOUT_WARNING: 5000,\n    MAX_CONCURRENT_REQUESTS: 10\n};\n// Logging Configuration\nconst LOGGING_CONFIG = {\n    LEVELS: {\n        ERROR: 0,\n        WARN: 1,\n        INFO: 2,\n        DEBUG: 3\n    },\n    MAX_LOG_SIZE: 10 * 1024 * 1024,\n    MAX_LOG_FILES: 5,\n    LOG_ROTATION_INTERVAL: 24 * 60 * 60 * 1000\n};\n// Cache Configuration\nconst CACHE_CONFIG = {\n    DEFAULT_TTL: 5 * 60 * 1000,\n    MARKET_DATA_TTL: 10 * 60 * 1000,\n    STATIC_DATA_TTL: 30 * 60 * 1000,\n    EXPIRY_CHECK_INTERVAL: 60 * 1000,\n    MAX_CACHE_SIZE: 50 * 1024 * 1024,\n    KEYS: {\n        INSTRUMENTS: \"instruments\",\n        MARKET_DATA: \"market_data\",\n        OPTION_CHAIN: \"option_chain\",\n        EXPIRY_DATES: \"expiry_dates\",\n        NIFTY_SPOT: \"nifty_spot\",\n        USER_SETTINGS: \"user_settings\",\n        USER_PREFERENCES: \"user_preferences\"\n    }\n};\n// Security Configuration\nconst SECURITY_CONFIG = {\n    RATE_LIMITING: {\n        WINDOW_MS: 15 * 60 * 1000,\n        MAX_REQUESTS: 1000\n    },\n    CORS: {\n        ORIGIN:  false ? 0 : [\n            \"http://localhost:3000\",\n            \"http://localhost:3001\"\n        ],\n        CREDENTIALS: true\n    },\n    HEADERS: {\n        CONTENT_SECURITY_POLICY: \"default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';\",\n        X_FRAME_OPTIONS: \"DENY\",\n        X_CONTENT_TYPE_OPTIONS: \"nosniff\"\n    }\n};\n// Environment Configuration\nconst ENV_CONFIG = {\n    NODE_ENV: \"development\" || 0,\n    PORT: parseInt(process.env.PORT || \"8080\"),\n    NEXT_PORT: parseInt(process.env.NEXT_PORT || \"3000\"),\n    LOG_LEVEL: process.env.LOG_LEVEL || \"info\",\n    ENABLE_METRICS: process.env.ENABLE_METRICS === \"true\"\n};\n// Default Values\nconst DEFAULTS = {\n    NIFTY_SPOT_PRICE: 24850,\n    SELECTED_EXPIRY: \"2025-06-19\",\n    STRIKES_TO_SHOW: 20,\n    REFRESH_INTERVAL: 1000,\n    CHART_TIMEFRAME: \"1D\",\n    TABLE_PAGE_SIZE: 50\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/constants.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/data-cache.ts":
/*!*******************************!*\
  !*** ./src/lib/data-cache.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MARKET_DATA_CACHE_KEYS: () => (/* binding */ MARKET_DATA_CACHE_KEYS),\n/* harmony export */   cacheHelpers: () => (/* binding */ cacheHelpers),\n/* harmony export */   dataCache: () => (/* binding */ dataCache)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(ssr)/./src/lib/constants.ts\");\n/**\r\n * Data Caching System for Market Data Persistence\r\n * Handles localStorage/sessionStorage with TTL and compression\r\n */ \nclass DataCache {\n    static{\n        this.instance = null;\n    }\n    /**\r\n   * Check if we're in a browser environment\r\n   */ isBrowser() {\n        return  false && 0;\n    }\n    static getInstance() {\n        if (!DataCache.instance) {\n            DataCache.instance = new DataCache();\n        }\n        return DataCache.instance;\n    }\n    /**\r\n   * Initialize cache for client-side usage\r\n   * Call this method when the component mounts on the client side\r\n   */ initializeClientSide() {\n        if (this.isBrowser()) {\n            console.log(\"\\uD83D\\uDE80 DataCache: Initializing client-side cache\");\n            // Perform any client-side specific initialization here\n            this.cleanupExpired() // Run initial cleanup\n            ;\n        }\n    }\n    constructor(){\n        this.version = \"1.0.0\";\n        this.compressionSupported = false;\n        this.checkCompressionSupport();\n        this.startCleanupInterval();\n    }\n    /**\r\n   * Check if compression is supported\r\n   */ checkCompressionSupport() {\n        try {\n            // Check if CompressionStream is available (modern browsers)\n            this.compressionSupported = typeof CompressionStream !== \"undefined\";\n        } catch  {\n            this.compressionSupported = false;\n        }\n    }\n    /**\r\n   * Set data in cache with TTL\r\n   */ async set(key, data, options = {}) {\n        try {\n            // Skip caching on server side\n            if (!this.isBrowser()) {\n                console.log(`⚠️ DataCache: Skipping cache on server side for ${key}`);\n                return false;\n            }\n            const { ttl = _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.DEFAULT_TTL, useCompression = false, storage = \"localStorage\" } = options;\n            const entry = {\n                data,\n                timestamp: Date.now(),\n                ttl,\n                version: this.version\n            };\n            let serializedData = JSON.stringify(entry);\n            // Apply compression if requested and supported\n            if (useCompression && this.compressionSupported) {\n                serializedData = await this.compress(serializedData);\n            }\n            const storageObj = storage === \"localStorage\" ? localStorage : sessionStorage;\n            const fullKey = this.getFullKey(key);\n            storageObj.setItem(fullKey, serializedData);\n            console.log(`💾 DataCache: Cached ${key} (${serializedData.length} bytes)`);\n            return true;\n        } catch (error) {\n            console.error(`❌ DataCache: Failed to cache ${key}:`, error);\n            return false;\n        }\n    }\n    /**\r\n   * Get data from cache\r\n   */ async get(key, options = {}) {\n        try {\n            // Return null on server side\n            if (!this.isBrowser()) {\n                return null;\n            }\n            const { storage = \"localStorage\" } = options;\n            const storageObj = storage === \"localStorage\" ? localStorage : sessionStorage;\n            const fullKey = this.getFullKey(key);\n            const serializedData = storageObj.getItem(fullKey);\n            if (!serializedData) {\n                return null;\n            }\n            let decompressedData = serializedData;\n            // Try to decompress if it looks compressed\n            if (this.compressionSupported && this.isCompressed(serializedData)) {\n                decompressedData = await this.decompress(serializedData);\n            }\n            const entry = JSON.parse(decompressedData);\n            // Check version compatibility\n            if (entry.version !== this.version) {\n                console.warn(`⚠️ DataCache: Version mismatch for ${key}, removing`);\n                this.remove(key, options);\n                return null;\n            }\n            // Check TTL\n            if (Date.now() - entry.timestamp > entry.ttl) {\n                console.log(`⏰ DataCache: ${key} expired, removing`);\n                this.remove(key, options);\n                return null;\n            }\n            console.log(`📖 DataCache: Retrieved ${key} from cache`);\n            return entry.data;\n        } catch (error) {\n            console.error(`❌ DataCache: Failed to retrieve ${key}:`, error);\n            // Remove corrupted entry\n            this.remove(key, options);\n            return null;\n        }\n    }\n    /**\r\n   * Remove data from cache\r\n   */ remove(key, options = {}) {\n        try {\n            // Skip on server side\n            if (!this.isBrowser()) {\n                return;\n            }\n            const { storage = \"localStorage\" } = options;\n            const storageObj = storage === \"localStorage\" ? localStorage : sessionStorage;\n            const fullKey = this.getFullKey(key);\n            storageObj.removeItem(fullKey);\n            console.log(`🗑️ DataCache: Removed ${key}`);\n        } catch (error) {\n            console.error(`❌ DataCache: Failed to remove ${key}:`, error);\n        }\n    }\n    /**\r\n   * Clear all cache entries\r\n   */ clear(storage = \"localStorage\") {\n        try {\n            // Skip on server side\n            if (!this.isBrowser()) {\n                return;\n            }\n            const storageObj = storage === \"localStorage\" ? localStorage : sessionStorage;\n            const keysToRemove = [];\n            // Find all our cache keys\n            for(let i = 0; i < storageObj.length; i++){\n                const key = storageObj.key(i);\n                if (key && key.startsWith(\"csv_market_dashboard_cache_\")) {\n                    keysToRemove.push(key);\n                }\n            }\n            // Remove them\n            keysToRemove.forEach((key)=>storageObj.removeItem(key));\n            console.log(`🧹 DataCache: Cleared ${keysToRemove.length} entries from ${storage}`);\n        } catch (error) {\n            console.error(`❌ DataCache: Failed to clear ${storage}:`, error);\n        }\n    }\n    /**\r\n   * Get cache statistics\r\n   */ getStats(storage = \"localStorage\") {\n        // Return empty stats on server side\n        if (!this.isBrowser()) {\n            return {\n                totalEntries: 0,\n                totalSize: 0,\n                oldestEntry: null,\n                newestEntry: null\n            };\n        }\n        const storageObj = storage === \"localStorage\" ? localStorage : sessionStorage;\n        let totalEntries = 0;\n        let totalSize = 0;\n        let oldestTimestamp = Infinity;\n        let newestTimestamp = 0;\n        try {\n            for(let i = 0; i < storageObj.length; i++){\n                const key = storageObj.key(i);\n                if (key && key.startsWith(\"csv_market_dashboard_cache_\")) {\n                    const value = storageObj.getItem(key);\n                    if (value) {\n                        totalEntries++;\n                        totalSize += value.length;\n                        try {\n                            const entry = JSON.parse(value);\n                            if (entry.timestamp) {\n                                oldestTimestamp = Math.min(oldestTimestamp, entry.timestamp);\n                                newestTimestamp = Math.max(newestTimestamp, entry.timestamp);\n                            }\n                        } catch  {\n                        // Ignore parsing errors for stats\n                        }\n                    }\n                }\n            }\n        } catch (error) {\n            console.error(\"❌ DataCache: Failed to get stats:\", error);\n        }\n        return {\n            totalEntries,\n            totalSize,\n            oldestEntry: oldestTimestamp === Infinity ? null : new Date(oldestTimestamp),\n            newestEntry: newestTimestamp === 0 ? null : new Date(newestTimestamp)\n        };\n    }\n    /**\r\n   * Start cleanup interval to remove expired entries\r\n   */ startCleanupInterval() {\n        // Only start cleanup interval on client side\n        if (this.isBrowser()) {\n            setInterval(()=>{\n                this.cleanupExpired();\n            }, _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.EXPIRY_CHECK_INTERVAL);\n        }\n    }\n    /**\r\n   * Clean up expired entries\r\n   */ cleanupExpired() {\n        try {\n            // Skip cleanup on server side\n            if (!this.isBrowser()) {\n                return;\n            }\n            const storages = [\n                \"localStorage\",\n                \"sessionStorage\"\n            ];\n            storages.forEach((storageType)=>{\n                const storageObj = storageType === \"localStorage\" ? localStorage : sessionStorage;\n                const keysToRemove = [];\n                for(let i = 0; i < storageObj.length; i++){\n                    const key = storageObj.key(i);\n                    if (key && key.startsWith(\"csv_market_dashboard_cache_\")) {\n                        try {\n                            const value = storageObj.getItem(key);\n                            if (value) {\n                                const entry = JSON.parse(value);\n                                if (Date.now() - entry.timestamp > entry.ttl) {\n                                    keysToRemove.push(key);\n                                }\n                            }\n                        } catch  {\n                            // Remove corrupted entries\n                            keysToRemove.push(key);\n                        }\n                    }\n                }\n                keysToRemove.forEach((key)=>storageObj.removeItem(key));\n                if (keysToRemove.length > 0) {\n                    console.log(`🧹 DataCache: Cleaned up ${keysToRemove.length} expired entries from ${storageType}`);\n                }\n            });\n        } catch (error) {\n            console.error(\"❌ DataCache: Cleanup failed:\", error);\n        }\n    }\n    /**\r\n   * Generate full cache key\r\n   */ getFullKey(key) {\n        return `csv_market_dashboard_cache_${key}`;\n    }\n    /**\r\n   * Check if data is compressed\r\n   */ isCompressed(data) {\n        // Simple heuristic: compressed data usually starts with specific bytes\n        return data.startsWith(\"H4sI\") || data.startsWith(\"eJy\") // Common compression signatures\n        ;\n    }\n    /**\r\n   * Compress data (placeholder for future implementation)\r\n   */ async compress(data) {\n        // For now, return as-is. Can implement actual compression later\n        return data;\n    }\n    /**\r\n   * Decompress data (placeholder for future implementation)\r\n   */ async decompress(data) {\n        // For now, return as-is. Can implement actual decompression later\n        return data;\n    }\n}\n// Export singleton instance\nconst dataCache = DataCache.getInstance();\n// Export cache keys for market data\nconst MARKET_DATA_CACHE_KEYS = _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.KEYS;\n// Export helper functions\nconst cacheHelpers = {\n    /**\r\n   * Cache market data with medium TTL for page refresh persistence\r\n   */ cacheMarketData: async (key, data)=>{\n        return dataCache.set(key, data, {\n            ttl: _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.MARKET_DATA_TTL,\n            storage: \"localStorage\" // Use localStorage for page refresh persistence\n        });\n    },\n    /**\r\n   * Cache static data with long TTL\r\n   */ cacheStaticData: async (key, data)=>{\n        return dataCache.set(key, data, {\n            ttl: _constants__WEBPACK_IMPORTED_MODULE_0__.CACHE_CONFIG.STATIC_DATA_TTL,\n            storage: \"localStorage\" // Use local storage for persistent data\n        });\n    },\n    /**\r\n   * Get cached market data\r\n   */ getCachedMarketData: async (key)=>{\n        return dataCache.get(key, {\n            storage: \"localStorage\"\n        });\n    },\n    /**\r\n   * Get cached static data\r\n   */ getCachedStaticData: async (key)=>{\n        return dataCache.get(key, {\n            storage: \"localStorage\"\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/data-cache.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketFormatters: () => (/* binding */ MarketFormatters),\n/* harmony export */   calculateImpliedVolatility: () => (/* binding */ calculateImpliedVolatility),\n/* harmony export */   capitalize: () => (/* binding */ capitalize),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatIndianNumber: () => (/* binding */ formatIndianNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   formatValueWithFallback: () => (/* binding */ formatValueWithFallback),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getRelativeTime: () => (/* binding */ getRelativeTime),\n/* harmony export */   getValueColor: () => (/* binding */ getValueColor),\n/* harmony export */   isEmpty: () => (/* binding */ isEmpty),\n/* harmony export */   isMarketHours: () => (/* binding */ isMarketHours),\n/* harmony export */   retry: () => (/* binding */ retry),\n/* harmony export */   safeJsonParse: () => (/* binding */ safeJsonParse),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   toTitleCase: () => (/* binding */ toTitleCase),\n/* harmony export */   truncate: () => (/* binding */ truncate)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\r\n * Utility function to merge Tailwind CSS classes\r\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\r\n * Format number as Indian currency\r\n */ function formatCurrency(value) {\n    return new Intl.NumberFormat(\"en-IN\", {\n        style: \"currency\",\n        currency: \"INR\",\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    }).format(value);\n}\n/**\r\n * Format number with Indian number system (lakhs, crores)\r\n */ function formatIndianNumber(value) {\n    if (value >= 10000000) {\n        return `${(value / 10000000).toFixed(2)} Cr`;\n    } else if (value >= 100000) {\n        return `${(value / 100000).toFixed(2)} L`;\n    } else if (value >= 1000) {\n        return `${(value / 1000).toFixed(2)} K`;\n    }\n    return value.toString();\n}\n/**\r\n * Format percentage with proper sign\r\n */ function formatPercentage(value) {\n    const sign = value >= 0 ? \"+\" : \"\";\n    return `${sign}${value.toFixed(2)}%`;\n}\n/**\r\n * Get color class based on value (green for positive, red for negative)\r\n */ function getValueColor(value) {\n    if (value > 0) return \"text-green-600\";\n    if (value < 0) return \"text-red-600\";\n    return \"text-gray-600\";\n}\n/**\r\n * Debounce function for performance optimization\r\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\r\n * Throttle function for performance optimization\r\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\r\n * Deep clone object\r\n */ function deepClone(obj) {\n    if (obj === null || typeof obj !== \"object\") return obj;\n    if (obj instanceof Date) return new Date(obj.getTime());\n    if (obj instanceof Array) return obj.map((item)=>deepClone(item));\n    if (typeof obj === \"object\") {\n        const clonedObj = {};\n        for(const key in obj){\n            if (obj.hasOwnProperty(key)) {\n                clonedObj[key] = deepClone(obj[key]);\n            }\n        }\n        return clonedObj;\n    }\n    return obj;\n}\n/**\r\n * Generate unique ID\r\n */ function generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n/**\r\n * Sleep function for async operations\r\n */ function sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\r\n * Retry function with exponential backoff\r\n */ async function retry(fn, maxAttempts = 3, baseDelay = 1000) {\n    let lastError;\n    for(let attempt = 1; attempt <= maxAttempts; attempt++){\n        try {\n            return await fn();\n        } catch (error) {\n            lastError = error;\n            if (attempt === maxAttempts) {\n                throw lastError;\n            }\n            const delay = baseDelay * Math.pow(2, attempt - 1);\n            await sleep(delay);\n        }\n    }\n    throw lastError;\n}\n/**\r\n * Safe JSON parse with fallback\r\n */ function safeJsonParse(json, fallback) {\n    try {\n        return JSON.parse(json);\n    } catch  {\n        return fallback;\n    }\n}\n/**\r\n * Check if value is empty (null, undefined, empty string, empty array, empty object)\r\n */ function isEmpty(value) {\n    if (value == null) return true;\n    if (typeof value === \"string\") return value.trim() === \"\";\n    if (Array.isArray(value)) return value.length === 0;\n    if (typeof value === \"object\") return Object.keys(value).length === 0;\n    return false;\n}\n/**\r\n * Format value with fallback to dash for empty values\r\n */ function formatValueWithFallback(value, formatter) {\n    if (isEmpty(value) || value === 0) return \"-\";\n    return formatter ? formatter(value) : String(value);\n}\n/**\r\n * Market data formatting utilities with consistent dash fallbacks\r\n */ const MarketFormatters = {\n    /**\r\n   * Format price with currency symbol\r\n   */ price: (price)=>{\n        if (!price || price <= 0) return \"-\";\n        return `₹${price.toFixed(2)}`;\n    },\n    /**\r\n   * Format number with locale formatting\r\n   */ number: (num)=>{\n        if (num === undefined || num === null) return \"-\";\n        return num.toLocaleString();\n    },\n    /**\r\n   * Format percentage with sign\r\n   */ percentage: (value)=>{\n        if (!value || value === 0) return \"-\";\n        const sign = value >= 0 ? \"+\" : \"\";\n        return `${sign}${value.toFixed(2)}%`;\n    },\n    /**\r\n   * Format change with price and percentage\r\n   */ change: (change, changePercent)=>{\n        if (!change || change === 0) return \"-\";\n        const sign = change > 0 ? \"+\" : \"\";\n        const percentStr = changePercent ? ` (${sign}${changePercent.toFixed(2)}%)` : \"\";\n        return `${sign}${change.toFixed(2)}${percentStr}`;\n    },\n    /**\r\n   * Format volume with Indian number system\r\n   */ volume: (volume)=>{\n        if (volume === undefined || volume === null) return \"-\";\n        return formatIndianNumber(volume);\n    },\n    /**\r\n   * Format time from timestamp\r\n   */ time: (timestamp)=>{\n        if (!timestamp) return \"-\";\n        return new Date(timestamp).toLocaleTimeString();\n    },\n    /**\r\n   * Format date from timestamp\r\n   */ date: (timestamp)=>{\n        if (!timestamp) return \"-\";\n        return new Date(timestamp).toLocaleDateString();\n    },\n    /**\r\n   * Format bid/ask with quantity\r\n   */ bidAsk: (price, qty)=>{\n        if (!price || price <= 0) return \"-\";\n        const qtyStr = qty ? ` (${qty})` : \"\";\n        return `₹${price.toFixed(2)}${qtyStr}`;\n    }\n};\n/**\r\n * Capitalize first letter of string\r\n */ function capitalize(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n}\n/**\r\n * Convert string to title case\r\n */ function toTitleCase(str) {\n    return str.replace(/\\w\\S*/g, (txt)=>txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());\n}\n/**\r\n * Truncate string with ellipsis\r\n */ function truncate(str, length) {\n    if (str.length <= length) return str;\n    return str.slice(0, length) + \"...\";\n}\n/**\r\n * Format file size in human readable format\r\n */ function formatFileSize(bytes) {\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\",\n        \"TB\"\n    ];\n    if (bytes === 0) return \"0 Bytes\";\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + \" \" + sizes[i];\n}\n/**\r\n * Get relative time string (e.g., \"2 minutes ago\")\r\n */ function getRelativeTime(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) return \"just now\";\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;\n    return date.toLocaleDateString();\n}\n/**\r\n * Check if current time is within market hours\r\n */ function isMarketHours() {\n    const now = new Date();\n    const hours = now.getHours();\n    const minutes = now.getMinutes();\n    const currentTime = hours * 60 + minutes;\n    // Market hours: 9:15 AM to 3:30 PM (IST)\n    const marketOpen = 9 * 60 + 15 // 9:15 AM\n    ;\n    const marketClose = 15 * 60 + 30 // 3:30 PM\n    ;\n    // Check if it's a weekday (Monday = 1, Sunday = 0)\n    const dayOfWeek = now.getDay();\n    const isWeekday = dayOfWeek >= 1 && dayOfWeek <= 5;\n    return isWeekday && currentTime >= marketOpen && currentTime <= marketClose;\n}\n/**\r\n * Calculate option Greeks (simplified)\r\n */ function calculateImpliedVolatility(optionPrice, spotPrice, strikePrice, timeToExpiry, riskFreeRate = 0.06) {\n    // Simplified Black-Scholes implied volatility calculation\n    // This is a basic approximation - use proper financial libraries for production\n    const moneyness = spotPrice / strikePrice;\n    const timeValue = optionPrice / spotPrice;\n    // Basic approximation formula\n    const iv = Math.sqrt(2 * Math.PI / timeToExpiry) * timeValue / moneyness;\n    return Math.max(0.01, Math.min(5.0, iv)) // Clamp between 1% and 500%\n    ;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/websocket-manager.ts":
/*!**************************************!*\
  !*** ./src/lib/websocket-manager.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebSocketManager: () => (/* binding */ WebSocketManager),\n/* harmony export */   createWebSocketConnection: () => (/* binding */ createWebSocketConnection),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   emitWebSocketEvent: () => (/* binding */ emitWebSocketEvent),\n/* harmony export */   getWebSocketStats: () => (/* binding */ getWebSocketStats),\n/* harmony export */   isWebSocketConnected: () => (/* binding */ isWebSocketConnected),\n/* harmony export */   removeWebSocketConnection: () => (/* binding */ removeWebSocketConnection),\n/* harmony export */   websocketManager: () => (/* binding */ websocketManager)\n/* harmony export */ });\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(ssr)/./src/lib/constants.ts\");\n/**\r\n * Robust WebSocket Connection Manager\r\n * Handles connection pooling, reconnection, and proper cleanup\r\n * FIXED: Enhanced singleton pattern with proper connection sharing\r\n */ \n\nclass WebSocketManager {\n    static{\n        this.instance = null;\n    }\n    static getInstance() {\n        if (!WebSocketManager.instance) {\n            WebSocketManager.instance = new WebSocketManager();\n            console.log(\"\\uD83D\\uDD27 WebSocketManager: New singleton instance created\");\n        }\n        return WebSocketManager.instance;\n    }\n    // Prevent direct instantiation\n    constructor(){\n        this.socket = null;\n        this.isConnecting = false;\n        this.clientCount = 0;\n        this.listeners = new Map();\n        this.clientCallbacks = new Map();\n        this.stats = {\n            connected: false,\n            clients: 0,\n            reconnectAttempts: 0,\n            lastConnected: null,\n            totalMessages: 0,\n            errors: 0,\n            connectionId: null\n        };\n        this.heartbeatInterval = null;\n        this.cleanupInterval = null;\n        this.reconnectTimeout = null;\n        console.log(\"\\uD83D\\uDD27 WebSocketManager: Constructor called\");\n    }\n    /**\r\n   * Get or create WebSocket connection with enhanced singleton behavior\r\n   */ async connect(options = {}) {\n        const clientId = this.generateClientId();\n        console.log(`🔌 WebSocketManager: Client ${clientId} requesting connection`);\n        // Store client callbacks for later use\n        this.clientCallbacks.set(clientId, options);\n        // Return existing connection if available and connected\n        if (this.socket && this.socket.connected) {\n            console.log(`✅ WebSocketManager: Reusing existing connection for client ${clientId}`);\n            this.addClient(options, clientId);\n            return this.socket;\n        }\n        // Prevent multiple simultaneous connection attempts\n        if (this.isConnecting) {\n            console.log(`⏳ WebSocketManager: Connection in progress, waiting for client ${clientId}`);\n            return new Promise((resolve, reject)=>{\n                const checkConnection = ()=>{\n                    if (this.socket && this.socket.connected) {\n                        console.log(`✅ WebSocketManager: Connection ready for waiting client ${clientId}`);\n                        this.addClient(options, clientId);\n                        resolve(this.socket);\n                    } else if (!this.isConnecting) {\n                        console.log(`❌ WebSocketManager: Connection failed for waiting client ${clientId}`);\n                        reject(new Error(\"Connection failed\"));\n                    } else {\n                        setTimeout(checkConnection, 100);\n                    }\n                };\n                checkConnection();\n            });\n        }\n        console.log(`🚀 WebSocketManager: Creating new connection for client ${clientId}`);\n        this.isConnecting = true;\n        try {\n            await this.createConnection(options);\n            this.addClient(options, clientId);\n            return this.socket;\n        } catch (error) {\n            this.isConnecting = false;\n            this.clientCallbacks.delete(clientId);\n            console.error(`❌ WebSocketManager: Connection failed for client ${clientId}:`, error);\n            throw error;\n        }\n    }\n    /**\r\n   * Generate unique client ID for tracking\r\n   */ generateClientId() {\n        return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    /**\r\n   * Create new WebSocket connection with enhanced error handling\r\n   */ async createConnection(options) {\n        return new Promise((resolve, reject)=>{\n            const serverUrl = _constants__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.BASE_URL;\n            console.log(`🔌 WebSocketManager: Creating connection to ${serverUrl}`);\n            // Clean up existing connection\n            if (this.socket) {\n                console.log(\"\\uD83E\\uDDF9 WebSocketManager: Cleaning up existing connection\");\n                this.socket.removeAllListeners();\n                this.socket.disconnect();\n            }\n            // Create new connection with optimized settings\n            this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(serverUrl, {\n                transports: [\n                    \"websocket\",\n                    \"polling\"\n                ],\n                upgrade: true,\n                rememberUpgrade: false,\n                timeout: _constants__WEBPACK_IMPORTED_MODULE_1__.WEBSOCKET_CONFIG.CONNECTION_TIMEOUT,\n                forceNew: false,\n                reconnection: true,\n                reconnectionAttempts: _constants__WEBPACK_IMPORTED_MODULE_1__.WEBSOCKET_CONFIG.MAX_RECONNECT_ATTEMPTS,\n                reconnectionDelay: _constants__WEBPACK_IMPORTED_MODULE_1__.WEBSOCKET_CONFIG.RECONNECT_INTERVAL,\n                reconnectionDelayMax: _constants__WEBPACK_IMPORTED_MODULE_1__.WEBSOCKET_CONFIG.RECONNECT_INTERVAL * 4,\n                randomizationFactor: 0.5,\n                autoConnect: true\n            });\n            // Connection event handlers\n            this.socket.on(\"connect\", ()=>{\n                console.log(\"✅ WebSocketManager: Connected successfully\");\n                this.isConnecting = false;\n                this.stats.connected = true;\n                this.stats.lastConnected = new Date();\n                this.stats.reconnectAttempts = 0;\n                this.stats.connectionId = this.socket?.id || null;\n                this.startHeartbeat();\n                // Notify all clients\n                this.notifyAllClients(\"onConnect\");\n                resolve();\n            });\n            this.socket.on(\"disconnect\", (reason)=>{\n                console.log(`❌ WebSocketManager: Disconnected - ${reason}`);\n                this.stats.connected = false;\n                this.stats.connectionId = null;\n                this.stopHeartbeat();\n                // Notify all clients\n                this.notifyAllClients(\"onDisconnect\", reason);\n                // Attempt auto-reconnection for certain disconnect reasons\n                if (reason !== \"io client disconnect\") {\n                    this.scheduleReconnection();\n                }\n            });\n            this.socket.on(\"connect_error\", (error)=>{\n                console.error(\"\\uD83D\\uDD25 WebSocketManager: Connection error:\", error.message);\n                this.stats.errors++;\n                this.isConnecting = false;\n                // Notify all clients\n                this.notifyAllClients(\"onError\", error);\n                reject(error);\n            });\n            this.socket.on(\"reconnect\", (attemptNumber)=>{\n                console.log(`🔄 WebSocketManager: Reconnected after ${attemptNumber} attempts`);\n                this.stats.reconnectAttempts = attemptNumber;\n                this.stats.connected = true;\n                this.stats.lastConnected = new Date();\n                this.stats.connectionId = this.socket?.id || null;\n                // Notify all clients\n                this.notifyAllClients(\"onReconnect\", attemptNumber);\n            });\n            this.socket.on(\"reconnect_attempt\", (attemptNumber)=>{\n                console.log(`🔄 WebSocketManager: Reconnection attempt ${attemptNumber}`);\n                this.stats.reconnectAttempts = attemptNumber;\n            });\n            this.socket.on(\"reconnect_failed\", ()=>{\n                console.error(\"\\uD83D\\uDCA5 WebSocketManager: Reconnection failed\");\n                this.stats.connected = false;\n                this.isConnecting = false;\n                this.stats.connectionId = null;\n            });\n            // Market data handlers\n            this.socket.on(\"marketData\", (data)=>{\n                this.stats.totalMessages++;\n                this.notifyListeners(\"marketData\", data);\n            });\n            this.socket.on(\"marketDataBatch\", (data)=>{\n                this.stats.totalMessages += data.length;\n                this.notifyListeners(\"marketDataBatch\", data);\n            });\n            // Start cleanup interval\n            this.startCleanupInterval();\n            // Connection timeout\n            setTimeout(()=>{\n                if (this.isConnecting) {\n                    this.isConnecting = false;\n                    reject(new Error(\"Connection timeout\"));\n                }\n            }, _constants__WEBPACK_IMPORTED_MODULE_1__.WEBSOCKET_CONFIG.CONNECTION_TIMEOUT);\n        });\n    }\n    /**\r\n   * Add client and register listeners with enhanced tracking\r\n   */ addClient(options, clientId) {\n        this.clientCount++;\n        this.stats.clients = this.clientCount;\n        // Register listeners\n        if (options.onMarketData) {\n            this.addListener(\"marketData\", options.onMarketData);\n        }\n        if (options.onMarketDataBatch) {\n            this.addListener(\"marketDataBatch\", options.onMarketDataBatch);\n        }\n        console.log(`📊 WebSocketManager: Client ${clientId} added (Total: ${this.clientCount})`);\n    }\n    /**\r\n   * Notify all clients of connection events\r\n   */ notifyAllClients(eventType, data) {\n        this.clientCallbacks.forEach((callbacks, clientId)=>{\n            try {\n                switch(eventType){\n                    case \"onConnect\":\n                        callbacks.onConnect?.();\n                        break;\n                    case \"onDisconnect\":\n                        callbacks.onDisconnect?.(data);\n                        break;\n                    case \"onError\":\n                        callbacks.onError?.(data);\n                        break;\n                    case \"onReconnect\":\n                        callbacks.onReconnect?.(data);\n                        break;\n                }\n            } catch (error) {\n                console.error(`❌ WebSocketManager: Error notifying client ${clientId}:`, error);\n            }\n        });\n    }\n    /**\r\n   * Schedule reconnection with exponential backoff\r\n   */ scheduleReconnection() {\n        if (this.reconnectTimeout) {\n            clearTimeout(this.reconnectTimeout);\n        }\n        const delay = Math.min(_constants__WEBPACK_IMPORTED_MODULE_1__.WEBSOCKET_CONFIG.RECONNECT_INTERVAL * Math.pow(2, this.stats.reconnectAttempts), 30000 // Max 30 seconds\n        );\n        console.log(`🔄 WebSocketManager: Scheduling reconnection in ${delay}ms`);\n        this.reconnectTimeout = setTimeout(()=>{\n            if (!this.stats.connected && this.clientCount > 0) {\n                console.log(\"\\uD83D\\uDD04 WebSocketManager: Attempting auto-reconnection\");\n                this.connect().catch((error)=>{\n                    console.error(\"❌ WebSocketManager: Auto-reconnection failed:\", error);\n                });\n            }\n        }, delay);\n    }\n    /**\r\n   * Remove client and cleanup listeners with enhanced tracking\r\n   */ removeClient(options) {\n        if (this.clientCount > 0) {\n            this.clientCount--;\n            this.stats.clients = this.clientCount;\n            // Find and remove client callback\n            let removedClientId = \"unknown\";\n            for (const [clientId, callbacks] of this.clientCallbacks.entries()){\n                if (callbacks === options) {\n                    this.clientCallbacks.delete(clientId);\n                    removedClientId = clientId;\n                    break;\n                }\n            }\n            // Remove listeners\n            if (options.onMarketData) {\n                this.removeListener(\"marketData\", options.onMarketData);\n            }\n            if (options.onMarketDataBatch) {\n                this.removeListener(\"marketDataBatch\", options.onMarketDataBatch);\n            }\n            console.log(`📊 WebSocketManager: Client ${removedClientId} removed (Total: ${this.clientCount})`);\n            // Disconnect if no clients with grace period\n            if (this.clientCount === 0) {\n                console.log(\"⏳ WebSocketManager: No clients remaining, scheduling disconnect\");\n                setTimeout(()=>{\n                    if (this.clientCount === 0) {\n                        console.log(\"\\uD83D\\uDD0C WebSocketManager: Disconnecting due to no clients\");\n                        this.disconnect();\n                    }\n                }, 5000) // 5 second grace period\n                ;\n            }\n        }\n    }\n    /**\r\n   * Add event listener\r\n   */ addListener(event, listener) {\n        if (!this.listeners.has(event)) {\n            this.listeners.set(event, new Set());\n        }\n        const eventListeners = this.listeners.get(event);\n        if (eventListeners.size >= _constants__WEBPACK_IMPORTED_MODULE_1__.WEBSOCKET_CONFIG.MAX_LISTENERS_PER_EVENT) {\n            console.warn(`⚠️ Maximum listeners reached for event: ${event}`);\n            return;\n        }\n        eventListeners.add(listener);\n    }\n    /**\r\n   * Remove event listener\r\n   */ removeListener(event, listener) {\n        const eventListeners = this.listeners.get(event);\n        if (eventListeners) {\n            eventListeners.delete(listener);\n            if (eventListeners.size === 0) {\n                this.listeners.delete(event);\n            }\n        }\n    }\n    /**\r\n   * Notify all listeners for an event\r\n   */ notifyListeners(event, data) {\n        const eventListeners = this.listeners.get(event);\n        if (eventListeners) {\n            eventListeners.forEach((listener)=>{\n                try {\n                    listener(data);\n                } catch (error) {\n                    console.error(`Error in ${event} listener:`, error);\n                }\n            });\n        }\n    }\n    /**\r\n   * Start heartbeat to keep connection alive\r\n   */ startHeartbeat() {\n        this.stopHeartbeat();\n        this.heartbeatInterval = setInterval(()=>{\n            if (this.socket && this.socket.connected) {\n                this.socket.emit(\"ping\");\n            }\n        }, _constants__WEBPACK_IMPORTED_MODULE_1__.WEBSOCKET_CONFIG.HEARTBEAT_INTERVAL);\n    }\n    /**\r\n   * Stop heartbeat\r\n   */ stopHeartbeat() {\n        if (this.heartbeatInterval) {\n            clearInterval(this.heartbeatInterval);\n            this.heartbeatInterval = null;\n        }\n    }\n    /**\r\n   * Start cleanup interval\r\n   */ startCleanupInterval() {\n        this.cleanupInterval = setInterval(()=>{\n            this.cleanup();\n        }, _constants__WEBPACK_IMPORTED_MODULE_1__.WEBSOCKET_CONFIG.CLEANUP_INTERVAL);\n    }\n    /**\r\n   * Cleanup stale listeners and connections\r\n   */ cleanup() {\n        // Remove empty listener sets\n        const eventsToDelete = [];\n        this.listeners.forEach((listeners, event)=>{\n            if (listeners.size === 0) {\n                eventsToDelete.push(event);\n            }\n        });\n        eventsToDelete.forEach((event)=>{\n            this.listeners.delete(event);\n        });\n        // Log stats\n        console.log(\"\\uD83D\\uDCCA WebSocket Stats:\", this.getStats());\n    }\n    /**\r\n   * Disconnect WebSocket with enhanced cleanup\r\n   */ disconnect() {\n        console.log(\"\\uD83D\\uDD0C WebSocketManager: Disconnecting...\");\n        this.stopHeartbeat();\n        if (this.cleanupInterval) {\n            clearInterval(this.cleanupInterval);\n            this.cleanupInterval = null;\n        }\n        if (this.reconnectTimeout) {\n            clearTimeout(this.reconnectTimeout);\n            this.reconnectTimeout = null;\n        }\n        if (this.socket) {\n            this.socket.removeAllListeners();\n            this.socket.disconnect();\n            this.socket = null;\n        }\n        this.listeners.clear();\n        this.clientCallbacks.clear();\n        this.clientCount = 0;\n        this.stats.connected = false;\n        this.stats.clients = 0;\n        this.stats.connectionId = null;\n        this.isConnecting = false;\n        console.log(\"✅ WebSocketManager: Disconnected and cleaned up\");\n    }\n    /**\r\n   * Get connection statistics\r\n   */ getStats() {\n        return {\n            ...this.stats\n        };\n    }\n    /**\r\n   * Check if connected\r\n   */ isConnected() {\n        return this.socket?.connected || false;\n    }\n    /**\r\n   * Emit event to server\r\n   */ emit(event, data) {\n        if (this.socket && this.socket.connected) {\n            this.socket.emit(event, data);\n        } else {\n            console.warn(`Cannot emit ${event}: WebSocket not connected`);\n        }\n    }\n}\n// Export singleton instance and helper functions\nconst websocketManager = WebSocketManager.getInstance();\nconst createWebSocketConnection = (options = {})=>{\n    return websocketManager.connect(options);\n};\nconst removeWebSocketConnection = (options = {})=>{\n    websocketManager.removeClient(options);\n};\nconst getWebSocketStats = ()=>{\n    return websocketManager.getStats();\n};\nconst isWebSocketConnected = ()=>{\n    return websocketManager.isConnected();\n};\nconst emitWebSocketEvent = (event, data)=>{\n    websocketManager.emit(event, data);\n};\n// Export the WebSocketManager class for direct usage\n\n// Also export as default for easier importing\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WebSocketManager);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/websocket-manager.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/marketStore.ts":
/*!**********************************!*\
  !*** ./src/store/marketStore.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _lib_websocket_manager__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/websocket-manager */ \"(ssr)/./src/lib/websocket-manager.ts\");\n/**\r\n * Zustand Store for Market Data Management\r\n * Handles state persistence, WebSocket connections, and caching\r\n */ \n\n\nconst useMarketStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.persist)((set, get)=>({\n        // Initial State\n        marketData: {},\n        isConnected: false,\n        isLoading: true,\n        lastUpdate: null,\n        connectionStatus: \"disconnected\",\n        error: null,\n        cacheLoaded: false,\n        wsManager: null,\n        // State Setters\n        setMarketData: (data)=>{\n            set({\n                marketData: data.reduce((acc, item)=>{\n                    acc[item.securityId] = item;\n                    return acc;\n                }, {}),\n                lastUpdate: new Date(),\n                isLoading: false\n            });\n            get().saveToLocalStorage();\n        },\n        updateMarketData: (tick)=>set((state)=>({\n                    marketData: {\n                        ...state.marketData,\n                        [tick.securityId]: {\n                            ...state.marketData[tick.securityId],\n                            ...tick\n                        }\n                    }\n                })),\n        updateMarketDataBatch: (ticks)=>set((state)=>{\n                const updated = {\n                    ...state.marketData\n                };\n                for (const tick of ticks){\n                    updated[tick.securityId] = {\n                        ...updated[tick.securityId],\n                        ...tick\n                    };\n                }\n                return {\n                    marketData: updated\n                };\n            }),\n        hydrateFromRedis: (initial)=>set({\n                marketData: initial.reduce((acc, tick)=>{\n                    acc[tick.securityId] = tick;\n                    return acc;\n                }, {})\n            }),\n        setConnectionStatus: (status)=>{\n            set({\n                connectionStatus: status,\n                isConnected: status === \"connected\",\n                error: status === \"error\" ? get().error : null\n            });\n        },\n        setError: (error)=>set({\n                error\n            }),\n        setLoading: (loading)=>set({\n                isLoading: loading\n            }),\n        setCacheLoaded: (loaded)=>set({\n                cacheLoaded: loaded\n            }),\n        // WebSocket Management\n        initializeWebSocket: ()=>{\n            const wsManager = _lib_websocket_manager__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance();\n            set({\n                wsManager\n            });\n            // Connect with proper options and event handlers\n            wsManager.connect({\n                onConnect: ()=>{\n                    console.log(\"\\uD83D\\uDD0C MarketStore: WebSocket connected\");\n                    get().setConnectionStatus(\"connected\");\n                },\n                onDisconnect: (reason)=>{\n                    console.log(\"\\uD83D\\uDD0C MarketStore: WebSocket disconnected:\", reason);\n                    get().setConnectionStatus(\"disconnected\");\n                },\n                onError: (error)=>{\n                    console.error(\"❌ MarketStore: WebSocket error:\", error);\n                    get().setConnectionStatus(\"error\");\n                    get().setError(error.message || \"WebSocket connection error\");\n                },\n                onReconnect: (attemptNumber)=>{\n                    console.log(\"\\uD83D\\uDD04 MarketStore: WebSocket reconnected after\", attemptNumber, \"attempts\");\n                    get().setConnectionStatus(\"connected\");\n                    get().setError(null);\n                },\n                onMarketData: (data)=>{\n                    if (data && typeof data === \"object\") {\n                        get().updateMarketData(data);\n                    }\n                },\n                onMarketDataBatch: (data)=>{\n                    if (Array.isArray(data) && data.length > 0) {\n                        get().updateMarketDataBatch(data);\n                    }\n                }\n            });\n        },\n        connect: ()=>{\n            const { wsManager } = get();\n            if (wsManager && !wsManager.isConnected()) {\n                get().setConnectionStatus(\"connecting\");\n                // WebSocket manager handles connection automatically when initialized\n                console.log(\"\\uD83D\\uDD0C MarketStore: Connection request - WebSocket manager will handle\");\n            }\n        },\n        disconnect: ()=>{\n            const { wsManager } = get();\n            if (wsManager) {\n                wsManager.disconnect();\n                get().setConnectionStatus(\"disconnected\");\n            }\n        },\n        // Cache Management\n        loadFromCache: async ()=>{\n            try {\n                set({\n                    isLoading: true\n                });\n                console.log(\"\\uD83D\\uDCD6 MarketStore: Loading cached market data...\");\n                // First try localStorage for instant display\n                const hasLocalData = get().loadFromLocalStorage();\n                // Then load from Redis\n                const response = await fetch(\"/api/cache/bulk\");\n                if (response.ok) {\n                    const result = await response.json();\n                    if (result.success && result.data && Array.isArray(result.data) && result.data.length > 0) {\n                        get().setMarketData(result.data);\n                        console.log(`✅ MarketStore: Loaded ${result.data.length} items from Redis cache`);\n                    } else if (!hasLocalData) {\n                        console.log(\"\\uD83D\\uDCED MarketStore: No cached data found\");\n                    }\n                } else if (!hasLocalData) {\n                    console.log(\"\\uD83D\\uDCED MarketStore: Failed to load cached data\");\n                }\n                set({\n                    cacheLoaded: true,\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error(\"❌ MarketStore: Failed to load cached data:\", error);\n                set({\n                    cacheLoaded: true,\n                    isLoading: false\n                });\n            }\n        },\n        refreshFromCache: async ()=>{\n            try {\n                const response = await fetch(\"/api/cache/bulk\");\n                if (response.ok) {\n                    const result = await response.json();\n                    if (result.success && result.data && Array.isArray(result.data) && result.data.length > 0) {\n                        get().setMarketData(result.data);\n                        console.log(`✅ MarketStore: Refreshed ${result.data.length} items from cache`);\n                    }\n                }\n            } catch (error) {\n                console.error(\"❌ MarketStore: Failed to refresh from cache:\", error);\n            }\n        },\n        clearCache: async ()=>{\n            try {\n                const response = await fetch(\"/api/cache/clear\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        pattern: \"market_data:*\"\n                    })\n                });\n                if (response.ok) {\n                    set({\n                        marketData: {},\n                        lastUpdate: null\n                    });\n                    localStorage.removeItem(\"marketData\");\n                    localStorage.removeItem(\"marketDataTimestamp\");\n                    console.log(\"\\uD83E\\uDDF9 MarketStore: Cache cleared\");\n                }\n            } catch (error) {\n                console.error(\"❌ MarketStore: Failed to clear cache:\", error);\n            }\n        },\n        // Local Storage Management\n        saveToLocalStorage: ()=>{\n            const { marketData } = get();\n            if (Object.keys(marketData).length > 0) {\n                try {\n                    localStorage.setItem(\"marketData\", JSON.stringify(Object.values(marketData)));\n                    localStorage.setItem(\"marketDataTimestamp\", new Date().toISOString());\n                } catch (error) {\n                    console.warn(\"⚠️ MarketStore: Failed to save to localStorage:\", error);\n                }\n            }\n        },\n        loadFromLocalStorage: ()=>{\n            try {\n                const localData = localStorage.getItem(\"marketData\");\n                const localTimestamp = localStorage.getItem(\"marketDataTimestamp\");\n                if (localData && localTimestamp) {\n                    const parsedData = JSON.parse(localData);\n                    const timestamp = new Date(localTimestamp);\n                    const now = new Date();\n                    const ageMinutes = (now.getTime() - timestamp.getTime()) / (1000 * 60);\n                    // Use localStorage data if it's less than 10 minutes old\n                    if (ageMinutes < 10 && Array.isArray(parsedData) && parsedData.length > 0) {\n                        set({\n                            marketData: parsedData.reduce((acc, item)=>{\n                                acc[item.securityId] = item;\n                                return acc;\n                            }, {}),\n                            lastUpdate: timestamp,\n                            isLoading: false\n                        });\n                        console.log(`⚡ MarketStore: Loaded ${parsedData.length} items from localStorage (${ageMinutes.toFixed(1)}min old)`);\n                        return true;\n                    }\n                }\n            } catch (error) {\n                console.warn(\"⚠️ MarketStore: Failed to load from localStorage:\", error);\n                localStorage.removeItem(\"marketData\");\n                localStorage.removeItem(\"marketDataTimestamp\");\n            }\n            return false;\n        },\n        // Utility Functions\n        reset: ()=>{\n            set({\n                marketData: {},\n                isConnected: false,\n                isLoading: true,\n                lastUpdate: null,\n                connectionStatus: \"disconnected\",\n                error: null,\n                cacheLoaded: false\n            });\n            localStorage.removeItem(\"marketData\");\n            localStorage.removeItem(\"marketDataTimestamp\");\n        },\n        getMarketDataBySymbol: (symbol)=>{\n            return Object.values(get().marketData).find((item)=>item.symbol === symbol);\n        },\n        getMarketDataBySecurityId: (securityId)=>{\n            return get().marketData[securityId];\n        }\n    }), {\n    name: \"market-store\",\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.createJSONStorage)(()=>localStorage),\n    partialize: (state)=>({\n            marketData: state.marketData\n        })\n}));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useMarketStore);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/marketStore.ts\n");

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"146531be5b05\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3N2LW1hcmtldC1kYXNoYm9hcmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2VlZGYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxNDY1MzFiZTViMDVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\love\dashboard\csv-market-dashboard\src\app\layout.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/option-chain/page.tsx":
/*!***************************************!*\
  !*** ./src/app/option-chain/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\love\dashboard\csv-market-dashboard\src\app\option-chain\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/react-hot-toast","vendor-chunks/react-error-boundary","vendor-chunks/goober","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/socket.io-parser","vendor-chunks/engine.io-parser","vendor-chunks/zustand","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/@socket.io","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/supports-color","vendor-chunks/ms","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Foption-chain%2Fpage&page=%2Foption-chain%2Fpage&appPaths=%2Foption-chain%2Fpage&pagePath=private-next-app-dir%2Foption-chain%2Fpage.tsx&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();