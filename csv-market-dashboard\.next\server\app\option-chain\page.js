(()=>{var e={};e.id=190,e.ids=[190],e.modules={702:()=>{},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1569:(e,t,s)=>{"use strict";let{tokenChars:r}=s(7377);function n(e,t,s){void 0===e[t]?e[t]=[s]:e[t].push(s)}e.exports={format:function(e){return Object.keys(e).map(t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map(e=>[t].concat(Object.keys(e).map(t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map(e=>!0===e?t:`${t}=${e}`).join("; ")})).join("; ")).join(", ")}).join(", ")},parse:function(e){let t,s,i=Object.create(null),o=Object.create(null),a=!1,c=!1,l=!1,h=-1,d=-1,u=-1,p=0;for(;p<e.length;p++)if(d=e.charCodeAt(p),void 0===t)if(-1===u&&1===r[d])-1===h&&(h=p);else if(0!==p&&(32===d||9===d))-1===u&&-1!==h&&(u=p);else if(59===d||44===d){if(-1===h)throw SyntaxError(`Unexpected character at index ${p}`);-1===u&&(u=p);let s=e.slice(h,u);44===d?(n(i,s,o),o=Object.create(null)):t=s,h=u=-1}else throw SyntaxError(`Unexpected character at index ${p}`);else if(void 0===s)if(-1===u&&1===r[d])-1===h&&(h=p);else if(32===d||9===d)-1===u&&-1!==h&&(u=p);else if(59===d||44===d){if(-1===h)throw SyntaxError(`Unexpected character at index ${p}`);-1===u&&(u=p),n(o,e.slice(h,u),!0),44===d&&(n(i,t,o),o=Object.create(null),t=void 0),h=u=-1}else if(61===d&&-1!==h&&-1===u)s=e.slice(h,p),h=u=-1;else throw SyntaxError(`Unexpected character at index ${p}`);else if(c){if(1!==r[d])throw SyntaxError(`Unexpected character at index ${p}`);-1===h?h=p:a||(a=!0),c=!1}else if(l)if(1===r[d])-1===h&&(h=p);else if(34===d&&-1!==h)l=!1,u=p;else if(92===d)c=!0;else throw SyntaxError(`Unexpected character at index ${p}`);else if(34===d&&61===e.charCodeAt(p-1))l=!0;else if(-1===u&&1===r[d])-1===h&&(h=p);else if(-1!==h&&(32===d||9===d))-1===u&&(u=p);else if(59===d||44===d){if(-1===h)throw SyntaxError(`Unexpected character at index ${p}`);-1===u&&(u=p);let r=e.slice(h,u);a&&(r=r.replace(/\\/g,""),a=!1),n(o,s,r),44===d&&(n(i,t,o),o=Object.create(null),t=void 0),s=void 0,h=u=-1}else throw SyntaxError(`Unexpected character at index ${p}`);if(-1===h||l||32===d||9===d)throw SyntaxError("Unexpected end of input");-1===u&&(u=p);let f=e.slice(h,u);return void 0===t?n(i,f,o):(void 0===s?n(o,f,!0):a?n(o,s,f.replace(/\\/g,"")):n(o,s,f),n(i,t,o)),i}}},1629:(e,t,s)=>{"use strict";let{EMPTY_BUFFER:r}=s(8169),n=Buffer[Symbol.species];function i(e,t,s,r,n){for(let i=0;i<n;i++)s[r+i]=e[i]^t[3&i]}function o(e,t){for(let s=0;s<e.length;s++)e[s]^=t[3&s]}function a(e){let t;return(a.readOnly=!0,Buffer.isBuffer(e))?e:(e instanceof ArrayBuffer?t=new n(e):ArrayBuffer.isView(e)?t=new n(e.buffer,e.byteOffset,e.byteLength):(t=Buffer.from(e),a.readOnly=!1),t)}if(e.exports={concat:function(e,t){if(0===e.length)return r;if(1===e.length)return e[0];let s=Buffer.allocUnsafe(t),i=0;for(let t=0;t<e.length;t++){let r=e[t];s.set(r,i),i+=r.length}return i<t?new n(s.buffer,s.byteOffset,i):s},mask:i,toArrayBuffer:function(e){return e.length===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.length)},toBuffer:a,unmask:o},!process.env.WS_NO_BUFFER_UTIL)try{let t=s(5991);e.exports.mask=function(e,s,r,n,o){o<48?i(e,s,r,n,o):t.mask(e,s,r,n,o)},e.exports.unmask=function(e,s){e.length<32?o(e,s):t.unmask(e,s)}}catch(e){}},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1759:(e,t,s)=>{"use strict";let r=s(4735),n=s(5591),i=s(1630),o=s(1645),a=s(4631),{randomBytes:c,createHash:l}=s(5511),{Duplex:h,Readable:d}=s(7910),{URL:u}=s(9551),p=s(2126),f=s(4687),m=s(8099),{BINARY_TYPES:g,EMPTY_BUFFER:y,GUID:_,kForOnEventAttribute:b,kListener:v,kStatusCode:x,kWebSocket:C,NOOP:k}=s(8169),{EventTarget:{addEventListener:w,removeEventListener:S}}=s(6416),{format:E,parse:O}=s(1569),{toBuffer:T}=s(1629),N=Symbol("kAborted"),F=[8,13],R=["CONNECTING","OPEN","CLOSING","CLOSED"],L=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class A extends r{constructor(e,t,s){super(),this._binaryType=g[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=y,this._closeTimer=null,this._extensions={},this._paused=!1,this._protocol="",this._readyState=A.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==e?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,void 0===t?t=[]:Array.isArray(t)||("object"==typeof t&&null!==t?(s=t,t=[]):t=[t]),function e(t,s,r,o){let a,h,d,f,m={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:F[1],maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...o,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(t._autoPong=m.autoPong,!F.includes(m.protocolVersion))throw RangeError(`Unsupported protocol version: ${m.protocolVersion} (supported versions: ${F.join(", ")})`);if(s instanceof u)a=s;else try{a=new u(s)}catch(e){throw SyntaxError(`Invalid URL: ${s}`)}"http:"===a.protocol?a.protocol="ws:":"https:"===a.protocol&&(a.protocol="wss:"),t._url=a.href;let g="wss:"===a.protocol,y="ws+unix:"===a.protocol;if("ws:"===a.protocol||g||y?y&&!a.pathname?h="The URL's pathname is empty":a.hash&&(h="The URL contains a fragment identifier"):h='The URL\'s protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"',h){let e=SyntaxError(h);if(0!==t._redirects)return void D(t,e);throw e}let b=g?443:80,v=c(16).toString("base64"),x=g?n.request:i.request,C=new Set;if(m.createConnection=m.createConnection||(g?j:I),m.defaultPort=m.defaultPort||b,m.port=a.port||b,m.host=a.hostname.startsWith("[")?a.hostname.slice(1,-1):a.hostname,m.headers={...m.headers,"Sec-WebSocket-Version":m.protocolVersion,"Sec-WebSocket-Key":v,Connection:"Upgrade",Upgrade:"websocket"},m.path=a.pathname+a.search,m.timeout=m.handshakeTimeout,m.perMessageDeflate&&(d=new p(!0!==m.perMessageDeflate?m.perMessageDeflate:{},!1,m.maxPayload),m.headers["Sec-WebSocket-Extensions"]=E({[p.extensionName]:d.offer()})),r.length){for(let e of r){if("string"!=typeof e||!L.test(e)||C.has(e))throw SyntaxError("An invalid or duplicated subprotocol was specified");C.add(e)}m.headers["Sec-WebSocket-Protocol"]=r.join(",")}if(m.origin&&(m.protocolVersion<13?m.headers["Sec-WebSocket-Origin"]=m.origin:m.headers.Origin=m.origin),(a.username||a.password)&&(m.auth=`${a.username}:${a.password}`),y){let e=m.path.split(":");m.socketPath=e[0],m.path=e[1]}if(m.followRedirects){if(0===t._redirects){t._originalIpc=y,t._originalSecure=g,t._originalHostOrSocketPath=y?m.socketPath:a.host;let e=o&&o.headers;if(o={...o,headers:{}},e)for(let[t,s]of Object.entries(e))o.headers[t.toLowerCase()]=s}else if(0===t.listenerCount("redirect")){let e=y?!!t._originalIpc&&m.socketPath===t._originalHostOrSocketPath:!t._originalIpc&&a.host===t._originalHostOrSocketPath;e&&(!t._originalSecure||g)||(delete m.headers.authorization,delete m.headers.cookie,e||delete m.headers.host,m.auth=void 0)}m.auth&&!o.headers.authorization&&(o.headers.authorization="Basic "+Buffer.from(m.auth).toString("base64")),f=t._req=x(m),t._redirects&&t.emit("redirect",t.url,f)}else f=t._req=x(m);m.timeout&&f.on("timeout",()=>{P(t,f,"Opening handshake has timed out")}),f.on("error",e=>{null===f||f[N]||(f=t._req=null,D(t,e))}),f.on("response",n=>{let i=n.headers.location,a=n.statusCode;if(i&&m.followRedirects&&a>=300&&a<400){let n;if(++t._redirects>m.maxRedirects)return void P(t,f,"Maximum redirects exceeded");f.abort();try{n=new u(i,s)}catch(e){D(t,SyntaxError(`Invalid URL: ${i}`));return}e(t,n,r,o)}else t.emit("unexpected-response",f,n)||P(t,f,`Unexpected server response: ${n.statusCode}`)}),f.on("upgrade",(e,s,r)=>{let n;if(t.emit("upgrade",e),t.readyState!==A.CONNECTING)return;f=t._req=null;let i=e.headers.upgrade;if(void 0===i||"websocket"!==i.toLowerCase())return void P(t,s,"Invalid Upgrade header");let o=l("sha1").update(v+_).digest("base64");if(e.headers["sec-websocket-accept"]!==o)return void P(t,s,"Invalid Sec-WebSocket-Accept header");let a=e.headers["sec-websocket-protocol"];if(void 0!==a?C.size?C.has(a)||(n="Server sent an invalid subprotocol"):n="Server sent a subprotocol but none was requested":C.size&&(n="Server sent no subprotocol"),n)return void P(t,s,n);a&&(t._protocol=a);let c=e.headers["sec-websocket-extensions"];if(void 0!==c){let e;if(!d)return void P(t,s,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");try{e=O(c)}catch(e){P(t,s,"Invalid Sec-WebSocket-Extensions header");return}let r=Object.keys(e);if(1!==r.length||r[0]!==p.extensionName)return void P(t,s,"Server indicated an extension that was not requested");try{d.accept(e[p.extensionName])}catch(e){P(t,s,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[p.extensionName]=d}t.setSocket(s,r,{allowSynchronousEvents:m.allowSynchronousEvents,generateMask:m.generateMask,maxPayload:m.maxPayload,skipUTF8Validation:m.skipUTF8Validation})}),m.finishRequest?m.finishRequest(f,t):f.end()}(this,e,t,s)):(this._autoPong=s.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){g.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,s){let r=new f({allowSynchronousEvents:s.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:s.maxPayload,skipUTF8Validation:s.skipUTF8Validation});this._sender=new m(e,this._extensions,s.generateMask),this._receiver=r,this._socket=e,r[C]=this,e[C]=this,r.on("conclude",B),r.on("drain",U),r.on("error",$),r.on("message",W),r.on("ping",V),r.on("pong",H),e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",z),e.on("data",Y),e.on("end",J),e.on("error",X),this._readyState=A.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=A.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[p.extensionName]&&this._extensions[p.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=A.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==A.CLOSED){if(this.readyState===A.CONNECTING)return void P(this,this._req,"WebSocket was closed before the connection was established");if(this.readyState===A.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=A.CLOSING,this._sender.close(e,t,!this._isServer,e=>{!e&&(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),3e4)}}pause(){this.readyState!==A.CONNECTING&&this.readyState!==A.CLOSED&&(this._paused=!0,this._socket.pause())}ping(e,t,s){if(this.readyState===A.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(s=e,e=t=void 0):"function"==typeof t&&(s=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==A.OPEN)return void M(this,e,s);void 0===t&&(t=!this._isServer),this._sender.ping(e||y,t,s)}pong(e,t,s){if(this.readyState===A.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(s=e,e=t=void 0):"function"==typeof t&&(s=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==A.OPEN)return void M(this,e,s);void 0===t&&(t=!this._isServer),this._sender.pong(e||y,t,s)}resume(){this.readyState!==A.CONNECTING&&this.readyState!==A.CLOSED&&(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,t,s){if(this.readyState===A.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof t&&(s=t,t={}),"number"==typeof e&&(e=e.toString()),this.readyState!==A.OPEN)return void M(this,e,s);let r={binary:"string"!=typeof e,mask:!this._isServer,compress:!0,fin:!0,...t};this._extensions[p.extensionName]||(r.compress=!1),this._sender.send(e||y,r,s)}terminate(){if(this.readyState!==A.CLOSED){if(this.readyState===A.CONNECTING)return void P(this,this._req,"WebSocket was closed before the connection was established");this._socket&&(this._readyState=A.CLOSING,this._socket.destroy())}}}function D(e,t){e._readyState=A.CLOSING,e.emit("error",t),e.emitClose()}function I(e){return e.path=e.socketPath,o.connect(e)}function j(e){return e.path=void 0,e.servername||""===e.servername||(e.servername=o.isIP(e.host)?"":e.host),a.connect(e)}function P(e,t,s){e._readyState=A.CLOSING;let r=Error(s);Error.captureStackTrace(r,P),t.setHeader?(t[N]=!0,t.abort(),t.socket&&!t.socket.destroyed&&t.socket.destroy(),process.nextTick(D,e,r)):(t.destroy(r),t.once("error",e.emit.bind(e,"error")),t.once("close",e.emitClose.bind(e)))}function M(e,t,s){if(t){let s=T(t).length;e._socket?e._sender._bufferedBytes+=s:e._bufferedAmount+=s}if(s){let t=Error(`WebSocket is not open: readyState ${e.readyState} (${R[e.readyState]})`);process.nextTick(s,t)}}function B(e,t){let s=this[C];s._closeFrameReceived=!0,s._closeMessage=t,s._closeCode=e,void 0!==s._socket[C]&&(s._socket.removeListener("data",Y),process.nextTick(G,s._socket),1005===e?s.close():s.close(e,t))}function U(){let e=this[C];e.isPaused||e._socket.resume()}function $(e){let t=this[C];void 0!==t._socket[C]&&(t._socket.removeListener("data",Y),process.nextTick(G,t._socket),t.close(e[x])),t.emit("error",e)}function q(){this[C].emitClose()}function W(e,t){this[C].emit("message",e,t)}function V(e){let t=this[C];t._autoPong&&t.pong(e,!this._isServer,k),t.emit("ping",e)}function H(e){this[C].emit("pong",e)}function G(e){e.resume()}function z(){let e,t=this[C];this.removeListener("close",z),this.removeListener("data",Y),this.removeListener("end",J),t._readyState=A.CLOSING,this._readableState.endEmitted||t._closeFrameReceived||t._receiver._writableState.errorEmitted||null===(e=t._socket.read())||t._receiver.write(e),t._receiver.end(),this[C]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",q),t._receiver.on("finish",q))}function Y(e){this[C]._receiver.write(e)||this.pause()}function J(){let e=this[C];e._readyState=A.CLOSING,e._receiver.end(),this.end()}function X(){let e=this[C];this.removeListener("error",X),this.on("error",k),e&&(e._readyState=A.CLOSING,this.destroy())}Object.defineProperty(A,"CONNECTING",{enumerable:!0,value:R.indexOf("CONNECTING")}),Object.defineProperty(A.prototype,"CONNECTING",{enumerable:!0,value:R.indexOf("CONNECTING")}),Object.defineProperty(A,"OPEN",{enumerable:!0,value:R.indexOf("OPEN")}),Object.defineProperty(A.prototype,"OPEN",{enumerable:!0,value:R.indexOf("OPEN")}),Object.defineProperty(A,"CLOSING",{enumerable:!0,value:R.indexOf("CLOSING")}),Object.defineProperty(A.prototype,"CLOSING",{enumerable:!0,value:R.indexOf("CLOSING")}),Object.defineProperty(A,"CLOSED",{enumerable:!0,value:R.indexOf("CLOSED")}),Object.defineProperty(A.prototype,"CLOSED",{enumerable:!0,value:R.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(e=>{Object.defineProperty(A.prototype,e,{enumerable:!0})}),["open","error","close","message"].forEach(e=>{Object.defineProperty(A.prototype,`on${e}`,{enumerable:!0,get(){for(let t of this.listeners(e))if(t[b])return t[v];return null},set(t){for(let t of this.listeners(e))if(t[b]){this.removeListener(e,t);break}"function"==typeof t&&this.addEventListener(e,t,{[b]:!0})}})}),A.prototype.addEventListener=w,A.prototype.removeEventListener=S,e.exports=A},1820:e=>{"use strict";e.exports=require("os")},1985:(e,t,s)=>{"use strict";let r,n;s.r(t),s.d(t,{default:()=>ez});var i,o={};s.r(o),s.d(o,{Decoder:()=>eR,Encoder:()=>eN,PacketType:()=>i,protocol:()=>eT});var a=s(687),c=s(3210),l=s(4780),h=s(2457);function d({marketData:e}){let[t,s]=(0,c.useState)(null),[r,n]=(0,c.useState)(""),[i,o]=(0,c.useState)(null),[d,u]=(0,c.useState)(!1),[p,f]=(0,c.useState)(null),[m,g]=(0,c.useState)(0),[y,_]=(0,c.useState)(!1),b=(0,c.useCallback)(e=>{let t=new Date(e);return`${["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][t.getMonth()]}${t.getFullYear()}`},[]),v=(0,c.useCallback)(t=>{let s=new Set;for(let[t,n]of Array.from(e.entries()))if(n.symbol.includes("NIFTY")&&n.symbol.includes(b(r))){let e=n.symbol.split("-");if(e.length>=4){let t=parseFloat(e[2]);isNaN(t)||s.add(t)}}let n=Array.from(s).sort((e,t)=>e-t);if(0===n.length){console.warn("[STRIKE] ⚠️ No strikes found for expiry:",r);let e=[],s=50*Math.round(t/50);for(let t=-12;t<=12;t++)e.push(s+50*t);return e}let i=n.reduce((e,s)=>Math.abs(s-t)<Math.abs(e-t)?s:e),o=n.indexOf(i),a=[...n.slice(Math.max(0,o-12),o),i,...n.slice(o+1,o+13)];return console.log(`[STRIKE] ✅ Selected ${a.length} strikes around ATM:`,a),a},[r,e,b]),x=(0,c.useCallback)((t,s)=>{for(let[n,i]of Array.from(e.entries()))if(i.symbol.includes("NIFTY-")&&i.symbol.includes(`-${t}-${s}`)&&i.expiryDate===r)return console.log(`[OPTION] ✅ Found ${s} ${t}: ${i.symbol} (Expiry: ${i.expiryDate})`),{securityId:n,symbol:i.symbol,exchange:i.exchange,strikePrice:t,optionType:s,expiryDate:r,ltp:i.ltp||0,change:i.change||0,changePercent:i.changePercent||0,volume:i.volume||0,openInterest:i.openInterest,bid:i.marketDepth?.[0]?.bidPrice||i.bid,ask:i.marketDepth?.[0]?.askPrice||i.ask,bidQty:i.marketDepth?.[0]?.bidQty||i.bidQty,askQty:i.marketDepth?.[0]?.askQty||i.askQty,high:i.high,low:i.low,open:i.open,close:i.close,timestamp:i.timestamp};return null},[r,e]);(0,c.useCallback)(()=>{if(!r||m<=0)return;console.log("\uD83D\uDD17 Building option chain for expiry:",r,"spot:",m);let e=v(m).map(e=>{let t=x(e,"CE"),s=x(e,"PE");return{strikePrice:e,call:t,put:s}}),t={underlying:"NIFTY",spotPrice:m,expiry:r,rows:e,timestamp:Date.now()};o(t),h.ko.cacheStaticData(h.aq.OPTION_CHAIN,t),console.log("\uD83D\uDCBE OptionChain: Cached option chain data for",r)},[r,m,v,x]);let C=async()=>{try{u(!0),f(null);let e=await h.ko.getCachedStaticData(h.aq.EXPIRY_DATES);if(e&&"object"==typeof e&&"expiries"in e&&Array.isArray(e.expiries)){console.log("✅ OptionChain: Using cached expiry data"),s(e),e.expiries.length>0&&n(e.expiries[0]),u(!1);return}console.log("\uD83C\uDF10 OptionChain: Fetching fresh expiry data from API");let t=await fetch("/api/nifty-expiry");if(!t.ok)throw Error(`Failed to fetch expiry dates: ${t.statusText}`);let r=await t.json();if(r.success)s(r.data),await h.ko.cacheStaticData(h.aq.EXPIRY_DATES,r.data),console.log("\uD83D\uDCBE OptionChain: Cached expiry data"),r.data.expiries.length>0&&n(r.data.expiries[0]);else throw Error(r.message||"Failed to fetch expiry dates")}catch(e){console.error("❌ OptionChain: Error fetching expiry dates:",e),f(e instanceof Error?e.message:"Unknown error")}finally{u(!1)}},k=l.ap.price,w=l.ap.number,S=e=>e?e>0?"text-green-400":e<0?"text-red-400":"text-gray-400":"text-gray-400";return d?(0,a.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading option chain..."})]})}):p?(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-red-800 font-medium",children:"Error Loading Option Chain"}),(0,a.jsx)("p",{className:"text-red-600 text-sm mt-1",children:p}),(0,a.jsx)("button",{onClick:C,className:"mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700",children:"Retry"})]}):(0,a.jsxs)("div",{className:"bg-white min-h-screen",children:[(0,a.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Options"}),(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md",children:"\uD83D\uDD0D NIFTY"}),(0,a.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md",children:"Strategy builder"}),(0,a.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md",children:"Class"}),(0,a.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md",children:"Volatility"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"By expiration | by strike"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"NIFTY Spot:"}),(0,a.jsx)("span",{className:"text-lg font-bold text-gray-900",children:k(m)}),24850===m&&(0,a.jsx)("span",{className:"text-xs text-gray-500",children:"(Mock)"})]})]})]})}),t&&(0,a.jsxs)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2 overflow-x-auto pb-2",children:t.expiries.slice(0,15).map((e,t)=>{let s=new Date(e),i=e===r,o=s.toDateString()===new Date().toDateString(),c=6048e5>Math.abs(s.getTime()-new Date().getTime()),l=new Date().getFullYear(),h=s.getFullYear(),d=s.toLocaleDateString("en-US",{month:"short"}),u=s.getDate(),p=h!==l;return(0,a.jsx)("button",{onClick:()=>n(e),className:`flex-shrink-0 px-3 py-2 text-xs font-medium transition-colors border ${i?"bg-black text-white border-black":o?"bg-orange-500 text-white border-orange-500":c?"bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"}`,children:(0,a.jsxs)("div",{className:"text-center min-w-[40px]",children:[(0,a.jsxs)("div",{className:"text-xs font-normal text-gray-600",children:[d,p&&` ${h.toString().slice(-2)}`]}),(0,a.jsx)("div",{className:"font-bold text-sm",children:u})]})},e)})}),!1]}),i&&(0,a.jsxs)("div",{className:"bg-white",children:[(0,a.jsxs)("div",{className:"flex bg-gray-50 border-b border-gray-200 text-xs font-medium text-gray-700 uppercase tracking-wider",children:[(0,a.jsx)("div",{className:"flex-1 text-center py-3 border-r border-gray-200",children:(0,a.jsx)("span",{className:"text-green-600 font-bold",children:"Calls"})}),(0,a.jsx)("div",{className:"w-20 text-center py-3 border-r border-gray-200",children:(0,a.jsx)("span",{className:"font-bold",children:"Strike"})}),(0,a.jsx)("div",{className:"flex-1 text-center py-3",children:(0,a.jsx)("span",{className:"text-red-600 font-bold",children:"Puts"})})]}),(0,a.jsxs)("div",{className:"flex bg-gray-100 border-b border-gray-200 text-xs font-medium text-gray-600 uppercase tracking-wider",children:[(0,a.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"OI"}),(0,a.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Volume"}),(0,a.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Bid"}),(0,a.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Ask"}),(0,a.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Change"}),(0,a.jsx)("div",{className:"flex-1 text-center py-2 px-1 border-r border-gray-200",children:"LTP"}),(0,a.jsx)("div",{className:"w-20 text-center py-2 px-1 border-r border-gray-200 font-bold",children:"Strike"}),(0,a.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"LTP"}),(0,a.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Change"}),(0,a.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Ask"}),(0,a.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Bid"}),(0,a.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Volume"}),(0,a.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"OI"})]}),(0,a.jsx)("div",{className:"divide-y divide-gray-100",children:i.rows.map((e,t)=>{let s=50>=Math.abs(e.strikePrice-m),r=e.strikePrice<m,n=e.strikePrice>m;return(0,a.jsxs)("div",{className:`flex hover:bg-gray-50 transition-colors ${s?"bg-yellow-50":t%2==0?"bg-white":"bg-gray-25"}`,children:[(0,a.jsx)("div",{className:`flex-1 text-center py-3 px-1 text-sm ${r?"text-green-700 font-medium":"text-gray-700"}`,children:w(e.call?.openInterest)}),(0,a.jsx)("div",{className:`flex-1 text-center py-3 px-1 text-sm ${r?"text-green-700 font-medium":"text-gray-700"}`,children:w(e.call?.volume)}),(0,a.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm text-green-600",children:k(e.call?.bid)}),(0,a.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm text-red-600",children:k(e.call?.ask)}),(0,a.jsx)("div",{className:`flex-1 text-center py-3 px-1 text-sm font-medium ${S(e.call?.change)}`,children:e.call?.change?(0,a.jsxs)(a.Fragment,{children:[e.call.change>0?"+":"",e.call.change.toFixed(2),(0,a.jsxs)("div",{className:"text-xs",children:["(",e.call.changePercent>0?"+":"",e.call.changePercent.toFixed(1),"%)"]})]}):"-"}),(0,a.jsx)("div",{className:`flex-1 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 ${r?"text-green-600":"text-gray-700"}`,children:k(e.call?.ltp)}),(0,a.jsx)("div",{className:`w-20 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 ${s?"bg-yellow-100 text-yellow-800":"text-gray-900"}`,children:e.strikePrice}),(0,a.jsx)("div",{className:`flex-1 text-center py-3 px-1 text-sm font-bold ${n?"text-red-600":"text-gray-700"}`,children:k(e.put?.ltp)}),(0,a.jsx)("div",{className:`flex-1 text-center py-3 px-1 text-sm font-medium ${S(e.put?.change)}`,children:e.put?.change?(0,a.jsxs)(a.Fragment,{children:[e.put.change>0?"+":"",e.put.change.toFixed(2),(0,a.jsxs)("div",{className:"text-xs",children:["(",e.put.changePercent>0?"+":"",e.put.changePercent.toFixed(1),"%)"]})]}):"-"}),(0,a.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm text-red-600",children:k(e.put?.ask)}),(0,a.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm text-green-600",children:k(e.put?.bid)}),(0,a.jsx)("div",{className:`flex-1 text-center py-3 px-1 text-sm ${n?"text-red-700 font-medium":"text-gray-700"}`,children:w(e.put?.volume)}),(0,a.jsx)("div",{className:`flex-1 text-center py-3 px-1 text-sm ${n?"text-red-700 font-medium":"text-gray-700"}`,children:w(e.put?.openInterest)})]},e.strikePrice)})})]}),i&&(0,a.jsx)("div",{className:"bg-gray-50 border-t border-gray-200 px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[(0,a.jsx)("span",{children:"Show all"}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:["Showing ",i.rows.length," strikes around ATM"]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Last updated: ",new Date(i.timestamp).toLocaleTimeString()]})]})})]})}let u=e=>{let t,s=new Set,r=(e,r)=>{let n="function"==typeof e?e(t):e;if(!Object.is(n,t)){let e=t;t=(null!=r?r:"object"!=typeof n||null===n)?n:Object.assign({},t,n),s.forEach(s=>s(t,e))}},n=()=>t,i={setState:r,getState:n,getInitialState:()=>o,subscribe:e=>(s.add(e),()=>s.delete(e))},o=t=e(r,n,i);return i},p=e=>e?u(e):u,f=e=>e,m=e=>{let t=p(e),s=e=>(function(e,t=f){let s=c.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return c.useDebugValue(s),s})(t,e);return Object.assign(s,t),s};function g(e,t){let s;try{s=e()}catch(e){return}return{getItem:e=>{var r;let n=e=>null===e?null:JSON.parse(e,null==t?void 0:t.reviver),i=null!=(r=s.getItem(e))?r:null;return i instanceof Promise?i.then(n):n(i)},setItem:(e,r)=>s.setItem(e,JSON.stringify(r,null==t?void 0:t.replacer)),removeItem:e=>s.removeItem(e)}}let y=e=>t=>{try{let s=e(t);if(s instanceof Promise)return s;return{then:e=>y(e)(s),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>y(t)(e)}}};var _=s(7101),b=s.t(_,2);let v=Object.create(null);v.open="0",v.close="1",v.ping="2",v.pong="3",v.message="4",v.upgrade="5",v.noop="6";let x=Object.create(null);Object.keys(v).forEach(e=>{x[v[e]]=e});let C={type:"error",data:"parser error"},k=({type:e,data:t},s,r)=>r(t instanceof ArrayBuffer||ArrayBuffer.isView(t)?s?t:"b"+w(t,!0).toString("base64"):v[e]+(t||"")),w=(e,t)=>Buffer.isBuffer(e)||e instanceof Uint8Array&&!t?e:e instanceof ArrayBuffer?Buffer.from(e):Buffer.from(e.buffer,e.byteOffset,e.byteLength),S=(e,t)=>{if("string"!=typeof e)return{type:"message",data:E(e,t)};let s=e.charAt(0);return"b"===s?{type:"message",data:E(Buffer.from(e.substring(1),"base64"),t)}:x[s]?e.length>1?{type:x[s],data:e.substring(1)}:{type:x[s]}:C},E=(e,t)=>"arraybuffer"===t?e instanceof ArrayBuffer?e:Buffer.isBuffer(e)?e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength):e.buffer:Buffer.isBuffer(e)?e:Buffer.from(e),O=(e,t)=>{let s=e.length,r=Array(s),n=0;e.forEach((e,i)=>{k(e,!1,e=>{r[i]=e,++n===s&&t(r.join("\x1e"))})})},T=(e,t)=>{let s=e.split("\x1e"),r=[];for(let e=0;e<s.length;e++){let n=S(s[e],t);if(r.push(n),"error"===n.type)break}return r};function N(e){return e.reduce((e,t)=>e+t.length,0)}function F(e,t){if(e[0].length===t)return e.shift();let s=new Uint8Array(t),r=0;for(let n=0;n<t;n++)s[n]=e[0][r++],r===e[0].length&&(e.shift(),r=0);return e.length&&r<e[0].length&&(e[0]=e[0].slice(r)),s}function R(e){if(e){var t=e;for(var s in R.prototype)t[s]=R.prototype[s];return t}}R.prototype.on=R.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},R.prototype.once=function(e,t){function s(){this.off(e,s),t.apply(this,arguments)}return s.fn=t,this.on(e,s),this},R.prototype.off=R.prototype.removeListener=R.prototype.removeAllListeners=R.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var s,r=this._callbacks["$"+e];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var n=0;n<r.length;n++)if((s=r[n])===t||s.fn===t){r.splice(n,1);break}return 0===r.length&&delete this._callbacks["$"+e],this},R.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=Array(arguments.length-1),s=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(s){s=s.slice(0);for(var r=0,n=s.length;r<n;++r)s[r].apply(this,t)}return this},R.prototype.emitReserved=R.prototype.emit,R.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},R.prototype.hasListeners=function(e){return!!this.listeners(e).length};let L=process.nextTick,A=global;class D{constructor(){this._cookies=new Map}parseCookies(e){e&&e.forEach(e=>{let t=function(e){let t=e.split("; "),s=t[0].indexOf("=");if(-1===s)return;let r=t[0].substring(0,s).trim();if(!r.length)return;let n=t[0].substring(s+1).trim();34===n.charCodeAt(0)&&(n=n.slice(1,-1));let i={name:r,value:n};for(let e=1;e<t.length;e++){let s=t[e].split("=");if(2!==s.length)continue;let r=s[0].trim(),n=s[1].trim();switch(r){case"Expires":i.expires=new Date(n);break;case"Max-Age":let o=new Date;o.setUTCSeconds(o.getUTCSeconds()+parseInt(n,10)),i.expires=o}}return i}(e);t&&this._cookies.set(t.name,t)})}get cookies(){let e=Date.now();return this._cookies.forEach((t,s)=>{var r;(null==(r=t.expires)?void 0:r.getTime())<e&&this._cookies.delete(s)}),this._cookies.entries()}addCookies(e){let t=[];for(let[e,s]of this.cookies)t.push(`${e}=${s.value}`);t.length&&(e.setDisableHeaderCheck(!0),e.setRequestHeader("cookie",t.join("; ")))}appendCookies(e){for(let[t,s]of this.cookies)e.append("cookie",`${t}=${s.value}`)}}function I(e,...t){return t.reduce((t,s)=>(e.hasOwnProperty(s)&&(t[s]=e[s]),t),{})}let j=A.setTimeout,P=A.clearTimeout;function M(e,t){t.useNativeTimers?(e.setTimeoutFn=j.bind(A),e.clearTimeoutFn=P.bind(A)):(e.setTimeoutFn=A.setTimeout.bind(A),e.clearTimeoutFn=A.clearTimeout.bind(A))}function B(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}var U=s(9616);let $=U("engine.io-client:transport");class q extends Error{constructor(e,t,s){super(e),this.description=t,this.context=s,this.type="TransportError"}}class W extends R{constructor(e){super(),this.writable=!1,M(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,t,s){return super.emitReserved("error",new q(e,t,s)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return("opening"===this.readyState||"open"===this.readyState)&&(this.doClose(),this.onClose()),this}send(e){"open"===this.readyState?this.write(e):$("transport is not open, discarding packets")}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){let t=S(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e,t={}){return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){let e=this.opts.hostname;return -1===e.indexOf(":")?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(e){let t=function(e){let t="";for(let s in e)e.hasOwnProperty(s)&&(t.length&&(t+="&"),t+=encodeURIComponent(s)+"="+encodeURIComponent(e[s]));return t}(e);return t.length?"?"+t:""}}let V=U("engine.io-client:polling");class H extends W{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";let t=()=>{V("paused"),this.readyState="paused",e()};if(this._polling||!this.writable){let e=0;this._polling&&(V("we are currently polling - waiting to pause"),e++,this.once("pollComplete",function(){V("pre-pause polling complete"),--e||t()})),this.writable||(V("we are currently writing - waiting to pause"),e++,this.once("drain",function(){V("pre-pause writing complete"),--e||t()}))}else t()}_poll(){V("polling"),this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){V("polling got data %s",e),T(e,this.socket.binaryType).forEach(e=>{if("opening"===this.readyState&&"open"===e.type&&this.onOpen(),"close"===e.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(e)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState?this._poll():V('ignoring poll - transport state "%s"',this.readyState))}doClose(){let e=()=>{V("writing close packet"),this.write([{type:"close"}])};"open"===this.readyState?(V("transport open - closing"),e()):(V("transport not open - deferring close"),this.once("open",e))}write(e){this.writable=!1,O(e,e=>{this.doWrite(e,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){let e=this.opts.secure?"https":"http",t=this.query||{};return!1!==this.opts.timestampRequests&&(t[this.opts.timestampParam]=B()),this.supportsBinary||t.sid||(t.b64=1),this.createUri(e,t)}}let G=!1;try{G="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(e){}let z=G,Y=U("engine.io-client:polling");function J(){}class X extends H{constructor(e){if(super(e),"undefined"!=typeof location){let t="https:"===location.protocol,s=location.port;s||(s=t?"443":"80"),this.xd="undefined"!=typeof location&&e.hostname!==location.hostname||s!==e.port}}doWrite(e,t){let s=this.request({method:"POST",data:e});s.on("success",t),s.on("error",(e,t)=>{this.onError("xhr post error",e,t)})}doPoll(){Y("xhr poll");let e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(e,t)=>{this.onError("xhr poll error",e,t)}),this.pollXhr=e}}class K extends R{constructor(e,t,s){super(),this.createRequest=e,M(this,s),this._opts=s,this._method=s.method||"GET",this._uri=t,this._data=void 0!==s.data?s.data:null,this._create()}_create(){var e;let t=I(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this._opts.xd;let s=this._xhr=this.createRequest(t);try{Y("xhr open %s: %s",this._method,this._uri),s.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders)for(let e in s.setDisableHeaderCheck&&s.setDisableHeaderCheck(!0),this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(e)&&s.setRequestHeader(e,this._opts.extraHeaders[e])}catch(e){}if("POST"===this._method)try{s.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(e){}try{s.setRequestHeader("Accept","*/*")}catch(e){}null==(e=this._opts.cookieJar)||e.addCookies(s),"withCredentials"in s&&(s.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(s.timeout=this._opts.requestTimeout),s.onreadystatechange=()=>{var e;3===s.readyState&&(null==(e=this._opts.cookieJar)||e.parseCookies(s.getResponseHeader("set-cookie"))),4===s.readyState&&(200===s.status||1223===s.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"==typeof s.status?s.status:0)},0))},Y("xhr data %s",this._data),s.send(this._data)}catch(e){this.setTimeoutFn(()=>{this._onError(e)},0);return}"undefined"!=typeof document&&(this._index=K.requestsCount++,K.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if(void 0!==this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=J,e)try{this._xhr.abort()}catch(e){}"undefined"!=typeof document&&delete K.requests[this._index],this._xhr=null}}_onLoad(){let e=this._xhr.responseText;null!==e&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}function Q(){for(let e in K.requests)K.requests.hasOwnProperty(e)&&K.requests[e].abort()}K.requestsCount=0,K.requests={},"undefined"!=typeof document&&("function"==typeof attachEvent?attachEvent("onunload",Q):"function"==typeof addEventListener&&addEventListener("onpagehide"in A?"pagehide":"unload",Q,!1)),function(){let e=function(e){let t=e.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!t||z))return new XMLHttpRequest}catch(e){}if(!t)try{return new A[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch(e){}}({xdomain:!1});e&&e.responseType}();let Z=_||b;class ee extends X{request(e={}){var t;return Object.assign(e,{xd:this.xd,cookieJar:null==(t=this.socket)?void 0:t._cookieJar},this.opts),new K(e=>new Z(e),this.uri(),e)}}s(2590),s(4687),s(8099);var et=s(1759);s(3211);let es=U("engine.io-client:websocket"),er="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class en extends W{get name(){return"websocket"}doOpen(){let e=this.uri(),t=this.opts.protocols,s=er?{}:I(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(s.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,t,s)}catch(e){return this.emitReserved("error",e)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let s=e[t],r=t===e.length-1;k(s,this.supportsBinary,e=>{try{this.doWrite(s,e)}catch(e){es("websocket closed before onclose event")}r&&L(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){let e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=B()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}}A.WebSocket||A.MozWebSocket;class ei extends en{createSocket(e,t,s){var r;if(null==(r=this.socket)?void 0:r._cookieJar)for(let[e,t]of(s.headers=s.headers||{},s.headers.cookie="string"==typeof s.headers.cookie?[s.headers.cookie]:s.headers.cookie||[],this.socket._cookieJar.cookies))s.headers.cookie.push(`${e}=${t.value}`);return new et(e,t,s)}doWrite(e,t){let s={};e.options&&(s.compress=e.options.compress),this.opts.perMessageDeflate&&("string"==typeof t?Buffer.byteLength(t):t.length)<this.opts.perMessageDeflate.threshold&&(s.compress=!1),this.ws.send(t,s)}}let eo=U("engine.io-client:webtransport");class ea extends W{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(e){return this.emitReserved("error",e)}this._transport.closed.then(()=>{eo("transport closed gracefully"),this.onClose()}).catch(e=>{eo("transport closed due to %s",e),this.onError("webtransport error",e)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(e=>{let t=function(e,t){n||(n=new TextDecoder);let s=[],r=0,i=-1,o=!1;return new TransformStream({transform(a,c){for(s.push(a);;){if(0===r){if(1>N(s))break;let e=F(s,1);o=(128&e[0])==128,r=(i=127&e[0])<126?3:126===i?1:2}else if(1===r){if(2>N(s))break;let e=F(s,2);i=new DataView(e.buffer,e.byteOffset,e.length).getUint16(0),r=3}else if(2===r){if(8>N(s))break;let e=F(s,8),t=new DataView(e.buffer,e.byteOffset,e.length),n=t.getUint32(0);if(n>2097151){c.enqueue(C);break}i=0x100000000*n+t.getUint32(4),r=3}else{if(N(s)<i)break;let e=F(s,i);c.enqueue(S(o?e:n.decode(e),t)),r=0}if(0===i||i>e){c.enqueue(C);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),s=e.readable.pipeThrough(t).getReader(),i=new TransformStream({transform(e,t){!function(e,t){if(e.data instanceof ArrayBuffer||ArrayBuffer.isView(e.data))return t(w(e.data,!1));k(e,!0,e=>{r||(r=new TextEncoder),t(r.encode(e))})}(e,s=>{let r,n=s.length;if(n<126)new DataView((r=new Uint8Array(1)).buffer).setUint8(0,n);else if(n<65536){let e=new DataView((r=new Uint8Array(3)).buffer);e.setUint8(0,126),e.setUint16(1,n)}else{let e=new DataView((r=new Uint8Array(9)).buffer);e.setUint8(0,127),e.setBigUint64(1,BigInt(n))}e.data&&"string"!=typeof e.data&&(r[0]|=128),t.enqueue(r),t.enqueue(s)})}});i.readable.pipeTo(e.writable),this._writer=i.writable.getWriter();let o=()=>{s.read().then(({done:e,value:t})=>{if(e)return void eo("session is closed");eo("received chunk: %o",t),this.onPacket(t),o()}).catch(e=>{eo("an error occurred while reading: %s",e)})};o();let a={type:"open"};this.query.sid&&(a.data=`{"sid":"${this.query.sid}"}`),this._writer.write(a).then(()=>this.onOpen())})})}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let s=e[t],r=t===e.length-1;this._writer.write(s).then(()=>{r&&L(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var e;null==(e=this._transport)||e.close()}}let ec={websocket:ei,webtransport:ea,polling:ee},el=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,eh=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function ed(e){if(e.length>8e3)throw"URI too long";let t=e,s=e.indexOf("["),r=e.indexOf("]");-1!=s&&-1!=r&&(e=e.substring(0,s)+e.substring(s,r).replace(/:/g,";")+e.substring(r,e.length));let n=el.exec(e||""),i={},o=14;for(;o--;)i[eh[o]]=n[o]||"";return -1!=s&&-1!=r&&(i.source=t,i.host=i.host.substring(1,i.host.length-1).replace(/;/g,":"),i.authority=i.authority.replace("[","").replace("]","").replace(/;/g,":"),i.ipv6uri=!0),i.pathNames=function(e,t){let s=t.replace(/\/{2,9}/g,"/").split("/");return("/"==t.slice(0,1)||0===t.length)&&s.splice(0,1),"/"==t.slice(-1)&&s.splice(s.length-1,1),s}(0,i.path),i.queryKey=function(e,t){let s={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(e,t,r){t&&(s[t]=r)}),s}(0,i.query),i}let eu=U("engine.io-client:socket"),ep="function"==typeof addEventListener&&"function"==typeof removeEventListener,ef=[];ep&&addEventListener("offline",()=>{eu("closing %d connection(s) because the network was lost",ef.length),ef.forEach(e=>e())},!1);class em extends R{constructor(e,t){if(super(),this.binaryType="nodebuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&"object"==typeof e&&(t=e,e=null),e){let s=ed(e);t.hostname=s.host,t.secure="https"===s.protocol||"wss"===s.protocol,t.port=s.port,s.query&&(t.query=s.query)}else t.host&&(t.hostname=ed(t.host).host);M(this,t),this.secure=null!=t.secure?t.secure:"undefined"!=typeof location&&"https:"===location.protocol,t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=t.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},t.transports.forEach(e=>{let t=e.prototype.name;this.transports.push(t),this._transportsByName[t]=e}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(e){let t={},s=e.split("&");for(let e=0,r=s.length;e<r;e++){let r=s[e].split("=");t[decodeURIComponent(r[0])]=decodeURIComponent(r[1])}return t}(this.opts.query)),ep&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(eu("adding listener for the 'offline' event"),this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},ef.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=new D),this._open()}createTransport(e){eu('creating transport "%s"',e);let t=Object.assign({},this.opts.query);t.EIO=4,t.transport=e,this.id&&(t.sid=this.id);let s=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return eu("options: %j",s),new this._transportsByName[e](s)}_open(){if(0===this.transports.length)return void this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);let e=this.opts.rememberUpgrade&&em.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";let t=this.createTransport(e);t.open(),this.setTransport(t)}setTransport(e){eu("setting transport %s",e.name),this.transport&&(eu("clearing existing transport %s",this.transport.name),this.transport.removeAllListeners()),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",e=>this._onClose("transport close",e))}onOpen(){eu("socket open"),this.readyState="open",em.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(eu('socket receive: type "%s", data "%s"',e.type,e.data),this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":let t=Error("server error");t.code=e.data,this._onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data)}else eu('packet received with socket readyState "%s"',this.readyState)}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);let e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){let e=this._getWritablePackets();eu("flushing %d packets in socket",e.length),this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let e=1;for(let t=0;t<this.writeBuffer.length;t++){let s=this.writeBuffer[t].data;if(s&&(e+="string"==typeof s?function(e){let t=0,s=0;for(let r=0,n=e.length;r<n;r++)(t=e.charCodeAt(r))<128?s+=1:t<2048?s+=2:t<55296||t>=57344?s+=3:(r++,s+=4);return s}(s):Math.ceil(1.33*(s.byteLength||s.size))),t>0&&e>this._maxPayload)return eu("only send %d out of %d packets",t,this.writeBuffer.length),this.writeBuffer.slice(0,t);e+=2}return eu("payload size is %d (max: %d)",e,this._maxPayload),this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;let e=Date.now()>this._pingTimeoutTime;return e&&(eu("throttled timer detected, scheduling connection close"),this._pingTimeoutTime=0,L(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),e}write(e,t,s){return this._sendPacket("message",e,t,s),this}send(e,t,s){return this._sendPacket("message",e,t,s),this}_sendPacket(e,t,s,r){if("function"==typeof t&&(r=t,t=void 0),"function"==typeof s&&(r=s,s=null),"closing"===this.readyState||"closed"===this.readyState)return;(s=s||{}).compress=!1!==s.compress;let n={type:e,data:t,options:s};this.emitReserved("packetCreate",n),this.writeBuffer.push(n),r&&this.once("flush",r),this.flush()}close(){let e=()=>{this._onClose("forced close"),eu("socket closing - telling transport to close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},s=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return("opening"===this.readyState||"open"===this.readyState)&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?s():e()}):this.upgrading?s():e()),this}_onError(e){if(eu("socket error %j",e),em.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return eu("trying next transport"),this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(eu('socket close with reason: "%s"',e),this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),ep&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){let e=ef.indexOf(this._offlineEventListener);-1!==e&&(eu("removing listener for the 'offline' event"),ef.splice(e,1))}this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this._prevBufferLen=0}}}em.protocol=4;class eg extends em{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade){eu("starting upgrade probes");for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}}_probe(e){eu('probing transport "%s"',e);let t=this.createTransport(e),s=!1;em.priorWebsocketSuccess=!1;let r=()=>{s||(eu('probe transport "%s" opened',e),t.send([{type:"ping",data:"probe"}]),t.once("packet",r=>{if(!s)if("pong"===r.type&&"probe"===r.data){if(eu('probe transport "%s" pong',e),this.upgrading=!0,this.emitReserved("upgrading",t),!t)return;em.priorWebsocketSuccess="websocket"===t.name,eu('pausing current transport "%s"',this.transport.name),this.transport.pause(()=>{s||"closed"!==this.readyState&&(eu("changing transport and sending upgrade packet"),l(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())})}else{eu('probe transport "%s" failed',e);let s=Error("probe error");s.transport=t.name,this.emitReserved("upgradeError",s)}}))};function n(){s||(s=!0,l(),t.close(),t=null)}let i=s=>{let r=Error("probe error: "+s);r.transport=t.name,n(),eu('probe transport "%s" failed because of error: %s',e,s),this.emitReserved("upgradeError",r)};function o(){i("transport closed")}function a(){i("socket closed")}function c(e){t&&e.name!==t.name&&(eu('"%s" works - aborting "%s"',e.name,t.name),n())}let l=()=>{t.removeListener("open",r),t.removeListener("error",i),t.removeListener("close",o),this.off("close",a),this.off("upgrading",c)};t.once("open",r),t.once("error",i),t.once("close",o),this.once("close",a),this.once("upgrading",c),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==e?this.setTimeoutFn(()=>{s||t.open()},200):t.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){let t=[];for(let s=0;s<e.length;s++)~this.transports.indexOf(e[s])&&t.push(e[s]);return t}}class ey extends eg{constructor(e,t={}){let s="object"==typeof e?e:t;(!s.transports||s.transports&&"string"==typeof s.transports[0])&&(s.transports=(s.transports||["polling","websocket","webtransport"]).map(e=>ec[e]).filter(e=>!!e)),super(e,s)}}ey.protocol;var e_=s(7905);let eb=e_("socket.io-client:url"),ev="function"==typeof ArrayBuffer,ex=e=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,eC=Object.prototype.toString,ek="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===eC.call(Blob),ew="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===eC.call(File);function eS(e){return ev&&(e instanceof ArrayBuffer||ex(e))||ek&&e instanceof Blob||ew&&e instanceof File}let eE=s(5133)("socket.io-parser"),eO=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],eT=5;!function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"}(i||(i={}));class eN{constructor(e){this.replacer=e}encode(e){return(eE("encoding packet %j",e),(e.type===i.EVENT||e.type===i.ACK)&&function e(t,s){if(!t||"object"!=typeof t)return!1;if(Array.isArray(t)){for(let s=0,r=t.length;s<r;s++)if(e(t[s]))return!0;return!1}if(eS(t))return!0;if(t.toJSON&&"function"==typeof t.toJSON&&1==arguments.length)return e(t.toJSON(),!0);for(let s in t)if(Object.prototype.hasOwnProperty.call(t,s)&&e(t[s]))return!0;return!1}(e))?this.encodeAsBinary({type:e.type===i.EVENT?i.BINARY_EVENT:i.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id}):[this.encodeAsString(e)]}encodeAsString(e){let t=""+e.type;return(e.type===i.BINARY_EVENT||e.type===i.BINARY_ACK)&&(t+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(t+=e.nsp+","),null!=e.id&&(t+=e.id),null!=e.data&&(t+=JSON.stringify(e.data,this.replacer)),eE("encoded %j as %s",e,t),t}encodeAsBinary(e){let t=function(e){let t=[],s=e.data;return e.data=function e(t,s){if(!t)return t;if(eS(t)){let e={_placeholder:!0,num:s.length};return s.push(t),e}if(Array.isArray(t)){let r=Array(t.length);for(let n=0;n<t.length;n++)r[n]=e(t[n],s);return r}if("object"==typeof t&&!(t instanceof Date)){let r={};for(let n in t)Object.prototype.hasOwnProperty.call(t,n)&&(r[n]=e(t[n],s));return r}return t}(s,t),e.attachments=t.length,{packet:e,buffers:t}}(e),s=this.encodeAsString(t.packet),r=t.buffers;return r.unshift(s),r}}function eF(e){return"[object Object]"===Object.prototype.toString.call(e)}class eR extends R{constructor(e){super(),this.reviver=e}add(e){let t;if("string"==typeof e){if(this.reconstructor)throw Error("got plaintext data when reconstructing a packet");let s=(t=this.decodeString(e)).type===i.BINARY_EVENT;s||t.type===i.BINARY_ACK?(t.type=s?i.EVENT:i.ACK,this.reconstructor=new eL(t),0===t.attachments&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else if(eS(e)||e.base64)if(this.reconstructor)(t=this.reconstructor.takeBinaryData(e))&&(this.reconstructor=null,super.emitReserved("decoded",t));else throw Error("got binary data when not reconstructing a packet");else throw Error("Unknown type: "+e)}decodeString(e){let t=0,s={type:Number(e.charAt(0))};if(void 0===i[s.type])throw Error("unknown packet type "+s.type);if(s.type===i.BINARY_EVENT||s.type===i.BINARY_ACK){let r=t+1;for(;"-"!==e.charAt(++t)&&t!=e.length;);let n=e.substring(r,t);if(n!=Number(n)||"-"!==e.charAt(t))throw Error("Illegal attachments");s.attachments=Number(n)}if("/"===e.charAt(t+1)){let r=t+1;for(;++t&&","!==e.charAt(t)&&t!==e.length;);s.nsp=e.substring(r,t)}else s.nsp="/";let r=e.charAt(t+1);if(""!==r&&Number(r)==r){let r=t+1;for(;++t;){let s=e.charAt(t);if(null==s||Number(s)!=s){--t;break}if(t===e.length)break}s.id=Number(e.substring(r,t+1))}if(e.charAt(++t)){let r=this.tryParse(e.substr(t));if(eR.isPayloadValid(s.type,r))s.data=r;else throw Error("invalid payload")}return eE("decoded %s as %j",e,s),s}tryParse(e){try{return JSON.parse(e,this.reviver)}catch(e){return!1}}static isPayloadValid(e,t){switch(e){case i.CONNECT:return eF(t);case i.DISCONNECT:return void 0===t;case i.CONNECT_ERROR:return"string"==typeof t||eF(t);case i.EVENT:case i.BINARY_EVENT:return Array.isArray(t)&&("number"==typeof t[0]||"string"==typeof t[0]&&-1===eO.indexOf(t[0]));case i.ACK:case i.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class eL{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){var t,s;let e=(t=this.reconPack,s=this.buffers,t.data=function e(t,s){if(!t)return t;if(t&&!0===t._placeholder){if("number"==typeof t.num&&t.num>=0&&t.num<s.length)return s[t.num];throw Error("illegal attachments")}if(Array.isArray(t))for(let r=0;r<t.length;r++)t[r]=e(t[r],s);else if("object"==typeof t)for(let r in t)Object.prototype.hasOwnProperty.call(t,r)&&(t[r]=e(t[r],s));return t}(t.data,s),delete t.attachments,t);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function eA(e,t,s){return e.on(t,s),function(){e.off(t,s)}}let eD=e_("socket.io-client:socket"),eI=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class ej extends R{constructor(e,t,s){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,s&&s.auth&&(this.auth=s.auth),this._opts=Object.assign({},s),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;let e=this.io;this.subs=[eA(e,"open",this.onopen.bind(this)),eA(e,"packet",this.onpacket.bind(this)),eA(e,"error",this.onerror.bind(this)),eA(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...e){return e.unshift("message"),this.emit.apply(this,e),this}emit(e,...t){var s,r,n;if(eI.hasOwnProperty(e))throw Error('"'+e.toString()+'" is a reserved event name');if(t.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(t),this;let o={type:i.EVENT,data:t};if(o.options={},o.options.compress=!1!==this.flags.compress,"function"==typeof t[t.length-1]){let e=this.ids++;eD("emitting packet with ack id %d",e);let s=t.pop();this._registerAckCallback(e,s),o.id=e}let a=null==(r=null==(s=this.io.engine)?void 0:s.transport)?void 0:r.writable,c=this.connected&&!(null==(n=this.io.engine)?void 0:n._hasPingExpired());return this.flags.volatile&&!a?eD("discard packet as the transport is not currently writable"):c?(this.notifyOutgoingListeners(o),this.packet(o)):this.sendBuffer.push(o),this.flags={},this}_registerAckCallback(e,t){var s;let r=null!=(s=this.flags.timeout)?s:this._opts.ackTimeout;if(void 0===r){this.acks[e]=t;return}let n=this.io.setTimeoutFn(()=>{delete this.acks[e];for(let t=0;t<this.sendBuffer.length;t++)this.sendBuffer[t].id===e&&(eD("removing packet with ack id %d from the buffer",e),this.sendBuffer.splice(t,1));eD("event with ack id %d has timed out after %d ms",e,r),t.call(this,Error("operation has timed out"))},r),i=(...e)=>{this.io.clearTimeoutFn(n),t.apply(this,e)};i.withError=!0,this.acks[e]=i}emitWithAck(e,...t){return new Promise((s,r)=>{let n=(e,t)=>e?r(e):s(t);n.withError=!0,t.push(n),this.emit(e,...t)})}_addToQueue(e){let t;"function"==typeof e[e.length-1]&&(t=e.pop());let s={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((e,...r)=>{if(s===this._queue[0])return null!==e?s.tryCount>this._opts.retries&&(eD("packet [%d] is discarded after %d tries",s.id,s.tryCount),this._queue.shift(),t&&t(e)):(eD("packet [%d] was successfully sent",s.id),this._queue.shift(),t&&t(null,...r)),s.pending=!1,this._drainQueue()}),this._queue.push(s),this._drainQueue()}_drainQueue(e=!1){if(eD("draining queue"),!this.connected||0===this._queue.length)return;let t=this._queue[0];if(t.pending&&!e)return void eD("packet [%d] has already been sent and is waiting for an ack",t.id);t.pending=!0,t.tryCount++,eD("sending packet [%d] (try n\xb0%d)",t.id,t.tryCount),this.flags=t.flags,this.emit.apply(this,t.args)}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){eD("transport is open - connecting"),"function"==typeof this.auth?this.auth(e=>{this._sendConnectPacket(e)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:i.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){eD("close (%s)",e),this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(e=>{if(!this.sendBuffer.some(t=>String(t.id)===e)){let t=this.acks[e];delete this.acks[e],t.withError&&t.call(this,Error("socket has been disconnected"))}})}onpacket(e){if(e.nsp===this.nsp)switch(e.type){case i.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case i.EVENT:case i.BINARY_EVENT:this.onevent(e);break;case i.ACK:case i.BINARY_ACK:this.onack(e);break;case i.DISCONNECT:this.ondisconnect();break;case i.CONNECT_ERROR:this.destroy();let t=Error(e.data.message);t.data=e.data.data,this.emitReserved("connect_error",t)}}onevent(e){let t=e.data||[];eD("emitting event %j",t),null!=e.id&&(eD("attaching ack callback to event"),t.push(this.ack(e.id))),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length)for(let t of this._anyListeners.slice())t.apply(this,e);super.emit.apply(this,e),this._pid&&e.length&&"string"==typeof e[e.length-1]&&(this._lastOffset=e[e.length-1])}ack(e){let t=this,s=!1;return function(...r){s||(s=!0,eD("sending ack %j",r),t.packet({type:i.ACK,id:e,data:r}))}}onack(e){let t=this.acks[e.id];if("function"!=typeof t)return void eD("bad ack %s",e.id);delete this.acks[e.id],eD("calling ack %s with %j",e.id,e.data),t.withError&&e.data.unshift(null),t.apply(this,e.data)}onconnect(e,t){eD("socket connected with id %s",e),this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(e=>this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e=>{this.notifyOutgoingListeners(e),this.packet(e)}),this.sendBuffer=[]}ondisconnect(){eD("server disconnect (%s)",this.nsp),this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(e=>e()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&(eD("performing disconnect (%s)",this.nsp),this.packet({type:i.DISCONNECT})),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){let t=this._anyListeners;for(let s=0;s<t.length;s++)if(e===t[s]){t.splice(s,1);break}}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){let t=this._anyOutgoingListeners;for(let s=0;s<t.length;s++)if(e===t[s]){t.splice(s,1);break}}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length)for(let t of this._anyOutgoingListeners.slice())t.apply(this,e.data)}}function eP(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}eP.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),s=Math.floor(t*this.jitter*e);e=(1&Math.floor(10*t))==0?e-s:e+s}return 0|Math.min(e,this.max)},eP.prototype.reset=function(){this.attempts=0},eP.prototype.setMin=function(e){this.ms=e},eP.prototype.setMax=function(e){this.max=e},eP.prototype.setJitter=function(e){this.jitter=e};let eM=e_("socket.io-client:manager");class eB extends R{constructor(e,t){var s;super(),this.nsps={},this.subs=[],e&&"object"==typeof e&&(t=e,e=void 0),(t=t||{}).path=t.path||"/socket.io",this.opts=t,M(this,t),this.reconnection(!1!==t.reconnection),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor(null!=(s=t.randomizationFactor)?s:.5),this.backoff=new eP({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==t.timeout?2e4:t.timeout),this._readyState="closed",this.uri=e;let r=t.parser||o;this.encoder=new r.Encoder,this.decoder=new r.Decoder,this._autoConnect=!1!==t.autoConnect,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return void 0===e?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return void 0===e?this._reconnectionDelay:(this._reconnectionDelay=e,null==(t=this.backoff)||t.setMin(e),this)}randomizationFactor(e){var t;return void 0===e?this._randomizationFactor:(this._randomizationFactor=e,null==(t=this.backoff)||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return void 0===e?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,null==(t=this.backoff)||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(e){if(eM("readyState %s",this._readyState),~this._readyState.indexOf("open"))return this;eM("opening %s",this.uri),this.engine=new ey(this.uri,this.opts);let t=this.engine,s=this;this._readyState="opening",this.skipReconnect=!1;let r=eA(t,"open",function(){s.onopen(),e&&e()}),n=t=>{eM("error"),this.cleanup(),this._readyState="closed",this.emitReserved("error",t),e?e(t):this.maybeReconnectOnOpen()},i=eA(t,"error",n);if(!1!==this._timeout){let e=this._timeout;eM("connect attempt will timeout after %d",e);let s=this.setTimeoutFn(()=>{eM("connect attempt timed out after %d",e),r(),n(Error("timeout")),t.close()},e);this.opts.autoUnref&&s.unref(),this.subs.push(()=>{this.clearTimeoutFn(s)})}return this.subs.push(r),this.subs.push(i),this}connect(e){return this.open(e)}onopen(){eM("open"),this.cleanup(),this._readyState="open",this.emitReserved("open");let e=this.engine;this.subs.push(eA(e,"ping",this.onping.bind(this)),eA(e,"data",this.ondata.bind(this)),eA(e,"error",this.onerror.bind(this)),eA(e,"close",this.onclose.bind(this)),eA(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(e){this.onclose("parse error",e)}}ondecoded(e){L(()=>{this.emitReserved("packet",e)},this.setTimeoutFn)}onerror(e){eM("error",e),this.emitReserved("error",e)}socket(e,t){let s=this.nsps[e];return s?this._autoConnect&&!s.active&&s.connect():(s=new ej(this,e,t),this.nsps[e]=s),s}_destroy(e){for(let e of Object.keys(this.nsps))if(this.nsps[e].active)return void eM("socket %s is still active, skipping close",e);this._close()}_packet(e){eM("writing packet %j",e);let t=this.encoder.encode(e);for(let s=0;s<t.length;s++)this.engine.write(t[s],e.options)}cleanup(){eM("cleanup"),this.subs.forEach(e=>e()),this.subs.length=0,this.decoder.destroy()}_close(){eM("disconnect"),this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,t){var s;eM("closed due to %s",e),this.cleanup(),null==(s=this.engine)||s.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;let e=this;if(this.backoff.attempts>=this._reconnectionAttempts)eM("reconnect failed"),this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{let t=this.backoff.duration();eM("will wait %dms before reconnect attempt",t),this._reconnecting=!0;let s=this.setTimeoutFn(()=>{!e.skipReconnect&&(eM("attempting reconnect"),this.emitReserved("reconnect_attempt",e.backoff.attempts),e.skipReconnect||e.open(t=>{t?(eM("reconnect attempt error"),e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",t)):(eM("reconnect success"),e.onreconnect())}))},t);this.opts.autoUnref&&s.unref(),this.subs.push(()=>{this.clearTimeoutFn(s)})}}onreconnect(){let e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}let eU=e_("socket.io-client"),e$={};function eq(e,t){let s;"object"==typeof e&&(t=e,e=void 0);let r=function(e,t="",s){let r=e;s=s||"undefined"!=typeof location&&location,null==e&&(e=s.protocol+"//"+s.host),"string"==typeof e&&("/"===e.charAt(0)&&(e="/"===e.charAt(1)?s.protocol+e:s.host+e),/^(https?|wss?):\/\//.test(e)||(eb("protocol-less url %s",e),e=void 0!==s?s.protocol+"//"+e:"https://"+e),eb("parse %s",e),r=ed(e)),!r.port&&(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";let n=-1!==r.host.indexOf(":")?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+n+":"+r.port+t,r.href=r.protocol+"://"+n+(s&&s.port===r.port?"":":"+r.port),r}(e,(t=t||{}).path||"/socket.io"),n=r.source,i=r.id,o=r.path,a=e$[i]&&o in e$[i].nsps;return t.forceNew||t["force new connection"]||!1===t.multiplex||a?(eU("ignoring socket cache for %s",n),s=new eB(n,t)):(e$[i]||(eU("new io instance for %s",n),e$[i]=new eB(n,t)),s=e$[i]),r.query&&!t.query&&(t.query=r.queryKey),s.socket(r.path,t)}Object.assign(eq,{Manager:eB,Socket:ej,io:eq,connect:eq});var eW=s(6420);class eV{static{this.instance=null}static getInstance(){return eV.instance||(eV.instance=new eV,console.log("\uD83D\uDD27 WebSocketManager: New singleton instance created")),eV.instance}constructor(){this.socket=null,this.isConnecting=!1,this.clientCount=0,this.listeners=new Map,this.clientCallbacks=new Map,this.stats={connected:!1,clients:0,reconnectAttempts:0,lastConnected:null,totalMessages:0,errors:0,connectionId:null},this.heartbeatInterval=null,this.cleanupInterval=null,this.reconnectTimeout=null,console.log("\uD83D\uDD27 WebSocketManager: Constructor called")}async connect(e={}){let t=this.generateClientId();if(console.log(`🔌 WebSocketManager: Client ${t} requesting connection`),this.clientCallbacks.set(t,e),this.socket&&this.socket.connected)return console.log(`✅ WebSocketManager: Reusing existing connection for client ${t}`),this.addClient(e,t),this.socket;if(this.isConnecting)return console.log(`⏳ WebSocketManager: Connection in progress, waiting for client ${t}`),new Promise((s,r)=>{let n=()=>{this.socket&&this.socket.connected?(console.log(`✅ WebSocketManager: Connection ready for waiting client ${t}`),this.addClient(e,t),s(this.socket)):this.isConnecting?setTimeout(n,100):(console.log(`❌ WebSocketManager: Connection failed for waiting client ${t}`),r(Error("Connection failed")))};n()});console.log(`🚀 WebSocketManager: Creating new connection for client ${t}`),this.isConnecting=!0;try{return await this.createConnection(e),this.addClient(e,t),this.socket}catch(e){throw this.isConnecting=!1,this.clientCallbacks.delete(t),console.error(`❌ WebSocketManager: Connection failed for client ${t}:`,e),e}}generateClientId(){return`client_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}async createConnection(e){return new Promise((e,t)=>{let s=eW.i3.BASE_URL;console.log(`🔌 WebSocketManager: Creating connection to ${s}`),this.socket&&(console.log("\uD83E\uDDF9 WebSocketManager: Cleaning up existing connection"),this.socket.removeAllListeners(),this.socket.disconnect()),this.socket=eq(s,{transports:["websocket","polling"],upgrade:!0,rememberUpgrade:!1,timeout:eW.ld.CONNECTION_TIMEOUT,forceNew:!1,reconnection:!0,reconnectionAttempts:eW.ld.MAX_RECONNECT_ATTEMPTS,reconnectionDelay:eW.ld.RECONNECT_INTERVAL,reconnectionDelayMax:4*eW.ld.RECONNECT_INTERVAL,randomizationFactor:.5,autoConnect:!0}),this.socket.on("connect",()=>{console.log("✅ WebSocketManager: Connected successfully"),this.isConnecting=!1,this.stats.connected=!0,this.stats.lastConnected=new Date,this.stats.reconnectAttempts=0,this.stats.connectionId=this.socket?.id||null,this.startHeartbeat(),this.notifyAllClients("onConnect"),e()}),this.socket.on("disconnect",e=>{console.log(`❌ WebSocketManager: Disconnected - ${e}`),this.stats.connected=!1,this.stats.connectionId=null,this.stopHeartbeat(),this.notifyAllClients("onDisconnect",e),"io client disconnect"!==e&&this.scheduleReconnection()}),this.socket.on("connect_error",e=>{console.error("\uD83D\uDD25 WebSocketManager: Connection error:",e.message),this.stats.errors++,this.isConnecting=!1,this.notifyAllClients("onError",e),t(e)}),this.socket.on("reconnect",e=>{console.log(`🔄 WebSocketManager: Reconnected after ${e} attempts`),this.stats.reconnectAttempts=e,this.stats.connected=!0,this.stats.lastConnected=new Date,this.stats.connectionId=this.socket?.id||null,this.notifyAllClients("onReconnect",e)}),this.socket.on("reconnect_attempt",e=>{console.log(`🔄 WebSocketManager: Reconnection attempt ${e}`),this.stats.reconnectAttempts=e}),this.socket.on("reconnect_failed",()=>{console.error("\uD83D\uDCA5 WebSocketManager: Reconnection failed"),this.stats.connected=!1,this.isConnecting=!1,this.stats.connectionId=null}),this.socket.on("marketData",e=>{this.stats.totalMessages++,this.notifyListeners("marketData",e)}),this.socket.on("marketDataBatch",e=>{this.stats.totalMessages+=e.length,this.notifyListeners("marketDataBatch",e)}),this.startCleanupInterval(),setTimeout(()=>{this.isConnecting&&(this.isConnecting=!1,t(Error("Connection timeout")))},eW.ld.CONNECTION_TIMEOUT)})}addClient(e,t){this.clientCount++,this.stats.clients=this.clientCount,e.onMarketData&&this.addListener("marketData",e.onMarketData),e.onMarketDataBatch&&this.addListener("marketDataBatch",e.onMarketDataBatch),console.log(`📊 WebSocketManager: Client ${t} added (Total: ${this.clientCount})`)}notifyAllClients(e,t){this.clientCallbacks.forEach((s,r)=>{try{switch(e){case"onConnect":s.onConnect?.();break;case"onDisconnect":s.onDisconnect?.(t);break;case"onError":s.onError?.(t);break;case"onReconnect":s.onReconnect?.(t)}}catch(e){console.error(`❌ WebSocketManager: Error notifying client ${r}:`,e)}})}scheduleReconnection(){this.reconnectTimeout&&clearTimeout(this.reconnectTimeout);let e=Math.min(eW.ld.RECONNECT_INTERVAL*Math.pow(2,this.stats.reconnectAttempts),3e4);console.log(`🔄 WebSocketManager: Scheduling reconnection in ${e}ms`),this.reconnectTimeout=setTimeout(()=>{!this.stats.connected&&this.clientCount>0&&(console.log("\uD83D\uDD04 WebSocketManager: Attempting auto-reconnection"),this.connect().catch(e=>{console.error("❌ WebSocketManager: Auto-reconnection failed:",e)}))},e)}removeClient(e){if(this.clientCount>0){this.clientCount--,this.stats.clients=this.clientCount;let t="unknown";for(let[s,r]of this.clientCallbacks.entries())if(r===e){this.clientCallbacks.delete(s),t=s;break}e.onMarketData&&this.removeListener("marketData",e.onMarketData),e.onMarketDataBatch&&this.removeListener("marketDataBatch",e.onMarketDataBatch),console.log(`📊 WebSocketManager: Client ${t} removed (Total: ${this.clientCount})`),0===this.clientCount&&(console.log("⏳ WebSocketManager: No clients remaining, scheduling disconnect"),setTimeout(()=>{0===this.clientCount&&(console.log("\uD83D\uDD0C WebSocketManager: Disconnecting due to no clients"),this.disconnect())},5e3))}}addListener(e,t){this.listeners.has(e)||this.listeners.set(e,new Set);let s=this.listeners.get(e);if(s.size>=eW.ld.MAX_LISTENERS_PER_EVENT)return void console.warn(`⚠️ Maximum listeners reached for event: ${e}`);s.add(t)}removeListener(e,t){let s=this.listeners.get(e);s&&(s.delete(t),0===s.size&&this.listeners.delete(e))}notifyListeners(e,t){let s=this.listeners.get(e);s&&s.forEach(s=>{try{s(t)}catch(t){console.error(`Error in ${e} listener:`,t)}})}startHeartbeat(){this.stopHeartbeat(),this.heartbeatInterval=setInterval(()=>{this.socket&&this.socket.connected&&this.socket.emit("ping")},eW.ld.HEARTBEAT_INTERVAL)}stopHeartbeat(){this.heartbeatInterval&&(clearInterval(this.heartbeatInterval),this.heartbeatInterval=null)}startCleanupInterval(){this.cleanupInterval=setInterval(()=>{this.cleanup()},eW.ld.CLEANUP_INTERVAL)}cleanup(){let e=[];this.listeners.forEach((t,s)=>{0===t.size&&e.push(s)}),e.forEach(e=>{this.listeners.delete(e)}),console.log("\uD83D\uDCCA WebSocket Stats:",this.getStats())}disconnect(){console.log("\uD83D\uDD0C WebSocketManager: Disconnecting..."),this.stopHeartbeat(),this.cleanupInterval&&(clearInterval(this.cleanupInterval),this.cleanupInterval=null),this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.socket&&(this.socket.removeAllListeners(),this.socket.disconnect(),this.socket=null),this.listeners.clear(),this.clientCallbacks.clear(),this.clientCount=0,this.stats.connected=!1,this.stats.clients=0,this.stats.connectionId=null,this.isConnecting=!1,console.log("✅ WebSocketManager: Disconnected and cleaned up")}getStats(){return{...this.stats}}isConnected(){return this.socket?.connected||!1}emit(e,t){this.socket&&this.socket.connected?this.socket.emit(e,t):console.warn(`Cannot emit ${e}: WebSocket not connected`)}}eV.getInstance();let eH=(e=>e?m(e):m)()(((e,t)=>(s,r,n)=>{let i,o={storage:g(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},a=!1,c=new Set,l=new Set,h=o.storage;if(!h)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),s(...e)},r,n);let d=()=>{let e=o.partialize({...r()});return h.setItem(o.name,{state:e,version:o.version})},u=n.setState;n.setState=(e,t)=>{u(e,t),d()};let p=e((...e)=>{s(...e),d()},r,n);n.getInitialState=()=>p;let f=()=>{var e,t;if(!h)return;a=!1,c.forEach(e=>{var t;return e(null!=(t=r())?t:p)});let n=(null==(t=o.onRehydrateStorage)?void 0:t.call(o,null!=(e=r())?e:p))||void 0;return y(h.getItem.bind(h))(o.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===o.version)return[!1,e.state];else{if(o.migrate){let t=o.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[n,a]=e;if(s(i=o.merge(a,null!=(t=r())?t:p),!0),n)return d()}).then(()=>{null==n||n(i,void 0),i=r(),a=!0,l.forEach(e=>e(i))}).catch(e=>{null==n||n(void 0,e)})};return n.persist={setOptions:e=>{o={...o,...e},e.storage&&(h=e.storage)},clearStorage:()=>{null==h||h.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>f(),hasHydrated:()=>a,onHydrate:e=>(c.add(e),()=>{c.delete(e)}),onFinishHydration:e=>(l.add(e),()=>{l.delete(e)})},o.skipHydration||f(),i||p})((e,t)=>({marketData:{},isConnected:!1,isLoading:!0,lastUpdate:null,connectionStatus:"disconnected",error:null,cacheLoaded:!1,wsManager:null,setMarketData:s=>{e({marketData:s.reduce((e,t)=>(e[t.securityId]=t,e),{}),lastUpdate:new Date,isLoading:!1}),t().saveToLocalStorage()},updateMarketData:t=>e(e=>({marketData:{...e.marketData,[t.securityId]:{...e.marketData[t.securityId],...t}}})),updateMarketDataBatch:t=>e(e=>{let s={...e.marketData};for(let e of t)s[e.securityId]={...s[e.securityId],...e};return{marketData:s}}),hydrateFromRedis:t=>e({marketData:t.reduce((e,t)=>(e[t.securityId]=t,e),{})}),setConnectionStatus:s=>{e({connectionStatus:s,isConnected:"connected"===s,error:"error"===s?t().error:null})},setError:t=>e({error:t}),setLoading:t=>e({isLoading:t}),setCacheLoaded:t=>e({cacheLoaded:t}),initializeWebSocket:()=>{let s=eV.getInstance();e({wsManager:s}),s.connect({onConnect:()=>{console.log("\uD83D\uDD0C MarketStore: WebSocket connected"),t().setConnectionStatus("connected")},onDisconnect:e=>{console.log("\uD83D\uDD0C MarketStore: WebSocket disconnected:",e),t().setConnectionStatus("disconnected")},onError:e=>{console.error("❌ MarketStore: WebSocket error:",e),t().setConnectionStatus("error"),t().setError(e.message||"WebSocket connection error")},onReconnect:e=>{console.log("\uD83D\uDD04 MarketStore: WebSocket reconnected after",e,"attempts"),t().setConnectionStatus("connected"),t().setError(null)},onMarketData:e=>{e&&"object"==typeof e&&t().updateMarketData(e)},onMarketDataBatch:e=>{Array.isArray(e)&&e.length>0&&t().updateMarketDataBatch(e)}})},connect:()=>{let{wsManager:e}=t();e&&!e.isConnected()&&(t().setConnectionStatus("connecting"),console.log("\uD83D\uDD0C MarketStore: Connection request - WebSocket manager will handle"))},disconnect:()=>{let{wsManager:e}=t();e&&(e.disconnect(),t().setConnectionStatus("disconnected"))},loadFromCache:async()=>{try{e({isLoading:!0}),console.log("\uD83D\uDCD6 MarketStore: Loading cached market data...");let s=t().loadFromLocalStorage(),r=await fetch("/api/cache/bulk");if(r.ok){let e=await r.json();e.success&&e.data&&Array.isArray(e.data)&&e.data.length>0?(t().setMarketData(e.data),console.log(`✅ MarketStore: Loaded ${e.data.length} items from Redis cache`)):s||console.log("\uD83D\uDCED MarketStore: No cached data found")}else s||console.log("\uD83D\uDCED MarketStore: Failed to load cached data");e({cacheLoaded:!0,isLoading:!1})}catch(t){console.error("❌ MarketStore: Failed to load cached data:",t),e({cacheLoaded:!0,isLoading:!1})}},refreshFromCache:async()=>{try{let e=await fetch("/api/cache/bulk");if(e.ok){let s=await e.json();s.success&&s.data&&Array.isArray(s.data)&&s.data.length>0&&(t().setMarketData(s.data),console.log(`✅ MarketStore: Refreshed ${s.data.length} items from cache`))}}catch(e){console.error("❌ MarketStore: Failed to refresh from cache:",e)}},clearCache:async()=>{try{(await fetch("/api/cache/clear",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({pattern:"market_data:*"})})).ok&&(e({marketData:{},lastUpdate:null}),localStorage.removeItem("marketData"),localStorage.removeItem("marketDataTimestamp"),console.log("\uD83E\uDDF9 MarketStore: Cache cleared"))}catch(e){console.error("❌ MarketStore: Failed to clear cache:",e)}},saveToLocalStorage:()=>{let{marketData:e}=t();if(Object.keys(e).length>0)try{localStorage.setItem("marketData",JSON.stringify(Object.values(e))),localStorage.setItem("marketDataTimestamp",new Date().toISOString())}catch(e){console.warn("⚠️ MarketStore: Failed to save to localStorage:",e)}},loadFromLocalStorage:()=>{try{let t=localStorage.getItem("marketData"),s=localStorage.getItem("marketDataTimestamp");if(t&&s){let r=JSON.parse(t),n=new Date(s),i=(new Date().getTime()-n.getTime())/6e4;if(i<10&&Array.isArray(r)&&r.length>0)return e({marketData:r.reduce((e,t)=>(e[t.securityId]=t,e),{}),lastUpdate:n,isLoading:!1}),console.log(`⚡ MarketStore: Loaded ${r.length} items from localStorage (${i.toFixed(1)}min old)`),!0}}catch(e){console.warn("⚠️ MarketStore: Failed to load from localStorage:",e),localStorage.removeItem("marketData"),localStorage.removeItem("marketDataTimestamp")}return!1},reset:()=>{e({marketData:{},isConnected:!1,isLoading:!0,lastUpdate:null,connectionStatus:"disconnected",error:null,cacheLoaded:!1}),localStorage.removeItem("marketData"),localStorage.removeItem("marketDataTimestamp")},getMarketDataBySymbol:e=>Object.values(t().marketData).find(t=>t.symbol===e),getMarketDataBySecurityId:e=>t().marketData[e]}),{name:"market-store",storage:g(()=>localStorage),partialize:e=>({marketData:e.marketData})})),eG=()=>{let e=eH();return(0,c.useEffect)(()=>{fetch("/api/cache/all-latest").then(e=>e.json()).then(({data:t})=>{Array.isArray(t)&&e.hydrateFromRedis(t)})},[e]),{marketData:Object.values(e.marketData),updateMarketData:e.updateMarketData,updateMarketDataBatch:e.updateMarketDataBatch,hydrateFromRedis:e.hydrateFromRedis}};function ez(){let{marketData:e}=eG(),t=new Map;return e.forEach(e=>{e.securityId&&t.set(e.securityId,e)}),(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("a",{href:"/",className:"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors",children:"← Main Dashboard"}),(0,a.jsx)("a",{href:"/subscribed",className:"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors",children:"\uD83D\uDCCA Subscribed Data"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[e.length," instruments"]}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[e.filter(e=>(e.ltp??0)>0).length," active"]})]})]})}),(0,a.jsx)(d,{marketData:t})]})}},2009:(e,t,s)=>{let r=s(3997),n=s(8354);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let r=0;r<s.length;r++)e.inspectOpts[s[r]]=t.inspectOpts[s[r]]},t.log=function(...e){return process.stderr.write(n.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:r,useColors:n}=this;if(n){let t=this.color,n="\x1b[3"+(t<8?t:"8;5;"+t),i=`  ${n};1m${r} \u001B[0m`;s[0]=i+s[0].split("\n").join("\n"+i),s.push(n+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+r+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:r.isatty(process.stderr.fd)},t.destroy=n.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(9228);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),r=process.env[t];return r=!!/^(yes|on|true|enabled)$/i.test(r)||!/^(no|off|false|disabled)$/i.test(r)&&("null"===r?null:Number(r)),e[s]=r,e},{}),e.exports=s(9680)(t);let{formatters:i}=e.exports;i.o=function(e){return this.inspectOpts.colors=this.useColors,n.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},i.O=function(e){return this.inspectOpts.colors=this.useColors,n.inspect(e,this.inspectOpts)}},2126:(e,t,s)=>{"use strict";let r,n=s(4075),i=s(1629),o=s(6044),{kStatusCode:a}=s(8169),c=Buffer[Symbol.species],l=Buffer.from([0,0,255,255]),h=Symbol("permessage-deflate"),d=Symbol("total-length"),u=Symbol("callback"),p=Symbol("buffers"),f=Symbol("error");class m{constructor(e,t,s){this._maxPayload=0|s,this._options=e||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,r||(r=new o(void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10))}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[u];this._deflate.close(),this._deflate=null,e&&e(Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let t=this._options,s=e.find(e=>(!1!==t.serverNoContextTakeover||!e.server_no_context_takeover)&&(!e.server_max_window_bits||!1!==t.serverMaxWindowBits&&("number"!=typeof t.serverMaxWindowBits||!(t.serverMaxWindowBits>e.server_max_window_bits)))&&("number"!=typeof t.clientMaxWindowBits||!!e.client_max_window_bits));if(!s)throw Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(s.server_no_context_takeover=!0),t.clientNoContextTakeover&&(s.client_no_context_takeover=!0),"number"==typeof t.serverMaxWindowBits&&(s.server_max_window_bits=t.serverMaxWindowBits),"number"==typeof t.clientMaxWindowBits?s.client_max_window_bits=t.clientMaxWindowBits:(!0===s.client_max_window_bits||!1===t.clientMaxWindowBits)&&delete s.client_max_window_bits,s}acceptAsClient(e){let t=e[0];if(!1===this._options.clientNoContextTakeover&&t.client_no_context_takeover)throw Error('Unexpected parameter "client_no_context_takeover"');if(t.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(t.client_max_window_bits=this._options.clientMaxWindowBits);return t}normalizeParams(e){return e.forEach(e=>{Object.keys(e).forEach(t=>{let s=e[t];if(s.length>1)throw Error(`Parameter "${t}" must have only a single value`);if(s=s[0],"client_max_window_bits"===t){if(!0!==s){let e=+s;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else if(!this._isServer)throw TypeError(`Invalid value for parameter "${t}": ${s}`)}else if("server_max_window_bits"===t){let e=+s;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else if("client_no_context_takeover"===t||"server_no_context_takeover"===t){if(!0!==s)throw TypeError(`Invalid value for parameter "${t}": ${s}`)}else throw Error(`Unknown parameter "${t}"`);e[t]=s})}),e}decompress(e,t,s){r.add(r=>{this._decompress(e,t,(e,t)=>{r(),s(e,t)})})}compress(e,t,s){r.add(r=>{this._compress(e,t,(e,t)=>{r(),s(e,t)})})}_decompress(e,t,s){let r=this._isServer?"client":"server";if(!this._inflate){let e=`${r}_max_window_bits`,t="number"!=typeof this.params[e]?n.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=n.createInflateRaw({...this._options.zlibInflateOptions,windowBits:t}),this._inflate[h]=this,this._inflate[d]=0,this._inflate[p]=[],this._inflate.on("error",_),this._inflate.on("data",y)}this._inflate[u]=s,this._inflate.write(e),t&&this._inflate.write(l),this._inflate.flush(()=>{let e=this._inflate[f];if(e){this._inflate.close(),this._inflate=null,s(e);return}let n=i.concat(this._inflate[p],this._inflate[d]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[d]=0,this._inflate[p]=[],t&&this.params[`${r}_no_context_takeover`]&&this._inflate.reset()),s(null,n)})}_compress(e,t,s){let r=this._isServer?"server":"client";if(!this._deflate){let e=`${r}_max_window_bits`,t="number"!=typeof this.params[e]?n.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=n.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:t}),this._deflate[d]=0,this._deflate[p]=[],this._deflate.on("data",g)}this._deflate[u]=s,this._deflate.write(e),this._deflate.flush(n.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let e=i.concat(this._deflate[p],this._deflate[d]);t&&(e=new c(e.buffer,e.byteOffset,e.length-4)),this._deflate[u]=null,this._deflate[d]=0,this._deflate[p]=[],t&&this.params[`${r}_no_context_takeover`]&&this._deflate.reset(),s(null,e)})}}function g(e){this[p].push(e),this[d]+=e.length}function y(e){if(this[d]+=e.length,this[h]._maxPayload<1||this[d]<=this[h]._maxPayload)return void this[p].push(e);this[f]=RangeError("Max payload size exceeded"),this[f].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[f][a]=1009,this.removeListener("data",y),this.reset()}function _(e){this[h]._inflate=null,e[a]=1007,this[u](e)}e.exports=m},2425:(e,t,s)=>{Promise.resolve().then(s.bind(s,1985))},2590:(e,t,s)=>{"use strict";let{Duplex:r}=s(7910);function n(e){e.emit("close")}function i(){!this.destroyed&&this._writableState.finished&&this.destroy()}function o(e){this.removeListener("error",o),this.destroy(),0===this.listenerCount("error")&&this.emit("error",e)}e.exports=function(e,t){let s=!0,a=new r({...t,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return e.on("message",function(t,s){let r=!s&&a._readableState.objectMode?t.toString():t;a.push(r)||e.pause()}),e.once("error",function(e){a.destroyed||(s=!1,a.destroy(e))}),e.once("close",function(){a.destroyed||a.push(null)}),a._destroy=function(t,r){if(e.readyState===e.CLOSED){r(t),process.nextTick(n,a);return}let i=!1;e.once("error",function(e){i=!0,r(e)}),e.once("close",function(){i||r(t),process.nextTick(n,a)}),s&&e.terminate()},a._final=function(t){if(e.readyState===e.CONNECTING)return void e.once("open",function(){a._final(t)});null!==e._socket&&(e._socket._writableState.finished?(t(),a._readableState.endEmitted&&a.destroy()):(e._socket.once("finish",function(){t()}),e.close()))},a._read=function(){e.isPaused&&e.resume()},a._write=function(t,s,r){if(e.readyState===e.CONNECTING)return void e.once("open",function(){a._write(t,s,r)});e.send(t,r)},a.on("end",i),a.on("error",o),a}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3095:(e,t,s)=>{e.exports=function(e){function t(e){let s,n,i,o=null;function a(...e){if(!a.enabled)return;let r=Number(new Date);a.diff=r-(s||r),a.prev=s,a.curr=r,s=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let n=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,r)=>{if("%%"===s)return"%";n++;let i=t.formatters[r];if("function"==typeof i){let t=e[n];s=i.call(a,t),e.splice(n,1),n--}return s}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(n!==t.namespaces&&(n=t.namespaces,i=t.enabled(e)),i),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,s){let r=t(this.namespace+(void 0===s?":":s)+e);return r.log=this.log,r}function n(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(n),...t.skips.map(n).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let s;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let r=("string"==typeof e?e:"").split(/[\s,]+/),n=r.length;for(s=0;s<n;s++)r[s]&&("-"===(e=r[s].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let s,r;if("*"===e[e.length-1])return!0;for(s=0,r=t.skips.length;s<r;s++)if(t.skips[s].test(e))return!1;for(s=0,r=t.names.length;s<r;s++)if(t.names[s].test(e))return!0;return!1},t.humanize=s(7802),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},3121:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let r=0,n=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(r++,"%c"===e&&(n=r))}),t.splice(n,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(9680)(t);let{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},3211:(e,t,s)=>{"use strict";let r=s(4735),n=s(1630),{Duplex:i}=s(7910),{createHash:o}=s(5511),a=s(1569),c=s(2126),l=s(7790),h=s(1759),{GUID:d,kWebSocket:u}=s(8169),p=/^[+/0-9A-Za-z]{22}==$/;class f extends r{constructor(e,t){if(super(),null==(e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:h,...e}).port&&!e.server&&!e.noServer||null!=e.port&&(e.server||e.noServer)||e.server&&e.noServer)throw TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(null!=e.port?(this._server=n.createServer((e,t)=>{let s=n.STATUS_CODES[426];t.writeHead(426,{"Content-Length":s.length,"Content-Type":"text/plain"}),t.end(s)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){let e=this.emit.bind(this,"connection");this._removeListeners=function(e,t){for(let s of Object.keys(t))e.on(s,t[s]);return function(){for(let s of Object.keys(t))e.removeListener(s,t[s])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(t,s,r)=>{this.handleUpgrade(t,s,r,e)}})}!0===e.perMessageDeflate&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=0}address(){if(this.options.noServer)throw Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(2===this._state){e&&this.once("close",()=>{e(Error("The server is not running"))}),process.nextTick(m,this);return}if(e&&this.once("close",e),1!==this._state)if(this._state=1,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients&&this.clients.size?this._shouldEmitClose=!0:process.nextTick(m,this);else{let e=this._server;this._removeListeners(),this._removeListeners=this._server=null,e.close(()=>{m(this)})}}shouldHandle(e){if(this.options.path){let t=e.url.indexOf("?");if((-1!==t?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,s,r){t.on("error",g);let n=e.headers["sec-websocket-key"],i=e.headers.upgrade,o=+e.headers["sec-websocket-version"];if("GET"!==e.method)return void _(this,e,t,405,"Invalid HTTP method");if(void 0===i||"websocket"!==i.toLowerCase())return void _(this,e,t,400,"Invalid Upgrade header");if(void 0===n||!p.test(n))return void _(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");if(8!==o&&13!==o)return void _(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header");if(!this.shouldHandle(e))return void y(t,400);let h=e.headers["sec-websocket-protocol"],d=new Set;if(void 0!==h)try{d=l.parse(h)}catch(s){_(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}let u=e.headers["sec-websocket-extensions"],f={};if(this.options.perMessageDeflate&&void 0!==u){let s=new c(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let e=a.parse(u);e[c.extensionName]&&(s.accept(e[c.extensionName]),f[c.extensionName]=s)}catch(s){_(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let i={origin:e.headers[`${8===o?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(2===this.options.verifyClient.length)return void this.options.verifyClient(i,(i,o,a,c)=>{if(!i)return y(t,o||401,a,c);this.completeUpgrade(f,n,d,e,t,s,r)});if(!this.options.verifyClient(i))return y(t,401)}this.completeUpgrade(f,n,d,e,t,s,r)}completeUpgrade(e,t,s,r,n,i,l){if(!n.readable||!n.writable)return n.destroy();if(n[u])throw Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return y(n,503);let h=o("sha1").update(t+d).digest("base64"),p=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${h}`],f=new this.options.WebSocket(null,void 0,this.options);if(s.size){let e=this.options.handleProtocols?this.options.handleProtocols(s,r):s.values().next().value;e&&(p.push(`Sec-WebSocket-Protocol: ${e}`),f._protocol=e)}if(e[c.extensionName]){let t=e[c.extensionName].params,s=a.format({[c.extensionName]:[t]});p.push(`Sec-WebSocket-Extensions: ${s}`),f._extensions=e}this.emit("headers",p,r),n.write(p.concat("\r\n").join("\r\n")),n.removeListener("error",g),f.setSocket(n,i,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(f),f.on("close",()=>{this.clients.delete(f),this._shouldEmitClose&&!this.clients.size&&process.nextTick(m,this)})),l(f,r)}}function m(e){e._state=2,e.emit("close")}function g(){this.destroy()}function y(e,t,s,r){s=s||n.STATUS_CODES[t],r={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(s),...r},e.once("finish",e.destroy),e.end(`HTTP/1.1 ${t} ${n.STATUS_CODES[t]}\r
`+Object.keys(r).map(e=>`${e}: ${r[e]}`).join("\r\n")+"\r\n\r\n"+s)}function _(e,t,s,r,n){if(e.listenerCount("wsClientError")){let r=Error(n);Error.captureStackTrace(r,_),e.emit("wsClientError",r,s,t)}else y(s,r,n)}e.exports=f},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3890:(e,t,s)=>{let r=s(3997),n=s(8354);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let r=0;r<s.length;r++)e.inspectOpts[s[r]]=t.inspectOpts[s[r]]},t.log=function(...e){return process.stderr.write(n.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:r,useColors:n}=this;if(n){let t=this.color,n="\x1b[3"+(t<8?t:"8;5;"+t),i=`  ${n};1m${r} \u001B[0m`;s[0]=i+s[0].split("\n").join("\n"+i),s.push(n+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+r+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:r.isatty(process.stderr.fd)},t.destroy=n.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(9228);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),r=process.env[t];return r=!!/^(yes|on|true|enabled)$/i.test(r)||!/^(no|off|false|disabled)$/i.test(r)&&("null"===r?null:Number(r)),e[s]=r,e},{}),e.exports=s(3095)(t);let{formatters:i}=e.exports;i.o=function(e){return this.inspectOpts.colors=this.useColors,n.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},i.O=function(e){return this.inspectOpts.colors=this.useColors,n.inspect(e,this.inspectOpts)}},3997:e=>{"use strict";e.exports=require("tty")},4039:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let r=0,n=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(r++,"%c"===e&&(n=r))}),t.splice(n,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(3095)(t);let{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4687:(e,t,s)=>{"use strict";let{Writable:r}=s(7910),n=s(2126),{BINARY_TYPES:i,EMPTY_BUFFER:o,kStatusCode:a,kWebSocket:c}=s(8169),{concat:l,toArrayBuffer:h,unmask:d}=s(1629),{isValidStatusCode:u,isValidUTF8:p}=s(7377),f=Buffer[Symbol.species];class m extends r{constructor(e={}){super(),this._allowSynchronousEvents=void 0===e.allowSynchronousEvents||e.allowSynchronousEvents,this._binaryType=e.binaryType||i[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=0|e.maxPayload,this._skipUTF8Validation=!!e.skipUTF8Validation,this[c]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(e,t,s){if(8===this._opcode&&0==this._state)return s();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(s)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let t=this._buffers[0];return this._buffers[0]=new f(t.buffer,t.byteOffset+e,t.length-e),new f(t.buffer,t.byteOffset,e)}let t=Buffer.allocUnsafe(e);do{let s=this._buffers[0],r=t.length-e;e>=s.length?t.set(this._buffers.shift(),r):(t.set(new Uint8Array(s.buffer,s.byteOffset,e),r),this._buffers[0]=new f(s.buffer,s.byteOffset+e,s.length-e)),e-=s.length}while(e>0);return t}startLoop(e){this._loop=!0;do switch(this._state){case 0:this.getInfo(e);break;case 1:this.getPayloadLength16(e);break;case 2:this.getPayloadLength64(e);break;case 3:this.getMask();break;case 4:this.getData(e);break;case 5:case 6:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let t=this.consume(2);if((48&t[0])!=0)return void e(this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3"));let s=(64&t[0])==64;if(s&&!this._extensions[n.extensionName])return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(this._fin=(128&t[0])==128,this._opcode=15&t[0],this._payloadLength=127&t[1],0===this._opcode){if(s)return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(!this._fragmented)return void e(this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE"));this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented)return void e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));this._compressed=s}else{if(!(this._opcode>7)||!(this._opcode<11))return void e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));if(!this._fin)return void e(this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN"));if(s)return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(this._payloadLength>125||8===this._opcode&&1===this._payloadLength)return void e(this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH"))}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=(128&t[1])==128,this._isServer){if(!this._masked)return void e(this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK"))}else if(this._masked)return void e(this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK"));126===this._payloadLength?this._state=1:127===this._payloadLength?this._state=2:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let t=this.consume(8),s=t.readUInt32BE(0);if(s>2097151)return void e(this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH"));this._payloadLength=0x100000000*s+t.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0))return void e(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._masked?this._state=3:this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(e){let t=o;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!=0&&d(t,this._mask)}if(this._opcode>7)return void this.controlMessage(t,e);if(this._compressed){this._state=5,this.decompress(t,e);return}t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage(e)}decompress(e,t){this._extensions[n.extensionName].decompress(e,this._fin,(e,s)=>{if(e)return t(e);if(s.length){if(this._messageLength+=s.length,this._messageLength>this._maxPayload&&this._maxPayload>0)return void t(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._fragments.push(s)}this.dataMessage(t),0===this._state&&this.startLoop(t)})}dataMessage(e){if(!this._fin){this._state=0;return}let t=this._messageLength,s=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let r;r="nodebuffer"===this._binaryType?l(s,t):"arraybuffer"===this._binaryType?h(l(s,t)):s,this._allowSynchronousEvents?(this.emit("message",r,!0),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",r,!0),this._state=0,this.startLoop(e)}))}else{let r=l(s,t);if(!this._skipUTF8Validation&&!p(r))return void e(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));5===this._state||this._allowSynchronousEvents?(this.emit("message",r,!1),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",r,!1),this._state=0,this.startLoop(e)}))}}controlMessage(e,t){if(8===this._opcode){if(0===e.length)this._loop=!1,this.emit("conclude",1005,o),this.end();else{let s=e.readUInt16BE(0);if(!u(s))return void t(this.createError(RangeError,`invalid status code ${s}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE"));let r=new f(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!p(r))return void t(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));this._loop=!1,this.emit("conclude",s,r),this.end()}this._state=0;return}this._allowSynchronousEvents?(this.emit(9===this._opcode?"ping":"pong",e),this._state=0):(this._state=6,setImmediate(()=>{this.emit(9===this._opcode?"ping":"pong",e),this._state=0,this.startLoop(t)}))}createError(e,t,s,r,n){this._loop=!1,this._errored=!0;let i=new e(s?`Invalid WebSocket frame: ${t}`:t);return Error.captureStackTrace(i,this.createError),i.code=n,i[a]=r,i}}e.exports=m},4735:e=>{"use strict";e.exports=require("events")},4780:(e,t,s)=>{"use strict";s.d(t,{ap:()=>r});let r={price:e=>!e||e<=0?"-":`₹${e.toFixed(2)}`,number:e=>null==e?"-":e.toLocaleString(),percentage:e=>{if(!e||0===e)return"-";let t=e>=0?"+":"";return`${t}${e.toFixed(2)}%`},change:(e,t)=>{if(!e||0===e)return"-";let s=e>0?"+":"",r=t?` (${s}${t.toFixed(2)}%)`:"";return`${s}${e.toFixed(2)}${r}`},volume:e=>null==e?"-":function(e){return e>=1e7?`${(e/1e7).toFixed(2)} Cr`:e>=1e5?`${(e/1e5).toFixed(2)} L`:e>=1e3?`${(e/1e3).toFixed(2)} K`:e.toString()}(e),time:e=>e?new Date(e).toLocaleTimeString():"-",date:e=>e?new Date(e).toLocaleDateString():"-",bidAsk:(e,t)=>{if(!e||e<=0)return"-";let s=t?` (${t})`:"";return`₹${e.toFixed(2)}${s}`}}},4882:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\option-chain\\page.tsx","default")},5133:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(8573):e.exports=s(7461)},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},5991:()=>{},6044:e=>{"use strict";let t=Symbol("kDone"),s=Symbol("kRun");class r{constructor(e){this[t]=()=>{this.pending--,this[s]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[s]()}[s](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[t])}}}e.exports=r},6416:(e,t,s)=>{"use strict";let{kForOnEventAttribute:r,kListener:n}=s(8169),i=Symbol("kCode"),o=Symbol("kData"),a=Symbol("kError"),c=Symbol("kMessage"),l=Symbol("kReason"),h=Symbol("kTarget"),d=Symbol("kType"),u=Symbol("kWasClean");class p{constructor(e){this[h]=null,this[d]=e}get target(){return this[h]}get type(){return this[d]}}Object.defineProperty(p.prototype,"target",{enumerable:!0}),Object.defineProperty(p.prototype,"type",{enumerable:!0});class f extends p{constructor(e,t={}){super(e),this[i]=void 0===t.code?0:t.code,this[l]=void 0===t.reason?"":t.reason,this[u]=void 0!==t.wasClean&&t.wasClean}get code(){return this[i]}get reason(){return this[l]}get wasClean(){return this[u]}}Object.defineProperty(f.prototype,"code",{enumerable:!0}),Object.defineProperty(f.prototype,"reason",{enumerable:!0}),Object.defineProperty(f.prototype,"wasClean",{enumerable:!0});class m extends p{constructor(e,t={}){super(e),this[a]=void 0===t.error?null:t.error,this[c]=void 0===t.message?"":t.message}get error(){return this[a]}get message(){return this[c]}}Object.defineProperty(m.prototype,"error",{enumerable:!0}),Object.defineProperty(m.prototype,"message",{enumerable:!0});class g extends p{constructor(e,t={}){super(e),this[o]=void 0===t.data?null:t.data}get data(){return this[o]}}function y(e,t,s){"object"==typeof e&&e.handleEvent?e.handleEvent.call(e,s):e.call(t,s)}Object.defineProperty(g.prototype,"data",{enumerable:!0}),e.exports={CloseEvent:f,ErrorEvent:m,Event:p,EventTarget:{addEventListener(e,t,s={}){let i;for(let i of this.listeners(e))if(!s[r]&&i[n]===t&&!i[r])return;if("message"===e)i=function(e,s){let r=new g("message",{data:s?e:e.toString()});r[h]=this,y(t,this,r)};else if("close"===e)i=function(e,s){let r=new f("close",{code:e,reason:s.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});r[h]=this,y(t,this,r)};else if("error"===e)i=function(e){let s=new m("error",{error:e,message:e.message});s[h]=this,y(t,this,s)};else{if("open"!==e)return;i=function(){let e=new p("open");e[h]=this,y(t,this,e)}}i[r]=!!s[r],i[n]=t,s.once?this.once(e,i):this.on(e,i)},removeEventListener(e,t){for(let s of this.listeners(e))if(s[n]===t&&!s[r]){this.removeListener(e,s);break}}},MessageEvent:g}},7101:(e,t,s)=>{var r=s(9021),n=s(9551),i=s(9646).spawn;function o(e){"use strict";e=e||{};var t,o,a=this,c=s(1630),l=s(5591),h={},d=!1,u={"User-Agent":"node-XMLHttpRequest",Accept:"*/*"},p=Object.assign({},u),f=["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","content-transfer-encoding","cookie","cookie2","date","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","via"],m=["TRACE","TRACK","CONNECT"],g=!1,y=!1,_=!1,b={};this.UNSENT=0,this.OPENED=1,this.HEADERS_RECEIVED=2,this.LOADING=3,this.DONE=4,this.readyState=this.UNSENT,this.onreadystatechange=null,this.responseText="",this.responseXML="",this.response=Buffer.alloc(0),this.status=null,this.statusText=null,this.open=function(e,t,s,r,n){if(this.abort(),y=!1,_=!1,!(e&&-1===m.indexOf(e)))throw Error("SecurityError: Request method not allowed");h={method:e,url:t.toString(),async:"boolean"!=typeof s||s,user:r||null,password:n||null},v(this.OPENED)},this.setDisableHeaderCheck=function(e){d=e},this.setRequestHeader=function(e,t){if(this.readyState!=this.OPENED)throw Error("INVALID_STATE_ERR: setRequestHeader can only be called when state is OPEN");if(!d&&(!e||-1!==f.indexOf(e.toLowerCase())))return console.warn('Refused to set unsafe header "'+e+'"'),!1;if(g)throw Error("INVALID_STATE_ERR: send flag is true");return p[e]=t,!0},this.getResponseHeader=function(e){return"string"==typeof e&&this.readyState>this.OPENED&&o.headers[e.toLowerCase()]&&!y?o.headers[e.toLowerCase()]:null},this.getAllResponseHeaders=function(){if(this.readyState<this.HEADERS_RECEIVED||y)return"";var e="";for(var t in o.headers)"set-cookie"!==t&&"set-cookie2"!==t&&(e+=t+": "+o.headers[t]+"\r\n");return e.substr(0,e.length-2)},this.getRequestHeader=function(e){return"string"==typeof e&&p[e]?p[e]:""},this.send=function(s){if(this.readyState!=this.OPENED)throw Error("INVALID_STATE_ERR: connection must be opened before send() is called");if(g)throw Error("INVALID_STATE_ERR: send has already been called");var d,u=!1,f=!1,m=n.parse(h.url);switch(m.protocol){case"https:":u=!0;case"http:":d=m.hostname;break;case"file:":f=!0;break;case void 0:case"":d="localhost";break;default:throw Error("Protocol not supported.")}if(f){if("GET"!==h.method)throw Error("XMLHttpRequest: Only GET method is supported");if(h.async)r.readFile(unescape(m.pathname),function(e,t){e?a.handleError(e,e.errno||-1):(a.status=200,a.responseText=t.toString("utf8"),a.response=t,v(a.DONE))});else try{this.response=r.readFileSync(unescape(m.pathname)),this.responseText=this.response.toString("utf8"),this.status=200,v(a.DONE)}catch(e){this.handleError(e,e.errno||-1)}return}var _=m.port||(u?443:80),b=m.pathname+(m.search?m.search:"");if(p.Host=d,u&&443===_||80===_||(p.Host+=":"+m.port),h.user){void 0===h.password&&(h.password="");var x=new Buffer(h.user+":"+h.password);p.Authorization="Basic "+x.toString("base64")}"GET"===h.method||"HEAD"===h.method?s=null:s?(p["Content-Length"]=Buffer.isBuffer(s)?s.length:Buffer.byteLength(s),Object.keys(p).some(function(e){return"content-type"===e.toLowerCase()})||(p["Content-Type"]="text/plain;charset=UTF-8")):"POST"===h.method&&(p["Content-Length"]=0);var C=e.agent||!1,k={host:d,port:_,path:b,method:h.method,headers:p,agent:C};if(u&&(k.pfx=e.pfx,k.key=e.key,k.passphrase=e.passphrase,k.cert=e.cert,k.ca=e.ca,k.ciphers=e.ciphers,k.rejectUnauthorized=!1!==e.rejectUnauthorized),y=!1,h.async){var w=u?l.request:c.request;g=!0,a.dispatchEvent("readystatechange");var S=function(s){if(302===(o=s).statusCode||303===o.statusCode||307===o.statusCode){h.url=o.headers.location;var r=n.parse(h.url);d=r.hostname;var i={hostname:r.hostname,port:r.port,path:r.path,method:303===o.statusCode?"GET":h.method,headers:p};u&&(i.pfx=e.pfx,i.key=e.key,i.passphrase=e.passphrase,i.cert=e.cert,i.ca=e.ca,i.ciphers=e.ciphers,i.rejectUnauthorized=!1!==e.rejectUnauthorized),(t=w(i,S).on("error",E)).end();return}v(a.HEADERS_RECEIVED),a.status=o.statusCode,o.on("data",function(e){if(e){var t=Buffer.from(e);a.response=Buffer.concat([a.response,t])}g&&v(a.LOADING)}),o.on("end",function(){g&&(g=!1,v(a.DONE),a.responseText=a.response.toString("utf8"))}),o.on("error",function(e){a.handleError(e)})},E=function(e){if(t.reusedSocket&&"ECONNRESET"===e.code)return w(k,S).on("error",E);a.handleError(e)};t=w(k,S).on("error",E),e.autoUnref&&t.on("socket",e=>{e.unref()}),s&&t.write(s),t.end(),a.dispatchEvent("loadstart")}else{var O=".node-xmlhttprequest-content-"+process.pid,T=".node-xmlhttprequest-sync-"+process.pid;r.writeFileSync(T,"","utf8");for(var N="var http = require('http'), https = require('https'), fs = require('fs');var doRequest = http"+(u?"s":"")+".request;var options = "+JSON.stringify(k)+";var responseText = '';var responseData = Buffer.alloc(0);var req = doRequest(options, function(response) {response.on('data', function(chunk) {  var data = Buffer.from(chunk);  responseText += data.toString('utf8');  responseData = Buffer.concat([responseData, data]);});response.on('end', function() {fs.writeFileSync('"+O+"', JSON.stringify({err: null, data: {statusCode: response.statusCode, headers: response.headers, text: responseText, data: responseData.toString('base64')}}), 'utf8');fs.unlinkSync('"+T+"');});response.on('error', function(error) {fs.writeFileSync('"+O+"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');fs.unlinkSync('"+T+"');});}).on('error', function(error) {fs.writeFileSync('"+O+"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');fs.unlinkSync('"+T+"');});"+(s?"req.write('"+JSON.stringify(s).slice(1,-1).replace(/'/g,"\\'")+"');":"")+"req.end();",F=i(process.argv[0],["-e",N]);r.existsSync(T););if(a.responseText=r.readFileSync(O,"utf8"),F.stdin.end(),r.unlinkSync(O),a.responseText.match(/^NODE-XMLHTTPREQUEST-ERROR:/)){var R=JSON.parse(a.responseText.replace(/^NODE-XMLHTTPREQUEST-ERROR:/,""));a.handleError(R,503)}else{a.status=a.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:([0-9]*),.*/,"$1");var L=JSON.parse(a.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:[0-9]*,(.*)/,"$1"));o={statusCode:a.status,headers:L.data.headers},a.responseText=L.data.text,a.response=Buffer.from(L.data.data,"base64"),v(a.DONE,!0)}}},this.handleError=function(e,t){this.status=t||0,this.statusText=e,this.responseText=e.stack,y=!0,v(this.DONE)},this.abort=function(){t&&(t.abort(),t=null),p=Object.assign({},u),this.responseText="",this.responseXML="",this.response=Buffer.alloc(0),y=_=!0,this.readyState!==this.UNSENT&&(this.readyState!==this.OPENED||g)&&this.readyState!==this.DONE&&(g=!1,v(this.DONE)),this.readyState=this.UNSENT},this.addEventListener=function(e,t){e in b||(b[e]=[]),b[e].push(t)},this.removeEventListener=function(e,t){e in b&&(b[e]=b[e].filter(function(e){return e!==t}))},this.dispatchEvent=function(e){if("function"==typeof a["on"+e]&&(this.readyState===this.DONE&&h.async?setTimeout(function(){a["on"+e]()},0):a["on"+e]()),e in b)for(let t=0,s=b[e].length;t<s;t++)this.readyState===this.DONE?setTimeout(function(){b[e][t].call(a)},0):b[e][t].call(a)};var v=function(e){if(a.readyState!==e&&(a.readyState!==a.UNSENT||!_)&&(a.readyState=e,(h.async||a.readyState<a.OPENED||a.readyState===a.DONE)&&a.dispatchEvent("readystatechange"),a.readyState===a.DONE)){let e;e=_?"abort":y?"error":"load",a.dispatchEvent(e),a.dispatchEvent("loadend")}}}e.exports=o,o.XMLHttpRequest=o},7377:(e,t,s)=>{"use strict";let{isUtf8:r}=s(9428);function n(e){let t=e.length,s=0;for(;s<t;)if((128&e[s])==0)s++;else if((224&e[s])==192){if(s+1===t||(192&e[s+1])!=128||(254&e[s])==192)return!1;s+=2}else if((240&e[s])==224){if(s+2>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||224===e[s]&&(224&e[s+1])==128||237===e[s]&&(224&e[s+1])==160)return!1;s+=3}else{if((248&e[s])!=240||s+3>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||(192&e[s+3])!=128||240===e[s]&&(240&e[s+1])==128||244===e[s]&&e[s+1]>143||e[s]>244)return!1;s+=4}return!0}if(e.exports={isValidStatusCode:function(e){return e>=1e3&&e<=1014&&1004!==e&&1005!==e&&1006!==e||e>=3e3&&e<=4999},isValidUTF8:n,tokenChars:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0]},r)e.exports.isValidUTF8=function(e){return e.length<24?n(e):r(e)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let t=s(702);e.exports.isValidUTF8=function(e){return e.length<32?n(e):t(e)}}catch(e){}},7461:(e,t,s)=>{let r=s(3997),n=s(8354);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let r=0;r<s.length;r++)e.inspectOpts[s[r]]=t.inspectOpts[s[r]]},t.log=function(...e){return process.stderr.write(n.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:r,useColors:n}=this;if(n){let t=this.color,n="\x1b[3"+(t<8?t:"8;5;"+t),i=`  ${n};1m${r} \u001B[0m`;s[0]=i+s[0].split("\n").join("\n"+i),s.push(n+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+r+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:r.isatty(process.stderr.fd)},t.destroy=n.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(9228);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),r=process.env[t];return r=!!/^(yes|on|true|enabled)$/i.test(r)||!/^(no|off|false|disabled)$/i.test(r)&&("null"===r?null:Number(r)),e[s]=r,e},{}),e.exports=s(9940)(t);let{formatters:i}=e.exports;i.o=function(e){return this.inspectOpts.colors=this.useColors,n.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},i.O=function(e){return this.inspectOpts.colors=this.useColors,n.inspect(e,this.inspectOpts)}},7790:(e,t,s)=>{"use strict";let{tokenChars:r}=s(7377);e.exports={parse:function(e){let t=new Set,s=-1,n=-1,i=0;for(;i<e.length;i++){let o=e.charCodeAt(i);if(-1===n&&1===r[o])-1===s&&(s=i);else if(0!==i&&(32===o||9===o))-1===n&&-1!==s&&(n=i);else if(44===o){if(-1===s)throw SyntaxError(`Unexpected character at index ${i}`);-1===n&&(n=i);let r=e.slice(s,n);if(t.has(r))throw SyntaxError(`The "${r}" subprotocol is duplicated`);t.add(r),s=n=-1}else throw SyntaxError(`Unexpected character at index ${i}`)}if(-1===s||-1!==n)throw SyntaxError("Unexpected end of input");let o=e.slice(s,i);if(t.has(o))throw SyntaxError(`The "${o}" subprotocol is duplicated`);return t.add(o),t}}},7802:e=>{function t(e,t,s,r){return Math.round(e/s)+" "+r+(t>=1.5*s?"s":"")}e.exports=function(e,s){s=s||{};var r,n,i,o,a=typeof e;if("string"===a&&e.length>0){var c=e;if(!((c=String(c)).length>100)){var l=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(c);if(l){var h=parseFloat(l[1]);switch((l[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*h;case"weeks":case"week":case"w":return 6048e5*h;case"days":case"day":case"d":return 864e5*h;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*h;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*h;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*h;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return h;default:break}}}return}if("number"===a&&isFinite(e)){return s.long?(n=Math.abs(r=e))>=864e5?t(r,n,864e5,"day"):n>=36e5?t(r,n,36e5,"hour"):n>=6e4?t(r,n,6e4,"minute"):n>=1e3?t(r,n,1e3,"second"):r+" ms":(o=Math.abs(i=e))>=864e5?Math.round(i/864e5)+"d":o>=36e5?Math.round(i/36e5)+"h":o>=6e4?Math.round(i/6e4)+"m":o>=1e3?Math.round(i/1e3)+"s":i+"ms"}throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},7905:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(3121):e.exports=s(2009)},7910:e=>{"use strict";e.exports=require("stream")},8099:(e,t,s)=>{"use strict";let r,{Duplex:n}=s(7910),{randomFillSync:i}=s(5511),o=s(2126),{EMPTY_BUFFER:a}=s(8169),{isValidStatusCode:c}=s(7377),{mask:l,toBuffer:h}=s(1629),d=Symbol("kByteLength"),u=Buffer.alloc(4),p=8192;class f{constructor(e,t,s){this._extensions=t||{},s&&(this._generateMask=s,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(e,t){let s,n,o=!1,a=2,c=!1;t.mask&&(s=t.maskBuffer||u,t.generateMask?t.generateMask(s):(8192===p&&(void 0===r&&(r=Buffer.alloc(8192)),i(r,0,8192),p=0),s[0]=r[p++],s[1]=r[p++],s[2]=r[p++],s[3]=r[p++]),c=(s[0]|s[1]|s[2]|s[3])==0,a=6),"string"==typeof e?n=(!t.mask||c)&&void 0!==t[d]?t[d]:(e=Buffer.from(e)).length:(n=e.length,o=t.mask&&t.readOnly&&!c);let h=n;n>=65536?(a+=8,h=127):n>125&&(a+=2,h=126);let f=Buffer.allocUnsafe(o?n+a:a);return(f[0]=t.fin?128|t.opcode:t.opcode,t.rsv1&&(f[0]|=64),f[1]=h,126===h?f.writeUInt16BE(n,2):127===h&&(f[2]=f[3]=0,f.writeUIntBE(n,4,6)),t.mask)?(f[1]|=128,f[a-4]=s[0],f[a-3]=s[1],f[a-2]=s[2],f[a-1]=s[3],c)?[f,e]:o?(l(e,s,f,a,n),[f]):(l(e,s,e,0,n),[f,e]):[f,e]}close(e,t,s,r){let n;if(void 0===e)n=a;else if("number"==typeof e&&c(e))if(void 0!==t&&t.length){let s=Buffer.byteLength(t);if(s>123)throw RangeError("The message must not be greater than 123 bytes");(n=Buffer.allocUnsafe(2+s)).writeUInt16BE(e,0),"string"==typeof t?n.write(t,2):n.set(t,2)}else(n=Buffer.allocUnsafe(2)).writeUInt16BE(e,0);else throw TypeError("First argument must be a valid error code number");let i={[d]:n.length,fin:!0,generateMask:this._generateMask,mask:s,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._deflating?this.enqueue([this.dispatch,n,!1,i,r]):this.sendFrame(f.frame(n,i),r)}ping(e,t,s){let r,n;if("string"==typeof e?(r=Buffer.byteLength(e),n=!1):(r=(e=h(e)).length,n=h.readOnly),r>125)throw RangeError("The data size must not be greater than 125 bytes");let i={[d]:r,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:n,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,i,s]):this.sendFrame(f.frame(e,i),s)}pong(e,t,s){let r,n;if("string"==typeof e?(r=Buffer.byteLength(e),n=!1):(r=(e=h(e)).length,n=h.readOnly),r>125)throw RangeError("The data size must not be greater than 125 bytes");let i={[d]:r,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:n,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,i,s]):this.sendFrame(f.frame(e,i),s)}send(e,t,s){let r,n,i=this._extensions[o.extensionName],a=t.binary?2:1,c=t.compress;if("string"==typeof e?(r=Buffer.byteLength(e),n=!1):(r=(e=h(e)).length,n=h.readOnly),this._firstFragment?(this._firstFragment=!1,c&&i&&i.params[i._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(c=r>=i._threshold),this._compress=c):(c=!1,a=0),t.fin&&(this._firstFragment=!0),i){let i={[d]:r,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:n,rsv1:c};this._deflating?this.enqueue([this.dispatch,e,this._compress,i,s]):this.dispatch(e,this._compress,i,s)}else this.sendFrame(f.frame(e,{[d]:r,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:n,rsv1:!1}),s)}dispatch(e,t,s,r){if(!t)return void this.sendFrame(f.frame(e,s),r);let n=this._extensions[o.extensionName];this._bufferedBytes+=s[d],this._deflating=!0,n.compress(e,s.fin,(e,t)=>{if(this._socket.destroyed){let e=Error("The socket was closed while data was being compressed");"function"==typeof r&&r(e);for(let t=0;t<this._queue.length;t++){let s=this._queue[t],r=s[s.length-1];"function"==typeof r&&r(e)}return}this._bufferedBytes-=s[d],this._deflating=!1,s.readOnly=!1,this.sendFrame(f.frame(t,s),r),this.dequeue()})}dequeue(){for(;!this._deflating&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][d],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][d],this._queue.push(e)}sendFrame(e,t){2===e.length?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}}e.exports=f},8169:e=>{"use strict";e.exports={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}},8354:e=>{"use strict";e.exports=require("util")},8377:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>h,routeModule:()=>u,tree:()=>l});var r=s(5239),n=s(8088),i=s(8170),o=s.n(i),a=s(893),c={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>a[e]);s.d(t,c);let l={children:["",{children:["option-chain",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4882)),"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\option-chain\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,h=["D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\option-chain\\page.tsx"],d={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/option-chain/page",pathname:"/option-chain",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},8573:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let r=0,n=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(r++,"%c"===e&&(n=r))}),t.splice(n,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(9940)(t);let{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9207:e=>{"use strict";e.exports=(e,t=process.argv)=>{let s=e.startsWith("-")?"":1===e.length?"-":"--",r=t.indexOf(s+e),n=t.indexOf("--");return -1!==r&&(-1===n||r<n)}},9228:(e,t,s)=>{"use strict";let r,n=s(1820),i=s(3997),o=s(9207),{env:a}=process;function c(e,t={}){var s;return 0!==(s=function(e,{streamIsTTY:t,sniffFlags:s=!0}={}){let i=function(){if("FORCE_COLOR"in a)return"true"===a.FORCE_COLOR?1:"false"===a.FORCE_COLOR?0:0===a.FORCE_COLOR.length?1:Math.min(Number.parseInt(a.FORCE_COLOR,10),3)}();void 0!==i&&(r=i);let c=s?r:i;if(0===c)return 0;if(s){if(o("color=16m")||o("color=full")||o("color=truecolor"))return 3;if(o("color=256"))return 2}if(e&&!t&&void 0===c)return 0;let l=c||0;if("dumb"===a.TERM)return l;if("win32"===process.platform){let e=n.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in a)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE","DRONE"].some(e=>e in a)||"codeship"===a.CI_NAME?1:l;if("TEAMCITY_VERSION"in a)return+!!/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(a.TEAMCITY_VERSION);if("truecolor"===a.COLORTERM)return 3;if("TERM_PROGRAM"in a){let e=Number.parseInt((a.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(a.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(a.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(a.TERM)||"COLORTERM"in a?1:l}(e,{streamIsTTY:e&&e.isTTY,...t}))&&{level:s,hasBasic:!0,has256:s>=2,has16m:s>=3}}o("no-color")||o("no-colors")||o("color=false")||o("color=never")?r=0:(o("color")||o("colors")||o("color=true")||o("color=always"))&&(r=1),e.exports={supportsColor:c,stdout:c({isTTY:i.isatty(1)}),stderr:c({isTTY:i.isatty(2)})}},9273:(e,t,s)=>{Promise.resolve().then(s.bind(s,4882))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9616:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(4039):e.exports=s(3890)},9646:e=>{"use strict";e.exports=require("child_process")},9680:(e,t,s)=>{e.exports=function(e){function t(e){let s,n,i,o=null;function a(...e){if(!a.enabled)return;let r=Number(new Date);a.diff=r-(s||r),a.prev=s,a.curr=r,s=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let n=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,r)=>{if("%%"===s)return"%";n++;let i=t.formatters[r];if("function"==typeof i){let t=e[n];s=i.call(a,t),e.splice(n,1),n--}return s}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(n!==t.namespaces&&(n=t.namespaces,i=t.enabled(e)),i),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,s){let r=t(this.namespace+(void 0===s?":":s)+e);return r.log=this.log,r}function n(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(n),...t.skips.map(n).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let s;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let r=("string"==typeof e?e:"").split(/[\s,]+/),n=r.length;for(s=0;s<n;s++)r[s]&&("-"===(e=r[s].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let s,r;if("*"===e[e.length-1])return!0;for(s=0,r=t.skips.length;s<r;s++)if(t.skips[s].test(e))return!1;for(s=0,r=t.names.length;s<r;s++)if(t.names[s].test(e))return!0;return!1},t.humanize=s(7802),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},9940:(e,t,s)=>{e.exports=function(e){function t(e){let s,n,i,o=null;function a(...e){if(!a.enabled)return;let r=Number(new Date);a.diff=r-(s||r),a.prev=s,a.curr=r,s=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let n=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,r)=>{if("%%"===s)return"%";n++;let i=t.formatters[r];if("function"==typeof i){let t=e[n];s=i.call(a,t),e.splice(n,1),n--}return s}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(n!==t.namespaces&&(n=t.namespaces,i=t.enabled(e)),i),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,s){let r=t(this.namespace+(void 0===s?":":s)+e);return r.log=this.log,r}function n(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(n),...t.skips.map(n).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let s;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let r=("string"==typeof e?e:"").split(/[\s,]+/),n=r.length;for(s=0;s<n;s++)r[s]&&("-"===(e=r[s].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let s,r;if("*"===e[e.length-1])return!0;for(s=0,r=t.skips.length;s<r;s++)if(t.skips[s].test(e))return!1;for(s=0,r=t.names.length;s<r;s++)if(t.names[s].test(e))return!0;return!1},t.humanize=s(7802),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,151,590],()=>s(8377));module.exports=r})();