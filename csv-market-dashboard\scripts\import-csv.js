#!/usr/bin/env node

const { PrismaClient } = require('../generated/prisma');
const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');

const prisma = new PrismaClient();

async function parseCSVFile(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    const stream = fs.createReadStream(filePath);
    let rowCount = 0;

    console.log('📖 Starting to read CSV file...');

    stream
      .pipe(csv())
      .on('data', (row) => {
        rowCount++;
        if (rowCount <= 5) {
          console.log(`📝 Sample row ${rowCount}:`, Object.keys(row).slice(0, 10));
        }
        try {
          // Parse the CSV row using actual column names from the CSV
          const instrument = {
            securityId: row.SECURITY_ID || '',
            symbol: row.UNDERLYING_SYMBOL || '',
            exchange: row.EXCH_ID || '',
            exchangeCode: 0, // Will be set based on exchange
            segment: row.SEGMENT || '',
            instrumentType: row.INSTRUMENT_TYPE || '',
          };

          // Set exchange code based on exchange
          if (instrument.exchange === 'NSE') instrument.exchangeCode = 1;
          else if (instrument.exchange === 'BSE') instrument.exchangeCode = 2;
          else if (instrument.exchange === 'MCX') instrument.exchangeCode = 3;

          // Parse optional fields
          const strikePrice = row.STRIKE_PRICE || '';
          if (strikePrice && strikePrice !== '' && strikePrice !== '-0.01000' && !isNaN(parseFloat(strikePrice))) {
            instrument.strikePrice = parseFloat(strikePrice);
          }

          const expiryDate = row.SM_EXPIRY_DATE || '';
          if (expiryDate && expiryDate !== '' && expiryDate !== '1979-12-31') {
            instrument.expiryDate = expiryDate;
          }

          const optionType = row.OPTION_TYPE || '';
          if (optionType && optionType !== '' && optionType !== 'XX') {
            instrument.optionType = optionType;
          }

          const lotSize = row.LOT_SIZE || '';
          if (lotSize && lotSize !== '' && !isNaN(parseFloat(lotSize))) {
            instrument.lotSize = parseInt(parseFloat(lotSize));
          }

          const tickSize = row.TICK_SIZE || '';
          if (tickSize && tickSize !== '' && !isNaN(parseFloat(tickSize))) {
            instrument.tickSize = parseFloat(tickSize);
          }

          // Only add valid instruments with proper data
          if (instrument.securityId && instrument.symbol && instrument.exchange && instrument.instrumentType) {
            results.push(instrument);
          }
        } catch (error) {
          console.warn('⚠️ Skipping invalid row:', error.message);
        }
      })
      .on('end', () => {
        console.log(`✅ Parsed ${results.length} instruments from CSV`);
        resolve(results);
      })
      .on('error', (error) => {
        console.error('❌ Error reading CSV:', error);
        reject(error);
      });
  });
}

async function importInstruments(instruments) {
  console.log(`🚀 Starting import of ${instruments.length} instruments...`);
  
  const batchSize = 500; // Smaller batch size for SQLite
  let imported = 0;
  let skipped = 0;

  for (let i = 0; i < instruments.length; i += batchSize) {
    const batch = instruments.slice(i, i + batchSize);
    
    try {
      // Use createMany for better performance
      await prisma.instrument.createMany({
        data: batch,
        skipDuplicates: true,
      });
      
      imported += batch.length;
      
      console.log(`📊 Imported batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(instruments.length / batchSize)} (${imported}/${instruments.length})`);
    } catch (error) {
      console.error(`❌ Error importing batch ${Math.floor(i / batchSize) + 1}:`, error.message);
      skipped += batch.length;
    }
  }

  console.log(`✅ Import completed: ${imported} imported, ${skipped} skipped`);
}

async function main() {
  try {
    console.log('🔄 Starting CSV import process...');
    
    // Check if CSV file exists
    const csvPath = path.join(process.cwd(), 'instruments.csv');
    if (!fs.existsSync(csvPath)) {
      throw new Error(`CSV file not found: ${csvPath}`);
    }

    console.log(`📁 Reading CSV file: ${csvPath}`);
    
    // Parse CSV file
    const instruments = await parseCSVFile(csvPath);
    
    if (instruments.length === 0) {
      throw new Error('No valid instruments found in CSV file');
    }

    // Clear existing data (optional)
    console.log('🗑️ Clearing existing instruments...');
    await prisma.instrument.deleteMany();
    
    // Import instruments
    await importInstruments(instruments);
    
    // Show statistics
    const totalCount = await prisma.instrument.count();
    const niftyOptions = await prisma.instrument.count({
      where: {
        symbol: { contains: 'NIFTY' },
        instrumentType: 'OPTIDX'
      }
    });
    
    console.log('\n📊 Import Statistics:');
    console.log(`   Total instruments: ${totalCount}`);
    console.log(`   NIFTY options: ${niftyOptions}`);
    
    console.log('\n🎉 CSV import completed successfully!');
    
  } catch (error) {
    console.error('❌ Import failed:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the import
if (require.main === module) {
  main();
}

module.exports = { importCSV: main };
