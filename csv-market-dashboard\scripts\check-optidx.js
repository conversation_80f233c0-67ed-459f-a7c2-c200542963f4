require('dotenv').config({ path: '.env.database' });
const { Client } = require('pg');

async function checkOptidx() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: { rejectUnauthorized: false }
  });
  
  try {
    await client.connect();
    console.log('✅ Connected to PostgreSQL');
    
    // Check OPTIDX instruments
    console.log('\n🔍 Checking OPTIDX instruments...');
    
    const optidxSample = await client.query(`
      SELECT "SECURITY_ID", "UNDERLYING_SYMBOL", "SYMBOL_NAME", "STRIKE_PRICE", "OPTION_TYPE", "SM_EXPIRY_DATE", "LOT_SIZE"
      FROM "Instruments" 
      WHERE "INSTRUMENT_TYPE" = 'OPTIDX'
      ORDER BY "UNDERLYING_SYMBOL", "SM_EXPIRY_DATE", "STRIKE_PRICE"
      LIMIT 20
    `);
    
    console.log('\n📋 Sample OPTIDX instruments:');
    optidxSample.rows.forEach(opt => {
      console.log(`   ${opt.UNDERLYING_SYMBOL} ${opt.SYMBOL_NAME} ${opt.STRIKE_PRICE} ${opt.OPTION_TYPE} (${opt.SM_EXPIRY_DATE}) - ID: ${opt.SECURITY_ID}`);
    });
    
    // Check unique underlying symbols in OPTIDX
    const underlyingSymbols = await client.query(`
      SELECT "UNDERLYING_SYMBOL", COUNT(*) as count 
      FROM "Instruments" 
      WHERE "INSTRUMENT_TYPE" = 'OPTIDX'
      GROUP BY "UNDERLYING_SYMBOL" 
      ORDER BY count DESC
    `);
    
    console.log('\n📊 OPTIDX underlying symbols:');
    underlyingSymbols.rows.forEach(symbol => {
      console.log(`   ${symbol.UNDERLYING_SYMBOL}: ${symbol.count} options`);
    });
    
    // Check for NIFTY in OPTIDX specifically
    const niftyOptidx = await client.query(`
      SELECT COUNT(*) as count 
      FROM "Instruments" 
      WHERE "INSTRUMENT_TYPE" = 'OPTIDX' 
      AND ("UNDERLYING_SYMBOL" LIKE '%NIFTY%' OR "SYMBOL_NAME" LIKE '%NIFTY%')
    `);
    console.log(`\n🎯 NIFTY-related OPTIDX: ${niftyOptidx.rows[0].count}`);
    
    // Get sample NIFTY OPTIDX
    const niftyOptidxSample = await client.query(`
      SELECT "SECURITY_ID", "UNDERLYING_SYMBOL", "SYMBOL_NAME", "STRIKE_PRICE", "OPTION_TYPE", "SM_EXPIRY_DATE"
      FROM "Instruments" 
      WHERE "INSTRUMENT_TYPE" = 'OPTIDX' 
      AND ("UNDERLYING_SYMBOL" LIKE '%NIFTY%' OR "SYMBOL_NAME" LIKE '%NIFTY%')
      ORDER BY "SM_EXPIRY_DATE", "STRIKE_PRICE"
      LIMIT 10
    `);
    
    console.log('\n📋 Sample NIFTY OPTIDX:');
    niftyOptidxSample.rows.forEach(opt => {
      console.log(`   ${opt.UNDERLYING_SYMBOL} ${opt.SYMBOL_NAME} ${opt.STRIKE_PRICE} ${opt.OPTION_TYPE} (${opt.SM_EXPIRY_DATE}) - ID: ${opt.SECURITY_ID}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await client.end();
  }
}

checkOptidx();
