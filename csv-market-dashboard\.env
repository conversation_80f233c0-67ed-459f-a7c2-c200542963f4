# CSV Market Dashboard Environment Variables

# Server Configuration
PORT=8081
NODE_ENV=development

# Dhan API Configuration (Optional - will use mock data if not provided)
ACCESS_TOKEN=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUyMDQwNzEwLCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwMDIzMjM2OSJ9.9c5rfryVkvqxsOXKq8QRO7XxuR2-pENTmBq_mNN9l22A0N-CxOXxVZP8MT2ZtIUwN6AGiv9GeqDKfKlZpBeBZg
CLIENT_ID=1100232369
# WebSocket Configuration
SUBSCRIPTION_TYPE=quote
MAX_INSTRUMENTS=25000
PREFERRED_EXCHANGE=NSE

# CSV File Configuration
CSV_FILE_PATH=./instruments.csv
CSV_CACHE_TTL=3600000

# Security
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_DIR=uploads

# Logging
LOG_LEVEL=info

# Performance
CACHE_TTL=300000
BATCH_SIZE=1000
UPDATE_INTERVAL=1000

# Next.js Configuration
NEXT_PUBLIC_SERVER_URL=http://localhost:8081
DATABASE_URL="***************************************************************/postgres"
POSTGRES_DATABASE_URL="***************************************************************/postgres"

REDIS_URL=redis://default:<EMAIL>:13842

