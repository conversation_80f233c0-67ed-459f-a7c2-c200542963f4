"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/option-chain/page",{

/***/ "(app-pages-browser)/./src/hooks/useMarketData.ts":
/*!************************************!*\
  !*** ./src/hooks/useMarketData.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMarketData: function() { return /* binding */ useMarketData; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_marketStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/marketStore */ \"(app-pages-browser)/./src/store/marketStore.ts\");\n/**\r\n * Custom hook for Market Data Management\r\n * Provides a clean interface to the Zustand market store\r\n */ \n\nconst useMarketData = ()=>{\n    const store = (0,_store_marketStore__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    // Hydrate from Redis on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        fetch(\"/api/cache/all-latest\").then((res)=>res.json()).then((param)=>{\n            let { data } = param;\n            if (Array.isArray(data)) store.hydrateFromRedis(data);\n        });\n    }, [\n        store\n    ]);\n    // WebSocket tick handling (example, adapt as needed)\n    // ws.on('tick', tick => store.updateMarketData(tick));\n    // ws.on('batch', ticks => store.updateMarketDataBatch(ticks));\n    return {\n        marketData: Object.values(store.marketData),\n        updateMarketData: store.updateMarketData,\n        updateMarketDataBatch: store.updateMarketDataBatch,\n        hydrateFromRedis: store.hydrateFromRedis\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useMarketData);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useMarketData.ts\n"));

/***/ })

});