"use strict";
/**
 * NIFTY Spot Service - Integration with dedicated spot calculator
 * Provides live NIFTY spot price data for the option chain
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.niftySpotService = exports.NiftySpotService = void 0;
const events_1 = require("events");
/**
 * NIFTY Spot Service
 * Manages the dedicated NIFTY spot calculator and provides data to the frontend
 */
class NiftySpotService extends events_1.EventEmitter {
    calculator = null;
    isInitialized = false;
    currentData = null;
    connectionStatus = null;
    constructor() {
        super();
        this.initializeCalculator();
    }
    /**
     * Initialize the NIFTY spot calculator
     */
    async initializeCalculator() {
        try {
            console.log('🚀 Initializing NIFTY Spot Calculator...');
            // Dynamically import the calculator (Node.js module)
            const { NiftySpotCalculator } = require('../lib/nifty-spot-calculator.js');
            this.calculator = new NiftySpotCalculator();
            // Set up data callback
            this.calculator.onData((data) => {
                this.currentData = data;
                this.emit('data', data);
                console.log(`📊 [NiftySpotService] Data Update: LTP=₹${data.ltp.toFixed(2)}, Change=${data.formatted.points}`);
            });
            // Set up status callback
            this.calculator.onStatus((connected, error) => {
                this.connectionStatus = this.calculator.getStatus();
                this.emit('status', { connected, error, status: this.connectionStatus });
                console.log(`📡 [NiftySpotService] Status: ${connected ? '🟢 Connected' : '🔴 Disconnected'}${error ? ` - ${error}` : ''}`);
            });
            this.isInitialized = true;
            console.log('✅ NIFTY Spot Calculator initialized successfully');
        }
        catch (error) {
            console.error('❌ Failed to initialize NIFTY Spot Calculator:', error);
            this.emit('error', error);
        }
    }
    /**
     * Connect to the NIFTY spot data feed
     */
    async connect() {
        if (!this.isInitialized || !this.calculator) {
            throw new Error('NIFTY Spot Calculator not initialized');
        }
        try {
            console.log('🔌 Connecting to NIFTY spot data feed...');
            await this.calculator.connect();
            console.log('✅ Connected to NIFTY spot data feed');
        }
        catch (error) {
            console.error('❌ Failed to connect to NIFTY spot data feed:', error);
            throw error;
        }
    }
    /**
     * Disconnect from the NIFTY spot data feed
     */
    disconnect() {
        if (this.calculator) {
            console.log('🔌 Disconnecting from NIFTY spot data feed...');
            this.calculator.disconnect();
            this.currentData = null;
            this.connectionStatus = null;
        }
    }
    /**
     * Get current NIFTY spot data
     */
    getCurrentData() {
        return this.currentData;
    }
    /**
     * Get current connection status
     */
    getStatus() {
        if (this.calculator) {
            return this.calculator.getStatus();
        }
        return this.connectionStatus;
    }
    /**
     * Check if the service is connected and has data
     */
    isReady() {
        return this.currentData?.isReady === true;
    }
    /**
     * Get the current NIFTY LTP (Last Traded Price)
     */
    getCurrentLTP() {
        return this.currentData?.ltp || 0;
    }
    /**
     * Get formatted change data
     */
    getFormattedChange() {
        return this.currentData?.formatted || null;
    }
    /**
     * Display current status (for debugging)
     */
    displayStatus() {
        if (this.calculator) {
            this.calculator.displayStatus();
        }
        else {
            console.log('❌ NIFTY Spot Calculator not initialized');
        }
    }
    /**
     * Start the service (connect and begin data flow)
     */
    async start() {
        try {
            if (!this.isInitialized) {
                await this.initializeCalculator();
            }
            await this.connect();
            console.log('🚀 NIFTY Spot Service started successfully');
        }
        catch (error) {
            console.error('❌ Failed to start NIFTY Spot Service:', error);
            throw error;
        }
    }
    /**
     * Stop the service (disconnect and cleanup)
     */
    stop() {
        this.disconnect();
        this.removeAllListeners();
        console.log('🛑 NIFTY Spot Service stopped');
    }
}
exports.NiftySpotService = NiftySpotService;
// Export singleton instance
exports.niftySpotService = new NiftySpotService();
// Auto-start the service in server environment
if (typeof window === 'undefined') {
    // Server-side: Auto-start the service
    exports.niftySpotService.start().catch(error => {
        console.error('❌ Failed to auto-start NIFTY Spot Service:', error);
    });
    // Graceful shutdown
    process.on('SIGINT', () => {
        console.log('\n👋 Shutting down NIFTY Spot Service...');
        exports.niftySpotService.displayStatus();
        exports.niftySpotService.stop();
    });
}
exports.default = exports.niftySpotService;
//# sourceMappingURL=NiftySpotService.js.map