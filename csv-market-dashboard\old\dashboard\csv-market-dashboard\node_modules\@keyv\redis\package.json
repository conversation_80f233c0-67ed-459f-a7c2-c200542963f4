{"name": "@keyv/redis", "version": "4.4.1", "description": "Redis storage adapter for Keyv", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.js"}}, "repository": {"type": "git", "url": "git+https://github.com/jaredwray/keyv.git"}, "keywords": ["redis", "keyv", "storage", "adapter", "key", "value", "store", "cache", "ttl"], "author": "<PERSON> <<EMAIL>> (http://jaredwray.com)", "license": "MIT", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "homepage": "https://github.com/jaredwray/keyv", "dependencies": {"@redis/client": "^1.6.0", "cluster-key-slot": "^1.1.2"}, "peerDependencies": {"keyv": "^5.3.4"}, "devDependencies": {"@vitest/coverage-v8": "^3.2.3", "rimraf": "^6.0.1", "timekeeper": "^2.3.1", "tsd": "^0.32.0", "vitest": "^3.2.3", "xo": "^1.1.0", "@keyv/test-suite": "^2.0.8"}, "tsd": {"directory": "test"}, "engines": {"node": ">= 18"}, "files": ["dist", "LICENSE"], "scripts": {"build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "test": "xo --fix && vitest run --coverage", "test:ci": "xo && vitest --run --sequence.setupFiles=list --coverage", "clean": "rimraf ./node_modules ./coverage ./dist"}}