import { NextRequest, NextResponse } from 'next/server';

/**
 * API endpoint to get NIFTY spot price
 * GET /api/nifty-spot
 */
export async function GET(request: NextRequest) {
  try {
    console.log('📈 Fetching NIFTY spot price from dedicated calculator...');

    // 🚀 NEW: Try to get from dedicated NIFTY spot service first
    try {
      // Import the NIFTY spot service (server-side only)
      const { niftySpotService } = await import('../../../services/NiftySpotService');

      const currentData = niftySpotService.getCurrentData();
      const status = niftySpotService.getStatus();

      if (currentData && currentData.isReady && currentData.ltp > 0) {
        console.log('✅ Found NIFTY spot price from dedicated calculator:', currentData.ltp);
        return NextResponse.json({
          success: true,
          data: {
            securityId: currentData.securityId,
            symbol: currentData.symbol,
            ltp: currentData.ltp,
            change: currentData.changePoints,
            changePercent: currentData.changePercent,
            previousClose: currentData.previousClose,
            lastUpdateTime: currentData.lastUpdateTime,
            totalUpdates: currentData.totalUpdates,
            source: 'dedicated_calculator',
            status: {
              isConnected: status?.isConnected || false,
              isReady: currentData.isReady,
              updateRate: status?.updateRate || '0'
            }
          },
          timestamp: new Date().toISOString()
        });
      } else {
        console.log('⚠️ Dedicated calculator not ready, trying server fallback...');
      }
    } catch (calculatorError) {
      console.warn('⚠️ Could not use dedicated calculator:', calculatorError);
    }

    // Fallback: Try to get from server's market data service
    const serverUrl = process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:8081';
    
    try {
      const response = await fetch(`${serverUrl}/api/market-data`);
      if (response.ok) {
        const data = await response.json();
        
        // Look for NIFTY index data (security ID 13 or symbol NIFTY)
        const niftyData = data.data.instruments.find((item: any) => 
          item.securityId === '13' || 
          item.symbol === 'NIFTY' || 
          item.symbol.includes('NIFTY') && item.instrumentType === 'INDEX'
        );

        if (niftyData && niftyData.ltp > 0) {
          console.log('✅ Found NIFTY spot price:', niftyData.ltp);
          return NextResponse.json({
            success: true,
            data: {
              securityId: niftyData.securityId,
              symbol: niftyData.symbol,
              ltp: niftyData.ltp,
              change: niftyData.change,
              changePercent: niftyData.changePercent,
              high: niftyData.high,
              low: niftyData.low,
              open: niftyData.open,
              close: niftyData.close,
              timestamp: niftyData.timestamp,
              source: 'live_data'
            },
            timestamp: new Date().toISOString()
          });
        }
      }
    } catch (serverError) {
      console.warn('⚠️ Could not fetch from server:', serverError);
    }

    // Fallback: Return error indicating no live data
    console.warn('⚠️ No NIFTY spot price available from live data');
    return NextResponse.json({
      success: false,
      error: 'NIFTY spot price not available from live data',
      data: null,
      timestamp: new Date().toISOString()
    }, { status: 404 });

  } catch (error) {
    console.error('❌ Error fetching NIFTY spot price:', error);
    return NextResponse.json({
      success: false,
      error: (error as Error).message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
