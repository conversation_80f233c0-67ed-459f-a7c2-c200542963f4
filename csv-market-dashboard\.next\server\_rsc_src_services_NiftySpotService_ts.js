"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_services_NiftySpotService_ts";
exports.ids = ["_rsc_src_services_NiftySpotService_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/nifty-spot-calculator.js":
/*!******************************************!*\
  !*** ./src/lib/nifty-spot-calculator.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// NIFTY Spot Price Calculator - Integrated with CSV Market Dashboard\n\nconsole.log('📊 NIFTY SPOT CALCULATOR - CSV DASHBOARD INTEGRATION');\nconsole.log('====================================================\\n');\nconst WebSocket = __webpack_require__(/*! ws */ \"ws\");\n// Dhan API Configuration\nconst DHAN_CONFIG = {\n    clientId: '1100232369',\n    accessToken: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUyMDQwNzEwLCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwMDIzMjM2OSJ9.9c5rfryVkvqxsOXKq8QRO7XxuR2-pENTmBq_mNN9l22A0N-CxOXxVZP8MT2ZtIUwN6AGiv9GeqDKfKlZpBeBZg'\n};\nconst WEBSOCKET_URL = `wss://api-feed.dhan.co?version=2&token=${DHAN_CONFIG.accessToken}&clientId=${DHAN_CONFIG.clientId}&authType=2`;\n// NIFTY Spot Configuration\nconst NIFTY_SPOT = {\n    securityId: '13',\n    exchange: 'IDX_I',\n    symbol: 'NIFTY'\n};\nconsole.log('🎯 Target: NIFTY Spot Price Calculation');\nconsole.log(`   Security ID: ${NIFTY_SPOT.securityId}`);\nconsole.log(`   Exchange: ${NIFTY_SPOT.exchange}`);\nconsole.log(`   Symbol: ${NIFTY_SPOT.symbol}\\n`);\nclass NiftySpotCalculator {\n    constructor(){\n        this.ws = null;\n        this.isConnected = false;\n        this.messageCount = 0;\n        this.startTime = Date.now();\n        // NIFTY market data\n        this.niftyData = {\n            symbol: NIFTY_SPOT.symbol,\n            securityId: NIFTY_SPOT.securityId,\n            ltp: 0,\n            previousClose: 0,\n            changePoints: 0,\n            changePercent: 0,\n            lastUpdateTime: null,\n            totalUpdates: 0,\n            isReady: false\n        };\n        // Callbacks for external integration\n        this.onDataCallback = null;\n        this.onStatusCallback = null;\n    }\n    // Binary parsing functions for Dhan feed\n    parseResponseHeader(buffer) {\n        if (buffer.length < 8) return null;\n        return {\n            feedResponseCode: buffer.readUInt8(0),\n            messageLength: buffer.readUInt16LE(1),\n            exchangeSegment: buffer.readUInt8(3),\n            securityId: buffer.readUInt32LE(4)\n        };\n    }\n    parseTickerPacket(buffer) {\n        if (buffer.length < 16) return null;\n        const header = this.parseResponseHeader(buffer);\n        return {\n            ...header,\n            ltp: buffer.readFloatLE(8),\n            ltt: buffer.readUInt32LE(12)\n        };\n    }\n    parsePrevClosePacket(buffer) {\n        if (buffer.length < 16) return null;\n        const header = this.parseResponseHeader(buffer);\n        return {\n            ...header,\n            prevClose: buffer.readFloatLE(8),\n            prevOI: buffer.readUInt32LE(12)\n        };\n    }\n    // Calculate change data\n    calculateChange() {\n        if (this.niftyData.ltp > 0 && this.niftyData.previousClose > 0) {\n            this.niftyData.changePoints = this.niftyData.ltp - this.niftyData.previousClose;\n            this.niftyData.changePercent = this.niftyData.changePoints / this.niftyData.previousClose * 100;\n            this.niftyData.isReady = true;\n            return true;\n        }\n        return false;\n    }\n    // Format change data for display\n    formatChange() {\n        const pointsSign = this.niftyData.changePoints >= 0 ? '+' : '';\n        const percentSign = this.niftyData.changePercent >= 0 ? '+' : '';\n        return {\n            points: `${pointsSign}₹${this.niftyData.changePoints.toFixed(2)}`,\n            percent: `${percentSign}${this.niftyData.changePercent.toFixed(3)}%`,\n            color: this.niftyData.changePoints >= 0 ? '🟢' : '🔴',\n            isPositive: this.niftyData.changePoints >= 0\n        };\n    }\n    // Get current NIFTY data for external use\n    getNiftyData() {\n        return {\n            ...this.niftyData,\n            formatted: this.formatChange()\n        };\n    }\n    // Set callback for data updates\n    onData(callback) {\n        this.onDataCallback = callback;\n    }\n    // Set callback for status updates\n    onStatus(callback) {\n        this.onStatusCallback = callback;\n    }\n    // Connect to Dhan WebSocket\n    async connect() {\n        return new Promise((resolve, reject)=>{\n            console.log('🔌 Connecting to Dhan market feed...');\n            this.ws = new WebSocket(WEBSOCKET_URL);\n            this.ws.on('open', ()=>{\n                console.log('✅ Connected to Dhan WebSocket successfully');\n                this.isConnected = true;\n                this.onStatusCallback?.(true);\n                // Subscribe to NIFTY spot\n                this.subscribeToNifty();\n                resolve();\n            });\n            this.ws.on('message', (data)=>{\n                this.handleMessage(data);\n            });\n            this.ws.on('close', (code, reason)=>{\n                console.log(`🔌 Connection closed: ${code} - ${reason}`);\n                this.isConnected = false;\n                this.onStatusCallback?.(false);\n            });\n            this.ws.on('error', (error)=>{\n                console.error('❌ WebSocket error:', error.message);\n                this.onStatusCallback?.(false, error.message);\n                reject(error);\n            });\n        });\n    }\n    // Subscribe to NIFTY spot data\n    subscribeToNifty() {\n        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {\n            console.error('❌ WebSocket not connected');\n            return;\n        }\n        // CORRECT: RequestCode 15 for NIFTY spot subscription\n        const subscriptionRequest = {\n            RequestCode: 15,\n            InstrumentCount: 1,\n            InstrumentList: [\n                {\n                    ExchangeSegment: NIFTY_SPOT.exchange,\n                    SecurityId: NIFTY_SPOT.securityId\n                }\n            ]\n        };\n        console.log('📡 Subscribing to NIFTY spot with RequestCode 15...');\n        console.log(`   Security ID: ${NIFTY_SPOT.securityId} (NIFTY)`);\n        console.log(`   Exchange: ${NIFTY_SPOT.exchange}`);\n        console.log(`   Request: ${JSON.stringify(subscriptionRequest, null, 2)}`);\n        this.ws.send(JSON.stringify(subscriptionRequest));\n        console.log('✅ RequestCode 15 subscription sent - expecting Ticker (Code 2) + Previous Close (Code 6)');\n    }\n    // Handle incoming WebSocket messages\n    handleMessage(data) {\n        this.messageCount++;\n        try {\n            if (Buffer.isBuffer(data)) {\n                const header = this.parseResponseHeader(data);\n                // Only process NIFTY data (security ID 13)\n                if (header && header.securityId == NIFTY_SPOT.securityId) {\n                    switch(header.feedResponseCode){\n                        case 2:\n                            const ticker = this.parseTickerPacket(data);\n                            if (ticker) {\n                                this.niftyData.ltp = ticker.ltp;\n                                this.niftyData.lastUpdateTime = new Date();\n                                this.niftyData.totalUpdates++;\n                                // Calculate change if we have previous close\n                                if (this.calculateChange()) {\n                                    console.log(`📈 NIFTY LTP: ₹${this.niftyData.ltp.toFixed(2)} | Change: ${this.formatChange().points} (${this.formatChange().percent})`);\n                                    // Notify external callback\n                                    this.onDataCallback?.(this.getNiftyData());\n                                }\n                            }\n                            break;\n                        case 6:\n                            const prevClose = this.parsePrevClosePacket(data);\n                            if (prevClose) {\n                                this.niftyData.previousClose = prevClose.prevClose;\n                                console.log(`📊 NIFTY Previous Close: ₹${this.niftyData.previousClose.toFixed(2)}`);\n                                // Calculate change if we already have LTP\n                                if (this.niftyData.ltp > 0) {\n                                    this.calculateChange();\n                                    this.onDataCallback?.(this.getNiftyData());\n                                }\n                            }\n                            break;\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('❌ Error processing message:', error);\n        }\n    }\n    // Disconnect from WebSocket\n    disconnect() {\n        if (this.ws) {\n            console.log('🔌 Disconnecting from Dhan WebSocket...');\n            this.ws.close();\n            this.ws = null;\n            this.isConnected = false;\n        }\n    }\n    // Get connection status\n    getStatus() {\n        const runtime = ((Date.now() - this.startTime) / 1000).toFixed(1);\n        return {\n            isConnected: this.isConnected,\n            messageCount: this.messageCount,\n            runtime: runtime,\n            updateRate: (this.niftyData.totalUpdates / (runtime || 1)).toFixed(2),\n            isDataReady: this.niftyData.isReady\n        };\n    }\n    // Display current status\n    displayStatus() {\n        const status = this.getStatus();\n        const change = this.formatChange();\n        console.log('\\n📊 NIFTY SPOT STATUS:');\n        console.log(`   LTP: ₹${this.niftyData.ltp.toFixed(2)}`);\n        console.log(`   Previous Close: ₹${this.niftyData.previousClose.toFixed(2)}`);\n        console.log(`   Change: ${change.color} ${change.points} (${change.percent})`);\n        console.log(`   Updates: ${this.niftyData.totalUpdates} | Rate: ${status.updateRate}/sec`);\n        console.log(`   Status: ${status.isConnected ? '🟢 Connected' : '🔴 Disconnected'}`);\n        console.log(`   Data Ready: ${status.isDataReady ? '✅ Yes' : '❌ No'}`);\n    }\n}\n// Export for integration with CSV Market Dashboard\nmodule.exports = {\n    NiftySpotCalculator,\n    NIFTY_SPOT,\n    DHAN_CONFIG\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/nifty-spot-calculator.js\n");

/***/ }),

/***/ "(rsc)/./src/services/NiftySpotService.ts":
/*!******************************************!*\
  !*** ./src/services/NiftySpotService.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NiftySpotService: () => (/* binding */ NiftySpotService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   niftySpotService: () => (/* binding */ niftySpotService)\n/* harmony export */ });\n/* harmony import */ var events__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! events */ \"events\");\n/* harmony import */ var events__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(events__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * NIFTY Spot Service - Integration with dedicated spot calculator\n * Provides live NIFTY spot price data for the option chain\n */ \n/**\n * NIFTY Spot Service\n * Manages the dedicated NIFTY spot calculator and provides data to the frontend\n */ class NiftySpotService extends events__WEBPACK_IMPORTED_MODULE_0__.EventEmitter {\n    constructor(){\n        super(), this.calculator = null, this.isInitialized = false, this.currentData = null, this.connectionStatus = null;\n        this.initializeCalculator();\n    }\n    /**\n   * Initialize the NIFTY spot calculator\n   */ async initializeCalculator() {\n        try {\n            console.log('🚀 Initializing NIFTY Spot Calculator...');\n            // Dynamically import the calculator (Node.js module)\n            const { NiftySpotCalculator } = __webpack_require__(/*! ../lib/nifty-spot-calculator.js */ \"(rsc)/./src/lib/nifty-spot-calculator.js\");\n            this.calculator = new NiftySpotCalculator();\n            // Set up data callback\n            this.calculator.onData((data)=>{\n                this.currentData = data;\n                this.emit('data', data);\n                console.log(`📊 [NiftySpotService] Data Update: LTP=₹${data.ltp.toFixed(2)}, Change=${data.formatted.points}`);\n            });\n            // Set up status callback\n            this.calculator.onStatus((connected, error)=>{\n                this.connectionStatus = this.calculator.getStatus();\n                this.emit('status', {\n                    connected,\n                    error,\n                    status: this.connectionStatus\n                });\n                console.log(`📡 [NiftySpotService] Status: ${connected ? '🟢 Connected' : '🔴 Disconnected'}${error ? ` - ${error}` : ''}`);\n            });\n            this.isInitialized = true;\n            console.log('✅ NIFTY Spot Calculator initialized successfully');\n        } catch (error) {\n            console.error('❌ Failed to initialize NIFTY Spot Calculator:', error);\n            this.emit('error', error);\n        }\n    }\n    /**\n   * Connect to the NIFTY spot data feed\n   */ async connect() {\n        if (!this.isInitialized || !this.calculator) {\n            throw new Error('NIFTY Spot Calculator not initialized');\n        }\n        try {\n            console.log('🔌 Connecting to NIFTY spot data feed...');\n            await this.calculator.connect();\n            console.log('✅ Connected to NIFTY spot data feed');\n        } catch (error) {\n            console.error('❌ Failed to connect to NIFTY spot data feed:', error);\n            throw error;\n        }\n    }\n    /**\n   * Disconnect from the NIFTY spot data feed\n   */ disconnect() {\n        if (this.calculator) {\n            console.log('🔌 Disconnecting from NIFTY spot data feed...');\n            this.calculator.disconnect();\n            this.currentData = null;\n            this.connectionStatus = null;\n        }\n    }\n    /**\n   * Get current NIFTY spot data\n   */ getCurrentData() {\n        return this.currentData;\n    }\n    /**\n   * Get current connection status\n   */ getStatus() {\n        if (this.calculator) {\n            return this.calculator.getStatus();\n        }\n        return this.connectionStatus;\n    }\n    /**\n   * Check if the service is connected and has data\n   */ isReady() {\n        return this.currentData?.isReady === true;\n    }\n    /**\n   * Get the current NIFTY LTP (Last Traded Price)\n   */ getCurrentLTP() {\n        return this.currentData?.ltp || 0;\n    }\n    /**\n   * Get formatted change data\n   */ getFormattedChange() {\n        return this.currentData?.formatted || null;\n    }\n    /**\n   * Display current status (for debugging)\n   */ displayStatus() {\n        if (this.calculator) {\n            this.calculator.displayStatus();\n        } else {\n            console.log('❌ NIFTY Spot Calculator not initialized');\n        }\n    }\n    /**\n   * Start the service (connect and begin data flow)\n   */ async start() {\n        try {\n            if (!this.isInitialized) {\n                await this.initializeCalculator();\n            }\n            await this.connect();\n            console.log('🚀 NIFTY Spot Service started successfully');\n        } catch (error) {\n            console.error('❌ Failed to start NIFTY Spot Service:', error);\n            throw error;\n        }\n    }\n    /**\n   * Stop the service (disconnect and cleanup)\n   */ stop() {\n        this.disconnect();\n        this.removeAllListeners();\n        console.log('🛑 NIFTY Spot Service stopped');\n    }\n}\n// Export singleton instance\nconst niftySpotService = new NiftySpotService();\n// Auto-start the service in server environment\nif (true) {\n    // Server-side: Auto-start the service\n    niftySpotService.start().catch((error)=>{\n        console.error('❌ Failed to auto-start NIFTY Spot Service:', error);\n    });\n    // Graceful shutdown\n    process.on('SIGINT', ()=>{\n        console.log('\\n👋 Shutting down NIFTY Spot Service...');\n        niftySpotService.displayStatus();\n        niftySpotService.stop();\n    });\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (niftySpotService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/NiftySpotService.ts\n");

/***/ })

};
;