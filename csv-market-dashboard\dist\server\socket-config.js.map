{"version": 3, "file": "socket-config.js", "sourceRoot": "", "sources": ["../../src/server/socket-config.ts"], "names": [], "mappings": ";AAAA;;GAEG;;AASH,gDAeC;AAKD,8CAiNC;AAKD,kDAIC;AAKD,4DAIC;AAKD,gDAOC;AA1QD,yCAAmE;AAEnE,gDAAoE;AAEpE;;GAEG;AACH,SAAgB,kBAAkB;IAChC,OAAO;QACL,IAAI,EAAE;YACJ,MAAM,EAAE,2BAAe,CAAC,IAAI,CAAC,MAAM;YACnC,WAAW,EAAE,2BAAe,CAAC,IAAI,CAAC,WAAW;SAC9C;QACD,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAQ;QAC3C,WAAW,EAAE,4BAAgB,CAAC,YAAY;QAC1C,YAAY,EAAE,4BAAgB,CAAC,aAAa;QAC5C,cAAc,EAAE,KAAK;QACrB,iBAAiB,EAAE,GAAG,EAAE,MAAM;QAC9B,SAAS,EAAE,IAAI;QACf,cAAc,EAAE,4BAAgB,CAAC,kBAAkB;QACnD,WAAW,EAAE,KAAK;KACnB,CAAA;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,iBAAiB,CAAC,UAAsB;IACtD,MAAM,MAAM,GAAG,kBAAkB,EAAE,CAAA;IAEnC,MAAM,EAAE,GAAG,IAAI,kBAAc,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;IAEjD,sBAAsB;IACtB,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAU,CAAA;IAC1C,MAAM,aAAa,GAAG,IAAI,GAAG,EAKzB,CAAA;IAEJ,oCAAoC;IACpC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;QACtB,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,CAAA;QACzC,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;QAExD,OAAO,CAAC,GAAG,CAAC,kCAAkC,QAAQ,EAAE,CAAC,CAAA;QACzD,OAAO,CAAC,GAAG,CAAC,kBAAkB,SAAS,EAAE,CAAC,CAAA;QAE1C,IAAI,EAAE,CAAA;IACR,CAAC,CAAC,CAAA;IAEF,qBAAqB;IACrB,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,EAAE;QAC7B,MAAM,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAA;QAC1B,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAE9B,kBAAkB;QAClB,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QAC9B,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE;YAC1B,WAAW;YACX,YAAY,EAAE,WAAW;YACzB,YAAY,EAAE,CAAC;YACf,gBAAgB,EAAE,CAAC;SACpB,CAAC,CAAA;QAEF,OAAO,CAAC,GAAG,CAAC,uBAAuB,QAAQ,YAAY,gBAAgB,CAAC,IAAI,GAAG,CAAC,CAAA;QAEhF,+BAA+B;QAC/B,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE;YACzB,YAAY,EAAE,QAAQ;YACtB,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACpC,SAAS,EAAE,IAAI;YACf,YAAY,EAAE,gBAAgB,CAAC,IAAI;SACpC,CAAC,CAAA;QAEF,sBAAsB;QACtB,MAAM,iBAAiB,GAAG,WAAW,CAAC,GAAG,EAAE;YACzC,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;gBACrB,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;YACrD,CAAC;iBAAM,CAAC;gBACN,aAAa,CAAC,iBAAiB,CAAC,CAAA;YAClC,CAAC;QACH,CAAC,EAAE,4BAAgB,CAAC,kBAAkB,CAAC,CAAA;QAEvC,mBAAmB;QACnB,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YACzB,MAAM,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;YAC3C,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;gBACjC,OAAO,CAAC,gBAAgB,EAAE,CAAA;YAC5B,CAAC;YACD,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE;gBAClB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,eAAe,EAAE,IAAI,EAAE,SAAS;aACjC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YACzB,MAAM,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;YAC3C,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;gBACjC,OAAO,CAAC,gBAAgB,EAAE,CAAA;YAC5B,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,kCAAkC;QAClC,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,IAA+B,EAAE,EAAE;YACzD,OAAO,CAAC,GAAG,CAAC,gCAAgC,QAAQ,GAAG,EAAE,IAAI,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,EAAE,aAAa,CAAC,CAAA;YAEtG,iDAAiD;YACjD,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBACnC,OAAO,EAAE,yFAAyF;gBAClG,aAAa,EAAE,IAAI;aACpB,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,wBAAwB;QACxB,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAA+B,EAAE,EAAE;YAC3D,OAAO,CAAC,GAAG,CAAC,kCAAkC,QAAQ,GAAG,EAAE,IAAI,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,EAAE,aAAa,CAAC,CAAA;YAExG,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBACrC,OAAO,EAAE,2FAA2F;gBACpG,aAAa,EAAE,IAAI;aACpB,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,uBAAuB;QACvB,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YAC3B,OAAO,CAAC,KAAK,CAAC,sBAAsB,QAAQ,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,CAAA;QACjE,CAAC,CAAC,CAAA;QAEF,uBAAuB;QACvB,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,EAAE;YACjC,aAAa,CAAC,iBAAiB,CAAC,CAAA;YAChC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;YAEjC,MAAM,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;YAC3C,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,WAAW,CAAA;gBACxD,OAAO,CAAC,GAAG,CAAC,0BAA0B,QAAQ,EAAE,CAAC,CAAA;gBACjD,OAAO,CAAC,GAAG,CAAC,cAAc,MAAM,EAAE,CAAC,CAAA;gBACnC,OAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;gBAC1E,OAAO,CAAC,GAAG,CAAC,qBAAqB,OAAO,CAAC,YAAY,EAAE,CAAC,CAAA;gBACxD,OAAO,CAAC,GAAG,CAAC,yBAAyB,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAA;gBAChE,OAAO,CAAC,GAAG,CAAC,qBAAqB,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAA;gBAEzD,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;YAChC,CAAC;YAED,gDAAgD;YAChD,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,iBAAiB;oBACpB,OAAO,CAAC,IAAI,CAAC,mCAAmC,QAAQ,EAAE,CAAC,CAAA;oBAC3D,MAAK;gBACP,KAAK,iBAAiB;oBACpB,OAAO,CAAC,IAAI,CAAC,kCAAkC,QAAQ,EAAE,CAAC,CAAA;oBAC1D,MAAK;gBACP,KAAK,6BAA6B;oBAChC,OAAO,CAAC,GAAG,CAAC,sCAAsC,QAAQ,EAAE,CAAC,CAAA;oBAC7D,MAAK;gBACP,KAAK,6BAA6B;oBAChC,OAAO,CAAC,GAAG,CAAC,sCAAsC,QAAQ,EAAE,CAAC,CAAA;oBAC7D,MAAK;gBACP,KAAK,cAAc;oBACjB,OAAO,CAAC,IAAI,CAAC,sBAAsB,QAAQ,EAAE,CAAC,CAAA;oBAC9C,MAAK;gBACP;oBACE,OAAO,CAAC,GAAG,CAAC,mCAAmC,QAAQ,KAAK,MAAM,EAAE,CAAC,CAAA;YACzE,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,6BAA6B;QAC7B,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,EAAE;YACpC,OAAO,CAAC,GAAG,CAAC,4BAA4B,QAAQ,aAAa,MAAM,GAAG,CAAC,CAAA;QACzE,CAAC,CAAC,CAAA;QAEF,iCAAiC;QACjC,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,GAAG,IAAI,EAAE,EAAE;YAC9B,MAAM,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;YAC3C,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;gBACjC,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,EAAE,CAAC;oBACzC,OAAO,CAAC,gBAAgB,EAAE,CAAA;gBAC5B,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,0BAA0B;QAC1B,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAA;QAChC,MAAM,CAAC,IAAI,GAAG,UAAS,KAAa,EAAE,GAAG,IAAW;YAClD,MAAM,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;YAC3C,IAAI,OAAO,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,EAAE,CAAC;gBACpD,OAAO,CAAC,YAAY,EAAE,CAAA;YACxB,CAAC;YACD,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,CAAA;QAChD,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,8BAA8B;IAC9B,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,GAAG,EAAE,EAAE;QACvC,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE;YAC9C,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,WAAW,EAAE,GAAG,CAAC,WAAW;YAC5B,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,IAAI,EAAE,GAAG,CAAC,IAAI;SACf,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,wCAAwC;IACxC,WAAW,CAAC,GAAG,EAAE;QACf,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,cAAc,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAA,CAAC,YAAY;QAEjD,KAAK,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;YAC1D,IAAI,GAAG,GAAG,OAAO,CAAC,YAAY,GAAG,cAAc,EAAE,CAAC;gBAChD,OAAO,CAAC,IAAI,CAAC,oCAAoC,QAAQ,EAAE,CAAC,CAAA;gBAC5D,MAAM,MAAM,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;gBAC/C,IAAI,MAAM,EAAE,CAAC;oBACX,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;gBACzB,CAAC;gBACD,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;gBAC9B,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;YACnC,CAAC;QACH,CAAC;IACH,CAAC,EAAE,4BAAgB,CAAC,gBAAgB,CAAC,CAAA;IAErC,yBAAyB;IACzB,WAAW,CAAC,GAAG,EAAE;QACf,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAA;QACnC,OAAO,CAAC,GAAG,CAAC,0BAA0B,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAA;QAC9D,OAAO,CAAC,GAAG,CAAC,2BAA2B,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,CAAA;QACxH,OAAO,CAAC,GAAG,CAAC,+BAA+B,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,gBAAgB,EAAE,CAAC,CAAC,EAAE,CAAC,CAAA;IAClI,CAAC,EAAE,KAAK,CAAC,CAAA,CAAC,eAAe;IAEzB,OAAO,EAAE,CAAA;AACX,CAAC;AAED;;GAEG;AACH,SAAgB,mBAAmB,CAAC,EAAkB,EAAE,IAAS;IAC/D,IAAI,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;QAChC,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAA;IAC7B,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,wBAAwB,CAAC,EAAkB,EAAE,SAAgB;IAC3E,IAAI,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxD,EAAE,CAAC,IAAI,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAA;IACvC,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAAC,EAAkB;IACnD,OAAO;QACL,gBAAgB,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI;QACzC,gBAAgB,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACvD,YAAY,EAAE,OAAO,CAAC,MAAM,EAAE;QAC9B,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE;KACnC,CAAA;AACH,CAAC"}