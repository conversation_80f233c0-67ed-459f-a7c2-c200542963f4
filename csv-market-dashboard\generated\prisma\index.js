
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
  PrismaClientRustPanicError,
  PrismaClientInitializationError,
  PrismaClientValidationError,
  getPrismaClient,
  sqltag,
  empty,
  join,
  raw,
  skip,
  Decimal,
  Debug,
  objectEnumValues,
  makeStrictEnum,
  Extensions,
  warnOnce,
  defineDmmfProperty,
  Public,
  getRuntime,
  createParam,
} = require('./runtime/library.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.11.0
 * Query Engine version: 9c30299f5a0ea26a96790e13f796dc6094db3173
 */
Prisma.prismaVersion = {
  client: "6.11.0",
  engine: "9c30299f5a0ea26a96790e13f796dc6094db3173"
}

Prisma.PrismaClientKnownRequestError = PrismaClientKnownRequestError;
Prisma.PrismaClientUnknownRequestError = PrismaClientUnknownRequestError
Prisma.PrismaClientRustPanicError = PrismaClientRustPanicError
Prisma.PrismaClientInitializationError = PrismaClientInitializationError
Prisma.PrismaClientValidationError = PrismaClientValidationError
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = sqltag
Prisma.empty = empty
Prisma.join = join
Prisma.raw = raw
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = Extensions.getExtensionContext
Prisma.defineExtension = Extensions.defineExtension

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}




  const path = require('path')

/**
 * Enums
 */
exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.InstrumentScalarFieldEnum = {
  SECURITY_ID: 'SECURITY_ID',
  SYMBOL_NAME: 'SYMBOL_NAME',
  EXCH_ID: 'EXCH_ID',
  SEGMENT: 'SEGMENT',
  INSTRUMENT_TYPE: 'INSTRUMENT_TYPE',
  STRIKE_PRICE: 'STRIKE_PRICE',
  SM_EXPIRY_DATE: 'SM_EXPIRY_DATE',
  OPTION_TYPE: 'OPTION_TYPE',
  LOT_SIZE: 'LOT_SIZE',
  TICK_SIZE: 'TICK_SIZE',
  ISIN: 'ISIN',
  INSTRUMENT: 'INSTRUMENT',
  UNDERLYING_SECURITY_ID: 'UNDERLYING_SECURITY_ID',
  UNDERLYING_SYMBOL: 'UNDERLYING_SYMBOL',
  DISPLAY_NAME: 'DISPLAY_NAME',
  SERIES: 'SERIES',
  EXPIRY_FLAG: 'EXPIRY_FLAG',
  BRACKET_FLAG: 'BRACKET_FLAG',
  COVER_FLAG: 'COVER_FLAG',
  ASM_GSM_FLAG: 'ASM_GSM_FLAG',
  ASM_GSM_CATEGORY: 'ASM_GSM_CATEGORY',
  BUY_SELL_INDICATOR: 'BUY_SELL_INDICATOR',
  BUY_CO_MIN_MARGIN_PER: 'BUY_CO_MIN_MARGIN_PER',
  SELL_CO_MIN_MARGIN_PER: 'SELL_CO_MIN_MARGIN_PER',
  BUY_CO_SL_RANGE_MAX_PERC: 'BUY_CO_SL_RANGE_MAX_PERC',
  SELL_CO_SL_RANGE_MAX_PERC: 'SELL_CO_SL_RANGE_MAX_PERC',
  BUY_CO_SL_RANGE_MIN_PERC: 'BUY_CO_SL_RANGE_MIN_PERC',
  SELL_CO_SL_RANGE_MIN_PERC: 'SELL_CO_SL_RANGE_MIN_PERC',
  BUY_BO_MIN_MARGIN_PER: 'BUY_BO_MIN_MARGIN_PER',
  SELL_BO_MIN_MARGIN_PER: 'SELL_BO_MIN_MARGIN_PER',
  BUY_BO_SL_RANGE_MAX_PERC: 'BUY_BO_SL_RANGE_MAX_PERC',
  SELL_BO_SL_RANGE_MAX_PERC: 'SELL_BO_SL_RANGE_MAX_PERC',
  BUY_BO_SL_RANGE_MIN_PERC: 'BUY_BO_SL_RANGE_MIN_PERC',
  SELL_BO_SL_MIN_RANGE: 'SELL_BO_SL_MIN_RANGE',
  BUY_BO_PROFIT_RANGE_MAX_PERC: 'BUY_BO_PROFIT_RANGE_MAX_PERC',
  SELL_BO_PROFIT_RANGE_MAX_PERC: 'SELL_BO_PROFIT_RANGE_MAX_PERC',
  BUY_BO_PROFIT_RANGE_MIN_PERC: 'BUY_BO_PROFIT_RANGE_MIN_PERC',
  SELL_BO_PROFIT_RANGE_MIN_PERC: 'SELL_BO_PROFIT_RANGE_MIN_PERC',
  MTF_LEVERAGE: 'MTF_LEVERAGE'
};

exports.Prisma.MarketDataScalarFieldEnum = {
  id: 'id',
  securityId: 'securityId',
  symbol: 'symbol',
  ltp: 'ltp',
  change: 'change',
  changePercent: 'changePercent',
  volume: 'volume',
  openInterest: 'openInterest',
  bid: 'bid',
  ask: 'ask',
  high: 'high',
  low: 'low',
  open: 'open',
  close: 'close',
  lastUpdateTime: 'lastUpdateTime',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};


exports.Prisma.ModelName = {
  Instrument: 'Instrument',
  MarketData: 'MarketData'
};
/**
 * Create the Client
 */
const config = {
  "generator": {
    "name": "client",
    "provider": {
      "fromEnvVar": null,
      "value": "prisma-client-js"
    },
    "output": {
      "value": "D:\\love\\dashboard\\csv-market-dashboard\\generated\\prisma",
      "fromEnvVar": null
    },
    "config": {
      "engineType": "library"
    },
    "binaryTargets": [
      {
        "fromEnvVar": null,
        "value": "windows",
        "native": true
      }
    ],
    "previewFeatures": [],
    "sourceFilePath": "D:\\love\\dashboard\\csv-market-dashboard\\prisma\\schema.prisma",
    "isCustomOutput": true
  },
  "relativeEnvPaths": {
    "rootEnvPath": null,
    "schemaEnvPath": "../../.env"
  },
  "relativePath": "../../prisma",
  "clientVersion": "6.11.0",
  "engineVersion": "9c30299f5a0ea26a96790e13f796dc6094db3173",
  "datasourceNames": [
    "db"
  ],
  "activeProvider": "postgresql",
  "postinstall": false,
  "inlineDatasources": {
    "db": {
      "url": {
        "fromEnvVar": "DATABASE_URL",
        "value": null
      }
    }
  },
  "inlineSchema": "// This is your Prisma schema file,\n// learn more about it in the docs: https://pris.ly/d/prisma-schema\n\n// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?\n// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init\n\ngenerator client {\n  provider = \"prisma-client-js\"\n  output   = \"../generated/prisma\"\n}\n\ndatasource db {\n  provider  = \"postgresql\"\n  url       = env(\"DATABASE_URL\")\n  directUrl = env(\"DIRECT_URL\")\n}\n\n// Market Instruments Model - Matching actual CSV database structure\nmodel Instrument {\n  SECURITY_ID                   BigInt  @map(\"SECURITY_ID\")\n  SYMBOL_NAME                   String? @map(\"SYMBOL_NAME\")\n  EXCH_ID                       String? @map(\"EXCH_ID\")\n  SEGMENT                       String? @map(\"SEGMENT\")\n  INSTRUMENT_TYPE               String? @map(\"INSTRUMENT_TYPE\")\n  STRIKE_PRICE                  String? @map(\"STRIKE_PRICE\")\n  SM_EXPIRY_DATE                String? @map(\"SM_EXPIRY_DATE\")\n  OPTION_TYPE                   String? @map(\"OPTION_TYPE\")\n  LOT_SIZE                      BigInt? @map(\"LOT_SIZE\")\n  TICK_SIZE                     String? @map(\"TICK_SIZE\")\n  ISIN                          String? @map(\"ISIN\")\n  INSTRUMENT                    String? @map(\"INSTRUMENT\")\n  UNDERLYING_SECURITY_ID        String? @map(\"UNDERLYING_SECURITY_ID\")\n  UNDERLYING_SYMBOL             String? @map(\"UNDERLYING_SYMBOL\")\n  DISPLAY_NAME                  String? @map(\"DISPLAY_NAME\")\n  SERIES                        String? @map(\"SERIES\")\n  EXPIRY_FLAG                   String? @map(\"EXPIRY_FLAG\")\n  BRACKET_FLAG                  String? @map(\"BRACKET_FLAG\")\n  COVER_FLAG                    String? @map(\"COVER_FLAG\")\n  ASM_GSM_FLAG                  String? @map(\"ASM_GSM_FLAG\")\n  ASM_GSM_CATEGORY              String? @map(\"ASM_GSM_CATEGORY\")\n  BUY_SELL_INDICATOR            String? @map(\"BUY_SELL_INDICATOR\")\n  BUY_CO_MIN_MARGIN_PER         String? @map(\"BUY_CO_MIN_MARGIN_PER\")\n  SELL_CO_MIN_MARGIN_PER        String? @map(\"SELL_CO_MIN_MARGIN_PER\")\n  BUY_CO_SL_RANGE_MAX_PERC      String? @map(\"BUY_CO_SL_RANGE_MAX_PERC\")\n  SELL_CO_SL_RANGE_MAX_PERC     String? @map(\"SELL_CO_SL_RANGE_MAX_PERC\")\n  BUY_CO_SL_RANGE_MIN_PERC      String? @map(\"BUY_CO_SL_RANGE_MIN_PERC\")\n  SELL_CO_SL_RANGE_MIN_PERC     String? @map(\"SELL_CO_SL_RANGE_MIN_PERC\")\n  BUY_BO_MIN_MARGIN_PER         String? @map(\"BUY_BO_MIN_MARGIN_PER\")\n  SELL_BO_MIN_MARGIN_PER        String? @map(\"SELL_BO_MIN_MARGIN_PER\")\n  BUY_BO_SL_RANGE_MAX_PERC      String? @map(\"BUY_BO_SL_RANGE_MAX_PERC\")\n  SELL_BO_SL_RANGE_MAX_PERC     String? @map(\"SELL_BO_SL_RANGE_MAX_PERC\")\n  BUY_BO_SL_RANGE_MIN_PERC      String? @map(\"BUY_BO_SL_RANGE_MIN_PERC\")\n  SELL_BO_SL_MIN_RANGE          String? @map(\"SELL_BO_SL_MIN_RANGE\")\n  BUY_BO_PROFIT_RANGE_MAX_PERC  String? @map(\"BUY_BO_PROFIT_RANGE_MAX_PERC\")\n  SELL_BO_PROFIT_RANGE_MAX_PERC String? @map(\"SELL_BO_PROFIT_RANGE_MAX_PERC\")\n  BUY_BO_PROFIT_RANGE_MIN_PERC  String? @map(\"BUY_BO_PROFIT_RANGE_MIN_PERC\")\n  SELL_BO_PROFIT_RANGE_MIN_PERC String? @map(\"SELL_BO_PROFIT_RANGE_MIN_PERC\")\n  MTF_LEVERAGE                  String? @map(\"MTF_LEVERAGE\")\n\n  @@id([SECURITY_ID])\n  // Indexes for performance\n  @@index([SYMBOL_NAME])\n  @@index([EXCH_ID])\n  @@index([INSTRUMENT_TYPE])\n  @@index([SM_EXPIRY_DATE])\n  @@index([STRIKE_PRICE])\n  @@index([SYMBOL_NAME, SM_EXPIRY_DATE])\n  @@index([SYMBOL_NAME, INSTRUMENT_TYPE])\n  @@map(\"Instruments\")\n}\n\n// Market Data Model for live prices (separate table, not used with CSV data)\nmodel MarketData {\n  id            String @id @default(cuid())\n  securityId    String @unique @map(\"security_id\")\n  symbol        String\n  ltp           Float? // Last Traded Price\n  change        Float?\n  changePercent Float? @map(\"change_percent\")\n  volume        Int?\n  openInterest  Int?   @map(\"open_interest\")\n  bid           Float?\n  ask           Float?\n  high          Float?\n  low           Float?\n  open          Float?\n  close         Float?\n\n  // Timestamps\n  lastUpdateTime DateTime @default(now()) @map(\"last_update_time\")\n  createdAt      DateTime @default(now()) @map(\"created_at\")\n  updatedAt      DateTime @updatedAt @map(\"updated_at\")\n\n  // Indexes for performance\n  @@index([symbol])\n  @@index([lastUpdateTime])\n  @@map(\"market_data\")\n}\n",
  "inlineSchemaHash": "7a6e9ee70691b64ea371ac1e278aeceea6cdc4b279a8871c9d3b0d0d0fb2ae97",
  "copyEngine": true
}

const fs = require('fs')

config.dirname = __dirname
if (!fs.existsSync(path.join(__dirname, 'schema.prisma'))) {
  const alternativePaths = [
    "generated/prisma",
    "prisma",
  ]
  
  const alternativePath = alternativePaths.find((altPath) => {
    return fs.existsSync(path.join(process.cwd(), altPath, 'schema.prisma'))
  }) ?? alternativePaths[0]

  config.dirname = path.join(process.cwd(), alternativePath)
  config.isBundled = true
}

config.runtimeDataModel = JSON.parse("{\"models\":{\"Instrument\":{\"dbName\":\"Instruments\",\"schema\":null,\"fields\":[{\"name\":\"SECURITY_ID\",\"dbName\":\"SECURITY_ID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"BigInt\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"SYMBOL_NAME\",\"dbName\":\"SYMBOL_NAME\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"EXCH_ID\",\"dbName\":\"EXCH_ID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"SEGMENT\",\"dbName\":\"SEGMENT\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"INSTRUMENT_TYPE\",\"dbName\":\"INSTRUMENT_TYPE\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"STRIKE_PRICE\",\"dbName\":\"STRIKE_PRICE\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"SM_EXPIRY_DATE\",\"dbName\":\"SM_EXPIRY_DATE\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"OPTION_TYPE\",\"dbName\":\"OPTION_TYPE\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"LOT_SIZE\",\"dbName\":\"LOT_SIZE\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"BigInt\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"TICK_SIZE\",\"dbName\":\"TICK_SIZE\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ISIN\",\"dbName\":\"ISIN\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"INSTRUMENT\",\"dbName\":\"INSTRUMENT\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UNDERLYING_SECURITY_ID\",\"dbName\":\"UNDERLYING_SECURITY_ID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UNDERLYING_SYMBOL\",\"dbName\":\"UNDERLYING_SYMBOL\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"DISPLAY_NAME\",\"dbName\":\"DISPLAY_NAME\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"SERIES\",\"dbName\":\"SERIES\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"EXPIRY_FLAG\",\"dbName\":\"EXPIRY_FLAG\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"BRACKET_FLAG\",\"dbName\":\"BRACKET_FLAG\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"COVER_FLAG\",\"dbName\":\"COVER_FLAG\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ASM_GSM_FLAG\",\"dbName\":\"ASM_GSM_FLAG\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ASM_GSM_CATEGORY\",\"dbName\":\"ASM_GSM_CATEGORY\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"BUY_SELL_INDICATOR\",\"dbName\":\"BUY_SELL_INDICATOR\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"BUY_CO_MIN_MARGIN_PER\",\"dbName\":\"BUY_CO_MIN_MARGIN_PER\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"SELL_CO_MIN_MARGIN_PER\",\"dbName\":\"SELL_CO_MIN_MARGIN_PER\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"BUY_CO_SL_RANGE_MAX_PERC\",\"dbName\":\"BUY_CO_SL_RANGE_MAX_PERC\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"SELL_CO_SL_RANGE_MAX_PERC\",\"dbName\":\"SELL_CO_SL_RANGE_MAX_PERC\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"BUY_CO_SL_RANGE_MIN_PERC\",\"dbName\":\"BUY_CO_SL_RANGE_MIN_PERC\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"SELL_CO_SL_RANGE_MIN_PERC\",\"dbName\":\"SELL_CO_SL_RANGE_MIN_PERC\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"BUY_BO_MIN_MARGIN_PER\",\"dbName\":\"BUY_BO_MIN_MARGIN_PER\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"SELL_BO_MIN_MARGIN_PER\",\"dbName\":\"SELL_BO_MIN_MARGIN_PER\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"BUY_BO_SL_RANGE_MAX_PERC\",\"dbName\":\"BUY_BO_SL_RANGE_MAX_PERC\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"SELL_BO_SL_RANGE_MAX_PERC\",\"dbName\":\"SELL_BO_SL_RANGE_MAX_PERC\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"BUY_BO_SL_RANGE_MIN_PERC\",\"dbName\":\"BUY_BO_SL_RANGE_MIN_PERC\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"SELL_BO_SL_MIN_RANGE\",\"dbName\":\"SELL_BO_SL_MIN_RANGE\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"BUY_BO_PROFIT_RANGE_MAX_PERC\",\"dbName\":\"BUY_BO_PROFIT_RANGE_MAX_PERC\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"SELL_BO_PROFIT_RANGE_MAX_PERC\",\"dbName\":\"SELL_BO_PROFIT_RANGE_MAX_PERC\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"BUY_BO_PROFIT_RANGE_MIN_PERC\",\"dbName\":\"BUY_BO_PROFIT_RANGE_MIN_PERC\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"SELL_BO_PROFIT_RANGE_MIN_PERC\",\"dbName\":\"SELL_BO_PROFIT_RANGE_MIN_PERC\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"MTF_LEVERAGE\",\"dbName\":\"MTF_LEVERAGE\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":{\"name\":null,\"fields\":[\"SECURITY_ID\"]},\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"MarketData\":{\"dbName\":\"market_data\",\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"securityId\",\"dbName\":\"security_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"symbol\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ltp\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"change\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"changePercent\",\"dbName\":\"change_percent\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"volume\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"openInterest\",\"dbName\":\"open_interest\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"bid\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ask\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"high\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"low\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"open\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"close\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"lastUpdateTime\",\"dbName\":\"last_update_time\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false}},\"enums\":{},\"types\":{}}")
defineDmmfProperty(exports.Prisma, config.runtimeDataModel)
config.engineWasm = undefined
config.compilerWasm = undefined


const { warnEnvConflicts } = require('./runtime/library.js')

warnEnvConflicts({
    rootEnvPath: config.relativeEnvPaths.rootEnvPath && path.resolve(config.dirname, config.relativeEnvPaths.rootEnvPath),
    schemaEnvPath: config.relativeEnvPaths.schemaEnvPath && path.resolve(config.dirname, config.relativeEnvPaths.schemaEnvPath)
})

const PrismaClient = getPrismaClient(config)
exports.PrismaClient = PrismaClient
Object.assign(exports, Prisma)

// file annotations for bundling tools to include these files
path.join(__dirname, "query_engine-windows.dll.node");
path.join(process.cwd(), "generated/prisma/query_engine-windows.dll.node")
// file annotations for bundling tools to include these files
path.join(__dirname, "schema.prisma");
path.join(process.cwd(), "generated/prisma/schema.prisma")
