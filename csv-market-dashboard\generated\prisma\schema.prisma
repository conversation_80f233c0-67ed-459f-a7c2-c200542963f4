// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Market Instruments Model
model Instrument {
  id             String  @id @default(cuid())
  securityId     String  @unique @map("security_id")
  symbol         String
  exchange       String
  exchangeCode   Int     @map("exchange_code")
  segment        String
  instrumentType String  @map("instrument_type")
  strikePrice    Float?  @map("strike_price")
  expiryDate     String? @map("expiry_date")
  optionType     String? @map("option_type")

  // Additional fields for market data
  lotSize  Int?   @map("lot_size")
  tickSize Float? @map("tick_size")

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relation to market data
  marketData MarketData?

  // Indexes for performance
  @@index([symbol])
  @@index([exchange])
  @@index([instrumentType])
  @@index([expiryDate])
  @@index([strikePrice])
  @@index([symbol, expiryDate])
  @@index([symbol, instrumentType])
  @@map("instruments")
}

// Market Data Model for live prices
model MarketData {
  id            String @id @default(cuid())
  securityId    String @unique @map("security_id")
  symbol        String
  ltp           Float? // Last Traded Price
  change        Float?
  changePercent Float? @map("change_percent")
  volume        Int?
  openInterest  Int?   @map("open_interest")
  bid           Float?
  ask           Float?
  high          Float?
  low           Float?
  open          Float?
  close         Float?

  // Timestamps
  lastUpdateTime DateTime @default(now()) @map("last_update_time")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relation to instrument
  instrument Instrument @relation(fields: [securityId], references: [securityId])

  // Indexes for performance
  @@index([symbol])
  @@index([lastUpdateTime])
  @@map("market_data")
}
