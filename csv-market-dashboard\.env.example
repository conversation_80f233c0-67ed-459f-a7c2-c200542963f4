# CSV Market Dashboard Environment Variables

# Server Configuration
PORT=8080
NODE_ENV=development

# Dhan API Configuration (Required for real market data)
ACCESS_TOKEN=your_dhan_access_token_here
CLIENT_ID=your_dhan_client_id_here

# WebSocket Configuration
SUBSCRIPTION_TYPE=quote
MAX_INSTRUMENTS=25000
PREFERRED_EXCHANGE=NSE

# CSV File Configuration
CSV_FILE_PATH=../api-scrip-master-detailed (7).csv
CSV_CACHE_TTL=3600000

# Security
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_DIR=uploads

# Logging
LOG_LEVEL=info

# Performance
CACHE_TTL=300000
BATCH_SIZE=1000
UPDATE_INTERVAL=1000
