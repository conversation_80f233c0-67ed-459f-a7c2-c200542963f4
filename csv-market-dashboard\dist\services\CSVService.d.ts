import { Instrument, InstrumentFilter, SearchResult, CSVService as ICSVService } from '../types';
export declare class CSVService implements ICSVService {
    private instruments;
    private cache;
    private csvFilePath;
    private cacheTTL;
    private lastLoadTime;
    constructor(csvFilePath?: string, cacheTTL?: number);
    /**
     * Load instruments from CSV file
     */
    loadInstruments(): Promise<Instrument[]>;
    /**
     * Parse CSV file and return raw data
     */
    private parseCSVFile;
    /**
     * Process raw CSV data into normalized instruments
     */
    private processCSVData;
    /**
     * Normalize CSV row to Instrument object
     */
    private normalizeInstrument;
    /**
     * Get exchange segment from exchange ID and segment
     */
    private getExchangeSegment;
    /**
     * Parse number from string
     */
    private parseNumber;
    /**
     * Parse date from string
     */
    private parseDate;
    /**
     * Get instruments with filtering
     */
    getInstruments(filter?: InstrumentFilter): Promise<SearchResult>;
    /**
     * Apply filters to instruments
     */
    private applyFilter;
    /**
     * Search instruments by query
     */
    searchInstruments(query: string, limit?: number): Promise<Instrument[]>;
    /**
     * Get instrument by security ID
     */
    getInstrumentById(securityId: string): Promise<Instrument | null>;
    /**
     * Get all exchanges
     */
    getExchanges(): Promise<string[]>;
    /**
     * Get all instrument types
     */
    getInstrumentTypes(): Promise<string[]>;
    /**
     * Get NSE derivatives and index instruments for subscription
     * Filters: EXCH_ID = NSE, SEGMENT = D, INSTRUMENT = FUTIDX, OPTIDX, INDEX
     */
    getNSEDerivativesAndIndex(): Promise<Instrument[]>;
    /**
     * Get security IDs for NSE derivatives and index instruments
     */
    getNSEDerivativesSecurityIds(): Promise<string[]>;
    /**
     * Refresh cache
     */
    refreshCache(): Promise<void>;
    /**
     * Cache management
     */
    private getFromCache;
    private setCache;
    /**
     * Log instrument statistics
     */
    private logInstrumentStats;
}
export declare const csvService: CSVService;
//# sourceMappingURL=CSVService.d.ts.map