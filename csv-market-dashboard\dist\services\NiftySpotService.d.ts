/**
 * NIFTY Spot Service - Integration with dedicated spot calculator
 * Provides live NIFTY spot price data for the option chain
 */
import { EventEmitter } from 'events';
export interface NiftySpotData {
    symbol: string;
    securityId: string;
    ltp: number;
    previousClose: number;
    changePoints: number;
    changePercent: number;
    lastUpdateTime: Date | null;
    totalUpdates: number;
    isReady: boolean;
    formatted: {
        points: string;
        percent: string;
        color: string;
        isPositive: boolean;
    };
}
export interface NiftySpotStatus {
    isConnected: boolean;
    messageCount: number;
    runtime: string;
    updateRate: string;
    isDataReady: boolean;
}
/**
 * NIFTY Spot Service
 * Manages the dedicated NIFTY spot calculator and provides data to the frontend
 */
export declare class NiftySpotService extends EventEmitter {
    private calculator;
    private isInitialized;
    private currentData;
    private connectionStatus;
    constructor();
    /**
     * Initialize the NIFTY spot calculator
     */
    private initializeCalculator;
    /**
     * Connect to the NIFTY spot data feed
     */
    connect(): Promise<void>;
    /**
     * Disconnect from the NIFTY spot data feed
     */
    disconnect(): void;
    /**
     * Get current NIFTY spot data
     */
    getCurrentData(): NiftySpotData | null;
    /**
     * Get current connection status
     */
    getStatus(): NiftySpotStatus | null;
    /**
     * Check if the service is connected and has data
     */
    isReady(): boolean;
    /**
     * Get the current NIFTY LTP (Last Traded Price)
     */
    getCurrentLTP(): number;
    /**
     * Get formatted change data
     */
    getFormattedChange(): {
        points: string;
        percent: string;
        color: string;
        isPositive: boolean;
    } | null;
    /**
     * Display current status (for debugging)
     */
    displayStatus(): void;
    /**
     * Start the service (connect and begin data flow)
     */
    start(): Promise<void>;
    /**
     * Stop the service (disconnect and cleanup)
     */
    stop(): void;
}
export declare const niftySpotService: NiftySpotService;
export default niftySpotService;
//# sourceMappingURL=NiftySpotService.d.ts.map