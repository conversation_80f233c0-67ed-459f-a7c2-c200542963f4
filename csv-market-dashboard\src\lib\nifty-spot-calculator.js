// NIFTY Spot Price Calculator - Integrated with CSV Market Dashboard
console.log('📊 NIFTY SPOT CALCULATOR - CSV DASHBOARD INTEGRATION');
console.log('====================================================\n');

const WebSocket = require('ws');

// Dhan API Configuration
const DHAN_CONFIG = {
  clientId: '1100232369',
  accessToken: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUyMDQwNzEwLCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwMDIzMjM2OSJ9.9c5rfryVkvqxsOXKq8QRO7XxuR2-pENTmBq_mNN9l22A0N-CxOXxVZP8MT2ZtIUwN6AGiv9GeqDKfKlZpBeBZg'
};

const WEBSOCKET_URL = `wss://api-feed.dhan.co?version=2&token=${DHAN_CONFIG.accessToken}&clientId=${DHAN_CONFIG.clientId}&authType=2`;

// NIFTY Spot Configuration
const NIFTY_SPOT = {
  securityId: '13',
  exchange: 'IDX_I',
  symbol: 'NIFTY'
};

console.log('🎯 Target: NIFTY Spot Price Calculation');
console.log(`   Security ID: ${NIFTY_SPOT.securityId}`);
console.log(`   Exchange: ${NIFTY_SPOT.exchange}`);
console.log(`   Symbol: ${NIFTY_SPOT.symbol}\n`);

class NiftySpotCalculator {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.messageCount = 0;
    this.startTime = Date.now();
    
    // NIFTY market data
    this.niftyData = {
      symbol: NIFTY_SPOT.symbol,
      securityId: NIFTY_SPOT.securityId,
      ltp: 0,
      previousClose: 0,
      changePoints: 0,
      changePercent: 0,
      lastUpdateTime: null,
      totalUpdates: 0,
      isReady: false
    };
    
    // Callbacks for external integration
    this.onDataCallback = null;
    this.onStatusCallback = null;
  }

  // Binary parsing functions for Dhan feed
  parseResponseHeader(buffer) {
    if (buffer.length < 8) return null;
    return {
      feedResponseCode: buffer.readUInt8(0),
      messageLength: buffer.readUInt16LE(1),
      exchangeSegment: buffer.readUInt8(3),
      securityId: buffer.readUInt32LE(4)
    };
  }

  parseTickerPacket(buffer) {
    if (buffer.length < 16) return null;
    const header = this.parseResponseHeader(buffer);
    return {
      ...header,
      ltp: buffer.readFloatLE(8),
      ltt: buffer.readUInt32LE(12)
    };
  }

  parsePrevClosePacket(buffer) {
    if (buffer.length < 16) return null;
    const header = this.parseResponseHeader(buffer);
    return {
      ...header,
      prevClose: buffer.readFloatLE(8),
      prevOI: buffer.readUInt32LE(12)
    };
  }

  // Calculate change data
  calculateChange() {
    if (this.niftyData.ltp > 0 && this.niftyData.previousClose > 0) {
      this.niftyData.changePoints = this.niftyData.ltp - this.niftyData.previousClose;
      this.niftyData.changePercent = (this.niftyData.changePoints / this.niftyData.previousClose) * 100;
      this.niftyData.isReady = true;
      return true;
    }
    return false;
  }

  // Format change data for display
  formatChange() {
    const pointsSign = this.niftyData.changePoints >= 0 ? '+' : '';
    const percentSign = this.niftyData.changePercent >= 0 ? '+' : '';
    
    return {
      points: `${pointsSign}₹${this.niftyData.changePoints.toFixed(2)}`,
      percent: `${percentSign}${this.niftyData.changePercent.toFixed(3)}%`,
      color: this.niftyData.changePoints >= 0 ? '🟢' : '🔴',
      isPositive: this.niftyData.changePoints >= 0
    };
  }

  // Get current NIFTY data for external use
  getNiftyData() {
    return {
      ...this.niftyData,
      formatted: this.formatChange()
    };
  }

  // Set callback for data updates
  onData(callback) {
    this.onDataCallback = callback;
  }

  // Set callback for status updates
  onStatus(callback) {
    this.onStatusCallback = callback;
  }

  // Connect to Dhan WebSocket
  async connect() {
    return new Promise((resolve, reject) => {
      console.log('🔌 Connecting to Dhan market feed...');
      
      this.ws = new WebSocket(WEBSOCKET_URL);

      this.ws.on('open', () => {
        console.log('✅ Connected to Dhan WebSocket successfully');
        this.isConnected = true;
        this.onStatusCallback?.(true);
        
        // Subscribe to NIFTY spot
        this.subscribeToNifty();
        resolve();
      });

      this.ws.on('message', (data) => {
        this.handleMessage(data);
      });

      this.ws.on('close', (code, reason) => {
        console.log(`🔌 Connection closed: ${code} - ${reason}`);
        this.isConnected = false;
        this.onStatusCallback?.(false);
      });

      this.ws.on('error', (error) => {
        console.error('❌ WebSocket error:', error.message);
        this.onStatusCallback?.(false, error.message);
        reject(error);
      });
    });
  }

  // Subscribe to NIFTY spot data
  subscribeToNifty() {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.error('❌ WebSocket not connected');
      return;
    }

    // CORRECT: RequestCode 15 for NIFTY spot subscription
    const subscriptionRequest = {
      RequestCode: 15, // ✅ CORRECT: Quote data request (gives us Ticker + Previous Close)
      InstrumentCount: 1,
      InstrumentList: [
        {
          ExchangeSegment: NIFTY_SPOT.exchange,
          SecurityId: NIFTY_SPOT.securityId
        }
      ]
    };

    console.log('📡 Subscribing to NIFTY spot with RequestCode 15...');
    console.log(`   Security ID: ${NIFTY_SPOT.securityId} (NIFTY)`);
    console.log(`   Exchange: ${NIFTY_SPOT.exchange}`);
    console.log(`   Request: ${JSON.stringify(subscriptionRequest, null, 2)}`);

    this.ws.send(JSON.stringify(subscriptionRequest));
    console.log('✅ RequestCode 15 subscription sent - expecting Ticker (Code 2) + Previous Close (Code 6)');
  }

  // Handle incoming WebSocket messages
  handleMessage(data) {
    this.messageCount++;
    
    try {
      if (Buffer.isBuffer(data)) {
        const header = this.parseResponseHeader(data);
        
        // Only process NIFTY data (security ID 13)
        if (header && header.securityId == NIFTY_SPOT.securityId) {
          switch (header.feedResponseCode) {
            case 2: // Ticker Data - LTP updates
              const ticker = this.parseTickerPacket(data);
              if (ticker) {
                this.niftyData.ltp = ticker.ltp;
                this.niftyData.lastUpdateTime = new Date();
                this.niftyData.totalUpdates++;
                
                // Calculate change if we have previous close
                if (this.calculateChange()) {
                  console.log(`📈 NIFTY LTP: ₹${this.niftyData.ltp.toFixed(2)} | Change: ${this.formatChange().points} (${this.formatChange().percent})`);
                  
                  // Notify external callback
                  this.onDataCallback?.(this.getNiftyData());
                }
              }
              break;
              
            case 6: // Previous Close Data
              const prevClose = this.parsePrevClosePacket(data);
              if (prevClose) {
                this.niftyData.previousClose = prevClose.prevClose;
                console.log(`📊 NIFTY Previous Close: ₹${this.niftyData.previousClose.toFixed(2)}`);
                
                // Calculate change if we already have LTP
                if (this.niftyData.ltp > 0) {
                  this.calculateChange();
                  this.onDataCallback?.(this.getNiftyData());
                }
              }
              break;
          }
        }
      }
    } catch (error) {
      console.error('❌ Error processing message:', error);
    }
  }

  // Disconnect from WebSocket
  disconnect() {
    if (this.ws) {
      console.log('🔌 Disconnecting from Dhan WebSocket...');
      this.ws.close();
      this.ws = null;
      this.isConnected = false;
    }
  }

  // Get connection status
  getStatus() {
    const runtime = ((Date.now() - this.startTime) / 1000).toFixed(1);
    return {
      isConnected: this.isConnected,
      messageCount: this.messageCount,
      runtime: runtime,
      updateRate: (this.niftyData.totalUpdates / (runtime || 1)).toFixed(2),
      isDataReady: this.niftyData.isReady
    };
  }

  // Display current status
  displayStatus() {
    const status = this.getStatus();
    const change = this.formatChange();
    
    console.log('\n📊 NIFTY SPOT STATUS:');
    console.log(`   LTP: ₹${this.niftyData.ltp.toFixed(2)}`);
    console.log(`   Previous Close: ₹${this.niftyData.previousClose.toFixed(2)}`);
    console.log(`   Change: ${change.color} ${change.points} (${change.percent})`);
    console.log(`   Updates: ${this.niftyData.totalUpdates} | Rate: ${status.updateRate}/sec`);
    console.log(`   Status: ${status.isConnected ? '🟢 Connected' : '🔴 Disconnected'}`);
    console.log(`   Data Ready: ${status.isDataReady ? '✅ Yes' : '❌ No'}`);
  }
}

// Export for integration with CSV Market Dashboard
module.exports = { NiftySpotCalculator, NIFTY_SPOT, DHAN_CONFIG };
