// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// Market Instruments Model - Matching actual CSV database structure
model Instrument {
  SECURITY_ID               BigInt   @map("SECURITY_ID")
  SYMBOL_NAME               String?  @map("SYMBOL_NAME")
  EXCH_ID                   String?  @map("EXCH_ID")
  SEGMENT                   String?  @map("SEGMENT")
  INSTRUMENT_TYPE           String?  @map("INSTRUMENT_TYPE")
  STRIKE_PRICE              String?  @map("STRIKE_PRICE")
  SM_EXPIRY_DATE            String?  @map("SM_EXPIRY_DATE")
  OPTION_TYPE               String?  @map("OPTION_TYPE")
  LOT_SIZE                  BigInt?  @map("LOT_SIZE")
  TICK_SIZE                 String?  @map("TICK_SIZE")
  ISIN                      String?  @map("ISIN")
  INSTRUMENT                String?  @map("INSTRUMENT")
  UNDERLYING_SECURITY_ID    String?  @map("UNDERLYING_SECURITY_ID")
  UNDERLYING_SYMBOL         String?  @map("UNDERLYING_SYMBOL")
  DISPLAY_NAME              String?  @map("DISPLAY_NAME")
  SERIES                    String?  @map("SERIES")
  EXPIRY_FLAG               String?  @map("EXPIRY_FLAG")
  BRACKET_FLAG              String?  @map("BRACKET_FLAG")
  COVER_FLAG                String?  @map("COVER_FLAG")
  ASM_GSM_FLAG              String?  @map("ASM_GSM_FLAG")
  ASM_GSM_CATEGORY          String?  @map("ASM_GSM_CATEGORY")
  BUY_SELL_INDICATOR        String?  @map("BUY_SELL_INDICATOR")
  BUY_CO_MIN_MARGIN_PER     String?  @map("BUY_CO_MIN_MARGIN_PER")
  SELL_CO_MIN_MARGIN_PER    String?  @map("SELL_CO_MIN_MARGIN_PER")
  BUY_CO_SL_RANGE_MAX_PERC  String?  @map("BUY_CO_SL_RANGE_MAX_PERC")
  SELL_CO_SL_RANGE_MAX_PERC String?  @map("SELL_CO_SL_RANGE_MAX_PERC")
  BUY_CO_SL_RANGE_MIN_PERC  String?  @map("BUY_CO_SL_RANGE_MIN_PERC")
  SELL_CO_SL_RANGE_MIN_PERC String?  @map("SELL_CO_SL_RANGE_MIN_PERC")
  BUY_BO_MIN_MARGIN_PER     String?  @map("BUY_BO_MIN_MARGIN_PER")
  SELL_BO_MIN_MARGIN_PER    String?  @map("SELL_BO_MIN_MARGIN_PER")
  BUY_BO_SL_RANGE_MAX_PERC  String?  @map("BUY_BO_SL_RANGE_MAX_PERC")
  SELL_BO_SL_RANGE_MAX_PERC String?  @map("SELL_BO_SL_RANGE_MAX_PERC")
  BUY_BO_SL_RANGE_MIN_PERC  String?  @map("BUY_BO_SL_RANGE_MIN_PERC")
  SELL_BO_SL_MIN_RANGE      String?  @map("SELL_BO_SL_MIN_RANGE")
  BUY_BO_PROFIT_RANGE_MAX_PERC String? @map("BUY_BO_PROFIT_RANGE_MAX_PERC")
  SELL_BO_PROFIT_RANGE_MAX_PERC String? @map("SELL_BO_PROFIT_RANGE_MAX_PERC")
  BUY_BO_PROFIT_RANGE_MIN_PERC String? @map("BUY_BO_PROFIT_RANGE_MIN_PERC")
  SELL_BO_PROFIT_RANGE_MIN_PERC String? @map("SELL_BO_PROFIT_RANGE_MIN_PERC")
  MTF_LEVERAGE              String?  @map("MTF_LEVERAGE")

  // Indexes for performance
  @@index([SYMBOL_NAME])
  @@index([EXCH_ID])
  @@index([INSTRUMENT_TYPE])
  @@index([SM_EXPIRY_DATE])
  @@index([STRIKE_PRICE])
  @@index([SYMBOL_NAME, SM_EXPIRY_DATE])
  @@index([SYMBOL_NAME, INSTRUMENT_TYPE])
  @@id([SECURITY_ID])
  @@map("Instruments")
}

// Market Data Model for live prices (separate table, not used with CSV data)
model MarketData {
  id                String   @id @default(cuid())
  securityId        String   @unique @map("security_id")
  symbol            String
  ltp               Float?   // Last Traded Price
  change            Float?
  changePercent     Float?   @map("change_percent")
  volume            Int?
  openInterest      Int?     @map("open_interest")
  bid               Float?
  ask               Float?
  high              Float?
  low               Float?
  open              Float?
  close             Float?

  // Timestamps
  lastUpdateTime    DateTime @default(now()) @map("last_update_time")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  // Indexes for performance
  @@index([symbol])
  @@index([lastUpdateTime])
  @@map("market_data")
}

// NIFTY Expiry Dates Cache Model
model NiftyExpiry {
  id                String   @id @default(cuid())
  expiryDate        String   @unique @map("expiry_date")
  underlying        String   @default("NIFTY")
  segment           String   @default("IDX_I")
  securityId        String   @default("13") @map("security_id")

  // Metadata
  source            String   @default("dhan_api") // dhan_api, manual, etc.
  isActive          Boolean  @default(true) @map("is_active")

  // Timestamps
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")
  lastFetchedAt     DateTime @default(now()) @map("last_fetched_at")

  // Indexes for performance
  @@index([expiryDate])
  @@index([underlying])
  @@index([isActive])
  @@index([lastFetchedAt])
  @@map("nifty_expiries")
}
