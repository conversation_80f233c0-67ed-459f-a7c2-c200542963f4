# CSV Market Dashboard

A high-performance real-time market data dashboard that reads instruments from CSV files and subscribes to live market data feeds.

## Features

- 📊 **CSV-based Instrument Loading**: Reads market instruments from CSV files
- 🔄 **Real-time Market Data**: WebSocket-based live data streaming
- 🎯 **Smart Filtering**: Filter by exchange, instrument type, sector
- 🔍 **Advanced Search**: Search instruments by symbol, name, ISIN
- ⚡ **High Performance**: Optimized for handling large datasets
- 📱 **Responsive Design**: Modern React UI with Tailwind CSS
- 🛡️ **Type Safety**: Full TypeScript implementation
- 🚀 **Production Ready**: Built with Next.js and Express

## Quick Start

1. **Clone and Install**
   ```bash
   cd csv-market-dashboard
   npm install
   ```

2. **Setup Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your Dhan API credentials
   ```

3. **Place CSV File**
   ```bash
   # Copy your CSV file to the project root or update CSV_FILE_PATH in .env
   cp ../api-scrip-master-detailed\ \(7\).csv ./instruments.csv
   ```

4. **Start Development**
   ```bash
   npm run dev
   ```

5. **Access Dashboard**
   - Frontend: http://localhost:3000
   - WebSocket Server: ws://localhost:8080

## CSV File Format

The system expects a CSV file with the following columns:
- `EXCH_ID`: Exchange identifier (BSE, NSE, etc.)
- `SEGMENT`: Market segment
- `SECURITY_ID`: Unique security identifier
- `SYMBOL_NAME`: Trading symbol
- `DISPLAY_NAME`: Display name
- `INSTRUMENT`: Instrument type (EQUITY, FUTCUR, OPTCUR, etc.)
- `ISIN`: ISIN number
- `LOT_SIZE`: Lot size for trading

## Architecture

```
┌─────────────────────────────────────────┐
│           Frontend (Next.js)            │
│  ┌─────────────────────────────────────┐ │
│  │     Market Dashboard UI             │ │
│  │  - Real-time data display           │ │
│  │  - Filtering & Search               │ │
│  │  - Responsive design                │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
                    │
                    ▼
┌─────────────────────────────────────────┐
│        WebSocket Server (Express)       │
│  ┌─────────────────────────────────────┐ │
│  │     CSV Instrument Loader           │ │
│  │  - Parse CSV files                  │ │
│  │  - Cache instruments                │ │
│  │  - Filter & search                  │ │
│  └─────────────────────────────────────┘ │
│  ┌─────────────────────────────────────┐ │
│  │     Market Data Subscription        │ │
│  │  - Dhan WebSocket connection        │ │
│  │  - Real-time data streaming         │ │
│  │  - Data broadcasting                │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
                    │
                    ▼
┌─────────────────────────────────────────┐
│         Dhan Market Feed API            │
│         (wss://api-feed.dhan.co)        │
└─────────────────────────────────────────┘
```

## Scripts

- `npm run dev` - Start development (frontend + backend)
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run csv:parse` - Parse and validate CSV file
- `npm run csv:test` - Test CSV instrument loading

## Environment Variables

See `.env.example` for all available configuration options.

## Performance Features

- **Efficient CSV Parsing**: Streaming CSV parser for large files
- **Smart Caching**: In-memory caching with TTL
- **Batch Processing**: Optimized batch operations
- **Connection Pooling**: Efficient WebSocket management
- **Memory Optimization**: Garbage collection friendly

## License

MIT License - see LICENSE file for details.
