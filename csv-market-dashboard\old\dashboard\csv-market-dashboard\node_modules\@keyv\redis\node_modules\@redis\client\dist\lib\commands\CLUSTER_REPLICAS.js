"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.transformReply = exports.transformArguments = void 0;
function transformArguments(nodeId) {
    return ['CLUSTER', 'REPLICAS', nodeId];
}
exports.transformArguments = transformArguments;
var CLUSTER_NODES_1 = require("./CLUSTER_NODES");
Object.defineProperty(exports, "transformReply", { enumerable: true, get: function () { return CLUSTER_NODES_1.transformReply; } });
