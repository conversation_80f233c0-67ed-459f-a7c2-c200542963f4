/**
 * NIFTY Expiry Service - Manages NIFTY expiry dates with database caching
 * Fetches from Dhan API and stores in database to reduce memory load
 */

import { PrismaClient } from '../../generated/prisma';
import { getDhanAPIService } from './DhanAPIService';

export interface NiftyExpiryData {
  expiryDate: string;
  underlying: string;
  segment: string;
  securityId: string;
  source: string;
  isActive: boolean;
  lastFetchedAt: Date;
}

export class NiftyExpiryService {
  private prisma: PrismaClient | null = null;
  private dbConnected = false;
  private cacheTimeout = 24 * 60 * 60 * 1000; // 24 hours

  constructor() {
    this.initializeDatabase();
  }

  /**
   * Initialize database connection
   */
  private async initializeDatabase() {
    try {
      this.prisma = new PrismaClient();
      await this.prisma.$connect();
      this.dbConnected = true;
      console.log('✅ NiftyExpiryService: Database connected');
    } catch (error) {
      console.warn('⚠️ NiftyExpiryService: Database connection failed, using API only:', error);
      this.dbConnected = false;
    }
  }

  /**
   * Get NIFTY expiry dates with caching
   */
  async getNiftyExpiryDates(): Promise<string[]> {
    try {
      // Try to get from database cache first
      if (this.dbConnected && this.prisma) {
        const cachedExpiries = await this.getCachedExpiries();
        if (cachedExpiries.length > 0) {
          console.log(`📅 Using cached NIFTY expiry dates: ${cachedExpiries.length} dates`);
          return cachedExpiries;
        }
      }

      // Fetch fresh data from API
      console.log('🔄 Fetching fresh NIFTY expiry dates from API...');
      const freshExpiries = await this.fetchFromAPI();

      // Store in database for future use
      if (this.dbConnected && this.prisma && freshExpiries.length > 0) {
        await this.storeExpiries(freshExpiries);
      }

      return freshExpiries;

    } catch (error) {
      console.error('❌ Error getting NIFTY expiry dates:', error);
      
      // Fallback: try to get any cached data (even if expired)
      if (this.dbConnected && this.prisma) {
        const fallbackExpiries = await this.getFallbackExpiries();
        if (fallbackExpiries.length > 0) {
          console.log('⚠️ Using fallback cached expiry dates');
          return fallbackExpiries;
        }
      }

      // Last resort: return mock data
      return this.getMockExpiries();
    }
  }

  /**
   * Get cached expiry dates from database
   */
  private async getCachedExpiries(): Promise<string[]> {
    if (!this.prisma) return [];

    try {
      const cutoffTime = new Date(Date.now() - this.cacheTimeout);
      
      const expiries = await this.prisma.niftyExpiry.findMany({
        where: {
          underlying: 'NIFTY',
          isActive: true,
          lastFetchedAt: {
            gte: cutoffTime
          }
        },
        select: {
          expiryDate: true
        },
        orderBy: {
          expiryDate: 'asc'
        }
      });

      return expiries.map(e => e.expiryDate);

    } catch (error) {
      console.warn('⚠️ Error getting cached expiries:', error);
      return [];
    }
  }

  /**
   * Get fallback expiry dates (even if expired)
   */
  private async getFallbackExpiries(): Promise<string[]> {
    if (!this.prisma) return [];

    try {
      const expiries = await this.prisma.niftyExpiry.findMany({
        where: {
          underlying: 'NIFTY',
          isActive: true
        },
        select: {
          expiryDate: true
        },
        orderBy: {
          expiryDate: 'asc'
        },
        take: 20 // Limit to recent expiries
      });

      return expiries.map(e => e.expiryDate);

    } catch (error) {
      console.warn('⚠️ Error getting fallback expiries:', error);
      return [];
    }
  }

  /**
   * Fetch expiry dates from Dhan API
   */
  private async fetchFromAPI(): Promise<string[]> {
    const dhanAPI = getDhanAPIService();
    
    // NIFTY parameters for Dhan API
    const NIFTY_SCRIP = 13;  // NIFTY security ID
    const NIFTY_SEGMENT = 'IDX_I';  // Index segment

    const expiryDates = await dhanAPI.getExpiryDates(NIFTY_SCRIP, NIFTY_SEGMENT);
    console.log(`✅ Fetched ${expiryDates.length} NIFTY expiry dates from API`);
    
    return expiryDates;
  }

  /**
   * Store expiry dates in database
   */
  private async storeExpiries(expiryDates: string[]): Promise<void> {
    if (!this.prisma) return;

    try {
      const now = new Date();

      // First, mark all existing expiries as inactive
      await this.prisma.niftyExpiry.updateMany({
        where: {
          underlying: 'NIFTY'
        },
        data: {
          isActive: false
        }
      });

      // Insert or update new expiry dates
      for (const expiryDate of expiryDates) {
        await this.prisma.niftyExpiry.upsert({
          where: {
            expiryDate: expiryDate
          },
          update: {
            isActive: true,
            lastFetchedAt: now,
            source: 'dhan_api'
          },
          create: {
            expiryDate: expiryDate,
            underlying: 'NIFTY',
            segment: 'IDX_I',
            securityId: '13',
            source: 'dhan_api',
            isActive: true,
            lastFetchedAt: now
          }
        });
      }

      console.log(`💾 Stored ${expiryDates.length} NIFTY expiry dates in database`);

    } catch (error) {
      console.error('❌ Error storing expiry dates:', error);
    }
  }

  /**
   * Get mock expiry dates as fallback
   */
  private getMockExpiries(): string[] {
    const today = new Date();
    const expiries: string[] = [];

    // Generate weekly expiries for next 8 weeks
    for (let i = 0; i < 8; i++) {
      const expiryDate = new Date(today);
      expiryDate.setDate(today.getDate() + (i * 7));
      
      // Set to Thursday (NIFTY weekly expiry day)
      const dayOfWeek = expiryDate.getDay();
      const daysToThursday = (4 - dayOfWeek + 7) % 7;
      expiryDate.setDate(expiryDate.getDate() + daysToThursday);
      
      expiries.push(expiryDate.toISOString().split('T')[0]);
    }

    // Add monthly expiries
    for (let i = 1; i <= 6; i++) {
      const monthlyExpiry = new Date(today.getFullYear(), today.getMonth() + i, 1);
      
      // Last Thursday of the month
      monthlyExpiry.setMonth(monthlyExpiry.getMonth() + 1, 0); // Last day of month
      const lastDay = monthlyExpiry.getDay();
      const daysToLastThursday = (lastDay + 3) % 7;
      monthlyExpiry.setDate(monthlyExpiry.getDate() - daysToLastThursday);
      
      expiries.push(monthlyExpiry.toISOString().split('T')[0]);
    }

    console.log('⚠️ Using mock NIFTY expiry dates');
    return expiries.sort();
  }

  /**
   * Force refresh expiry dates from API
   */
  async refreshExpiries(): Promise<string[]> {
    console.log('🔄 Force refreshing NIFTY expiry dates...');
    
    try {
      const freshExpiries = await this.fetchFromAPI();
      
      if (this.dbConnected && this.prisma && freshExpiries.length > 0) {
        await this.storeExpiries(freshExpiries);
      }
      
      return freshExpiries;
      
    } catch (error) {
      console.error('❌ Error refreshing expiry dates:', error);
      throw error;
    }
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      dbConnected: this.dbConnected,
      cacheTimeout: this.cacheTimeout
    };
  }

  /**
   * Disconnect from database
   */
  async disconnect() {
    if (this.prisma) {
      await this.prisma.$disconnect();
      this.dbConnected = false;
    }
  }
}

// Export singleton instance
export const niftyExpiryService = new NiftyExpiryService();
export default niftyExpiryService;
