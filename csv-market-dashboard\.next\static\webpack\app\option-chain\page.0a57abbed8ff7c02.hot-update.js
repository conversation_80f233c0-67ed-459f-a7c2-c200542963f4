"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/option-chain/page",{

/***/ "(app-pages-browser)/./src/components/OptionChain.tsx":
/*!****************************************!*\
  !*** ./src/components/OptionChain.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OptionChain; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/data-cache */ \"(app-pages-browser)/./src/lib/data-cache.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OptionChain(param) {\n    let { marketData } = param;\n    _s();\n    const [expiryData, setExpiryData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedExpiry, setSelectedExpiry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [optionChain, setOptionChain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [niftySpotPrice, setNiftySpotPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [cacheLoaded, setCacheLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load cached data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadCachedData = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDCD6 OptionChain: Loading cached data...\");\n                // Load cached option chain data\n                const cachedOptionChain = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.getCachedStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.OPTION_CHAIN);\n                if (cachedOptionChain && typeof cachedOptionChain === \"object\" && \"rows\" in cachedOptionChain) {\n                    setOptionChain(cachedOptionChain);\n                    console.log(\"✅ OptionChain: Loaded option chain from cache\");\n                }\n                // Load cached expiry data\n                const cachedExpiryData = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.getCachedStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.EXPIRY_DATES);\n                if (cachedExpiryData && typeof cachedExpiryData === \"object\" && \"expiries\" in cachedExpiryData && Array.isArray(cachedExpiryData.expiries)) {\n                    setExpiryData(cachedExpiryData);\n                    if (cachedExpiryData.expiries.length > 0) {\n                        setSelectedExpiry(cachedExpiryData.expiries[0]);\n                    }\n                    console.log(\"✅ OptionChain: Loaded expiry data from cache\");\n                }\n                // Load cached NIFTY spot price\n                const cachedNiftySpot = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.getCachedMarketData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.NIFTY_SPOT);\n                if (cachedNiftySpot && typeof cachedNiftySpot === \"object\" && \"ltp\" in cachedNiftySpot && typeof cachedNiftySpot.ltp === \"number\") {\n                    setNiftySpotPrice(cachedNiftySpot.ltp);\n                    console.log(\"✅ OptionChain: Loaded NIFTY spot from cache:\", cachedNiftySpot.ltp);\n                }\n                setCacheLoaded(true);\n            } catch (error) {\n                console.error(\"❌ OptionChain: Failed to load cached data:\", error);\n                setCacheLoaded(true);\n            }\n        };\n        loadCachedData();\n    }, []);\n    // ✅ ENHANCED: Get NIFTY spot price from subscribed market data with caching\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // ✅ FIXED: Direct lookup for NIFTY spot (security ID 13) - should be subscribed with IDX_I exchange\n        const niftyData = marketData.get(\"13\");\n        if (niftyData && niftyData.ltp > 0) {\n            setNiftySpotPrice(niftyData.ltp);\n            // Cache the NIFTY spot price\n            _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.cacheMarketData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.NIFTY_SPOT, niftyData);\n            console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Updated:\", niftyData.ltp, \"from security ID 13 (IDX_I)\");\n            return;\n        }\n        // ✅ ENHANCED: Fallback search for any NIFTY index data with better logging\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol === \"NIFTY\" && data.ltp > 0) {\n                setNiftySpotPrice(data.ltp);\n                // Cache the NIFTY spot price\n                _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.cacheMarketData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.NIFTY_SPOT, data);\n                console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Found:\", data.ltp, \"from security:\", securityId, \"exchange:\", data.exchange);\n                return;\n            }\n        }\n        // ✅ FIXED: Only use mock price if absolutely no real data is available and no cached data\n        if (niftySpotPrice === 0 && cacheLoaded) {\n            const mockPrice = 24850;\n            setNiftySpotPrice(mockPrice);\n            console.warn(\"[SPOT] ⚠️ Using mock NIFTY Spot Price:\", mockPrice, \"(INDEX data not available - check subscription)\");\n        }\n    }, [\n        marketData,\n        cacheLoaded,\n        niftySpotPrice\n    ]);\n    // Fetch expiry dates on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchExpiryDates();\n    }, []);\n    // Build option chain when expiry is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedExpiry && niftySpotPrice > 0) {\n            buildOptionChain();\n        }\n    }, [\n        selectedExpiry,\n        niftySpotPrice,\n        marketData,\n        buildOptionChain\n    ]);\n    const fetchExpiryDates = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Check cache first\n            const cachedExpiryData = await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.getCachedStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.EXPIRY_DATES);\n            if (cachedExpiryData && typeof cachedExpiryData === \"object\" && \"expiries\" in cachedExpiryData && Array.isArray(cachedExpiryData.expiries)) {\n                console.log(\"✅ OptionChain: Using cached expiry data\");\n                setExpiryData(cachedExpiryData);\n                if (cachedExpiryData.expiries.length > 0) {\n                    setSelectedExpiry(cachedExpiryData.expiries[0]);\n                }\n                setLoading(false);\n                return;\n            }\n            console.log(\"\\uD83C\\uDF10 OptionChain: Fetching fresh expiry data from API\");\n            const response = await fetch(\"/api/nifty-expiry\");\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch expiry dates: \".concat(response.statusText));\n            }\n            const result = await response.json();\n            if (result.success) {\n                setExpiryData(result.data);\n                // Cache the expiry data\n                await _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.cacheStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.EXPIRY_DATES, result.data);\n                console.log(\"\\uD83D\\uDCBE OptionChain: Cached expiry data\");\n                // Auto-select first expiry\n                if (result.data.expiries.length > 0) {\n                    setSelectedExpiry(result.data.expiries[0]);\n                }\n            } else {\n                throw new Error(result.message || \"Failed to fetch expiry dates\");\n            }\n        } catch (err) {\n            console.error(\"❌ OptionChain: Error fetching expiry dates:\", err);\n            setError(err instanceof Error ? err.message : \"Unknown error\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const buildOptionChain = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!selectedExpiry || niftySpotPrice <= 0) return;\n        console.log(\"\\uD83D\\uDD17 Building option chain for expiry:\", selectedExpiry, \"spot:\", niftySpotPrice);\n        // Generate strike prices around spot price\n        const strikes = generateStrikes(niftySpotPrice);\n        // Build option chain rows\n        const rows = strikes.map((strike)=>{\n            const callData = findOptionData(strike, \"CE\");\n            const putData = findOptionData(strike, \"PE\");\n            return {\n                strikePrice: strike,\n                call: callData,\n                put: putData\n            };\n        });\n        const optionChainData = {\n            underlying: \"NIFTY\",\n            spotPrice: niftySpotPrice,\n            expiry: selectedExpiry,\n            rows,\n            timestamp: Date.now()\n        };\n        setOptionChain(optionChainData);\n        // Cache the option chain data\n        _lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.cacheHelpers.cacheStaticData(_lib_data_cache__WEBPACK_IMPORTED_MODULE_3__.MARKET_DATA_CACHE_KEYS.OPTION_CHAIN, optionChainData);\n        console.log(\"\\uD83D\\uDCBE OptionChain: Cached option chain data for\", selectedExpiry);\n    }, [\n        selectedExpiry,\n        niftySpotPrice,\n        marketData\n    ]);\n    const generateStrikes = (spotPrice)=>{\n        // First, get all available strikes from market data for the selected expiry\n        const availableStrikes = new Set();\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol.includes(\"NIFTY\") && data.symbol.includes(formatExpiryForSymbol(selectedExpiry))) {\n                // Extract strike from symbol (e.g., NIFTY-Jun2025-24850-CE)\n                const symbolParts = data.symbol.split(\"-\");\n                if (symbolParts.length >= 4) {\n                    const strike = parseFloat(symbolParts[2]);\n                    if (!isNaN(strike)) {\n                        availableStrikes.add(strike);\n                    }\n                }\n            }\n        }\n        if (availableStrikes.size === 0) {\n            // Fallback: generate strikes around spot price\n            const strikes = [];\n            const baseStrike = Math.round(spotPrice / 50) * 50;\n            for(let i = -10; i <= 10; i++){\n                strikes.push(baseStrike + i * 50);\n            }\n            return strikes.sort((a, b)=>a - b);\n        }\n        // Convert to sorted array\n        const allStrikes = Array.from(availableStrikes).sort((a, b)=>a - b);\n        // Find ATM strike (closest to spot price)\n        const atmStrike = allStrikes.reduce((prev, curr)=>Math.abs(curr - spotPrice) < Math.abs(prev - spotPrice) ? curr : prev);\n        console.log(\"[STRIKE] \\uD83C\\uDFAF ATM Strike identified: \".concat(atmStrike, \" (Spot: \").concat(spotPrice, \")\"));\n        console.log(\"[STRIKE] \\uD83D\\uDCCA Available strikes: \".concat(allStrikes.length));\n        // ✅ ENHANCED: Select more strikes around ATM (±12 strikes for better coverage)\n        const atmIndex = allStrikes.indexOf(atmStrike);\n        const selectedStrikes = [\n            ...allStrikes.slice(Math.max(0, atmIndex - 12), atmIndex),\n            atmStrike,\n            ...allStrikes.slice(atmIndex + 1, atmIndex + 13)\n        ];\n        console.log(\"[STRIKE] ✅ Selected \".concat(selectedStrikes.length, \" strikes around ATM:\"), selectedStrikes);\n        return selectedStrikes;\n    };\n    const findOptionData = (strike, optionType)=>{\n        // ✅ FIXED: Match by exact expiry date from CSV SM_EXPIRY_DATE column\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            // Check if this is a NIFTY option with matching criteria\n            if (data.symbol.includes(\"NIFTY-\") && // Exact NIFTY options (not BANKNIFTY, etc.)\n            data.symbol.includes(\"-\".concat(strike, \"-\").concat(optionType)) && data.expiryDate === selectedExpiry) {\n                var _data_marketDepth_, _data_marketDepth, _data_marketDepth_1, _data_marketDepth1, _data_marketDepth_2, _data_marketDepth2, _data_marketDepth_3, _data_marketDepth3;\n                console.log(\"[OPTION] ✅ Found \".concat(optionType, \" \").concat(strike, \": \").concat(data.symbol, \" (Expiry: \").concat(data.expiryDate, \")\"));\n                return {\n                    securityId,\n                    symbol: data.symbol,\n                    exchange: data.exchange,\n                    strikePrice: strike,\n                    optionType,\n                    expiryDate: selectedExpiry,\n                    ltp: data.ltp || 0,\n                    change: data.change || 0,\n                    changePercent: data.changePercent || 0,\n                    volume: data.volume || 0,\n                    openInterest: data.openInterest,\n                    bid: ((_data_marketDepth = data.marketDepth) === null || _data_marketDepth === void 0 ? void 0 : (_data_marketDepth_ = _data_marketDepth[0]) === null || _data_marketDepth_ === void 0 ? void 0 : _data_marketDepth_.bidPrice) || data.bid,\n                    ask: ((_data_marketDepth1 = data.marketDepth) === null || _data_marketDepth1 === void 0 ? void 0 : (_data_marketDepth_1 = _data_marketDepth1[0]) === null || _data_marketDepth_1 === void 0 ? void 0 : _data_marketDepth_1.askPrice) || data.ask,\n                    bidQty: ((_data_marketDepth2 = data.marketDepth) === null || _data_marketDepth2 === void 0 ? void 0 : (_data_marketDepth_2 = _data_marketDepth2[0]) === null || _data_marketDepth_2 === void 0 ? void 0 : _data_marketDepth_2.bidQty) || data.bidQty,\n                    askQty: ((_data_marketDepth3 = data.marketDepth) === null || _data_marketDepth3 === void 0 ? void 0 : (_data_marketDepth_3 = _data_marketDepth3[0]) === null || _data_marketDepth_3 === void 0 ? void 0 : _data_marketDepth_3.askQty) || data.askQty,\n                    high: data.high,\n                    low: data.low,\n                    open: data.open,\n                    close: data.close,\n                    timestamp: data.timestamp\n                };\n            }\n        }\n        // Debug: Log when option not found\n        if (true) {\n            console.log(\"[OPTION] ❌ Not found \".concat(optionType, \" \").concat(strike, \" for expiry \").concat(selectedExpiry));\n            // Show available options for debugging\n            const availableOptions = Array.from(marketData.values()).filter((data)=>data.symbol.includes(\"NIFTY-\") && data.symbol.includes(\"-\".concat(optionType))).slice(0, 5);\n            console.log(\"[DEBUG] Available \".concat(optionType, \" options:\"), availableOptions.map((opt)=>\"\".concat(opt.symbol, \" (Expiry: \").concat(opt.expiryDate, \")\")));\n        }\n        return null;\n    };\n    const formatExpiryForSymbol = (expiry)=>{\n        // Convert YYYY-MM-DD to format used in symbols (e.g., Jun2025)\n        const date = new Date(expiry);\n        const months = [\n            \"Jan\",\n            \"Feb\",\n            \"Mar\",\n            \"Apr\",\n            \"May\",\n            \"Jun\",\n            \"Jul\",\n            \"Aug\",\n            \"Sep\",\n            \"Oct\",\n            \"Nov\",\n            \"Dec\"\n        ];\n        return \"\".concat(months[date.getMonth()]).concat(date.getFullYear());\n    };\n    // Use centralized formatters for consistency\n    const formatPrice = _lib_utils__WEBPACK_IMPORTED_MODULE_2__.MarketFormatters.price;\n    const formatNumber = _lib_utils__WEBPACK_IMPORTED_MODULE_2__.MarketFormatters.number;\n    // Use centralized formatters for consistency\n    const getChangeColor = (change)=>{\n        if (!change) return \"text-gray-400\";\n        return change > 0 ? \"text-green-400\" : change < 0 ? \"text-red-400\" : \"text-gray-400\";\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading option chain...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 312,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 311,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-red-800 font-medium\",\n                    children: \"Error Loading Option Chain\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 text-sm mt-1\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: fetchExpiryDates,\n                    className: \"mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700\",\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 322,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Options\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md\",\n                                            children: \"\\uD83D\\uDD0D NIFTY\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Strategy builder\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Class\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Volatility\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"By expiration | by strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: formatPrice(niftySpotPrice)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this),\n                                        niftySpotPrice === 24850 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"(Mock)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this),\n            expiryData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 overflow-x-auto pb-2\",\n                        children: expiryData.expiries.slice(0, 15).map((expiry, index)=>{\n                            const date = new Date(expiry);\n                            const isSelected = expiry === selectedExpiry;\n                            const isToday = date.toDateString() === new Date().toDateString();\n                            const isCurrentWeek = Math.abs(date.getTime() - new Date().getTime()) < 7 * 24 * 60 * 60 * 1000;\n                            // Enhanced date formatting\n                            const currentYear = new Date().getFullYear();\n                            const expiryYear = date.getFullYear();\n                            const monthShort = date.toLocaleDateString(\"en-US\", {\n                                month: \"short\"\n                            });\n                            const day = date.getDate();\n                            // Show year if different from current year\n                            const showYear = expiryYear !== currentYear;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedExpiry(expiry),\n                                className: \"flex-shrink-0 px-3 py-2 text-xs font-medium transition-colors border \".concat(isSelected ? \"bg-black text-white border-black\" : isToday ? \"bg-orange-500 text-white border-orange-500\" : isCurrentWeek ? \"bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200\" : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center min-w-[40px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-normal text-gray-600\",\n                                            children: [\n                                                monthShort,\n                                                showYear && \" \".concat(expiryYear.toString().slice(-2))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-bold text-sm\",\n                                            children: day\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 19\n                                }, this)\n                            }, expiry, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 11\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-xs text-gray-500 bg-gray-100 p-3 rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Selected Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        selectedExpiry\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" ₹\",\n                                        niftySpotPrice,\n                                        \" \",\n                                        niftySpotPrice === 24850 ? \"(Mock)\" : \"(Live)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Available Strikes:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        (optionChain === null || optionChain === void 0 ? void 0 : optionChain.rows.length) || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Market Data Size:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        marketData.size,\n                                        \" instruments\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Options:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\")).length,\n                                        \" found\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Options with Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\") && d.expiryDate).length,\n                                        \" matched\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 427,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 380,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-50 border-b border-gray-200 text-xs font-medium text-gray-700 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 font-bold\",\n                                    children: \"Calls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold\",\n                                    children: \"Strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-600 font-bold\",\n                                    children: \"Puts\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-100 border-b border-gray-200 text-xs font-medium text-gray-600 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1 border-r border-gray-200\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-2 px-1 border-r border-gray-200 font-bold\",\n                                children: \"Strike\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-100\",\n                        children: optionChain.rows.map((row, index)=>{\n                            var _row_call, _row_call1, _row_call2, _row_call3, _row_call4, _row_call5, _row_call6, _row_put, _row_put1, _row_put2, _row_put3, _row_put4, _row_put5, _row_put6;\n                            const isATM = Math.abs(row.strikePrice - niftySpotPrice) <= 50;\n                            const isITM_Call = row.strikePrice < niftySpotPrice;\n                            const isITM_Put = row.strikePrice > niftySpotPrice;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex hover:bg-gray-50 transition-colors \".concat(isATM ? \"bg-yellow-50\" : index % 2 === 0 ? \"bg-white\" : \"bg-gray-25\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_call = row.call) === null || _row_call === void 0 ? void 0 : _row_call.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_call1 = row.call) === null || _row_call1 === void 0 ? void 0 : _row_call1.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice((_row_call2 = row.call) === null || _row_call2 === void 0 ? void 0 : _row_call2.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice((_row_call3 = row.call) === null || _row_call3 === void 0 ? void 0 : _row_call3.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-medium \".concat(getChangeColor((_row_call4 = row.call) === null || _row_call4 === void 0 ? void 0 : _row_call4.change)),\n                                        children: ((_row_call5 = row.call) === null || _row_call5 === void 0 ? void 0 : _row_call5.change) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.call.change > 0 ? \"+\" : \"\",\n                                                row.call.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.call.changePercent > 0 ? \"+\" : \"\",\n                                                        row.call.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 \".concat(isITM_Call ? \"text-green-600\" : \"text-gray-700\"),\n                                        children: formatPrice((_row_call6 = row.call) === null || _row_call6 === void 0 ? void 0 : _row_call6.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 \".concat(isATM ? \"bg-yellow-100 text-yellow-800\" : \"text-gray-900\"),\n                                        children: row.strikePrice\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-bold \".concat(isITM_Put ? \"text-red-600\" : \"text-gray-700\"),\n                                        children: formatPrice((_row_put = row.put) === null || _row_put === void 0 ? void 0 : _row_put.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-medium \".concat(getChangeColor((_row_put1 = row.put) === null || _row_put1 === void 0 ? void 0 : _row_put1.change)),\n                                        children: ((_row_put2 = row.put) === null || _row_put2 === void 0 ? void 0 : _row_put2.change) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.put.change > 0 ? \"+\" : \"\",\n                                                row.put.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.put.changePercent > 0 ? \"+\" : \"\",\n                                                        row.put.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice((_row_put3 = row.put) === null || _row_put3 === void 0 ? void 0 : _row_put3.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice((_row_put4 = row.put) === null || _row_put4 === void 0 ? void 0 : _row_put4.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_put5 = row.put) === null || _row_put5 === void 0 ? void 0 : _row_put5.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_put6 = row.put) === null || _row_put6 === void 0 ? void 0 : _row_put6.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, row.strikePrice, true, {\n                                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 482,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 442,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 border-t border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Show all\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Showing \",\n                                        optionChain.rows.length,\n                                        \" strikes around ATM\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 570,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"Last updated: \",\n                                new Date(optionChain.timestamp).toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 575,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 569,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 568,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n        lineNumber: 336,\n        columnNumber: 5\n    }, this);\n}\n_s(OptionChain, \"naDbbm5DhU4Xmmuc/H1Kior45rM=\");\n_c = OptionChain;\nvar _c;\n$RefreshReg$(_c, \"OptionChain\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OptionChain.tsx\n"));

/***/ })

});