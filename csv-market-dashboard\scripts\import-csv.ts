#!/usr/bin/env ts-node

import { PrismaClient } from '../generated/prisma';
import * as fs from 'fs';
import * as path from 'path';
import * as csv from 'csv-parser';

const prisma = new PrismaClient();

interface CSVRow {
  exchange: string;
  exchangeCode: string;
  securityId: string;
  isin: string;
  instrumentType: string;
  underlyingToken: string;
  symbol: string;
  tradingSymbol: string;
  name: string;
  segment: string;
  series: string;
  lotSize: string;
  expiryDate: string;
  strikePrice: string;
  optionType: string;
  tickSize: string;
  [key: string]: string;
}

interface InstrumentData {
  securityId: string;
  symbol: string;
  exchange: string;
  exchangeCode: number;
  segment: string;
  instrumentType: string;
  strikePrice?: number;
  expiryDate?: string;
  optionType?: string;
  lotSize?: number;
  tickSize?: number;
}

async function parseCSVFile(filePath: string): Promise<InstrumentData[]> {
  return new Promise((resolve, reject) => {
    const results: InstrumentData[] = [];
    const stream = fs.createReadStream(filePath);

    stream
      .pipe(csv())
      .on('data', (row: CSVRow) => {
        try {
          // Parse the CSV row into our instrument format
          const instrument: InstrumentData = {
            securityId: row.securityId || '',
            symbol: row.symbol || '',
            exchange: row.exchange || '',
            exchangeCode: parseInt(row.exchangeCode) || 0,
            segment: row.segment || '',
            instrumentType: row.instrumentType || '',
          };

          // Parse optional fields
          if (row.strikePrice && row.strikePrice !== '' && !isNaN(parseFloat(row.strikePrice))) {
            instrument.strikePrice = parseFloat(row.strikePrice);
          }

          if (row.expiryDate && row.expiryDate !== '' && row.expiryDate !== '1979-12-31') {
            instrument.expiryDate = row.expiryDate;
          }

          if (row.optionType && row.optionType !== '' && row.optionType !== 'XX') {
            instrument.optionType = row.optionType;
          }

          if (row.lotSize && row.lotSize !== '' && !isNaN(parseInt(row.lotSize))) {
            instrument.lotSize = parseInt(row.lotSize);
          }

          if (row.tickSize && row.tickSize !== '' && !isNaN(parseFloat(row.tickSize))) {
            instrument.tickSize = parseFloat(row.tickSize);
          }

          // Only add valid instruments
          if (instrument.securityId && instrument.symbol && instrument.exchange) {
            results.push(instrument);
          }
        } catch (error) {
          console.warn('⚠️ Skipping invalid row:', error);
        }
      })
      .on('end', () => {
        console.log(`✅ Parsed ${results.length} instruments from CSV`);
        resolve(results);
      })
      .on('error', (error) => {
        console.error('❌ Error reading CSV:', error);
        reject(error);
      });
  });
}

async function importInstruments(instruments: InstrumentData[]) {
  console.log(`🚀 Starting import of ${instruments.length} instruments...`);
  
  const batchSize = 1000;
  let imported = 0;
  let skipped = 0;

  for (let i = 0; i < instruments.length; i += batchSize) {
    const batch = instruments.slice(i, i + batchSize);
    
    try {
      // Use upsert to handle duplicates
      const promises = batch.map(instrument => 
        prisma.instrument.upsert({
          where: { securityId: instrument.securityId },
          update: instrument,
          create: instrument,
        })
      );

      await Promise.all(promises);
      imported += batch.length;
      
      console.log(`📊 Imported batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(instruments.length / batchSize)} (${imported}/${instruments.length})`);
    } catch (error) {
      console.error(`❌ Error importing batch ${Math.floor(i / batchSize) + 1}:`, error);
      skipped += batch.length;
    }
  }

  console.log(`✅ Import completed: ${imported} imported, ${skipped} skipped`);
}

async function main() {
  try {
    console.log('🔄 Starting CSV import process...');
    
    // Check if CSV file exists
    const csvPath = path.join(process.cwd(), 'instruments.csv');
    if (!fs.existsSync(csvPath)) {
      throw new Error(`CSV file not found: ${csvPath}`);
    }

    console.log(`📁 Reading CSV file: ${csvPath}`);
    
    // Parse CSV file
    const instruments = await parseCSVFile(csvPath);
    
    if (instruments.length === 0) {
      throw new Error('No valid instruments found in CSV file');
    }

    // Clear existing data (optional)
    console.log('🗑️ Clearing existing instruments...');
    await prisma.instrument.deleteMany();
    
    // Import instruments
    await importInstruments(instruments);
    
    // Show statistics
    const totalCount = await prisma.instrument.count();
    const niftyOptions = await prisma.instrument.count({
      where: {
        symbol: { contains: 'NIFTY' },
        instrumentType: 'OPTIDX'
      }
    });
    
    console.log('\n📊 Import Statistics:');
    console.log(`   Total instruments: ${totalCount}`);
    console.log(`   NIFTY options: ${niftyOptions}`);
    
    console.log('\n🎉 CSV import completed successfully!');
    
  } catch (error) {
    console.error('❌ Import failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the import
if (require.main === module) {
  main();
}

export { main as importCSV };
