export interface CSVInstrument {
    EXCH_ID: string;
    SEGMENT: string;
    SECURITY_ID: string;
    ISIN: string;
    INSTRUMENT: string;
    UNDERLYING_SECURITY_ID: string;
    UNDERLYING_SYMBOL: string;
    SYMBOL_NAME: string;
    DISPLAY_NAME: string;
    INSTRUMENT_TYPE: string;
    SERIES: string;
    LOT_SIZE: string;
    SM_EXPIRY_DATE: string;
    STRIKE_PRICE: string;
    OPTION_TYPE: string;
    TICK_SIZE: string;
    EXPIRY_FLAG: string;
    BRACKET_FLAG: string;
    COVER_FLAG: string;
    ASM_GSM_FLAG: string;
    ASM_GSM_CATEGORY: string;
    BUY_SELL_INDICATOR: string;
    MTF_LEVERAGE: string;
}
export interface Instrument {
    securityId: string;
    symbol: string;
    displayName: string;
    exchange: string;
    segment: string;
    instrumentType: string;
    isin?: string;
    lotSize: number;
    tickSize: number;
    underlyingSymbol?: string;
    expiryDate?: Date;
    strikePrice?: number;
    optionType?: 'CE' | 'PE' | 'XX';
    isActive: boolean;
    exchangeCode: number;
}
export interface MarketDepthLevel {
    level: number;
    bidPrice: number;
    bidQty: number;
    askPrice: number;
    askQty: number;
}
export interface MarketData {
    securityId: string;
    symbol: string;
    exchange: string;
    ltp: number;
    change: number;
    changePercent: number;
    volume: number;
    high: number;
    low: number;
    open: number;
    close: number;
    timestamp: number;
    bid?: number;
    ask?: number;
    bidQty?: number;
    askQty?: number;
    openInterest?: number;
    expiryDate?: string;
    strikePrice?: number;
    optionType?: 'CE' | 'PE' | 'XX';
    instrumentType?: string;
    marketDepth?: MarketDepthLevel[];
    previousClose?: number;
    ltt?: number;
    ltq?: number;
    atp?: number;
    totalBuyQuantity?: number;
    totalSellQuantity?: number;
}
export interface WebSocketMessage {
    type: 'subscribe' | 'unsubscribe' | 'marketData' | 'error' | 'connection' | 'initialData';
    data?: any;
    error?: string;
    timestamp?: number;
}
export interface SubscriptionRequest {
    exchangeSegment: number;
    securityId: string;
}
export interface InstrumentFilter {
    exchange?: string[];
    instrumentType?: string[];
    segment?: string[];
    search?: string;
    isActive?: boolean;
    hasExpiry?: boolean;
    minLotSize?: number;
    maxLotSize?: number;
}
export interface SearchResult {
    instruments: Instrument[];
    total: number;
    page: number;
    pageSize: number;
    hasMore: boolean;
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
    timestamp: number;
}
export interface InstrumentsResponse {
    instruments: Instrument[];
    total: number;
    exchanges: string[];
    instrumentTypes: string[];
    segments: string[];
}
export interface MarketDataResponse {
    connected: boolean;
    instruments: MarketData[];
    totalInstruments: number;
    activeSubscriptions: number;
    lastUpdate: number;
}
export interface CSVConfig {
    filePath: string;
    delimiter: string;
    encoding: string;
    skipEmptyLines: boolean;
    headers: boolean;
    cacheTTL: number;
}
export interface ServerConfig {
    port: number;
    host: string;
    cors: {
        origin: string[];
        credentials: boolean;
    };
    rateLimit: {
        windowMs: number;
        max: number;
    };
}
export interface CacheEntry<T> {
    data: T;
    timestamp: number;
    ttl: number;
}
export interface CacheStats {
    hits: number;
    misses: number;
    size: number;
    memoryUsage: number;
}
export interface CSVService {
    loadInstruments(): Promise<Instrument[]>;
    getInstruments(filter?: InstrumentFilter): Promise<SearchResult>;
    searchInstruments(query: string, limit?: number): Promise<Instrument[]>;
    getInstrumentById(securityId: string): Promise<Instrument | null>;
    getExchanges(): Promise<string[]>;
    getInstrumentTypes(): Promise<string[]>;
    refreshCache(): Promise<void>;
}
export interface IMarketDataService {
    connect(): Promise<void>;
    disconnect(): void;
    subscribe(instruments: Instrument[]): void;
    unsubscribe(instruments: Instrument[]): void;
    getMarketData(securityId: string): MarketData | null;
    getConnectionStatus(): boolean;
    connected: boolean;
}
export interface AppError extends Error {
    code?: string;
    statusCode?: number;
    isOperational?: boolean;
}
export interface DashboardProps {
    initialInstruments?: Instrument[];
    autoConnect?: boolean;
    refreshInterval?: number;
}
export interface InstrumentTableProps {
    instruments: Instrument[];
    marketData: Map<string, MarketData>;
    onInstrumentSelect?: (instrument: Instrument) => void;
    loading?: boolean;
}
export interface FilterPanelProps {
    filter: InstrumentFilter;
    onFilterChange: (filter: InstrumentFilter) => void;
    exchanges: string[];
    instrumentTypes: string[];
    segments: string[];
}
export type ExchangeSegment = 'NSE_EQ' | 'NSE_FNO' | 'BSE_EQ' | 'BSE_FNO' | 'MCX_COMM' | 'IDX_I';
export type InstrumentType = 'EQUITY' | 'FUTCUR' | 'OPTCUR' | 'FUTSTK' | 'OPTSTK' | 'INDEX';
export type MarketStatus = 'OPEN' | 'CLOSED' | 'PRE_OPEN' | 'POST_CLOSE';
export type DataSource = 'LIVE' | 'MOCK' | 'CACHED';
export interface InstrumentLoadEvent {
    type: 'INSTRUMENTS_LOADED';
    payload: {
        count: number;
        exchanges: string[];
        instrumentTypes: string[];
    };
}
export interface MarketDataEvent {
    type: 'MARKET_DATA_UPDATE';
    payload: MarketData;
}
export interface ConnectionEvent {
    type: 'CONNECTION_STATUS';
    payload: {
        connected: boolean;
        timestamp: number;
    };
}
export type AppEvent = InstrumentLoadEvent | MarketDataEvent | ConnectionEvent;
export interface AppState {
    instruments: Instrument[];
    marketData: Map<string, MarketData>;
    filter: InstrumentFilter;
    connectionStatus: boolean;
    loading: boolean;
    error: string | null;
    lastUpdate: number;
}
export interface AppAction {
    type: string;
    payload?: any;
}
export interface PerformanceMetrics {
    csvParseTime: number;
    instrumentCount: number;
    memoryUsage: number;
    cacheHitRate: number;
    subscriptionCount: number;
    dataUpdateRate: number;
    timestamp: number;
}
export interface OptionData {
    securityId: string;
    symbol: string;
    exchange: string;
    strikePrice: number;
    optionType: 'CE' | 'PE';
    expiryDate: string;
    ltp: number;
    change: number;
    changePercent: number;
    volume: number;
    openInterest?: number;
    bid?: number;
    ask?: number;
    bidQty?: number;
    askQty?: number;
    high?: number;
    low?: number;
    open?: number;
    close?: number;
    timestamp: number;
}
export interface OptionChainRow {
    strikePrice: number;
    call: OptionData | null;
    put: OptionData | null;
}
export interface OptionChainData {
    underlying: string;
    spotPrice: number;
    expiry: string;
    rows: OptionChainRow[];
    timestamp: number;
}
export interface ExpiryData {
    underlying: string;
    securityId: number;
    segment: string;
    expiries: string[];
    count: number;
    hasCredentials: boolean;
}
//# sourceMappingURL=index.d.ts.map