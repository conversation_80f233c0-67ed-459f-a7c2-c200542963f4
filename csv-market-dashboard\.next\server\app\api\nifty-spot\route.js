/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/nifty-spot/route";
exports.ids = ["app/api/nifty-spot/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnifty-spot%2Froute&page=%2Fapi%2Fnifty-spot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnifty-spot%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnifty-spot%2Froute&page=%2Fapi%2Fnifty-spot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnifty-spot%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_love_dashboard_csv_market_dashboard_src_app_api_nifty_spot_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/nifty-spot/route.ts */ \"(rsc)/./src/app/api/nifty-spot/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/nifty-spot/route\",\n        pathname: \"/api/nifty-spot\",\n        filename: \"route\",\n        bundlePath: \"app/api/nifty-spot/route\"\n    },\n    resolvedPagePath: \"D:\\\\love\\\\dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\api\\\\nifty-spot\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_love_dashboard_csv_market_dashboard_src_app_api_nifty_spot_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnifty-spot%2Froute&page=%2Fapi%2Fnifty-spot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnifty-spot%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/nifty-spot/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/nifty-spot/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n/**\n * API endpoint to get NIFTY spot price\n * GET /api/nifty-spot\n */ async function GET(request) {\n    try {\n        console.log('📈 Fetching NIFTY spot price from dedicated calculator...');\n        // 🚀 NEW: Try to get from dedicated NIFTY spot service first\n        try {\n            // Import the NIFTY spot service (server-side only)\n            const { niftySpotService } = await __webpack_require__.e(/*! import() */ \"_rsc_src_services_NiftySpotService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ../../../services/NiftySpotService */ \"(rsc)/./src/services/NiftySpotService.ts\"));\n            const currentData = niftySpotService.getCurrentData();\n            const status = niftySpotService.getStatus();\n            if (currentData && currentData.isReady && currentData.ltp > 0) {\n                console.log('✅ Found NIFTY spot price from dedicated calculator:', currentData.ltp);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: {\n                        securityId: currentData.securityId,\n                        symbol: currentData.symbol,\n                        ltp: currentData.ltp,\n                        change: currentData.changePoints,\n                        changePercent: currentData.changePercent,\n                        previousClose: currentData.previousClose,\n                        lastUpdateTime: currentData.lastUpdateTime,\n                        totalUpdates: currentData.totalUpdates,\n                        source: 'dedicated_calculator',\n                        status: {\n                            isConnected: status?.isConnected || false,\n                            isReady: currentData.isReady,\n                            updateRate: status?.updateRate || '0'\n                        }\n                    },\n                    timestamp: new Date().toISOString()\n                });\n            } else {\n                console.log('⚠️ Dedicated calculator not ready, trying server fallback...');\n            }\n        } catch (calculatorError) {\n            console.warn('⚠️ Could not use dedicated calculator:', calculatorError);\n        }\n        // Fallback: Try to get from server's market data service\n        const serverUrl = \"http://localhost:8081\" || 0;\n        try {\n            const response = await fetch(`${serverUrl}/api/market-data`);\n            if (response.ok) {\n                const data = await response.json();\n                // Look for NIFTY index data (security ID 13 or symbol NIFTY)\n                const niftyData = data.data.instruments.find((item)=>item.securityId === '13' || item.symbol === 'NIFTY' || item.symbol.includes('NIFTY') && item.instrumentType === 'INDEX');\n                if (niftyData && niftyData.ltp > 0) {\n                    console.log('✅ Found NIFTY spot price:', niftyData.ltp);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: true,\n                        data: {\n                            securityId: niftyData.securityId,\n                            symbol: niftyData.symbol,\n                            ltp: niftyData.ltp,\n                            change: niftyData.change,\n                            changePercent: niftyData.changePercent,\n                            high: niftyData.high,\n                            low: niftyData.low,\n                            open: niftyData.open,\n                            close: niftyData.close,\n                            timestamp: niftyData.timestamp,\n                            source: 'live_data'\n                        },\n                        timestamp: new Date().toISOString()\n                    });\n                }\n            }\n        } catch (serverError) {\n            console.warn('⚠️ Could not fetch from server:', serverError);\n        }\n        // Fallback: Return error indicating no live data\n        console.warn('⚠️ No NIFTY spot price available from live data');\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'NIFTY spot price not available from live data',\n            data: null,\n            timestamp: new Date().toISOString()\n        }, {\n            status: 404\n        });\n    } catch (error) {\n        console.error('❌ Error fetching NIFTY spot price:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error.message,\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/nifty-spot/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "ws":
/*!*********************!*\
  !*** external "ws" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("ws");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnifty-spot%2Froute&page=%2Fapi%2Fnifty-spot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnifty-spot%2Froute.ts&appDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clove%5Cdashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();