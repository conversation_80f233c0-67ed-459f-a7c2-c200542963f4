require('dotenv').config({ path: '.env.database' });
const { PrismaClient } = require('./generated/prisma');

async function checkNiftyOptions() {
  const prisma = new PrismaClient();

  try {
    console.log('🔌 Connecting to database...');
    await prisma.$connect();
    console.log('✅ Connected successfully');

    // Check OPTIDX instruments
    const optidxSymbols = await prisma.$queryRaw`
      SELECT DISTINCT "SYMBOL_NAME", COUNT(*) as count
      FROM "Instruments" 
      WHERE "INSTRUMENT_TYPE" = 'OPTIDX'
      GROUP BY "SYMBOL_NAME"
      ORDER BY count DESC
      LIMIT 10
    `;
    
    console.log('\n📊 OPTIDX symbols (Index Options):');
    optidxSymbols.forEach(sym => {
      console.log(`  - ${sym.SYMBOL_NAME}: ${sym.count} instruments`);
    });

    // Check for NIFTY in UNDERLYING_SYMBOL
    const underlyingNifty = await prisma.$queryRaw`
      SELECT DISTINCT "UNDERLYING_SYMBOL", COUNT(*) as count
      FROM "Instruments" 
      WHERE "UNDERLYING_SYMBOL" LIKE '%NIFTY%'
      GROUP BY "UNDERLYING_SYMBOL"
      ORDER BY count DESC
      LIMIT 10
    `;
    
    console.log('\n🎯 Underlying NIFTY symbols:');
    underlyingNifty.forEach(sym => {
      console.log(`  - ${sym.UNDERLYING_SYMBOL}: ${sym.count} instruments`);
    });

    // Check sample NIFTY options
    const niftyOptions = await prisma.$queryRaw`
      SELECT "SYMBOL_NAME", "UNDERLYING_SYMBOL", "INSTRUMENT_TYPE", "SM_EXPIRY_DATE", "STRIKE_PRICE", "OPTION_TYPE"
      FROM "Instruments" 
      WHERE "UNDERLYING_SYMBOL" = 'NIFTY'
      AND "INSTRUMENT_TYPE" = 'OPTIDX'
      LIMIT 10
    `;
    
    console.log('\n📋 Sample NIFTY Options:');
    niftyOptions.forEach(row => {
      console.log(`  - ${row.SYMBOL_NAME} | ${row.UNDERLYING_SYMBOL} | ${row.SM_EXPIRY_DATE} | ${row.STRIKE_PRICE} | ${row.OPTION_TYPE}`);
    });

    // Check expiry dates for NIFTY options
    const expiries = await prisma.$queryRaw`
      SELECT DISTINCT "SM_EXPIRY_DATE"
      FROM "Instruments" 
      WHERE "UNDERLYING_SYMBOL" = 'NIFTY'
      AND "INSTRUMENT_TYPE" = 'OPTIDX'
      AND "SM_EXPIRY_DATE" IS NOT NULL
      AND "SM_EXPIRY_DATE" != ''
      ORDER BY "SM_EXPIRY_DATE"
      LIMIT 10
    `;
    
    console.log('\n📅 NIFTY Option Expiry Dates:');
    expiries.forEach(exp => {
      console.log(`  - ${exp.SM_EXPIRY_DATE}`);
    });

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkNiftyOptions();
