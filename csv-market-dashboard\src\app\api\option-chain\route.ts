import { NextRequest, NextResponse } from 'next/server';
import { databaseInstrumentService } from '../../../services/DatabaseInstrumentService';
import { PrismaClient } from '../../../generated/prisma';

/**
 * API endpoint to get option chain data
 * GET /api/option-chain?expiry=YYYY-MM-DD&symbol=NIFTY
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const expiry = searchParams.get('expiry');
    const symbol = searchParams.get('symbol') || 'NIFTY';

    console.log('📊 Fetching option chain data:', { symbol, expiry });

    // Get NIFTY options from database/CSV
    const options = await databaseInstrumentService.getNiftyOptions(expiry || undefined);
    
    if (options.length === 0) {
      console.warn('⚠️ No options found for:', { symbol, expiry });
      return NextResponse.json({
        success: false,
        error: 'No options found for the specified criteria',
        data: null
      }, { status: 404 });
    }

    // Group options by strike price and option type
    const optionChain: { [strike: string]: { CE?: any, PE?: any } } = {};
    const strikes = new Set<number>();

    options.forEach(option => {
      if (option.strikePrice && option.optionType) {
        const strike = option.strikePrice.toString();
        strikes.add(option.strikePrice);
        
        if (!optionChain[strike]) {
          optionChain[strike] = {};
        }
        
        optionChain[strike][option.optionType as 'CE' | 'PE'] = {
          securityId: option.securityId,
          symbol: option.symbol,
          strikePrice: option.strikePrice,
          expiryDate: option.expiryDate,
          optionType: option.optionType,
          lotSize: option.lotSize,
          tickSize: option.tickSize,
          // Placeholder for live market data
          ltp: 0,
          bid: 0,
          ask: 0,
          volume: 0,
          openInterest: 0,
          change: 0,
          changePercent: 0
        };
      }
    });

    // Sort strikes
    const sortedStrikes = Array.from(strikes).sort((a, b) => a - b);

    // Build option chain rows
    const rows = sortedStrikes.map(strike => {
      const strikeStr = strike.toString();
      return {
        strike,
        CE: optionChain[strikeStr]?.CE || null,
        PE: optionChain[strikeStr]?.PE || null
      };
    });

    // Get available expiry dates
    const expiryDates = await databaseInstrumentService.getNiftyExpiryDates();

    console.log(`✅ Found ${options.length} options, ${sortedStrikes.length} strikes, ${expiryDates.length} expiries`);

    return NextResponse.json({
      success: true,
      data: {
        symbol,
        expiry: expiry || expiryDates[0] || null,
        availableExpiries: expiryDates,
        strikes: sortedStrikes,
        rows,
        totalOptions: options.length,
        source: databaseInstrumentService.getStatus().dbConnected ? 'database' : 'csv'
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error fetching option chain:', error);
    return NextResponse.json({
      success: false,
      error: (error as Error).message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * API endpoint to get available expiry dates
 * GET /api/option-chain/expiries
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { symbol = 'NIFTY' } = body;

    console.log('📅 Fetching expiry dates for:', symbol);

    const expiryDates = await databaseInstrumentService.getNiftyExpiryDates();

    return NextResponse.json({
      success: true,
      data: {
        symbol,
        expiries: expiryDates,
        count: expiryDates.length,
        source: databaseInstrumentService.getStatus().dbConnected ? 'database' : 'csv'
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error fetching expiry dates:', error);
    return NextResponse.json({
      success: false,
      error: (error as Error).message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
