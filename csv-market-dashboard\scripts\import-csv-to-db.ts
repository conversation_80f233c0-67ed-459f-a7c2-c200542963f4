/**
 * CSV to Database Import Script
 * Imports instruments.csv data into PostgreSQL database using Prisma
 */

import { PrismaClient } from '../generated/prisma';
import * as fs from 'fs';
import * as path from 'path';
import * as csv from 'csv-parser';

// Initialize Prisma client
const prisma = new PrismaClient();

interface CSVInstrument {
  SEM_EXM_EXCH_ID: string;
  SEM_SMST_SECURITY_ID: string;
  SEM_INSTRUMENT_NAME: string;
  SEM_TRADING_SYMBOL: string;
  SEM_INSTRUMENT_TYPE: string;
  SEM_ASSET_TOKEN: string;
  SEM_ISIN_CODE: string;
  SEM_LOT_UNITS: string;
  SEM_APM_ID: string;
  SEM_FACE_VALUE: string;
  SEM_TICK_SIZE: string;
  SEM_STRIKE_PRICE: string;
  SEM_EXPIRY_DATE: string;
  SEM_OPTION_TYPE: string;
}

interface ProcessedInstrument {
  securityId: string;
  symbol: string;
  exchange: string;
  exchangeCode: number;
  segment: string;
  instrumentType: string;
  strikePrice?: number;
  expiryDate?: string;
  optionType?: string;
  lotSize?: number;
  tickSize?: number;
}

/**
 * Parse CSV row to instrument data
 */
function parseCSVRow(row: CSVInstrument): ProcessedInstrument | null {
  try {
    // Map exchange codes
    const exchangeCodeMap: { [key: string]: number } = {
      'NSE': 1,
      'BSE': 2,
      'MCX': 3,
      'NCDEX': 4,
      'CDS': 5,
      'IDX_I': 6
    };

    const exchange = row.SEM_EXM_EXCH_ID || 'NSE';
    const exchangeCode = exchangeCodeMap[exchange] || 1;

    // Parse numeric values
    const strikePrice = row.SEM_STRIKE_PRICE ? parseFloat(row.SEM_STRIKE_PRICE) : undefined;
    const lotSize = row.SEM_LOT_UNITS ? parseInt(row.SEM_LOT_UNITS) : undefined;
    const tickSize = row.SEM_TICK_SIZE ? parseFloat(row.SEM_TICK_SIZE) : undefined;

    // Format expiry date
    let expiryDate: string | undefined;
    if (row.SEM_EXPIRY_DATE && row.SEM_EXPIRY_DATE !== '0') {
      // Convert from DDMMMYYYY format to YYYY-MM-DD
      const expiry = row.SEM_EXPIRY_DATE;
      if (expiry.length >= 9) {
        const day = expiry.substring(0, 2);
        const monthStr = expiry.substring(2, 5);
        const year = expiry.substring(5, 9);
        
        const monthMap: { [key: string]: string } = {
          'JAN': '01', 'FEB': '02', 'MAR': '03', 'APR': '04',
          'MAY': '05', 'JUN': '06', 'JUL': '07', 'AUG': '08',
          'SEP': '09', 'OCT': '10', 'NOV': '11', 'DEC': '12'
        };
        
        const month = monthMap[monthStr.toUpperCase()];
        if (month) {
          expiryDate = `${year}-${month}-${day}`;
        }
      }
    }

    return {
      securityId: row.SEM_SMST_SECURITY_ID,
      symbol: row.SEM_TRADING_SYMBOL || row.SEM_INSTRUMENT_NAME,
      exchange,
      exchangeCode,
      segment: row.SEM_INSTRUMENT_TYPE?.substring(0, 1) || 'E',
      instrumentType: row.SEM_INSTRUMENT_TYPE || 'EQ',
      strikePrice: strikePrice && strikePrice > 0 ? strikePrice : undefined,
      expiryDate,
      optionType: row.SEM_OPTION_TYPE && row.SEM_OPTION_TYPE !== '0' ? row.SEM_OPTION_TYPE : undefined,
      lotSize: lotSize && lotSize > 0 ? lotSize : undefined,
      tickSize: tickSize && tickSize > 0 ? tickSize : undefined
    };
  } catch (error) {
    console.error('Error parsing CSV row:', error, row);
    return null;
  }
}

/**
 * Import CSV data to database
 */
async function importCSVToDatabase() {
  const csvFilePath = path.join(__dirname, '..', 'instruments.csv');
  
  console.log('🚀 Starting CSV import to database...');
  console.log('📁 CSV file path:', csvFilePath);
  
  if (!fs.existsSync(csvFilePath)) {
    throw new Error(`CSV file not found: ${csvFilePath}`);
  }

  const instruments: ProcessedInstrument[] = [];
  let totalRows = 0;
  let validRows = 0;
  let errorRows = 0;

  return new Promise<void>((resolve, reject) => {
    fs.createReadStream(csvFilePath)
      .pipe(csv())
      .on('data', (row: CSVInstrument) => {
        totalRows++;
        
        const instrument = parseCSVRow(row);
        if (instrument) {
          instruments.push(instrument);
          validRows++;
        } else {
          errorRows++;
        }

        // Log progress every 1000 rows
        if (totalRows % 1000 === 0) {
          console.log(`📊 Processed ${totalRows} rows (${validRows} valid, ${errorRows} errors)`);
        }
      })
      .on('end', async () => {
        console.log(`✅ CSV parsing complete: ${totalRows} total, ${validRows} valid, ${errorRows} errors`);
        
        try {
          // Clear existing data
          console.log('🗑️ Clearing existing instruments...');
          await prisma.marketData.deleteMany();
          await prisma.instrument.deleteMany();
          
          // Insert instruments in batches
          const batchSize = 1000;
          const totalBatches = Math.ceil(instruments.length / batchSize);
          
          console.log(`📦 Inserting ${instruments.length} instruments in ${totalBatches} batches...`);
          
          for (let i = 0; i < totalBatches; i++) {
            const start = i * batchSize;
            const end = Math.min(start + batchSize, instruments.length);
            const batch = instruments.slice(start, end);
            
            await prisma.instrument.createMany({
              data: batch,
              skipDuplicates: true
            });
            
            console.log(`✅ Batch ${i + 1}/${totalBatches} complete (${batch.length} instruments)`);
          }
          
          console.log('🎉 Database import complete!');
          
          // Get statistics
          const stats = await prisma.instrument.groupBy({
            by: ['instrumentType'],
            _count: {
              id: true
            }
          });
          
          console.log('\n📊 Import Statistics:');
          stats.forEach(stat => {
            console.log(`   ${stat.instrumentType}: ${stat._count.id} instruments`);
          });
          
          resolve();
        } catch (error) {
          console.error('❌ Database import error:', error);
          reject(error);
        }
      })
      .on('error', (error) => {
        console.error('❌ CSV parsing error:', error);
        reject(error);
      });
  });
}

/**
 * Main execution
 */
async function main() {
  try {
    console.log('🔌 Connecting to database...');
    await prisma.$connect();
    console.log('✅ Database connected');
    
    await importCSVToDatabase();
    
  } catch (error) {
    console.error('❌ Import failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    console.log('👋 Database disconnected');
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { importCSVToDatabase, parseCSVRow };
